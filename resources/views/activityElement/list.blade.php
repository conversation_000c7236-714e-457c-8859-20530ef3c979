@include('css')
<style>

</style>

<section class="section-page">
    <form class="layui-form" onsubmit="return false" lay-filter="">
        <div class="layui-form-item mb0">
            <div class="layui-inline">
                <label class="layui-form-label">模块名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="element_name" placeholder="请输入模块名称号" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">视图标记</label>
                <div class="layui-input-inline">
                    <input type="text" name="element_code" placeholder="请输入视图标记" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-inline">
                <button class="layui-btn layui-btn-sm searchBtn" lay-submit lay-filter="getList">查询</button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
            </div>
        </div>
    </form>
    <table id="list" lay-filter="list"></table>
</section>

@verbatim
    <!--工具类-->
    <script type="text/html" id="toolbar">
        <div class="layui-btn-container">
            <a class="layui-btn layui-btn-sm btn-color" lay-event="activityElementAdd">添加模板配置</a>
        </div>
    </script>
    <!--新增模块-->
    <script type="text/html" id="activityElementAddHtml">
        <form class="layui-form layer-box-padding" onsubmit="return false;" lay-filter="activityElementAddForm">
            <input type="hidden" name="id" value="">
            <div class="layui-form-item">
                <label class="layui-form-label required">模板名称：</label>
                <div class="layui-input-block">
                    <input type="text" name="element_name" placeholder="请输入模板名称" lay-verify="required" lay-reqtext="请输入模板名称" lay-vertype="tips" class="layui-input" autocomplete="off"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required">视图标记：</label>
                <div class="layui-input-block">
                    <input type="text" name="element_code" placeholder="请输入视图标记" lay-verify="required" lay-reqtext="请输入网站标题" lay-vertype="tips" class="layui-input" autocomplete="off"/>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn layui-btn-sm activityElementAddSubmit" lay-submit="" lay-filter="activityElementAddSubmit">保存</button>
                    <button class="layui-btn layui-btn-sm  layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </script>
@endverbatim

@include('js')
<script type="text/javascript" src="/assets/js/activityElement/list.js?v={{time()}}"></script>
