@include('css')
<style>
    .activityClass .layui-layer-content {
        height: 500px !important;
        overflow-y: auto !important;
    }

    .layui-radio-disabled > i {
        color: #5fb878 !important;
    }
</style>

<section class="section-page">
    <form class="layui-form" onsubmit="return false" lay-filter="">
        <input type="hidden" name="user_id" value="{{request()->user->org_id}}">
        <div class="layui-form-item mb0">
            <div class="layui-inline">
                <label class="layui-form-label">活动编号</label>
                <div class="layui-input-inline">
                    <input type="text" name="activity_no" value="{{request()->get('activity_no')}}" placeholder="请输入活动编号" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">活动地址</label>
                <div class="layui-input-inline">
                    <input type="text" name="activity_url" placeholder="请输入活动地址" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">活动分类</label>
                <div class="layui-input-inline">
                    <select name="activity_type">
                        <option value="">全部</option>
                        <option value="1">专题活动</option>
                        <option value="2">促销活动</option>
                        <option value="3">邀请有礼活动</option>
                        <option value="4">抽奖活动</option>
                        <option value="5">拉新活动</option>
                        <option value="0">其他活动</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">活动状态</label>
                <div class="layui-input-inline">
                    <select name="activity_status">
                        <option value="">全部</option>
                        <option value="2">未上线</option>
                        <option value="3">已上线</option>
                        <option value="-1">已过期</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">启用状态</label>
                <div class="layui-input-inline">
                    <select name="activity_enable">
                        <option value="">全部</option>
                        <option value="1">启用</option>
                        <option value="-1">禁用</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">活动时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="activity_time" placeholder="请选择活动时间" autocomplete="off" class="layui-input" id="activity_time"/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">创建时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="create_time" placeholder="请选择创建时间" autocomplete="off" class="layui-input" id="create_time"/>
                </div>
            </div>
            <div class="layui-inline">
                <button class="layui-btn layui-btn-sm searchBtn" lay-submit lay-filter="getList">查询</button>
                <a href="/web/activity/list" class="layui-btn layui-btn-primary layui-btn-sm">重置</a>
            </div>
        </div>
    </form>
    <table id="list" lay-filter="list"></table>
</section>

<!--新增-编辑活动-->
<script type="text/html" id="addActivityHtml">
    <form class="layui-form layer-box-padding activityDiagramPic" onsubmit="return false;" lay-filter="activityAddForm">
        <input type="hidden" name="id" value="">
        <div class="layui-form-item">
            <label class="layui-form-label required">活动主体：</label>
            <div class="layui-input-block">
                <input type="radio" name="org_id" value="1" title="猎芯网" disabled>
                <input type="radio" name="org_id" value="3" title="爱智平台" disabled>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">活动名称：</label>
            <div class="layui-input-block">
                <input type="text" name="activity_name" placeholder="请输入活动名称" lay-verify="required" lay-reqtext="请输入活动名称" lay-vertype="tips" class="layui-input" autocomplete="off"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">网站标题：</label>
            <div class="layui-input-block">
                <input type="text" name="web_title" placeholder="请输入网站标题" lay-verify="required" lay-reqtext="请输入网站标题" lay-vertype="tips" class="layui-input" autocomplete="off"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">活动地址：</label>
            <div class="layui-input-block">
                <input type="text" onkeyup="value=value.replace(/[^\a-\z\A-\Z]/g,'')" onpaste="value=value.replace(/[^\a-\z\A-\Z]/g,'')" oncontextmenu="value=value.replace(/[^\a-\z\A-\Z]/g,'')" name="activity_url" placeholder="请输入活动地址-只能使用英文" lay-verify="required" lay-reqtext="请输入活动地址" lay-vertype="tips" class="layui-input activity_url" autocomplete="off"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">活动分类：</label>
            <div class="layui-input-block">
                <select name="activity_type" lay-verify="required" lay-reqtext="请选择活动分类" lay-vertype="tips">
                    <option value="">全部</option>
                    <option value="1">专题活动</option>
                    <option value="2">促销活动</option>
                    <option value="3">邀请有礼活动</option>
                    <option value="4">抽奖活动</option>
                    <option value="5">拉新活动</option>
                    <option value="0">其他活动</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">活动时间：</label>
            <div class="layui-input-block">
                <input type="text" name="activity_time" placeholder="请选择活动时间" lay-verify="required" lay-reqtext="请选择活动时间" lay-vertype="tips" class="layui-input" autocomplete="off" id="activity_time"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">活动平台：</label>
            <div class="layui-input-block web_type_box" style="height: 30px;">
                <input type="checkbox" lay-filter="webTypeChange" lay-skin="primary" title="PC" checked="" class="web_type">
                @if (request()->user->org_id == 1)
                    <input type="checkbox" lay-filter="webTypeChange" lay-skin="primary" title="H5" checked="" class="web_type">
                @endif
                <input type="hidden" name="web_type" value="3">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required" style="white-space: normal;width: 100px;">是否在活动中心展示：</label>
            <div class="layui-input-block">
                <input type="checkbox" name="is_activity_center_show_switch" lay-skin="switch" lay-text="是|否" checked lay-filter="isShowActivityChange">
                <input type="hidden" name="is_activity_center_show" value="1">
            </div>
        </div>
        <div class="layui-form-item hide-box">
            <label class="layui-form-label required" style="white-space: normal;width: 100px;">PC活动图：</label>
            <div class="layui-input-block">
                <input type="hidden" name="image_pc" value="" class="image_val"/>
                <a class="alink activityDiagram" style="height: 30px;line-height: 30px;">上传照片</a>
                <img src="" alt="" width="90" height="30" style="object-fit: cover;cursor: pointer;display: none">
            </div>
        </div>
        @if (request()->user->org_id == 1)
            <div class="layui-form-item hide-box">
                <label class="layui-form-label required" style="white-space: normal;width: 100px;">H5活动图：</label>
                <div class="layui-input-block">
                    <input type="hidden" name="image_h5" value="" class="image_val"/>
                    <a class="alink activityDiagram" style="height: 30px;line-height: 30px;">上传照片</a>
                    <img src="" alt="" width="90" height="30" style="object-fit: cover;cursor: pointer;display: none">
                </div>
            </div>
        @endif
        <div class="layui-form-item">
            <label class="layui-form-label">页面关键词：</label>
            <div class="layui-input-block">
                @if (request()->user->org_id == 1)
                    <textarea placeholder="请输入页面关键词" class="layui-textarea" name="web_keywords">猎芯网,电子元器件采购,电子元器件商城,电子元器件现货,IC采购网,电子元器件</textarea>
                @else
                    <textarea placeholder="请输入页面关键词" class="layui-textarea" name="web_keywords"></textarea>
                @endif
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">页面描述：</label>
            <div class="layui-input-block">
                @if (request()->user->org_id == 1)
                    <textarea placeholder="请输入页面描述" class="layui-textarea" name="web_description">猎芯网,快速成长的专业电子元器件采购商城,贸泽(MOUSER)全系产品、e络盟(element14)树莓派系列产品授权经销商,已获得多轮风险投资。为客户提供电子元器件现货采购,进口报关,电子元器件寄售,供应链金融等一站式电子元器件采购服务</textarea>
                @else
                    <textarea placeholder="请输入页面描述" class="layui-textarea" name="web_description"></textarea>
                @endif
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn layui-btn-sm activityAddSubmit" lay-submit="" lay-filter="activityAddSubmit">保存</button>
                <button class="layui-btn layui-btn-sm  layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</script>

@verbatim
    <!--工具类-->
    <script type="text/html" id="toolbar">
        <div class="layui-btn-container">
            <a class="layui-btn layui-btn-sm btn-color" lay-event="addActivity">新增活动</a>
        </div>
    </script>
@endverbatim

@include('js')
<script type="text/javascript" src="/assets/js/activity/list.js?v={{time()}}"></script>
