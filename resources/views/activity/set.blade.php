<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/assets/images/favicon.ico" rel="icon">
    <title>活动配置面板</title>
    @include('css')
    <link rel="stylesheet" href="/assets/css/activity.min.css?v={{time()}}">
    <style>

        @if($activityInfo['activity_status']==\App\Http\Models\Cube\ActivityModel::ACTIVITY_STATUS_EXPIRED)
         html, body {
            width: 100%;
        }

        @else
             html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        @endif
    </style>
</head>
<body>

<section class="cube-set">
    <!--配置面板头部-->
    <div class="cube-set-head row bothSide verCenter">
        <div class="row verCenter">
            <h2 style="margin-right: 5px;">活动名称</h2>
            <select name="env" lay-ignore class="env">
                <option selected value="1">PC</option>
                <option value="2">H5</option>
            </select>
            <label class="ml10">背景色：</label>
            <input type="text" placeholder="输入背景颜色代码" value="#FFFFFF" class="layui-input" style="width: 100px" id="page_color"/>
            <a class="layui-btn layui-btn-sm ml10" id="page_background_upload">上传背景图</a>
            <a href="javascript:;" class="clearBg">清除图片</a>
            <img src="" alt="" class="page-background" style="display: none" id="page_background">
        </div>
        <div class="btn row verCenter">
            <a class="layui-btn layui-btn-sm" id="preview" href="" target="_blank">预览</a>
            <a class="layui-btn layui-btn-sm layui-btn-primary" onclick="window.location.href='/?jumpUrl=/web/activity/list'">取消</a>
        </div>
    </div>
    <div class="left-menu">
        <div class="shousuo-box row rowCenter verCenter" title="收缩">
            <i class="iconfont icon-shousuo"></i>
        </div>
        <div class="layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this">元件库</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show">
                    <div class="layui-collapse">
                        <div class="layui-colla-item system-compant">
                            <h2 class="layui-colla-title">系统元件<em>（仅PC端有效）</em></h2>
                            <div class="layui-colla-content layui-show">
                                <p class="row verCenter operate">
                                    <input type="checkbox" lay-ignore id="page_navigation_bar" checked>
                                    <span>顶部导航栏</span>
                                </p>
                                <p class="row verCenter operate">
                                    <input type="checkbox" lay-ignore id="page_right_bar">
                                    <span>右侧快键入口</span>
                                </p>
                            </div>
                        </div>
                        <div class="layui-colla-item">
                            <h2 class="layui-colla-title">基本元件</h2>
                            <div class="layui-colla-content layui-show">
                                <ul class="package-list row" id="element"></ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--配置面板区域-->
    <div class="web-html">
        <div class="cube-set-drag-area" style="position: relative;"></div>
        <div class="cube-setting-layer"></div>
    </div>
</section>

@verbatim
    <!--基本元件-->
    <script type="text/html" id="elementHtml">
        {{# layui.each(d, function(index, item){ }}
        {{# if(item.element_status== 1){ }}
        <li class="column verCenter child" draggable="true" id="{{item.element_code}}">
            <i></i>
            <span>{{item.element_name}}</span>
        </li>
        {{# } }}
        {{# }); }}
    </script>
    <!--布局分类-->
    <script type="text/html" id="layoutHtml">
        {{# if (d == 'customLayout') { }}
        <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}" style="height: 300px">
            <div class="cube-set-drag-content customLayout row verCenter"></div>
            <div class="setting-fix-width">
                <ul class="setting column verCenter rowCenter">
                    <li class="row rowCenter verCenter settingObj" title="设置">
                        <i class="layui-icon layui-icon-set-sm"></i>
                    </li>
                    <li class="row rowCenter verCenter move-up" title="上移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter move-down" title="下移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter delete" title="删除">
                        <i class="layui-icon layui-icon-delete">
                        </i>
                    </li>
                </ul>
            </div>
        </div>
        {{# } else if(d=='lotteryCircle') { }}
        <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}">
            <div class="cube-set-drag-content lotteryCircle">
                <div class="lotteryCircle-bg">
                    <p class="number-of-draw row verCenter rowCenter" style="padding: 20px;">
                        <span>您今天还有抽奖机会</span>
                        <em>0</em>
                        <span>次数</span>
                    </p>
                    <div class="turntable-bg">
                        <img id="rotate" src="https://img.ichunt.com/images/ichunt/202303/10/ed68d3caee2395f7a3c3c4d7138a3ca1.png" alt="turntable" class="turntable-pic">
                        <div class="round_click"></div>
                    </div>
                    <div class="btn-wrap row bothSide verCenter" style="padding: 40px 0;width: 700px;">
                        <a href="javascript:;" class="contactBg">填写联系方式</a>
                        <a href="javascript:;" class="prizeBg">查看我的奖品</a>
                    </div>
                </div>
            </div>
            <div class="setting-fix-width">
                <ul class="setting column verCenter rowCenter">
                    <li class="row rowCenter verCenter settingObj" title="设置">
                        <i class="layui-icon layui-icon-set-sm"></i>
                    </li>
                    <li class="row rowCenter verCenter move-up" title="上移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter move-down" title="下移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter delete" title="删除">
                        <i class="layui-icon layui-icon-delete">
                        </i>
                    </li>
                </ul>
            </div>
        </div>
        {{# } else if(d=='lotterySquare') { }}
        <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}">
            <div class="cube-set-drag-content lotterySquare">
                <div class="lotterySquare-bg">
                    <p class="number-of-draw row verCenter rowCenter" style="padding: 20px;">
                        <span>您今天还有抽奖机会</span>
                        <em>0</em>
                        <span>次数</span>
                    </p>
                    <div class="lotterySquare-content" style="background: url('https://img.ichunt.com/images/ichunt/202303/02/6ba6bcfe5451234780fc410fe5d51645.png') no-repeat center;background-size: contain;width: 700px;height: 700px;">
                        <a class="box1"></a>
                        <a class="box2"></a>
                        <a class="box3"></a>
                        <a class="box4"></a>
                        <a class="box5"></a>
                        <a class="box6"></a>
                        <a class="box7"></a>
                        <a class="box8"></a>
                        <span class="btnClick"></span>
                    </div>
                    <div class="btn-wrap row bothSide verCenter" style="padding: 40px 0;width: 700px;">
                        <a href="javascript:;" class="contactBg">填写联系方式</a>
                        <a href="javascript:;" class="prizeBg">查看我的奖品</a>
                    </div>
                </div>
            </div>
            <div class="setting-fix-width">
                <ul class="setting column verCenter rowCenter">
                    <li class="row rowCenter verCenter settingObj" title="设置">
                        <i class="layui-icon layui-icon-set-sm"></i>
                    </li>
                    <li class="row rowCenter verCenter move-up" title="上移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter move-down" title="下移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter delete" title="删除">
                        <i class="layui-icon layui-icon-delete">
                        </i>
                    </li>
                </ul>
            </div>
        </div>
        {{# } else if(d=='coupon') { }}
        <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}">
            <div class="cube-set-drag-content coupon"></div>
            <div class="setting-fix-width">
                <ul class="setting column verCenter rowCenter">
                    <li class="row rowCenter verCenter settingObj" title="设置">
                        <i class="layui-icon layui-icon-set-sm"></i>
                    </li>
                    <li class="row rowCenter verCenter move-up" title="上移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter move-down" title="下移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter delete" title="删除">
                        <i class="layui-icon layui-icon-delete">
                        </i>
                    </li>
                </ul>
            </div>
        </div>
        {{# } else if(d=='shopList') { }}
        <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}">
            <div class="cube-set-drag-content shopList wrap-search"></div>
            <div class="setting-fix-width">
                <ul class="setting column verCenter rowCenter">
                    <li class="row rowCenter verCenter settingObj" title="设置">
                        <i class="layui-icon layui-icon-set-sm"></i>
                    </li>
                    <li class="row rowCenter verCenter move-up" title="上移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter move-down" title="下移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter delete" title="删除">
                        <i class="layui-icon layui-icon-delete">
                        </i>
                    </li>
                </ul>
            </div>
        </div>
        {{# } else if(d=='formModule') { }}
        <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}">
            <div class="cube-set-drag-content formModule">
                <div class="layui-form" lay-filter="formModuleFilterForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">联系方式</label>
                        <div class="layui-input-block">
                            <input type="text" name="mobile" lay-verify="required" lay-reqtext="请输入手机号码" lay-vertype="tips" placeholder="请输入手机号码" autocomplete="off" class="layui-input" maxlength="11">
                        </div>
                    </div>
                    <div class="layui-form-item" id="imgCode" style="display: none">
                        <label class="layui-form-label required">图形验证码</label>
                        <div class="row verCenter bothSide">
                            <input type="text" name="verify" placeholder="请输入图形验证码" autocomplete="off" class="layui-input" style="width: 252px;"/>
                            <img src="/v3/public/verify" alt="" style="width:82px;height:30px;display: inline-block;vertical-align: middle;" id="codePic" title="点击换一张">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">短信验证</label>
                        <div class="row verCenter bothSide">
                            <input type="text" name="sms_verify" lay-verify="required" lay-reqtext="请输入短信验证" lay-vertype="tips" placeholder="请输入短信验证" autocomplete="off" class="layui-input"/>
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" lay-on="get-vercode" style="margin-left: 10px;background: #fff;" id="verifyCode">获取验证码</button>
                        </div>
                    </div>
                    <div class="layui-form-item" id="company_name">
                        <label class="layui-form-label required">公司名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="company_name" lay-verify="required" lay-reqtext="请填写公司名称" lay-vertype="tips" placeholder="请填写公司名称" autocomplete="off" class="layui-input company_name">
                        </div>
                    </div>
                    <div class="columns-dynamics-form"></div>
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 128px;">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="formModuleFilterSubmit" id="formModuleFilterSubmit">提交</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="setting-fix-width">
                <ul class="setting column verCenter rowCenter">
                    <li class="row rowCenter verCenter settingObj" title="设置">
                        <i class="layui-icon layui-icon-set-sm"></i>
                    </li>
                    <li class="row rowCenter verCenter move-up" title="上移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter move-down" title="下移">
                        <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
                    </li>
                    <li class="row rowCenter verCenter delete" title="删除">
                        <i class="layui-icon layui-icon-delete">
                        </i>
                    </li>
                </ul>
            </div>
        </div>
        {{# } }}
    </script>
    <!--楼层分类-->
    <script type="text/html" id="floorHtml">
        {{# if (d == 'customLayout') { }}
        <div class="row bothSide verCenter title">
            <h2 class="tt">
                <em class="floor">楼层</em>
                <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
                <span class="floor-name"></span>
            </h2>
            <span class="toggle">关闭</span>
        </div>
        <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
            <ul class="layui-tab-title">
                <li class="layui-this">布局设置</li>
                <li>全局楼层</li>
                <li>热区</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show layui-form customLayout" lay-filter="floorSettingForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label required">宽度</label>
                        <div class="layui-input-inline" style="width: 100px;">
                            <input type="text" name="width" lay-verify="required" lay-reqtext="请填写宽度" lay-vertype="tips" autocomplete="off" placeholder="最小1190px，最大1920px" class="layui-input width" value="1190">
                        </div>
                        <div class="layui-form-mid layui-word-aux">px （最小1190px，最大1920px）</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">高度</label>
                        <div class="layui-input-inline">
                            <input type="text" name="height" lay-verify="required" lay-reqtext="请填写高度" lay-vertype="tips" autocomplete="off" placeholder="请输入高度" class="layui-input height" value="300">
                        </div>
                        <div class="layui-form-mid layui-word-aux">px</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">列数</label>
                        <div class="layui-input-inline">
                            <select lay-filter="columnsChange" name="columns" class="columns">
                                <option value="1">1列</option>
                                <option value="2">2列</option>
                                <option value="3">3列</option>
                                <option value="4">4列</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">间距</label>
                        <div class="layui-input-inline">
                            <input type="text" name="spacing" lay-verify="required" lay-reqtext="请填写间距" lay-vertype="tips" autocomplete="off" placeholder="请输入间距" class="layui-input spacing" value="0">
                        </div>
                        <div class="layui-form-mid layui-word-aux">px</div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required">列宽</label>
                        <div class="layui-input-inline columnWidthDynamics row verCenter">
                            <input type="text" name="columnWidthOne" lay-verify="required" lay-reqtext="请填写列宽" lay-vertype="tips" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="1190" style="width: 55px;">
                        </div>
                    </div>
                    <div class="columns-dynamics">
                        <div class="layui-form-item">
                            <label class="layui-form-label">第1列</label>
                            <div class="layui-input-block" style="margin-left: 62px;">
                                <input type="text" name="columnOneHref" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url">
                            </div>
                            <div class="row bothSide verCenter bar-wrap">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif/mp4</p>
                                </div>
                                <div>
                                    <img src="" alt="" class="pic" style="display: none">
                                    <input type="hidden" name="columnOneUrl" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 62px;">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="save">保存</button>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <table id="list"></table>
                </div>
                <div class="layui-tab-item layui-form hot-area" lay-filter="hotForm">
                    <div class="tip-text row verCenter" style="margin-bottom: 10px;">
                        <i class="iconfont icon-instant-error"></i>
                        <p>在元件上方增加图层，并赋予交互</p>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 90px;">列数</label>
                        <div class="layui-input-inline">
                            <select name="columns" class="columns">
                                <option value="1">1列</option>
                                <option value="2">2列</option>
                                <option value="3">3列</option>
                                <option value="4">4列</option>
                            </select>
                        </div>
                    </div>
                    <div class="hot-columns-dynamics">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 90px;">第1列宽度</label>
                            <div class="layui-input-inline">
                                <input type="text" name="width" autocomplete="off" placeholder="请输入宽度" class="layui-input width" value="">
                            </div>
                            <div class="layui-form-mid layui-word-aux">px</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 90px;">第1列高度</label>
                            <div class="layui-input-inline">
                                <input type="text" name="height" autocomplete="off" placeholder="请输入高度" class="layui-input height" value="">
                            </div>
                            <div class="layui-form-mid layui-word-aux">px</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 90px;">第1列X轴</label>
                            <div class="layui-input-inline" style="width: 108px;">
                                <input type="text" name="x" autocomplete="off" placeholder="请输入X轴位置" class="layui-input x">
                            </div>
                            <div class="layui-form-mid layui-word-aux">（可拖动热区调整）</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 90px;">第1列Y轴</label>
                            <div class="layui-input-inline" style="width: 108px;">
                                <input type="text" name="y" autocomplete="off" placeholder="请输入Y轴位置" class="layui-input y">
                            </div>
                            <div class="layui-form-mid layui-word-aux">（可拖动热区调整）</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 90px;">第1列地址</label>
                            <div class="layui-input-block" style="margin-left: 90px;">
                                <input type="text" name="columnOneHref" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url">
                            </div>
                            <div class="row bothSide verCenter bar-wrap" style="padding-left: 90px;">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif/mp4</p>
                                </div>
                                <div>
                                    <img src="" alt="" class="pic" style="display: none">
                                    <input type="hidden" name="columnOneUrl" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 90px;">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="hotSubmit" id="hotSubmit">保存</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{# } else if(d=='lotteryCircle') { }}
        <div class="row bothSide verCenter title">
            <h2 class="tt">
                <em class="floor">楼层</em>
                <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
                <span class="floor-name"></span>
            </h2>
            <span class="toggle">关闭</span>
        </div>
        <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
            <ul class="layui-tab-title">
                <li class="layui-this">布局设置</li>
                <li>全局楼层</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show  layui-form lotteryCircle" lay-filter="lotteryCircleForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">抽奖ID</label>
                        <div class="layui-input-inline">
                            <input type="text" name="lotteryId" lay-verify="required" lay-reqtext="请填写抽奖ID" lay-vertype="tips" autocomplete="off" placeholder="请输入抽奖ID" class="layui-input lotteryId">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">奖项个数</label>
                        <div class="layui-input-inline" style="width: 50px">
                            <input type="text" name="num" lay-verify="required" lay-reqtext="请输入奖项个数" lay-vertype="tips" autocomplete="off" placeholder="请输入奖项个数" class="layui-input layui-disabled num" value="8" disabled>
                        </div>
                        <div class="layui-form-mid layui-word-aux">角度：<em class="angle">45</em></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">实物中奖提示</label>
                        <div class="layui-input-inline">
                            <input type="text" name="prizeMsg" lay-verify="required" lay-reqtext="请填各实物中奖提示" lay-vertype="tips" autocomplete="off" placeholder="请填写中奖提示" class="layui-input prizeMsg" value="请填写联系方式，工作人员会寄送奖品">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">抽奖图片</label>
                        <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：708*708</p>
                                </div>
                                <div>
                                    <img src="https://img.ichunt.com/images/ichunt/202303/10/ed68d3caee2395f7a3c3c4d7138a3ca1.png" alt="" class="pic">
                                    <input type="hidden" name="lotteryCircleBg" value="https://img.ichunt.com/images/ichunt/202303/10/ed68d3caee2395f7a3c3c4d7138a3ca1.png">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">联系方式按钮</label>
                        <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：150*30</p>
                                </div>
                                <div>
                                    <img src="" alt="" class="pic" style="display: none;">
                                    <input type="hidden" name="contactBg" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">我的奖品按钮</label>
                        <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：150*30</p>
                                </div>
                                <div>
                                    <img src="" alt="" class="pic" style="display: none;">
                                    <input type="hidden" name="prizeBg" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">背景图</label>
                        <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif/mp4<br/>建议尺寸：1190*1010</p>
                                </div>
                                <div>
                                    <img src="https://img.ichunt.com/images/ichunt/202303/09/5280cd20b5f9b931a70875cc1236766c.jpg" alt="" class="pic">
                                    <input type="hidden" name="bg" value="https://img.ichunt.com/images/ichunt/202303/09/5280cd20b5f9b931a70875cc1236766c.jpg">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 111px;">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="lotteryCircleSubmit" id="lotteryCircleSubmit">保存</button>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <table id="list"></table>
                </div>
            </div>
        </div>
        {{# } else if(d=='lotterySquare') { }}
        <div class="row bothSide verCenter title">
            <h2 class="tt">
                <em class="floor">楼层</em>
                <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
                <span class="floor-name"></span>
            </h2>
            <span class="toggle">关闭</span>
        </div>
        <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
            <ul class="layui-tab-title">
                <li class="layui-this">布局设置</li>
                <li>全局楼层</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show  layui-form lotterySquare" lay-filter="lotterySquareForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">抽奖ID</label>
                        <div class="layui-input-inline">
                            <input type="text" name="lotteryId" lay-verify="required" lay-reqtext="请填写抽奖ID" lay-vertype="tips" autocomplete="off" placeholder="请输入抽奖ID" class="layui-input lotteryId">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">各奖项长/宽</label>
                        <div class="layui-input-inline" style="width: 50px">
                            <input type="text" name="width" lay-verify="required" lay-reqtext="请填各奖项长/宽" lay-vertype="tips" autocomplete="off" placeholder="" class="layui-input width" value="226">
                        </div>
                        <div class="layui-form-mid layui-word-aux">间隔11，总宽度:<em class="total-width">700</em>&nbsp;&nbsp;总高度:<em class="total-width">700</em></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">实物中奖提示</label>
                        <div class="layui-input-inline">
                            <input type="text" name="prizeMsg" lay-verify="required" lay-reqtext="请填各实物中奖提示" lay-vertype="tips" autocomplete="off" placeholder="请填写中奖提示" class="layui-input prizeMsg" value="请填写联系方式，工作人员会寄送奖品">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">抽奖图片</label>
                        <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：700*700</p>
                                </div>
                                <div>
                                    <img src="https://img.ichunt.com/images/ichunt/202303/02/6ba6bcfe5451234780fc410fe5d51645.png" alt="" class="pic">
                                    <input type="hidden" name="lotterySquareBg" value="https://img.ichunt.com/images/ichunt/202303/02/6ba6bcfe5451234780fc410fe5d51645.png">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">联系方式按钮</label>
                        <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：209*59</p>
                                </div>
                                <div>
                                    <img src="" alt="" class="pic" style="display: none;">
                                    <input type="hidden" name="contactBg" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">我的奖品按钮</label>
                        <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：209*59</p>
                                </div>
                                <div>
                                    <img src="" alt="" class="pic" style="display: none;">
                                    <input type="hidden" name="prizeBg" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 110px;">背景图</label>
                        <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif/mp4<br/>建议尺寸：1190*59</p>
                                </div>
                                <div>
                                    <img src="https://img.ichunt.com/images/ichunt/202303/02/39a26dcb241686ca2d679facb272e911.jpg" alt="" class="pic">
                                    <input type="hidden" name="bg" value="https://img.ichunt.com/images/ichunt/202303/02/39a26dcb241686ca2d679facb272e911.jpg">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 111px;">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="lotterySquareSubmit" id="lotterySquareSubmit">保存</button>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <table id="list"></table>
                </div>
            </div>
        </div>
        {{# } else if(d=='coupon') { }}
        <div class="row bothSide verCenter title">
            <h2 class="tt">
                <em class="floor">楼层</em>
                <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
                <span class="floor-name"></span>
            </h2>
            <span class="toggle">关闭</span>
        </div>
        <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
            <ul class="layui-tab-title">
                <li class="layui-this">布局设置</li>
                <li>全局楼层</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show  layui-form coupon" lay-filter="couponForm">
                    <div class="layui-form-item" style="display:none;">
                        <label class="layui-form-label" style="width: 105px;">背景图</label>
                        <div class="layui-input-inline" style="width: 254px">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif</p>
                                </div>
                                <div>
                                    <img src="" alt="" class="pic" style="display: none">
                                    <input type="hidden" name="bg" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 105px;">优惠券个数</label>
                        <div class="layui-input-inline">
                            <input type="text" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))" onkeyup="value=value.replace(/[^\d]/g,'')" name="couponNum" lay-verify="required" lay-reqtext="请填写优惠券个数" lay-vertype="tips" autocomplete="off" placeholder="请填写优惠券个数" class="layui-input couponNum">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 105px;">优惠券尺寸</label>
                        <div class="layui-input-inline row verCenter">
                            <input style="width: 62px;" type="text" onkeyup="value=value.replace(/[^\d]/g,'')" name="couponLength" lay-verify="required" lay-reqtext="请填写优惠长度" lay-vertype="tips" autocomplete="off" placeholder="长度" class="layui-input couponLength">
                            <span style="padding: 0 10px;">*</span>
                            <input style="width: 62px;" type="text" onkeyup="value=value.replace(/[^\d]/g,'')" name="couponWidth" lay-verify="required" lay-reqtext="请填写优惠宽度" lay-vertype="tips" autocomplete="off" placeholder="宽度" class="layui-input couponWidth">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 105px;">样式</label>
                        <div class="layui-input-inline">
                            <select name="pattern">
                                <option value="1">轮播（超3张轮播）</option>
                                <option value="2">平铺</option>
                            </select>
                        </div>
                    </div>
                    <div class="columns-dynamics">
                        <div class="layui-form-item">
                            <label class="layui-form-label required" style="width: 105px;">批次1</label>
                            <div class="layui-input-block" style="margin-left: 105px;">
                                <input type="text" lay-verify="required" lay-reqtext="请填写批次号" lay-vertype="tips" autocomplete="off" placeholder="批次号" class="layui-input columnCoupon">
                            </div>
                            <div class="row bothSide verCenter bar-wrap" style="padding-left: 105px;">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif</p>
                                </div>
                                <div>
                                    <img src="" alt="" class="pic" style="display: none">
                                    <input type="hidden" value="" class="columnCouponUrl">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 105px;">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="couponSubmit" id="couponSubmit">保存</button>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <table id="list"></table>
                </div>
            </div>
        </div>
        {{# } else if(d=='shopList') { }}
        <div class="row bothSide verCenter title">
            <h2 class="tt">
                <em class="floor">楼层</em>
                <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
                <span class="floor-name"></span>
            </h2>
            <span class="toggle">关闭</span>
        </div>
        <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
            <ul class="layui-tab-title">
                <li class="layui-this">布局设置</li>
                <li>全局楼层</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show  layui-form shopList" lay-filter="shopListForm">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 105px;">筛选</label>
                        <div class="layui-input-block checkbox-style row verCenter">
                            <div class="row verCenter" style="margin-right: 20px">
                                <input type="checkbox" title="品牌" lay-skin="primary" lay-ignore id="is_filter_brand">
                                <span style="margin-left: 5px;">品牌</span>
                            </div>
                            <div class="row verCenter" style="margin-right: 20px;">
                                <input type="checkbox" title="供应商" lay-skin="primary" lay-ignore id="is_filter_supplier">
                                <span style="margin-left: 5px;">供应商</span>
                            </div>
                            <div class="row verCenter">
                                <input type="checkbox" title="搜索框" lay-skin="primary" lay-ignore id="is_search">
                                <span style="margin-left: 5px;">搜索框</span>
                                <input type="text" placeholder="颜色配置" class="layui-input" id="is_search_color" style="width: 100px;margin-left: 10px;display: none;">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 105px;">选择维度</label>
                        <div class="layui-input-block">
                            <input type="radio" name="dimensionChange" lay-filter="dimensionChange" value="1" title="活动价">
                            <input type="radio" name="dimensionChange" lay-filter="dimensionChange" value="2" title="SKUID">
                            <input type="radio" name="dimensionChange" lay-filter="dimensionChange" value="3" title="商品属性" checked="">
                            <input type="hidden" name="is_activity_price" value="0">
                            <input type="hidden" name="is_sku_id_upload" value="0">
                            <input type="hidden" name="is_sku_property" value="1">
                        </div>
                    </div>
                    <!--切换维度html-->
                    <div class="dimension-box">
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="width: 105px;">商品分类</label>
                            <div class="layui-input-block">
                                <input type="radio" name="is_sku_type" lay-filter="skuTypeChange" value="1" title="自营" checked>
                                <input type="radio" name="is_sku_type" lay-filter="skuTypeChange" value="2" title="专营">
                            </div>
                        </div>
                        <div id="shopSort">
                            <div class="layui-form-item">
                                <div class="layui-input-block" style="margin-left: 105px;">
                                    <button type="button" class="layui-btn layui-btn-sm" id="addRowSelf">新增行</button>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block" style="margin-left: 105px;">
                                    <table class="layui-table">
                                        <thead>
                                        <tr>
                                            <th style="white-space: nowrap">分类ID</th>
                                            <th style="white-space: nowrap">品牌ID</th>
                                            <th style="white-space: nowrap">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody id="rowAreaSelf">
                                        <tr>
                                            <td class="classify">
                                                <input type="hidden" class="type" value="1">
                                                <input type="text" placeholder="逗号隔开" class="layui-input class_ids">
                                            </td>
                                            <td class="brand">
                                                <input type="text" placeholder="逗号隔开" class="layui-input brand_ids">
                                            </td>
                                            <td>
                                                <button type="button" class="layui-btn layui-btn-sm layui-btn-danger delete">删除</button>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 105px;">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="shopListSubmit" id="shopListSubmit">保存</button>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <table id="list"></table>
                </div>
            </div>
        </div>
        {{# } else if(d=='formModule') { }}
        <div class="row bothSide verCenter title">
            <h2 class="tt">
                <em class="floor">楼层</em>
                <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
                <span class="floor-name"></span>
            </h2>
            <span class="toggle">关闭</span>
        </div>
        <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
            <ul class="layui-tab-title">
                <li class="layui-this">布局设置</li>
                <li>全局楼层</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show  layui-form formModule" lay-filter="formModuleForm">
                    <div class="tip-text row verCenter" style="margin-bottom: 10px;"><i class="iconfont icon-instant-error"></i>
                        <p>手机号为表单必填项，且无法调整配置</p></div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 127px;">背景图</label>
                        <div class="layui-input-inline" style="width: 280px">
                            <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                                <div>
                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                                    <a href="javascript:;" class="clearPic">清除图片</a>
                                    <p class="tip">支持扩展名png/jpg/gif</p>
                                </div>
                                <div>
                                    <img src="" alt="" class="pic" style="display: none">
                                    <input type="hidden" name="bg" value="">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 127px;">仅针对新用户</label>
                        <div class="layui-input-inline">
                            <input type="checkbox" name="switch" lay-skin="switch" value="1" lay-text="是|否">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 127px;">公司名称</label>
                        <div class="layui-input-inline">
                            <input type="radio" name="company_name" value="0" title="不显示">
                            <input type="radio" name="company_name" value="1" title="显示（非必填）">
                            <input type="radio" name="company_name" value="2" title="显示（必填）" checked>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label required" style="width: 127px;">自定义字段个数</label>
                        <div class="layui-input-inline">
                            <input type="text" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d]/g,''))" onkeyup="value=value.replace(/[^\d]/g,'')" name="fieldNum" lay-verify="required" lay-reqtext="请填写自定义字段个数" lay-vertype="tips" autocomplete="off" placeholder="请填写自定义字段个数" class="layui-input fieldNum" value="1">
                        </div>
                    </div>
                    <div class="columns-dynamics">
                        <div class="layui-form-item">
                            <label class="layui-form-label required" style="width: 127px;">输入框1</label>
                            <div class="layui-input-inline">
                                <input type="text" lay-verify="required" lay-reqtext="请填写该输入框名称" lay-vertype="tips" autocomplete="off" placeholder="请填写该输入框名称" class="layui-input input_name">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 127px;">提交成功提示语</label>
                        <div class="layui-input-inline">
                            <input type="text" name="submitMsg" autocomplete="off" placeholder="请填写提交成功提示语" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 127px;">提交后跳转至</label>
                        <div class="layui-input-inline">
                            <input type="text" name="submitUrl" autocomplete="off" placeholder="请填写提交后跳转至" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 127px;">提交按钮名称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="submitBtn" autocomplete="off" placeholder="请填写提交按钮名称" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 128px;">
                            <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="formModuleSubmit" id="formModuleSubmit">保存</button>
                        </div>
                    </div>
                </div>
                <div class="layui-tab-item">
                    <table id="list"></table>
                </div>
            </div>
        </div>
        {{# } }}
    </script>
    <!--优惠券-->
    <script type="text/html" id="couponHtml">
        {{# if (d.pattern == 1) { }}
        <div class="carousel row rowCenter verCenter">
            <div class="bd">
                <ul class="picList">
                    {{# layui.each(d.columnCouponUrl, function(index, item){ }}
                    <li data-coupon-id="{{d.columnCouponId[index]}}" style="width: {{d.couponLength}}px;height: {{d.couponWidth}}px;">
                        <a href="javascript:;">
                            <img src="{{item}}" alt="">
                        </a>
                    </li>
                    {{# }); }}
                </ul>
            </div>
            <div class="hd">
                <a class="layui-icon prev">&#xe603;</a>
                <a class="layui-icon next">&#xe602;</a>
            </div>
        </div>
        {{# } else if(d.pattern ==2) { }}
        <div class="carousel-list">
            <ul class="picList row verCenter">
                {{# layui.each(d.columnCouponUrl, function(index, item){ }}
                <li data-coupon-id="{{d.columnCouponId[index]}}" style="width: {{d.couponLength}}px;height: {{d.couponWidth}}px;">
                    <a href="javascript:;">
                        <img src="{{item}}" alt="">
                    </a>
                </li>
                {{# }); }}
            </ul>
        </div>
        {{# } }}
    </script>
    <!--列表-选择维度-->
    <script type="text/html" id="dimensionHtml">
        {{# if (d == 1) { }}
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 105px;">活动价ID</label>
            <div class="layui-input-block" style="margin-left: 105px;">
                <div id="activity_price_ids"></div>
            </div>
        </div>
        {{# } else if(d == 2) { }}
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 105px;">SKU ID</label>
            <div class="layui-input-block row verCenter" style="margin-left: 105px;">
                <a href="/template/sku_upload.xlsx" class="alink" style="margin-right: 6px;">下载模板</a>
                <button class="layui-btn layui-btn-primary layui-btn-sm layui-border-green" style="margin-right: 6px;" id="sku_id_upload">上传文件</button>
                <a class="layui-icon layui-icon-download-circle" title="下载文件" style="font-size: 22px;color: #1c8eff;cursor: pointer;"></a>
                <input type="hidden" name="sku_id_upload_file" value="">
                <input type="hidden" name="sku_ids" value="">
            </div>
        </div>
        {{# } else if(d == 3) { }}
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 105px;">商品分类</label>
            <div class="layui-input-block">
                <input type="radio" name="is_sku_type" lay-filter="skuTypeChange" value="1" title="自营" checked>
                <input type="radio" name="is_sku_type" lay-filter="skuTypeChange" value="2" title="专营">
            </div>
        </div>
        <div id="shopSort">
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 105px;">
                    <button type="button" class="layui-btn layui-btn-sm" id="addRowSelf">新增行</button>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 105px;">
                    <table class="layui-table">
                        <thead>
                        <tr>
                            <th style="white-space: nowrap">分类ID</th>
                            <th style="white-space: nowrap">品牌ID</th>
                            <th style="white-space: nowrap">操作</th>
                        </tr>
                        </thead>
                        <tbody id="rowAreaSelf">
                        <tr>
                            <td class="classify">
                                <input type="hidden" class="type" value="1">
                                <input type="text" placeholder="逗号隔开" class="layui-input class_ids">
                            </td>
                            <td class="brand">
                                <input type="text" placeholder="逗号隔开" class="layui-input brand_ids">
                            </td>
                            <td>
                                <button type="button" class="layui-btn layui-btn-sm layui-btn-danger delete">删除</button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {{# } }}
    </script>
    <!--列表-商品分类-->
    <script type="text/html" id="shopSortHtml">
        {{# if (d == 1) { }}
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 105px;">
                <button type="button" class="layui-btn layui-btn-sm" id="addRowSelf">新增行</button>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 105px;">
                <table class="layui-table">
                    <thead>
                    <tr>
                        <th style="white-space: nowrap">分类ID</th>
                        <th style="white-space: nowrap">品牌ID</th>
                        <th style="white-space: nowrap">操作</th>
                    </tr>
                    </thead>
                    <tbody id="rowAreaSelf">
                    <tr>
                        <td class="classify">
                            <input type="hidden" class="type" value="1">
                            <input type="text" placeholder="逗号隔开" class="layui-input class_ids">
                        </td>
                        <td class="brand">
                            <input type="text" placeholder="逗号隔开" class="layui-input brand_ids">
                        </td>
                        <td>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-danger delete">删除</button>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        {{# } else if(d == 2) { }}
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 105px;">
                <button type="button" class="layui-btn layui-btn-sm" id="addRow">新增行</button>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 105px;">
                <table class="layui-table">
                    <colgroup>
                        <col width="100">
                    </colgroup>
                    <thead>
                    <tr>
                        <th>商品分类</th>
                        <th style="white-space: nowrap">供应商</th>
                        <th style="white-space: nowrap">分类ID</th>
                        <th style="white-space: nowrap">品牌ID</th>
                        <th style="white-space: nowrap">专营内部编码</th>
                        <th style="white-space: nowrap">专营渠道标签</th>
                        <th style="white-space: nowrap">操作</th>
                    </tr>
                    </thead>
                    <tbody id="rowArea">
                    <tr>
                        <td>
                            <select name="type" lay-filter="typeChange" class="type">
                                <option selected value="2">专营</option>
                                <option value="3">代购</option>
                            </select>
                        </td>
                        <td class="supplier">
                            <input type="text" placeholder="输入1个供应商编码" class="layui-input supplier_codes" style="width: 122px;">
                            <input type="text" placeholder="输入1个供应商ID" class="layui-input supplier_ids" style="display: none;width: 110px;">
                        </td>
                        <td class="classify">
                            <input type="text" placeholder="逗号隔开" class="layui-input class_ids">
                        </td>
                        <td class="brand">
                            <input type="text" placeholder="逗号隔开" class="layui-input brand_ids">
                        </td>
                        <td class="code">
                            <input type="text" placeholder="逗号隔开" class="layui-input canal_code">
                        </td>
                        <td class="label">
                            <input type="text" placeholder="逗号隔开" class="layui-input goods_labels">
                        </td>
                        <td>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-danger delete">删除</button>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        {{# } }}
    </script>
    <!--商品属性分自营&&专营-->
    <script type="text/html" id="skuPropertyHtml">
        {{# if (d.is_sku_type == 1) { }}
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 105px;">
                <button type="button" class="layui-btn layui-btn-sm" id="addRowSelf">新增行</button>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 105px;">
                <table class="layui-table">
                    <thead>
                    <tr>
                        <th style="white-space: nowrap">分类ID</th>
                        <th style="white-space: nowrap">品牌ID</th>
                        <th style="white-space: nowrap">操作</th>
                    </tr>
                    </thead>
                    <tbody id="rowAreaSelf">
                    {{# layui.each(d.data, function(index, item){ }}
                    <tr>
                        <td class="classify">
                            <input type="hidden" class="type" value="1">
                            <input type="text" placeholder="逗号隔开" class="layui-input class_ids" value="{{item.class_ids}}">
                        </td>
                        <td class="brand">
                            <input type="text" placeholder="逗号隔开" class="layui-input brand_ids" value="{{item.brand_ids}}">
                        </td>
                        <td>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-danger delete">删除</button>
                        </td>
                    </tr>
                    {{# }); }}
                    </tbody>
                </table>
            </div>
        </div>
        {{# } else if(d.is_sku_type==2) { }}
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 105px;">
                <button type="button" class="layui-btn layui-btn-sm" id="addRow">新增行</button>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 105px;">
                <table class="layui-table">
                    <colgroup>
                        <col width="100">
                    </colgroup>
                    <thead>
                    <tr>
                        <th>商品分类</th>
                        <th style="white-space: nowrap">供应商</th>
                        <th style="white-space: nowrap">分类ID</th>
                        <th style="white-space: nowrap">品牌ID</th>
                        <th style="white-space: nowrap">专营内部编码</th>
                        <th style="white-space: nowrap">专营渠道标签</th>
                        <th style="white-space: nowrap">操作</th>
                    </tr>
                    </thead>
                    <tbody id="rowArea">
                    {{# layui.each(d.data, function(index, item){ }}
                    <tr>
                        <td>
                            <select name="type" lay-filter="typeChange" class="type">
                                <option {{item.type == 2 ? 'selected' : ''}}  value="2">专营</option>
                                <option {{item.type == 3 ? 'selected' : ''}}  value="3">代购</option>
                            </select>
                        </td>
                        <td class="supplier" data="{{JSON.stringify(item)}}">
                            {{# if (item.type == 1) { }}
                            <input type="text" placeholder="输入1个供应商编码" class="layui-input supplier_codes" style="display: none;width: 122px;">
                            <input type="text" placeholder="输入1个供应商ID" class="layui-input supplier_ids" style="display: none;width: 110px;">
                            {{# } else if(item.type==2) { }}
                            <input type="text" placeholder="输入1个供应商编码" class="layui-input supplier_codes" style="width: 122px;" value="{{item.supplier_codes}}" data-supplier-ids="{{item.supplier_ids}}" data-supplier-codes="{{item.supplier_codes}}" data-supplier-names="{{item.supplier_names}}">
                            <input type="text" placeholder="输入1个供应商ID" class="layui-input supplier_ids" style="display: none;width: 110px;">
                            {{# } else if(item.type==3) { }}
                            <input type="text" placeholder="输入1个供应商编码" class="layui-input supplier_codes" style="display: none;width: 122px;">
                            <input type="text" placeholder="输入1个供应商ID" class="layui-input supplier_ids" style="width: 110px;" value="{{item.supplier_ids}}" data-supplier-ids="{{item.supplier_ids}}" data-supplier-names="{{item.supplier_names}}">
                            {{# } }}
                        </td>
                        <td class="classify">
                            <input type="text" placeholder="逗号隔开" class="layui-input class_ids" value="{{item.class_ids}}">
                        </td>
                        <td class="brand">
                            <input type="text" placeholder="逗号隔开" class="layui-input brand_ids" value="{{item.brand_ids}}">
                        </td>
                        <td class="code">
                            {{# if (item.type == 1) { }}
                            <input type="text" placeholder="逗号隔开" class="layui-input canal_code" style="display: none" value="{{item.canal_code}}">
                            {{# } else if(item.type==2) { }}
                            <input type="text" placeholder="逗号隔开" class="layui-input canal_code" value="{{item.canal_code}}">
                            {{# } else if(item.type==3) { }}
                            <input type="text" placeholder="逗号隔开" class="layui-input canal_code" style="display: none" value="{{item.canal_code}}">
                            {{# } }}
                        </td>
                        <td class="label">
                            {{# if (item.type == 1) { }}
                            <input type="text" placeholder="逗号隔开" class="layui-input goods_labels" style="display: none" value="{{item.goods_labels}}">
                            {{# } else if(item.type==2) { }}
                            <input type="text" placeholder="逗号隔开" class="layui-input goods_labels" value="{{item.goods_labels}}">
                            {{# } else if(item.type==3) { }}
                            <input type="text" placeholder="逗号隔开" class="layui-input goods_labels" style="display: none" value="{{item.goods_labels}}">
                            {{# } }}
                        </td>
                        <td>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-danger delete">删除</button>
                        </td>
                    </tr>
                    {{# }); }}
                    </tbody>
                </table>
            </div>
        </div>
        {{# } }}
    </script>
    <!--供应商 数据模板区域--->
    <script type="text/html" id="lyDataTmp">
        <input type="hidden" id="allsupplier" value="{{JSON.stringify(d.supplier_list)}}"/>
        <input type="hidden" id="allbrand" value="{{JSON.stringify(d.brand_list)}}"/>
        <input type="hidden" id="allParams" value="{{JSON.stringify(d.configParams)}}"/>

        <!--搜索-->
        {{# if (d.configParams.is_search == 1) { }}
        <div class="is-search-box row verCenter rowCenter">
            <input type="text" placeholder="请输入关键字" class="is-search-input" name="goods_name" style="border: 2px solid {{d.configParams.is_search_color}}">
            <button class="is-search-btn" style="background: {{d.configParams.is_search_color}}">快速查找</button>
        </div>
        {{# } }}
        <!--筛选-->
        <div class="shit-head">
            <!---供应商筛选--->
            <div class="supplier-sort clear" id="supplier-sort">
                <div class="czbsg clear">
                    <div class="clear-alls fl-r">清除条件</div>
                    <div class="tog-con act fl-r" style="display:none;">收起<i></i></div>
                </div>
                <div class="supplier-title fl">供应商：</div>
                <div class="supplier-item-box fl">
                    {{# layui.each(d.supplier_list, function(index, item){ }}
                    <div class="supplier-item" supplier_id="{{index}}">{{item}}<i class="supplier-close"></i></div>
                    {{# }); }}
                </div>
            </div>
            <!---品牌筛选--->
            <div class="supplier-sort clear" id="brand-sort">
                <div class="czbsg clear">
                    <div class="clear-alls fl-r">清除条件</div>
                    <div class="tog-con act fl-r" style="display:none;">收起<i></i></div>
                </div>
                <div class="supplier-title fl">品牌：</div>
                <div class="supplier-item-box fl">
                    {{# layui.each(d.brand_list, function(index, item){ }}
                    <div class="supplier-item" brand_id="{{index}}">{{item}}<i class="supplier-close"></i></div>
                    {{# }); }}
                </div>
            </div>
        </div>
        <!--搜索主体-->
        <div class="data-con">
            <!---供应商 列表区域--->
            <div class="list-con supdatabox">
                <div class="list-th clear">
                    <div class="fl-l th w-155">型号</div>
                    <div class="fl-l th w-80">品牌</div>
                    <div class="fl-l th w-75">供应商</div>
                    <div class="fl-l th w-91">库存|批次</div>
                    <div class="fl-l th w-50">阶梯</div>
                    <div class="fl-l th w-147">大陆交货(含税)</div>
                    <div class="fl-l th w-89">香港交货</div>
                    <div class="fl-l th w-82">交期(工作日)</div>
                    <div class="fl-l th w-89">起订|递增</div>
                    <div class="fl-l th w-104">数量</div>
                    <div class="fl-l th noborder ">操作</div>
                </div>
                <div class="list-td clear lyData" id="lyData">
                    {{# layui.each(d.data, function(index, item){ }}
                    <div stocksort="{{item.stockNum}}" zmsort="{{item.zmsort}}" pricesort="{{item.priceSort}}" class="list-group {{item.is_buy!=1?'nobuyGroup':''}} {{(item.stockNum==0&&item.stock_info&&value.stock_info.stock)?'noStockGroupZt':''}} clear" supplier_id="46001" supplier_name="国内现货">
                        <div class="fl-l td item w-155">
                            <div class="clear mb3">
                                <a class="goods-title" href="{{item.goods_url}}" title="{{item.goods_name_org||item.goods_name}}" target="_blank">
                                    {{ item.goods_name_temp}}
                                </a>
                                <span class="copygoods" textt="{{item.goods_name_org||item.goods_name}}" title="复制型号"></span>
                            </div>
                            <div class="clear tagbox mb3">
                                <div class="fl-l yc">原厂直供</div>
                            </div>
                            <div class="kecpn">
                                <div class="group-kf">
                                    {{# if (item.is_buy == 1) { }}
                                    <a href="" rel="nofollow" target="_blank">
                                        <span></span>
                                        <font>联系销售</font>
                                    </a>
                                    {{# } }}
                                    <a href="javascript:void(0)" class="sjfkbtn" sku_id="{{item.goods_id}}" goods_name="{{item.goods_name_org || item.goods_name}}">
                                        <span class="p2fk"></span>
                                        <font>数据纠错</font>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="fl-l td item w-80">
                            {{# if (item.standard_brand_url) { }}
                            <div class="mb3 wordwarp"><a href="{{item.standard_brand_url}}" style="color:#387FFF;">{{ item.brand_name}}</a></div>
                            {{# } else { }}
                            <div class="mb3 wordwarp">{{ item.brand_name}}</div>
                            {{# } }}
                            {{# if (item.goods_type==1||item.goods_type==2) { }}
                            {{# if (item.packing) { }}
                            <div class="mb3"><span>包装:</span>{{item.packing}}</div>
                            {{# } }}
                            {{# } }}
                            {{# if (item.encoded) { }}
                            <div class="mb3"><span>内部编码: </span>{{item.encoded}}</div>
                            {{# } }}
                            {{# if (item.eccn) { }}
                            <div class="mb3"><span>ECCN:</span>{{item.eccn}}</div>
                            {{# } }}
                        </div>
                        <div class="fl-l td item w-75">国内现货</div>
                        <div class="fl-l td item w-405">
                            <div class="clear kpcon oldRender">
                                <div class="kcpc fl-l w-91">
                                    <div class="mb3">
                                        <span>库存：</span>
                                        <span class="yellownumbers">
                                            <font class="asfgdeews ilweiskvn acbmeruwu pkdsieurn"></font
                                            >
                                            <font class="asfgdpolk asdwpkxmi eruwulurj lweiskvnx"></font
                                            >
                                            <font class="asfgdpoqw uiopkdsie pkxmiqplm rnvewjeil"></font
                                            >
                                            <font class="asfgdtyhg rtyuiopkd urjlauejr dwpkxmiqp"></font
                                            >
                                            <font class="asfgdqwer nvewjeilw lauejrifk ulurjlaue"></font
                                            >
                                        </span>
                                    </div>
                                    <div class="batch mb3"><span>批次：</span>23+</div>
                                </div>
                                <div class="w-316 item fl-l">
                                    <div class="jt-con">
                                        <div
                                                class="jt-group clear"
                                                purchases='<font class="asfgdtrdb vbnmqwwer cbmeruwul pkdsieurn"></font><font class="asfgdrtyh ulurjlaue dwpkxmiqp tyuiopkds"></font><font class="asfgdpolk yuiopkdsi urnvewjei eilweiskv"></font>'
                                        >
                                            <div class="fl-l w-70">
                                                <font class="asfgdtrdb vbnmqwwer cbmeruwul pkdsieurn"></font
                                                ><font class="asfgdrtyh ulurjlaue dwpkxmiqp tyuiopkds"></font
                                                ><font class="asfgdpolk yuiopkdsi urnvewjei eilweiskv"></font>+：
                                            </div>

                                            <div class="fl-l w-78 cn-price">￥0.7273</div>

                                            <div class="fl-l w-78 ac-price">&nbsp;</div>
                                            <div class="fl-l w-70 us-price">- -</div>
                                        </div>

                                        <div
                                                class="jt-group clear"
                                                purchases='<font class="asfgdyutr rnvewjeil ghjklzxcv kxmiqplmz"></font><font class="asfgdpoqw klzxcvbnm qwwertyui rifkfghjk"></font><font class="asfgdpolk fkfghjklz xcvbnmqww rjlauejri"></font>'
                                        >
                                            <div class="fl-l w-70">
                                                <font class="asfgdyutr rnvewjeil ghjklzxcv kxmiqplmz"></font
                                                ><font class="asfgdpoqw klzxcvbnm qwwertyui rifkfghjk"></font
                                                ><font class="asfgdpolk fkfghjklz xcvbnmqww rjlauejri"></font>+：
                                            </div>

                                            <div class="fl-l w-78 cn-price">￥0.7339</div>

                                            <div class="fl-l w-78 ac-price">&nbsp;</div>
                                            <div class="fl-l w-70 us-price">- -</div>
                                        </div>

                                        <div
                                                class="jt-group clear"
                                                purchases='<font class="asfgdrfvb meruwulur ertyuiopk plmzacbme"></font><font class="asfgdtyhg eilweiskv miqplmzac meruwulur"></font><font class="asfgdqwer lzxcvbnmq dsieurnve fkfghjklz"></font>'
                                        >
                                            <div class="fl-l w-70">
                                                <font class="asfgdrfvb meruwulur ertyuiopk plmzacbme"></font
                                                ><font class="asfgdtyhg eilweiskv miqplmzac meruwulur"></font
                                                ><font class="asfgdqwer lzxcvbnmq dsieurnve fkfghjklz"></font>+：
                                            </div>

                                            <div class="fl-l w-78 cn-price">￥0.7405</div>

                                            <div class="fl-l w-78 ac-price">&nbsp;</div>
                                            <div class="fl-l w-70 us-price">- -</div>
                                        </div>

                                        <div
                                                class="jt-group clear"
                                                purchases='<font class="asfgderfd acbmeruwu kdsieurnv lmzacbmer"></font><font class="asfgdeews qwwertyui dwpkxmiqp urnvewjei"></font><font class="asfgdpolk hjklzxcvb miqplmzac meruwulur"></font>'
                                        >
                                            <div class="fl-l w-70">
                                                <font class="asfgderfd acbmeruwu kdsieurnv lmzacbmer"></font
                                                ><font class="asfgdeews qwwertyui dwpkxmiqp urnvewjei"></font
                                                ><font class="asfgdpolk hjklzxcvb miqplmzac meruwulur"></font>+：
                                            </div>

                                            <div class="fl-l w-78 cn-price">￥0.7472</div>

                                            <div class="fl-l w-78 ac-price">&nbsp;</div>
                                            <div class="fl-l w-70 us-price">- -</div>
                                        </div>

                                        <div
                                                class="jt-group clear act"
                                                purchases='<font class="asfgdyutr nvewjeilw kxmiqplmz wpkxmiqpl"></font><font class="asfgdpolk iqplmzacb dwpkxmiqp cvbnmqwwe"></font>'
                                        >
                                            <div class="fl-l w-70">
                                                <font class="asfgdyutr nvewjeilw kxmiqplmz wpkxmiqpl"></font
                                                ><font class="asfgdpolk iqplmzacb dwpkxmiqp cvbnmqwwe"></font>+：
                                            </div>

                                            <div class="fl-l w-78 cn-price">￥0.7604</div>

                                            <div class="fl-l w-78 ac-price">&nbsp;</div>
                                            <div class="fl-l w-70 us-price">- -</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="more-con act">批次/阶梯价<font></font></div>
                        </div>
                        <div class="fl-l td item w-82">
                            <div class="dl check-b act clear" guid="1">
                                <div class="checkbox fl-l"><i class="icon-check"></i></div>
                                <span class="fl-l">大陆3-5</span>
                            </div>

                            <br/>
                            &nbsp;
                        </div>
                        <div class="fl td item w-89">
                            <div class="mb3">
      <span>起订量：</span
      ><font class="asfgdeews xcvbnmqww xmiqplmza nmqwwerty"></font
                                ><font class="asfgdpoqw iqplmzacb lauejrifk jrifkfghj"></font>
                            </div>
                            <div class="beisu">
      <span>递增量：</span
      ><font class="asfgdeews wwertyuio wwertyuio wertyuiop"></font
                                ><font class="asfgdpolk dwpkxmiqp ilweiskvn xmiqplmza"></font>
                            </div>
                        </div>
                        <div class="fl td item w-104">
                            <div
                                    class="edit-input clear mb-12"
                                    stock='<font class="asfgdeews ilweiskvn acbmeruwu pkdsieurn"></font><font class="asfgdpolk asdwpkxmi eruwulurj lweiskvnx"></font><font class="asfgdpoqw uiopkdsie pkxmiqplm rnvewjeil"></font><font class="asfgdtyhg rtyuiopkd urjlauejr dwpkxmiqp"></font><font class="asfgdqwer nvewjeilw lauejrifk ulurjlaue"></font>'
                                    mpl='<font class="asfgdeews wwertyuio wwertyuio wertyuiop"></font><font class="asfgdpolk dwpkxmiqp ilweiskvn xmiqplmza"></font>'
                                    moq='<font class="asfgdeews xcvbnmqww xmiqplmza nmqwwerty"></font><font class="asfgdpoqw iqplmzacb lauejrifk jrifkfghj"></font>'
                                    mpq='<font class="asfgdpogh meruwulur auejrifkf klzxcvbnm"></font><font class="asfgdqwer zxcvbnmqw rjlauejri uiopkdsie"></font><font class="asfgdpolk cvbnmqwwe yuiopkdsi fkfghjklz"></font><font class="asfgdtyhg pkdsieurn jlauejrif nmqwwerty"></font>'
                            >
                                <span class="samllp">-</span>
                                <input
                                        type="text"
                                        class="valuep"
                                        value="50"
                                        onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                        onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"
                                />
                                <span class="addp">+</span>
                            </div>
                            <div class="total be">
                                <font class="f-999">合计：</font><span>¥38.02</span>
                            </div>
                        </div>
                        <div class="fl td item w-76">
                            <div class="btn buyGood" guid="-1" goods_id="1167780691443464267">加入购物车</div>
                            <div class="btn buyGood btn-lk" guid="1" goods_id="1167780691443464267">立即购买</div>
                        </div>
                    </div>
                    {{# }); }}
                </div>
                <div class="no_content" style="display:none;">
                    <dl class="clear">
                        <dt><i class="icon-ss-wu icon-ss-wu-sy"></i></dt>
                        <dd>
                            <h2>很抱歉！当前型号库存不足</h2>
                            <p><a href="{$kfqq_xk}" rel="nofollow" target="_blank" class="but">去人工找货</a></p>
                        </dd>
                    </dl>
                </div>
                <!---分页--->
                <div class="M-box266 pages-blue m-style"></div>
            </div>
        </div>
    </script>
@endverbatim

@include('js')
<script type="text/javascript" src="/assets/libs/superSlide.js?v={{time()}}"></script>
<script type="text/javascript" src="/assets/libs/Tdrag.min.js?v={{time()}}"></script>
<script type="text/javascript" src="/assets/js/activity/set.js?v={{time()}}"></script>
</body>
</html>