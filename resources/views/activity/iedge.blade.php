<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link href="/assets/images/favicon.ico" rel="icon">
  <title>活动配置面板</title>
  @include('css')
  <link rel="stylesheet" href="/assets/css/iedge.min.css?v={{time()}}">
  <style>
      html, body {
          width: 100%;
          height: 100%;
          overflow: hidden;
      }
  </style>
</head>
<body>

<section class="cube-set">
  <!--配置面板头部-->
  <div class="cube-set-head row bothSide verCenter">
    <div class="row verCenter">
      <h2 style="margin-right: 5px;">活动名称</h2>
      <select name="env" lay-ignore class="env">
        <option selected value="1">PC</option>
      </select>
      <label class="ml10">背景色：</label>
      <input type="text" placeholder="输入背景颜色代码" value="#FFFFFF" class="layui-input" style="width: 100px" id="page_color"/>
      <a class="layui-btn layui-btn-sm ml10" id="page_background_upload">上传背景图</a>
      <a href="javascript:;" class="clearBg">清除图片</a>
      <img src="" alt="" class="page-background" style="display: none" id="page_background">
    </div>
    <div class="btn row verCenter">
      <a class="layui-btn layui-btn-sm" id="preview" href="" target="_blank">预览</a>
      <a class="layui-btn layui-btn-sm layui-btn-primary" onclick="window.location.href='/?jumpUrl=/web/activity/list'">取消</a>
    </div>
  </div>
  <div class="left-menu">
    <div class="shousuo-box row rowCenter verCenter" title="收缩">
      <i class="iconfont icon-shousuo"></i>
    </div>
    <div class="layui-tab layui-tab-brief">
      <ul class="layui-tab-title">
        <li class="layui-this">元件库</li>
      </ul>
      <div class="layui-tab-content">
        <div class="layui-tab-item layui-show">
          <div class="layui-collapse">
            <div class="layui-colla-item system-compant">
              <h2 class="layui-colla-title">系统元件<em>（仅PC端有效）</em></h2>
              <div class="layui-colla-content layui-show">
                <p class="row verCenter operate">
                  <input type="checkbox" lay-ignore id="page_navigation_bar" checked>
                  <span>顶部导航栏</span>
                </p>
                <p class="row verCenter operate">
                  <input type="checkbox" lay-ignore id="page_right_bar">
                  <span>右侧快键入口</span>
                </p>
              </div>
            </div>
            <div class="layui-colla-item">
              <h2 class="layui-colla-title">基本元件</h2>
              <div class="layui-colla-content layui-show">
                <ul class="package-list row" id="element"></ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--配置面板区域-->
  <div class="web-html">
    <div class="cube-set-drag-area" style="position: relative;"></div>
    <div class="cube-setting-layer"></div>
  </div>
</section>

@verbatim
  <!--基本元件-->
  <script type="text/html" id="elementHtml">
    {{# layui.each(d, function(index, item){ }}
    {{# if(item.element_status== 1){ }}
    <li class="column verCenter child" draggable="true" id="{{item.element_code}}">
      <i></i>
      <span>{{item.element_name}}</span>
    </li>
    {{# } }}
    {{# }); }}
  </script>
  <!--布局分类-->
  <script type="text/html" id="layoutHtml">
    {{# if (d == 'customLayout') { }}
    <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}" style="height: 300px">
      <div class="cube-set-drag-content customLayout row verCenter"></div>
      <div class="setting-fix-width">
        <ul class="setting column verCenter rowCenter">
          <li class="row rowCenter verCenter settingObj" title="设置">
            <i class="layui-icon layui-icon-set-sm"></i>
          </li>
          <li class="row rowCenter verCenter move-up" title="上移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
          </li>
          <li class="row rowCenter verCenter move-down" title="下移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
          </li>
          <li class="row rowCenter verCenter delete" title="删除">
            <i class="layui-icon layui-icon-delete">
            </i>
          </li>
        </ul>
      </div>
    </div>
    {{# } else if(d=='lotteryCircle') { }}
    <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}">
      <div class="cube-set-drag-content lotteryCircle">
        <div class="lotteryCircle-bg">
          <p class="number-of-draw row verCenter rowCenter" style="padding: 20px;">
            <span>您今天还有抽奖机会</span>
            <em>0</em>
            <span>次数</span>
          </p>
          <div class="turntable-bg">
            <img id="rotate" src="https://img.ichunt.com/images/ichunt/202303/10/ed68d3caee2395f7a3c3c4d7138a3ca1.png" alt="turntable" class="turntable-pic">
            <div class="round_click"></div>
          </div>
          <div class="btn-wrap row bothSide verCenter" style="padding: 40px 0;width: 700px;">
            <a href="javascript:;" class="contactBg">填写联系方式</a>
            <a href="javascript:;" class="prizeBg">查看我的奖品</a>
          </div>
        </div>
      </div>
      <div class="setting-fix-width">
        <ul class="setting column verCenter rowCenter">
          <li class="row rowCenter verCenter settingObj" title="设置">
            <i class="layui-icon layui-icon-set-sm"></i>
          </li>
          <li class="row rowCenter verCenter move-up" title="上移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
          </li>
          <li class="row rowCenter verCenter move-down" title="下移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
          </li>
          <li class="row rowCenter verCenter delete" title="删除">
            <i class="layui-icon layui-icon-delete">
            </i>
          </li>
        </ul>
      </div>
    </div>
    {{# } else if(d=='lotterySquare') { }}
    <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}">
      <div class="cube-set-drag-content lotterySquare">
        <div class="lotterySquare-bg">
          <p class="number-of-draw row verCenter rowCenter" style="padding: 20px;">
            <span>您今天还有抽奖机会</span>
            <em>0</em>
            <span>次数</span>
          </p>
          <div class="lotterySquare-content" style="background: url('https://img.ichunt.com/images/ichunt/202303/02/6ba6bcfe5451234780fc410fe5d51645.png') no-repeat center;background-size: contain;width: 700px;height: 700px;">
            <a class="box1"></a>
            <a class="box2"></a>
            <a class="box3"></a>
            <a class="box4"></a>
            <a class="box5"></a>
            <a class="box6"></a>
            <a class="box7"></a>
            <a class="box8"></a>
            <span class="btnClick"></span>
          </div>
          <div class="btn-wrap row bothSide verCenter" style="padding: 40px 0;width: 700px;">
            <a href="javascript:;" class="contactBg">填写联系方式</a>
            <a href="javascript:;" class="prizeBg">查看我的奖品</a>
          </div>
        </div>
      </div>
      <div class="setting-fix-width">
        <ul class="setting column verCenter rowCenter">
          <li class="row rowCenter verCenter settingObj" title="设置">
            <i class="layui-icon layui-icon-set-sm"></i>
          </li>
          <li class="row rowCenter verCenter move-up" title="上移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
          </li>
          <li class="row rowCenter verCenter move-down" title="下移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
          </li>
          <li class="row rowCenter verCenter delete" title="删除">
            <i class="layui-icon layui-icon-delete">
            </i>
          </li>
        </ul>
      </div>
    </div>
    {{# } else if(d=='shopList') { }}
    <div class="cube-initial row verCenter rowCenter" data-type="{{d}}" data-uuid="{{uuid()}}">
      <div class="cube-set-drag-content shopList wrap-search"></div>
      <div class="setting-fix-width">
        <ul class="setting column verCenter rowCenter">
          <li class="row rowCenter verCenter settingObj" title="设置">
            <i class="layui-icon layui-icon-set-sm"></i>
          </li>
          <li class="row rowCenter verCenter move-up" title="上移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
          </li>
          <li class="row rowCenter verCenter move-down" title="下移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
          </li>
          <li class="row rowCenter verCenter delete" title="删除">
            <i class="layui-icon layui-icon-delete">
            </i>
          </li>
        </ul>
      </div>
    </div>
    {{# } else if(d=='video') { }}
    <div class="cube-initial row verCenter rowCenter" style="width: 1226px;height: 300px;" data-type="{{d}}" data-uuid="{{uuid()}}">
      <div class="cube-set-drag-content video" style="width: 1226px;height: 300px;"></div>
      <div class="setting-fix-width">
        <ul class="setting column verCenter rowCenter">
          <li class="row rowCenter verCenter settingObj" title="设置">
            <i class="layui-icon layui-icon-set-sm"></i>
          </li>
          <li class="row rowCenter verCenter move-up" title="上移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
          </li>
          <li class="row rowCenter verCenter move-down" title="下移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
          </li>
          <li class="row rowCenter verCenter delete" title="删除">
            <i class="layui-icon layui-icon-delete">
            </i>
          </li>
        </ul>
      </div>
    </div>
    {{# } else if(d=='carousel') { }}
    <div class="cube-initial row verCenter rowCenter" style="width: 100%;min-height: 300px;" data-type="{{d}}" data-uuid="{{uuid()}}">
      <div class="cube-set-drag-content carousel" style="width: 100%;min-height: 300px;"></div>
      <div class="setting-fix-width">
        <ul class="setting column verCenter rowCenter">
          <li class="row rowCenter verCenter settingObj" title="设置">
            <i class="layui-icon layui-icon-set-sm"></i>
          </li>
          <li class="row rowCenter verCenter move-up" title="上移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(90deg);"></i>
          </li>
          <li class="row rowCenter verCenter move-down" title="下移">
            <i class="layui-icon layui-icon-return" style="transform: rotate(-90deg);"></i>
          </li>
          <li class="row rowCenter verCenter delete" title="删除">
            <i class="layui-icon layui-icon-delete">
            </i>
          </li>
        </ul>
      </div>
    </div>
    {{# } }}
  </script>
  <!--楼层分类-->
  <script type="text/html" id="floorHtml">
    {{# if (d == 'customLayout') { }}
    <div class="row bothSide verCenter title">
      <h2 class="tt">
        <em class="floor">楼层</em>
        <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
        <span class="floor-name"></span>
      </h2>
      <span class="toggle">关闭</span>
    </div>
    <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
      <ul class="layui-tab-title">
        <li class="layui-this">布局设置</li>
        <li>全局楼层</li>
        <li>热区</li>
      </ul>
      <div class="layui-tab-content">
        <div class="layui-tab-item layui-show layui-form customLayout" lay-filter="floorSettingForm">
          <div class="layui-form-item">
            <label class="layui-form-label required">宽度</label>
            <div class="layui-input-inline" style="width: 100px;">
              <input type="text" name="width" lay-verify="required" lay-reqtext="请填写宽度" lay-vertype="tips" autocomplete="off" placeholder="最小1226px，最大1920px" class="layui-input width" value="1226">
            </div>
            <div class="layui-form-mid layui-word-aux">px （最小1226px，最大1920px）</div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required">高度</label>
            <div class="layui-input-inline">
              <input type="text" name="height" lay-verify="required" lay-reqtext="请填写高度" lay-vertype="tips" autocomplete="off" placeholder="请输入高度" class="layui-input height" value="300">
            </div>
            <div class="layui-form-mid layui-word-aux">px</div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required">列数</label>
            <div class="layui-input-inline">
              <select lay-filter="columnsChange" name="columns" class="columns">
                <option value="1">1列</option>
                <option value="2">2列</option>
                <option value="3">3列</option>
                <option value="4">4列</option>
                <option value="5">5列</option>
                <option value="6">6列</option>
                <option value="7">7列</option>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required">间距</label>
            <div class="layui-input-inline">
              <input type="text" name="spacing" lay-verify="required" lay-reqtext="请填写间距" lay-vertype="tips" autocomplete="off" placeholder="请输入间距" class="layui-input spacing" value="0">
            </div>
            <div class="layui-form-mid layui-word-aux">px</div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required">列宽</label>
            <div class="layui-input-inline columnWidthDynamics row verCenter" style="flex-wrap: wrap;width: 80%;">
              <input type="text" name="columnWidthOne" lay-verify="required" lay-reqtext="请填写列宽" lay-vertype="tips" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="1226" style="width: 55px;">
            </div>
          </div>
          <div class="columns-dynamics">
            <div class="layui-form-item">
              <label class="layui-form-label">第1列</label>
              <div class="layui-input-block" style="margin-left: 62px;">
                <input type="text" name="columnOneHref" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url">
              </div>
              <div class="row bothSide verCenter bar-wrap">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif/mp4</p>
                </div>
                <div>
                  <img src="" alt="" class="pic" style="display: none">
                  <input type="hidden" name="columnOneUrl" value="">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 62px;">
              <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="save">保存</button>
            </div>
          </div>
        </div>
        <div class="layui-tab-item">
          <table id="list"></table>
        </div>
        <div class="layui-tab-item layui-form hot-area" lay-filter="hotForm">
          <div class="tip-text row verCenter" style="margin-bottom: 10px;">
            <i class="iconfont icon-instant-error"></i>
            <p>在元件上方增加图层，并赋予交互</p>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width: 90px;">列数</label>
            <div class="layui-input-inline">
              <select name="columns" class="columns">
                <option value="1">1列</option>
                <option value="2">2列</option>
                <option value="3">3列</option>
                <option value="4">4列</option>
              </select>
            </div>
          </div>
          <div class="hot-columns-dynamics">
            <div class="layui-form-item">
              <label class="layui-form-label" style="width: 90px;">第1列宽度</label>
              <div class="layui-input-inline">
                <input type="text" name="width" autocomplete="off" placeholder="请输入宽度" class="layui-input width" value="">
              </div>
              <div class="layui-form-mid layui-word-aux">px</div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label" style="width: 90px;">第1列高度</label>
              <div class="layui-input-inline">
                <input type="text" name="height" autocomplete="off" placeholder="请输入高度" class="layui-input height" value="">
              </div>
              <div class="layui-form-mid layui-word-aux">px</div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label" style="width: 90px;">第1列X轴</label>
              <div class="layui-input-inline" style="width: 108px;">
                <input type="text" name="x" autocomplete="off" placeholder="请输入X轴位置" class="layui-input x">
              </div>
              <div class="layui-form-mid layui-word-aux">（可拖动热区调整）</div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label" style="width: 90px;">第1列Y轴</label>
              <div class="layui-input-inline" style="width: 108px;">
                <input type="text" name="y" autocomplete="off" placeholder="请输入Y轴位置" class="layui-input y">
              </div>
              <div class="layui-form-mid layui-word-aux">（可拖动热区调整）</div>
            </div>
            <div class="layui-form-item">
              <label class="layui-form-label" style="width: 90px;">第1列地址</label>
              <div class="layui-input-block" style="margin-left: 90px;">
                <input type="text" name="columnOneHref" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url">
              </div>
              <div class="row bothSide verCenter bar-wrap" style="padding-left: 90px;">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif/mp4</p>
                </div>
                <div>
                  <img src="" alt="" class="pic" style="display: none">
                  <input type="hidden" name="columnOneUrl" value="">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 90px;">
              <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="hotSubmit" id="hotSubmit">保存</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    {{# } else if(d=='lotteryCircle') { }}
    <div class="row bothSide verCenter title">
      <h2 class="tt">
        <em class="floor">楼层</em>
        <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
        <span class="floor-name"></span>
      </h2>
      <span class="toggle">关闭</span>
    </div>
    <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
      <ul class="layui-tab-title">
        <li class="layui-this">布局设置</li>
        <li>全局楼层</li>
      </ul>
      <div class="layui-tab-content">
        <div class="layui-tab-item layui-show  layui-form lotteryCircle" lay-filter="lotteryCircleForm">
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">抽奖ID</label>
            <div class="layui-input-inline">
              <input type="text" name="lotteryId" lay-verify="required" lay-reqtext="请填写抽奖ID" lay-vertype="tips" autocomplete="off" placeholder="请输入抽奖ID" class="layui-input lotteryId">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">奖项个数</label>
            <div class="layui-input-inline" style="width: 50px">
              <input type="text" name="num" lay-verify="required" lay-reqtext="请输入奖项个数" lay-vertype="tips" autocomplete="off" placeholder="请输入奖项个数" class="layui-input layui-disabled num" value="8" disabled>
            </div>
            <div class="layui-form-mid layui-word-aux">角度：<em class="angle">45</em></div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">实物中奖提示</label>
            <div class="layui-input-inline">
              <input type="text" name="prizeMsg" lay-verify="required" lay-reqtext="请填各实物中奖提示" lay-vertype="tips" autocomplete="off" placeholder="请填写中奖提示" class="layui-input prizeMsg" value="请填写联系方式，工作人员会寄送奖品">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">抽奖图片</label>
            <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
              <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：708*708</p>
                </div>
                <div>
                  <img src="https://img.ichunt.com/images/ichunt/202303/10/ed68d3caee2395f7a3c3c4d7138a3ca1.png" alt="" class="pic">
                  <input type="hidden" name="lotteryCircleBg" value="https://img.ichunt.com/images/ichunt/202303/10/ed68d3caee2395f7a3c3c4d7138a3ca1.png">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">联系方式按钮</label>
            <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
              <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：150*30</p>
                </div>
                <div>
                  <img src="" alt="" class="pic" style="display: none;">
                  <input type="hidden" name="contactBg" value="">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">我的奖品按钮</label>
            <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
              <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：150*30</p>
                </div>
                <div>
                  <img src="" alt="" class="pic" style="display: none;">
                  <input type="hidden" name="prizeBg" value="">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">背景图</label>
            <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
              <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif/mp4<br/>建议尺寸：1190*1010</p>
                </div>
                <div>
                  <img src="https://img.ichunt.com/images/ichunt/202303/09/5280cd20b5f9b931a70875cc1236766c.jpg" alt="" class="pic">
                  <input type="hidden" name="bg" value="https://img.ichunt.com/images/ichunt/202303/09/5280cd20b5f9b931a70875cc1236766c.jpg">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 111px;">
              <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="lotteryCircleSubmit" id="lotteryCircleSubmit">保存</button>
            </div>
          </div>
        </div>
        <div class="layui-tab-item">
          <table id="list"></table>
        </div>
      </div>
    </div>
    {{# } else if(d=='lotterySquare') { }}
    <div class="row bothSide verCenter title">
      <h2 class="tt">
        <em class="floor">楼层</em>
        <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
        <span class="floor-name"></span>
      </h2>
      <span class="toggle">关闭</span>
    </div>
    <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
      <ul class="layui-tab-title">
        <li class="layui-this">布局设置</li>
        <li>全局楼层</li>
      </ul>
      <div class="layui-tab-content">
        <div class="layui-tab-item layui-show  layui-form lotterySquare" lay-filter="lotterySquareForm">
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">抽奖ID</label>
            <div class="layui-input-inline">
              <input type="text" name="lotteryId" lay-verify="required" lay-reqtext="请填写抽奖ID" lay-vertype="tips" autocomplete="off" placeholder="请输入抽奖ID" class="layui-input lotteryId">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">各奖项长/宽</label>
            <div class="layui-input-inline" style="width: 50px">
              <input type="text" name="width" lay-verify="required" lay-reqtext="请填各奖项长/宽" lay-vertype="tips" autocomplete="off" placeholder="" class="layui-input width" value="226">
            </div>
            <div class="layui-form-mid layui-word-aux">间隔11，总宽度:<em class="total-width">700</em>&nbsp;&nbsp;总高度:<em class="total-width">700</em></div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">实物中奖提示</label>
            <div class="layui-input-inline">
              <input type="text" name="prizeMsg" lay-verify="required" lay-reqtext="请填各实物中奖提示" lay-vertype="tips" autocomplete="off" placeholder="请填写中奖提示" class="layui-input prizeMsg" value="请填写联系方式，工作人员会寄送奖品">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">抽奖图片</label>
            <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
              <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：700*700</p>
                </div>
                <div>
                  <img src="https://img.ichunt.com/images/ichunt/202303/02/6ba6bcfe5451234780fc410fe5d51645.png" alt="" class="pic">
                  <input type="hidden" name="lotterySquareBg" value="https://img.ichunt.com/images/ichunt/202303/02/6ba6bcfe5451234780fc410fe5d51645.png">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">联系方式按钮</label>
            <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
              <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：209*59</p>
                </div>
                <div>
                  <img src="" alt="" class="pic" style="display: none;">
                  <input type="hidden" name="contactBg" value="">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">我的奖品按钮</label>
            <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
              <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif<br/>建议尺寸：209*59</p>
                </div>
                <div>
                  <img src="" alt="" class="pic" style="display: none;">
                  <input type="hidden" name="prizeBg" value="">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 110px;">背景图</label>
            <div class="layui-input-inline" style="width: 252px;white-space: nowrap">
              <div class="row bothSide  bar-wrap" style="padding-left: 0;padding-top: 0">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><input class="layui-upload-file" type="file" accept="" name="file">
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif/mp4<br/>建议尺寸：1190*59</p>
                </div>
                <div>
                  <img src="https://img.ichunt.com/images/ichunt/202303/02/39a26dcb241686ca2d679facb272e911.jpg" alt="" class="pic">
                  <input type="hidden" name="bg" value="https://img.ichunt.com/images/ichunt/202303/02/39a26dcb241686ca2d679facb272e911.jpg">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 111px;">
              <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="lotterySquareSubmit" id="lotterySquareSubmit">保存</button>
            </div>
          </div>
        </div>
        <div class="layui-tab-item">
          <table id="list"></table>
        </div>
      </div>
    </div>
    {{# } else if(d=='video') { }}
    <div class="row bothSide verCenter title">
      <h2 class="tt">
        <em class="floor">楼层</em>
        <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
        <span class="floor-name"></span>
      </h2>
      <span class="toggle">关闭</span>
    </div>
    <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
      <ul class="layui-tab-title">
        <li class="layui-this">布局设置</li>
        <li>全局楼层</li>
      </ul>
      <div class="layui-tab-content">
        <div class="layui-tab-item layui-show layui-form video" lay-filter="videoSettingForm">
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 83px;">视频地址</label>
            <div class="layui-input-inline" style="width: 285px;">
              <input type="text" name="url" lay-verify="required|url" lay-reqtext="请填写视频地址" lay-vertype="tips" autocomplete="off" placeholder="请联系技术生成视频地址（暂不支持直接上传视频）" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 85px;">
              <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="saveVideo">保存</button>
            </div>
          </div>
        </div>
        <div class="layui-tab-item">
          <table id="list"></table>
        </div>
      </div>
    </div>
    {{# } else if(d=='carousel') { }}
    <div class="row bothSide verCenter title">
      <h2 class="tt">
        <em class="floor">楼层</em>
        <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
        <span class="floor-name"></span>
      </h2>
      <span class="toggle">关闭</span>
    </div>
    <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
      <ul class="layui-tab-title">
        <li class="layui-this">布局设置</li>
        <li>全局楼层</li>
      </ul>
      <div class="layui-tab-content">
        <div class="layui-tab-item layui-show layui-form carousel" lay-filter="carouselSettingForm">
          <div class="layui-form-item">
            <label class="layui-form-label required" style="width: 83px;">图片个数</label>
            <div class="layui-input-inline">
              <select name="num" lay-filter="carouselNumChange" lay-verify="required" lay-reqtext="请填写图片个数" lay-vertype="tips">
                <option value="">请选择个数</option>
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4">4</option>
                <option value="5">5</option>
                <option value="6">6</option>
                <option value="7">7</option>
                <option value="8">8</option>
                <option value="9">9</option>
                <option value="10">10</option>
              </select>
            </div>
          </div>
          <div class="columns-dynamics">
            <div class="layui-form-item">
              <label class="layui-form-label required" style="width: 83px;">第1列</label>
              <div class="layui-input-block" style="margin-left: 85px;">
                <input type="text" autocomplete="off" placeholder="跳转地址" class="layui-input url" lay-verify="required" lay-reqtext="请填写跳转地址" lay-vertype="tips">
              </div>
              <div class="row bothSide verCenter bar-wrap" style="padding-left: 85px;">
                <div>
                  <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>
                  <a href="javascript:;" class="clearPic">清除图片</a>
                  <p class="tip">支持扩展名png/jpg/gif/mp4</p>
                </div>
                <div>
                  <img src="" alt="" class="pic" style="display: none">
                </div>
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 85px;">
              <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="saveCarousel">保存</button>
            </div>
          </div>
        </div>
        <div class="layui-tab-item">
          <table id="list"></table>
        </div>
      </div>
    </div>
    {{# } else if(d=='shopList') { }}
    <div class="row bothSide verCenter title">
      <h2 class="tt">
        <em class="floor">楼层</em>
        <i class="layui-icon layui-icon-edit floor-text" style="font-size: 22px; color: #999;cursor: pointer"></i>
        <span class="floor-name"></span>
      </h2>
      <span class="toggle">关闭</span>
    </div>
    <div class="layui-tab layui-tab-brief" lay-filter="tabChange">
      <ul class="layui-tab-title">
        <li class="layui-this">布局设置</li>
        <li>全局楼层</li>
      </ul>
      <div class="layui-tab-content">
        <div class="layui-tab-item layui-show  layui-form shopList" lay-filter="shopListForm">
          <div class="layui-form-item">
            <label class="layui-form-label" style="width: 105px;">筛选</label>
            <div class="layui-input-block checkbox-style row verCenter">
              <div class="row verCenter">
                <input type="checkbox" title="搜索框" lay-skin="primary" lay-ignore id="is_search">
                <span style="margin-left: 5px;">搜索框</span>
                <input type="text" placeholder="颜色配置" class="layui-input" id="is_search_color" style="width: 100px;margin-left: 10px;display: none;">
              </div>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width: 105px;">分区</label>
            <div class="layui-input-block">
              <input type="radio" name="partition" value="1" title="无" checked>
              <input type="radio" name="partition" value="2" title="按分类">

            </div>
          </div>
          <!--选择维度html-->
          <div class="dimension-box">
            <div class="layui-form-item">
              <label class="layui-form-label" style="width: 105px;">选择维度</label>
              <div class="layui-input-block">
                <input type="radio" name="dimension" lay-filter="dimensionChange" value="1" title="型号" checked>
                <input type="radio" name="dimension" lay-filter="dimensionChange" value="2" title="商品属性">
                <input type="radio" name="dimension" lay-filter="dimensionChange" value="3" title="新品推荐">
              </div>
            </div>
            <!--型号切换-->
            <div id="goodsBox">
              <div class="layui-form-item">
                <label class="layui-form-label required" style="width: 105px;">供应商</label>
                <div class="layui-input-block" style="margin-left: 105px;">
                  <div id="supplier_ids"></div>
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label" style="width: 105px;">渠道标签</label>
                <div class="layui-input-block" style="margin-left: 105px;">
                  <div id="supplier_codes"></div>
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label" style="width: 105px;">商品型号</label>
                <div class="layui-input-block row verCenter" style="margin-left: 105px;">
                  <a href="/template/sku_upload.xlsx" class="alink" style="margin-right: 6px;">下载模板</a>
                  <button class="layui-btn layui-btn-primary layui-btn-sm layui-border-green" style="margin-right: 6px;" id="sku_id_upload">上传文件</button>
                  <a class="layui-icon layui-icon-download-circle" title="下载文件" style="font-size: 22px;color: #1c8eff;cursor: pointer;"></a>
                  <input type="hidden" name="sku_id_upload_file" value="">
                  <input type="hidden" name="sku_ids" value="">
                </div>
              </div>
            </div>
            <!--商品属性切换-->
            <div id="shopSort" style="display: none">
              <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 105px;">
                  <button type="button" class="layui-btn layui-btn-sm" id="addRowSelf">新增行</button>
                </div>
              </div>
              <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 105px;">
                  <table class="layui-table">
                    <thead>
                    <tr>
                      <th style="white-space: nowrap">分类ID</th>
                      <th style="white-space: nowrap">品牌ID</th>
                      <th style="white-space: nowrap">操作</th>
                    </tr>
                    </thead>
                    <tbody id="rowAreaSelf">
                    <tr>
                      <td class="classify">
                        <input type="hidden" class="type" value="2">
                        <input type="text" placeholder="逗号隔开" class="layui-input class_ids">
                      </td>
                      <td class="brand">
                        <input type="text" placeholder="逗号隔开" class="layui-input brand_ids">
                      </td>
                      <td>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete">删除</button>
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <!--新品推荐-->
            <div class="recommendation" style="display: none">

            </div>
          </div>
          <div class="layui-form-item">
            <div class="layui-input-block" style="margin-left: 105px;">
              <button type="submit" class="layui-btn layui-btn-sm" lay-submit="" lay-filter="shopListSubmit" id="shopListSubmit">保存</button>
            </div>
          </div>
        </div>
        <div class="layui-tab-item">
          <table id="list"></table>
        </div>
      </div>
    </div>
    {{# } }}
  </script>
@endverbatim

@include('js')
<script type="text/javascript" src="/assets/libs/superSlide.js?v={{time()}}"></script>
<script type="text/javascript" src="/assets/libs/Tdrag.min.js?v={{time()}}"></script>
<script type="text/javascript" src="/assets/js/activity/iedge.js?v={{time()}}"></script>
</body>
</html>