@include('css')
@include('js')
<style>
    .layui-form-label {
        width: 100px;
    }

    .layui-input-block {
        margin-left: 130px;
    }

    .layui-form-switch {
        margin-top: 0px;
    }

</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div style="margin-left: 10px;margin-bottom: 30px">
            <h2>
                @if(empty($rule))
                新增
                @else
                修改
                @endif
                运费规则配置
            </h2>
        </div>
        <input type="hidden" name="id" id="id" value="{{ $rule->id ?? '' }}">

        <div class="layui-form-item">
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('supplier_id','渠道 : ',!empty($rule) ? $rule->supplier_id : 0,$supplierList,['width'=>'100px','radio'=>true,'required'=>true , 'disabled'=> !empty($rule)? true:false ]) !!}
            </div>
        </div>

        <div class="layui-form-item" id="supplier_code_selector" style="
        @if(empty($rule))
        display: none
        @endif
        @if(!empty($rule) && !$rule->supplier_code)
        display: none
        @endif
        ">
            <div class="layui-inline">
                @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                {!! $multiSelectorPresenter->render('supplier_code','供应商 : ',!empty($rule) ? $rule->supplier_code : 0,$supplierListForXmSelect,['width'=>'350px','radio'=>true,'required'=>true , 'disabled'=> !empty($rule)? true:false]) !!}
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color: red">*</span> 配置方式 : </label>
            <div class="layui-input-block" style="margin-left: -10px;">
                <input type="radio" name="type" value="1" title="按订单金额配置" {{ (empty($rule) || (!empty($rule) && $rule->type == 1)) ? 'checked' : '' }} lay-filter="type">
                <input type="radio" name="type" value="2" title="按订单金额阶梯配置" {{ (!empty($rule) && $rule->type == 2) ? 'checked' : '' }} lay-filter="type">
                <input type="radio" name="type" value="3" title="按订单型号数量配置" {{ (!empty($rule) && $rule->type == 3) ? 'checked' : '' }} lay-filter="type">
                <input type="radio" name="type" value="4" title="按订单型号重量配置" {{ (!empty($rule) && $rule->type == 4) ? 'checked' : '' }} lay-filter="type">
            </div>
        </div>
        <div id="rule_div" style="margin-left: -30px; width: 800px;">

            {{-- order_amount_section --}}
            @include('shippingRule.rules.orderAmountSection')
            {{-- order_amount_ladder_section --}}
            @include('shippingRule.rules.orderAmountLadderSection')
            {{-- order_sku_num_section --}}
            @include('shippingRule.rules.orderSkuNumSection')
            {{-- order_sku_weight_section --}}
            @include('shippingRule.rules.orderSkuWeightSection')


        </div>
        <div class="layui-form-item" style="text-align: center;padding-top: 20px">
            <button type="button" class="layui-btn layui-btn-sm saveForm" lay-submit lay-filter="saveForm">确定</button>
            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm cancel">取消</button>
        </div>
        <br>
    </form>
</section>
<script type="text/javascript" src="/assets/js/shippingRule/saveShippingRule.js?v={{time()}}"></script>
