  <div class="layui-form-item" id="order_sku_num_section" @if(empty($rule) || (!empty($rule) && $rule->type != 4)) style="display: none;" @endif>
      <div class="layui-input-block" style="width: 800px;">
          <table class="layui-table" id="sku-weight-table">
              <thead>
                  <tr>
                      <th style="width: 280px">累计型号重量(KG)</th>
                      <th>运费 (CNY)</th>
                      <th>运费 (USD)</th>
                      <th>操作</th>
                  </tr>
              </thead>
              <tbody id="sku-weight-body">
                  <tr>
                      <td>
                          按汇率转换
                      </td>
                      <td>
                          <input type="checkbox" lay-skin="switch" id="weight_is_change_rate_cny" name="weight_is_change_rate_cny" value="{{ $rule->weight_is_change_rate_cny ?? '' }}" @if(!empty($rule)&&$rule->weight_is_change_rate_cny == 1)
                          checked
                          @endif
                          class="layui-input" lay-filter="weight_is_change_rate_cny">
                      </td>
                      <td>
                          <input type="checkbox" lay-skin="switch" id="weight_is_change_rate_usd" name="weight_is_change_rate_usd" value="{{ $rule->weight_is_change_rate_usd ?? '' }}" @if(!empty($rule)&&$rule->weight_is_change_rate_usd == 1)
                          checked
                          @endif
                          class="layui-input" lay-filter="weight_is_change_rate_usd">
                      </td>
                  </tr>
                  @if(!empty($rule) && !empty($rule->rule) && isset($rule->rule['sku_weight']) && is_array($rule->rule['sku_weight']))
                  @foreach($rule->rule['sku_weight'] as $index => $weight)
                  <tr class="sku-weight-item">
                      <td>
                          <div class="layui-input-inline" style="width: 65px;">
                              <input type="number" name="sku_weight[{{ $index }}][min_weight]" value="{{ $weight['min_weight'] ?? '' }}" class="layui-input min-weight" step="0.0001" min="0">
                          </div>
                          <div class="layui-form-mid">-</div>
                          <div class="layui-input-inline" style="width: 65px;">
                              <input type="number" name="sku_weight[{{ $index }}][max_weight]" value="{{ $weight['max_weight'] ?? '' }}" class="layui-input max-weight" step="0.0001" min="0">
                          </div>
                      </td>
                      <td>
                          <input type="number" name="sku_weight[{{ $index }}][cny_fee]" value="{{ $weight['cny_fee'] ?? '' }}" class="layui-input" step="0.01" min="0">
                      </td>
                      <td>
                          <input type="number" name="sku_weight[{{ $index }}][usd_fee]" value="{{ $weight['usd_fee'] ?? '' }}" class="layui-input" step="0.01" min="0">
                      </td>
                      <td>
                          <div class="layui-btn-group">
                              <button type="button" class="layui-btn layui-btn-xs layui-btn-normal add-sku-weight"><i class="layui-icon">&#xe654;</i></button>
                              <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-sku-weight"><i class="layui-icon">&#xe640;</i></button>
                          </div>
                      </td>
                  </tr>
                  @endforeach
                  @else
                  <tr class="sku-weight-item">
                      <td>
                          <div class="layui-input-inline" style="width: 100px;">
                              <input type="number" name="sku_weight[0][min_weight]" value="0.0000" class="layui-input min-weight layui-disabled" readonly step="0.0001" min="0">
                          </div>
                          <div class="layui-form-mid">-</div>
                          <div class="layui-input-inline" style="width: 100px;">
                              <input type="number" name="sku_weight[0][max_weight]" value="" class="layui-input max-weight" step="0.0001" min="0" placeholder="留空代表无限">
                          </div>
                      </td>
                      <td>
                          <input type="number" name="sku_weight[0][cny_fee]" value="" class="layui-input" step="0.01" min="0">
                      </td>
                      <td>
                          <input type="number" name="sku_weight[0][usd_fee]" value="" class="layui-input" step="0.01" min="0">
                      </td>
                      <td>
                          <div class="layui-btn-group">
                              <button type="button" class="layui-btn layui-btn-xs layui-btn-normal add-sku-weight"><i class="layui-icon">&#xe654;</i></button>
                              <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-sku-weight"><i class="layui-icon">&#xe640;</i></button>
                          </div>
                      </td>
                  </tr>
                  @endif
              </tbody>
          </table>
          <p style="font-weight: bold;font-size: 13px">
              累计型号重量：每一行重量范围为大于最小重量，小于等于最大重量
          </p>
      </div>
  </div>
