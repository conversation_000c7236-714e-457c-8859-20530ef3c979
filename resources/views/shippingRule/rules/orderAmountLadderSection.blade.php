<div class="layui-form-item" id="order_amount_ladder_section" @if(empty($rule) || (!empty($rule) && $rule->type != 2)) style="display: none;" @endif>
    <div class="layui-input-block">
        <table class="layui-table">
            <thead>
                <tr>
                    <th style="width: 80px">币种</th>
                    <th style="width: 120px">基础运费</th>
                    <th style="width: 360px">阶梯运费</th>
                    <th style="width: 120px">按汇率转换</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>人民币</td>
                    <td>
                        <div style="display: flex; align-items: center;">
                            <span style="font-weight: bold;margin-right: 5px">￥</span>
                            <input type="number" name="base_cny_amount" id="base_cny_amount" value="{{ $rule->base_cny_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; flex-direction: row; white-space: nowrap;align-items: center;">
                            <span style="font-weight: bold;margin-right: 5px">每满</span>
                            <input type="number" name="ladder_cny_amount" id="ladder_cny_amount" value="{{ $rule->ladder_cny_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                            <span style="font-weight: bold;margin-right: 5px;margin-left: 5px">元,加收</span>
                            <input type="number" name="ladder_additional_cny_amount" id="ladder_additional_cny_amount" value="{{ $rule->ladder_additional_cny_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                            <span style="font-weight: bold;margin-right: 5px;margin-left: 5px">元运费</span>
                        </div>
                    </td>
                    <td>
                        <input type="checkbox" lay-skin="switch" id="ladder_is_change_rate_cny" name="ladder_is_change_rate_cny" value="{{ $rule->ladder_is_change_rate_cny ?? 1 }}" @if(!empty($rule)&&$rule->ladder_is_change_rate_cny == 1)
                        checked
                        @endif
                        class="layui-input" lay-filter="ladder_is_change_rate_cny">
                    </td>
                </tr>
                <tr>
                    <td>美金</td>
                    <td>
                        <div style="display: flex; align-items: center;">
                            <span style="font-weight: bold;margin-right: 5px">$</span>
                            <input type="number" name="base_usd_amount" id="base_usd_amount" value="{{ $rule->base_usd_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                        </div>
                    </td>
                    <td>
                        <div style="display: flex; flex-direction: row; white-space: nowrap;align-items: center;">
                            <span style="font-weight: bold;margin-right: 5px">每满</span>
                            <input type="number" name="ladder_usd_amount" id="ladder_usd_amount" value="{{ $rule->ladder_usd_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                            <span style="font-weight: bold;margin-right: 5px;margin-left: 5px">美元,加收</span>
                            <input type="number" name="ladder_additional_usd_amount" id="ladder_additional_usd_amount" value="{{ $rule->ladder_additional_usd_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                            <span style="font-weight: bold;margin-right: 5px;margin-left: 5px">美元运费</span>
                        </div>
                    </td>
                    <td>
                        <input type="checkbox" lay-skin="switch" id="ladder_is_change_rate_usd" name="ladder_is_change_rate_usd" value="{{ $rule->ladder_is_change_rate_usd ?? 1 }}" @if(!empty($rule)&&$rule->ladder_is_change_rate_usd == 1)
                        checked
                        @endif
                        class="layui-input" lay-filter="ladder_is_change_rate_usd">
                    </td>
                </tr>
            </tbody>
        </table>
        <p>
        </p>
    </div>
</div>
