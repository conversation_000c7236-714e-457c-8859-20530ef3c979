  <div class="layui-form-item" id="order_sku_num_section" @if(empty($rule) || (!empty($rule) && $rule->type != 3) ) style="display: none;" @endif>
      <div class="layui-input-block" style="width: 800px;">
          <table class="layui-table" id="sku-count-table">
              <thead>
                  <tr>
                      <th style="width: 280px">累计型号数量</th>
                      <th>运费 (CNY)</th>
                      <th>运费 (USD)</th>
                      <th>操作</th>
                  </tr>
              </thead>
              <tbody id="sku-count-body">
                  <tr>
                      <td>
                          按汇率转换
                      </td>
                      <td>
                          <input type="checkbox" lay-skin="switch" id="count_is_change_rate_cny" name="count_is_change_rate_cny" value="{{ $rule->count_is_change_rate_cny ?? '' }}" @if(!empty($rule)&&$rule->count_is_change_rate_cny == 1)
                          checked
                          @endif
                          class="layui-input" lay-filter="count_is_change_rate_cny">
                      </td>
                      <td>
                          <input type="checkbox" lay-skin="switch" id="count_is_change_rate_usd" name="count_is_change_rate_usd" value="{{ $rule->count_is_change_rate_usd ?? '' }}" @if(!empty($rule)&&$rule->count_is_change_rate_usd == 1)
                          checked
                          @endif
                          class="layui-input" lay-filter="count_is_change_rate_usd">
                      </td>
                  </tr>
                  @if(!empty($rule) && !empty($rule->rule) && isset($rule->rule['sku_count']) && is_array($rule->rule['sku_count']))
                  @foreach($rule->rule['sku_count'] as $index => $count)
                  <tr class="sku-count-item">
                      <td>
                          <div class="layui-input-inline" style="width: 60px;">
                              <input type="number" name="sku_count[{{ $index }}][min_count]" value="{{ $count['min_count'] ?? '' }}" class="layui-input min-count" step="1" min="0">
                          </div>
                          <div class="layui-form-mid">-</div>
                          <div class="layui-input-inline" style="width: 60px;">
                              <input type="number" name="sku_count[{{ $index }}][max_count]" value="{{ $count['max_count'] ?? '' }}" class="layui-input max-count" step="1" min="0">
                          </div>
                      </td>
                      <td>
                          <input type="number" name="sku_count[{{ $index }}][cny_fee]" value="{{ $count['cny_fee'] ?? '' }}" class="layui-input" step="0.01" min="0">
                      </td>
                      <td>
                          <input type="number" name="sku_count[{{ $index }}][usd_fee]" value="{{ $count['usd_fee'] ?? '' }}" class="layui-input" step="0.01" min="0">
                      </td>
                      <td>
                          <div class="layui-btn-group">
                              <button type="button" class="layui-btn layui-btn-xs layui-btn-normal add-sku-count"><i class="layui-icon">&#xe654;</i></button>
                              <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-sku-count"><i class="layui-icon">&#xe640;</i></button>
                          </div>
                      </td>
                  </tr>
                  @endforeach
                  @else
                  <tr class="sku-count-item">
                      <td>
                          <div class="layui-input-inline" style="width: 100px;">
                              <input type="number" name="sku_count[0][min_count]" value="1" class="layui-input min-count layui-disabled" readonly step="1" min="0">
                          </div>
                          <div class="layui-form-mid">-</div>
                          <div class="layui-input-inline" style="width: 100px;">
                              <input type="number" name="sku_count[0][max_count]" value="" class="layui-input max-count" step="1" min="0" placeholder="留空代表无限">
                          </div>
                      </td>
                      <td>
                          <input type="number" name="sku_count[0][cny_fee]" value="" class="layui-input" step="0.01" min="0">
                      </td>
                      <td>
                          <input type="number" name="sku_count[0][usd_fee]" value="" class="layui-input" step="0.01" min="0">
                      </td>
                      <td>
                          <div class="layui-btn-group">
                              <button type="button" class="layui-btn layui-btn-xs layui-btn-normal add-sku-count"><i class="layui-icon">&#xe654;</i></button>
                              <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-sku-count"><i class="layui-icon">&#xe640;</i></button>
                          </div>
                      </td>
                  </tr>
                  @endif
              </tbody>
          </table>
      </div>
  </div>
