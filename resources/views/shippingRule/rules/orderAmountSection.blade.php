 <div class="layui-form-item" id="order_amount_section" @if((!empty($rule) && $rule->type != 1) ) style="display: none;" @endif>
     <div class="layui-input-block">
         <table class="layui-table">
             <thead>
                 <tr>
                     <th style="width: 80px">币种</th>
                     <th style="width: 240px">订单包邮最小金额</th>
                     <th style="width: 240px">不满足金额收取运费</th>
                     <th style="width: 100px">按汇率转换</th>
                 </tr>
             </thead>
             <tbody>
                 <tr>
                     <td>人民币</td>
                     <td>
                         <div style="display: flex; align-items: center;">
                             <span style="font-weight: bold;margin-right: 5px">￥</span>
                             <input type="number" name="free_shipping_cny_amount" id="free_shipping_cny_amount" value="{{ $rule->free_shipping_cny_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                         </div>
                     </td>
                     <td>
                         <div style="display: flex; align-items: center;">
                             <span style="font-weight: bold;margin-right: 5px">￥</span>
                             <input type="number" name="shipping_cny_amount" id="shipping_cny_amount" value="{{ $rule->shipping_cny_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                         </div>
                     </td>
                     <td>
                         <input type="checkbox" id="is_change_rate_cny" lay-skin="switch" name="is_change_rate_cny" value="{{ $rule->is_change_rate_cny ?? 1 }}" @if(!empty($rule)&&$rule->is_change_rate_cny == 1)
                         checked
                         @endif
                         class="layui-input" lay-filter="is_change_rate_cny">
                     </td>
                 </tr>
                 <tr>
                     <td>美金</td>
                     <td>
                         <div style="display: flex; align-items: center;">
                             <span style="font-weight: bold;margin-right: 10px">$</span>
                             <input type="number" name="free_shipping_usd_amount" id="free_shipping_usd_amount" value="{{ $rule->free_shipping_usd_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                         </div>
                     </td>
                     <td>
                         <div style="display: flex; align-items: center;">
                             <span style="font-weight: bold;margin-right: 10px">$</span>
                             <input type="number" name="shipping_usd_amount" id="shipping_usd_amount" value="{{ $rule->shipping_usd_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                         </div>
                     </td>
                     <td>
                         <input type="checkbox" id="is_change_rate_usd" lay-skin="switch" name="is_change_rate_usd" value="{{ $rule->is_change_rate_usd ?? 1 }}" @if(!empty($rule)&&$rule->is_change_rate_usd == 1)
                         checked
                         @endif
                         class="layui-input" lay-filter="is_change_rate_usd">
                     </td>
                 </tr>
             </tbody>
         </table>
         <p style="font-weight: bold;font-size: 13px">
             当订单中，该供应商/渠道总金额大于等于【订单包邮最小金额】时，包邮；
             <br>
             小于【订单包邮最小金额】时，按照配置的【不满足金额收取运费】的值收取相应运费
         </p>
     </div>
 </div>
