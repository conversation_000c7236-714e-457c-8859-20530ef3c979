@include('css')
@include('js')
<style>
    .layui-table-cell {
        height: auto;
        line-height: 58px;
    }

</style>
<section class="section-page">
    <div class="layui-form">
        <div class="layui-form-item">
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('supplier_id','渠道 : ',0,$supplierList,['width'=>'200px','radio'=>true]) !!}
            </div>
            <div class="layui-inline">
                @inject('multiSelect','App\Presenters\MultiSelectorPresenter')
                {!! $multiSelect->render('supplier_code','供应商 : ',0,$supplierListForXmSelect,['width'=>'200px','radio'=>true]) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('status','状态 : ',0,[1=>'启用',-1=>'禁用'],['width'=>'200px','radio'=>true]) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('type','方式 : ',0,config('field.ShippingRuleType'),['width'=>'300px','radio'=>true]) !!}
            </div>
            <div class="layui-inline">
                @inject('timeInterval','App\Presenters\TimeIntervalPresenter')
                {!! $timeInterval->render('create_time','创建时间') !!}
            </div>

        </div>
        <div class="layui-form-item" style="text-align: center">
            <div class="layui-inline">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-primary" lay-filter="reset" lay-submit lay-event="reset">重置</button>
            </div>
        </div>
    </div>

    <script type="text/html" id="toolbar">
        <div style="display: flex; align-items: center; gap: 5px;">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm" lay-event="add">新增</button>
            </div>
            <span style="color:orange;">未配置运费规则的供应商默认渠道运费收费方式为：包邮</span>
        </div>
    </script>
    <table class="layui-table" id="list" lay-filter="list"></table>
</section>
<script type="text/html" id="edit">
    <button class="layui-btn layui-btn-xs" lay-event="edit"><strong>编辑</strong></button>
    @{{# if(d.status == 1){ }}
        <button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable"><strong>禁用</strong></button>
        @{{# }else if(d.status == -1){ }}
            <button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="enable"><strong>启用</strong></button>
            @{{# } }}
                <button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="log"><strong>日志</strong></button>

</script>

<script type="text/html" id="status">
    @{{# if(d.status == 1){ }}
        <button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable"><strong>禁用</strong></button>
        @{{# }else if(d.status == 2){ }}
            <button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="enable"><strong>启用</strong></button>
            @{{# } }}

</script>
<script type="text/javascript" src="/assets/js/shippingRule/shippingRuleList.js?v={{time()}}"></script>
