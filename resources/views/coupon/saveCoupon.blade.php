@include('css')
@include('js')
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div style="margin-left: 10px;margin-bottom: 30px">
            <h2>@if(empty($coupon))
                    新增
                @else
                    编辑
                @endif优惠券
            </h2>
        </div>
        <input type="hidden" name="op_type" value="{{empty($coupon)?1:2}}">
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <div style="display: flex;">
                    <div style="width: 270px">
                        @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                        {!! $singleSelectPresenter->render('org_id','优惠券主体:',!empty($coupon)?$coupon['org_id']:request()->user->org_id,config('field.MainOrgList'),['required'=>true,'disabled' => true,'hide_not_select'=>true]) !!}
                    </div>

                    <div id="iedge_org_select_div" style="width: 100px;display: none">
                        <input type="hidden" id="iedge_org_id" name="iedge_org_id" value="{{$coupon['org_id']??''}}">
                        <select id="iedge_org_id_select" lay-verify="required" lay-filter="iedge_org_id_select"
                                @if(!empty($coupon))
                                    disabled
                            @endif>
                            {{--                            <option value="">请选择</option>--}}
                            <option value="3" @if(Arr::get($coupon??[],'org_id',1)==3) selected='selected' @endif>爱智中文站
                            </option>
                            <option value="6" @if(Arr::get($coupon??[],'org_id',1)==6) selected='selected' @endif>爱智英文站
                            </option>
                        </select>
                    </div>
                </div>

            </div>
            <div class="layui-col-md6">

            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    名称:</label>
                <div class="layui-input-block">
                    <div class="layui-col-md10">
                        <div class="layui-input-inline">
                            <input type="hidden" name="coupon_id" id="coupon_id" value="{{$coupon['coupon_id'] ?? 0}}">
                            <input type="text" name="coupon_name" autocomplete="off" style="width: 500px;"
                                   class="layui-input"
                                   id="coupon_name"
                                   value="{{$coupon['coupon_name'] ?? ''}}">
                        </div>
                    </div>
                    <div class="layui-col-md2" style="">
                        <div class="layui-form-mid layui-word-aux" id="coupon_name_length"></div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    描述:</label>
                <div class="layui-input-block">
                    <div class="layui-col-md10">
                        <div class="layui-input-inline">
                            <input type="text" id="coupon_desc" name="coupon_desc" autocomplete="off"
                                   style="width: 500px;"
                                   class="layui-input"
                                   value="{{$coupon['coupon_desc'] ?? ''}}">
                        </div>
                    </div>
                    <div class="layui-col-md2" style="">
                        <div class="layui-form-mid layui-word-aux" id="coupon_desc_length"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                {!! $singleSelectPresenter->render('coupon_type','类型:',!empty($coupon)?$coupon['coupon_type']:1,config('field.CouponType'),['required'=>true,'disabled' => !empty($coupon)?true:false]) !!}
            </div>
            <div class="layui-col-md6">
                @if($org_id == 1)
                    <input type="hidden" id="coupon_mall_type_value" value="{{$coupon['coupon_mall_type'] ?? 1}}">
                    @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                    {!! $singleSelectPresenter->render('coupon_mall_type','适用商品:',!empty($coupon)?$coupon['coupon_mall_type']:1,config('field.CouponMallType'),['required'=>true]) !!}
                @endif
                @if($org_id == 3)
                    <input type="hidden" id="coupon_mall_type_value" value="{{$coupon['coupon_mall_type'] ?? 3}}">
                    @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                    {!! $singleSelectPresenter->render('coupon_mall_type','适用商品:',!empty($coupon)?$coupon['coupon_mall_type']:3,[3=>'专营商品'],['required'=>true,'disabled'=>true]) !!}
                @endif
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <input type="hidden" id="time_type_value" value="{{$coupon['time_type'] ?? 1}}">
                @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                {!! $singleSelectPresenter->render('time_type','有效期类型:',!empty($coupon)?$coupon['time_type']:1,config('field.CouponTimeType'),['required'=>true,'disabled' => !empty($coupon)?true:false]) !!}
            </div>
            <div class="layui-col-md6">
                <input type="hidden" id="coupon_type_value" value="{{$coupon['coupon_type'] ?? 1}}">
                <div id="time_type_1"
                     @if(!empty($coupon)&&$coupon['time_type']==2)
                         style="display: none"
                    @endif
                >
                    <label class="layui-form-label">
                        <span style="color: red">*</span>
                        有效日期:</label>
                    <div class="layui-input-block">
                        <div class="layui-col-md3">
                            <input type="text" name="start_time" id="start_time" placeholder="开始时间"
                                   autocomplete="off"
                                   class="layui-input {{!empty($coupon)?'layui-disabled':''}}"
                                   value="{{$coupon['start_time'] ?? ''}}" {{!empty($coupon)?'disabled':''}}>
                        </div>
                        <div class="layui-col-md1" style="padding-top: 5px;padding-left: 15px">
                            ------
                        </div>
                        <div class="layui-col-md3">
                            <input type="text" name="end_time" id="end_time" placeholder="结束时间" autocomplete="off"
                                   class="layui-input {{!empty($coupon)?'layui-disabled':''}}"
                                   value="{{$coupon['end_time'] ?? ''}}" {{!empty($coupon)?'disabled':''}}>
                        </div>
                    </div>
                </div>
                <div id="time_type_2"
                     @if(!empty($coupon)&&$coupon['time_type']==1)
                         style="display: none"
                     @endif
                     @if(empty($coupon))
                         style="display: none"
                    @endif
                >
                    <label class="layui-form-label">
                        <span style="color: red">*</span>
                        有效日期(天):</label>
                    <div class="layui-input-inline">
                        <div class="layui-input-inline">
                            <input type="text" name="usable_time" autocomplete="off"
                                   class="layui-input"
                                   value="{{$coupon['usable_time'] ?? ''}}">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                {!! $singleSelectPresenter->render('issue_type','发放方式:',!empty($coupon)?$coupon['issue_type']:1,config('field.CouponIssueType'),['required'=>true,'disabled' => !empty($coupon)?true:false]) !!}
            </div>
            <div class="layui-col-md6">

                <div class="layui-col-md6">
                    <label class="layui-form-label">
                        <span style="color: red">*</span>
                        发行量(张):</label>
                    <div class="layui-input-inline">
                        <div class="layui-input-inline">
                            <input type="text" name="coupon_num" autocomplete="off"
                                   class="layui-input {{!empty($coupon)?'layui-disabled':''}}"
                                   value="{{$coupon['coupon_num'] ?? ''}}" {{!empty($coupon)?'disabled':''}}>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6" style="font-size: 12px;margin-left: -80px;padding-top: 5px;color: grey">
                            <span>最大可填写10000
                            </span>
                </div>
            </div>


        </div>
        <div class="layui-form-item" style="max-height: 30px">
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    面额:</label>
                <div class="layui-input-inline" style="width: 600px">
                    <p>
                    <div class="layui-col-md4">
                        <div class="layui-col-md2" style="color: red;font-size: 14px;margin-top: 5px">
                            <span style="color: #595959">满 </span> <span id="currency_symbol">¥</span>
                        </div>
                        <div class="layui-col-md6"><input type="text" name="require_amount" autocomplete="off"
                                                          class="layui-input {{!empty($coupon)?'layui-disabled':''}}"
                                                          value="{{$coupon['require_amount'] ?? ''}}" {{!empty($coupon)?'disabled':''}}>
                        </div>

                    </div>
                    <div class="layui-col-md8" style="margin-left: -40px">
                        <div class="layui-col-md3" style="font-size: 14px;margin-top: 5px"
                             id="sale_amount_operate_text">
                            <span style="color: #595959">可减</span> <span style="color: red">¥</span>
                        </div>
                        <div class="layui-col-md3" style="margin-left: -30px">
                            <input type="text" name="sale_amount"
                                   autocomplete="off"
                                   class="layui-input {{!empty($coupon)?'layui-disabled':''}}"
                                   value="{{$coupon['sale_amount'] ?? ''}}" {{!empty($coupon)?'disabled':''}}>
                        </div>
                        <div class="layui-col-md6"
                             style="font-size: 12px;padding-left: 10px;padding-top: 5px;color: grey">
                            <span id="sale_amount_type_text">
                            </span>
                        </div>
                    </div>
                    </p>
                </div>
            </div>

            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">
                        <span style="color: red">*</span>
                        限领规则:</label>
                    <div class="layui-input-inline" style="width: 100px;height: 30px">
                        <input type="checkbox" title="每人限领"
                               lay-skin="primary" value="1" {{!empty($coupon['total_receive_num'])? 'checked':''}}>
                        <div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>每人限领</span><i
                                class="layui-icon layui-icon-ok"></i></div>
                    </div>
                    <div class="layui-input-inline" style="width: 100px">
                        <input type="text" name="total_receive_num"
                               autocomplete="off" class="layui-input"
                               value="{{$coupon['total_receive_num'] ?? 0}}">
                    </div>
                    <div class="layui-form-mid layui-word-aux">张</div>
                    <div class="layui-input-inline" style="width: 130px;height: 30px">
                        <input type="checkbox" id="day_receive_num" title="每人每天限领"
                               {{!empty($coupon['day_receive_num']) ? 'checked':''}}   lay-skin="primary">
                        <div class="layui-unselect layui-form-checkbox" lay-skin="primary"><span>每人每天限领</span><i
                                class="layui-icon layui-icon-ok"></i></div>
                    </div>
                    <div class="layui-input-inline">
                        <input type="text" name="day_receive_num"
                               autocomplete="off" class="layui-input"
                               value="{{$coupon['day_receive_num'] ?? 0}}">
                    </div>
                    <div class="layui-form-mid layui-word-aux">张</div>
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="max_preferential_amount_div">
            <label class="layui-form-label">此券最多减:</label>
            <div class="layui-input-inline">
                <input type="text" name="max_preferential_amount" id="max_preferential_amount" class="layui-input"
                       autocomplete="off" value="{{ $coupon['max_preferential_amount'] ?? 0}}"/>
            </div>
            <div class="layui-form-mid layui-word-aux">元</div>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                @if($org_id == 1)
                    <input type="hidden" id="coupon_get_rule_value" value="{{$coupon['coupon_get_rule']??''}}">
                    @inject('statusPresenter','App\Presenters\StatusPresenter')
                    {!! $statusPresenter->render('coupon_get_rule','领取规则:',$coupon['coupon_get_rule'] ?? '',config('field.CouponGetRuler')) !!}
                @endif
                @if($org_id == 3)
                    <input type="hidden" id="coupon_get_rule_value" value="{{$coupon['coupon_get_rule']??''}}">
                    <div style="display: none">
                        @inject('statusPresenter','App\Presenters\StatusPresenter')
                        {!! $statusPresenter->render('coupon_get_rule','领取规则:',$coupon['coupon_get_rule'] ?? '',config('field.CouponGetRuler'),['disabled'=>true]) !!}
                    </div>
                @endif
            </div>
            <div class="layui-col-md6">
                <input type="hidden" id="coupon_get_rule_value" value="{{$coupon['coupon_get_rule']??''}}">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('is_activity_center_show','活动中心展示:',$coupon['is_activity_center_show'] ?? -1,[1=>'是',-1=>'否']) !!}
            </div>
            <div class="layui-col-md6" style="margin-left: -5px;margin-top: 5px;">
                <div id="reg_start_time_div">
                    <label class="layui-form-label" style="width: 130px;">
                        <span style="color: red;">*</span>
                        选择注册日期:</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" type="text" id="reg_start_time" name="reg_start_time"
                               value="{{$coupon['reg_start_time'] ?? ''}}">
                    </div>
                    <div class="reg_end_time_div" id="reg_end_time_div">
                        <label class="layui-form-label" style="width: 130px;">
                            <span style="color: red;">*</span>
                            发放截至时间:</label>
                        <div class="layui-input-inline">
                            <input class="layui-input" type="text" id="register_end_time" name="register_end_time"
                                   value="{{$coupon['end_time'] ?? ''}}">
                        </div>
                    </div>

                </div>
                <div id="order_num_div">
                    <label class="layui-form-label"><span style="color: red">*</span>每笔订单可领:</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" type="text" id="order_num" name="order_num"
                               value="{{$coupon['order_num'] ?? ''}}">
                    </div>
                </div>
            </div>

        </div>
        <!-- 是付款可领取才展示 -->
        @if(!empty($coupon)  &&  $coupon['coupon_get_rule'] == 3)
            <div class="layui-form-item" id="coupon_get_rule_order_time">
                <label class="layui-form-label">付款/赠送日期:</label>
                <div class="layui-input-inline" style="width:180px">
                    <input class="layui-input" type="text" id="effect_start_time" name="effect_start_time"
                           lay-verify="" value="{{$coupon['effect_start_time'] ?: ''}}">
                </div>
                <div class="layui-form-mid">-</div>
                <div class="layui-input-inline" style="width:180px">
                    <input class="layui-input" type="text" id="effect_end_time" name="effect_end_time"
                           lay-verify=""
                           value="{{$coupon['effect_end_time'] ? : ''}}">
                </div>
            </div>
        @endif
        <div class="layui-form-item" id="coupon_goods_range_div">
            @if($org_id==1)
                <input type="hidden" id="coupon_goods_range_value" value="{{$coupon['coupon_goods_range'] ?? 1}}">
                @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                {!! $singleSelectPresenter->render('coupon_goods_range','商品范围:',!empty($coupon)?$coupon['coupon_goods_range']:1,config('field.CouponGoodsRange'),['required'=>true]) !!}
            @endif
            @if($org_id==3)
                <input type="hidden" id="coupon_goods_range_value" value="{{$coupon['coupon_goods_range'] ?? 3}}">
                @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                {!! $singleSelectPresenter->render('coupon_goods_range','商品范围:',!empty($coupon)?$coupon['coupon_goods_range']:3,config('field.CouponGoodsRange'),['required'=>true,'disabled'=>true]) !!}
            @endif
        </div>
        <div class="layui-form-item" id="selected_supplier_id_div">
            <label class="layui-form-label">
                适用供应商:</label>
            <div class="layui-input-inline">
                <div id="supplier_selector" class="layui-input-inline" value="" style="width:700px;">
                </div>
                <input type="hidden" name="selected_supplier_id" value="{{$coupon['selected_supplier_id'] ?? ''}}"
                       id="selected_supplier_id">
            </div>
        </div>

        <div class="layui-form-item" id="supplier_ids_div">
            <label class="layui-form-label">
                <span style="color: red">*</span>
                供应商</label>
            <div class="layui-input-inline">
                <div id="supplier_ids_selector" class="layui-input-inline" value="" style="width:700px;">
                </div>
                @if($org_id==1)
                    <input type="hidden" name="supplier_ids" value="{{$coupon['supplier_ids'] ?? ''}}"
                           id="supplier_ids">
                @else
                    <input type="hidden" name="supplier_ids" value="{{$coupon['supplier_ids'] ?? 17}}"
                           id="supplier_ids">
                @endif
            </div>
        </div>


        @if($org_id==1)
            <input type="hidden" id="canal_init_value"
                   value="{{!empty($canal_init_value) ? json_encode($canal_init_value): ''}}">
        @endif
        @if($org_id==3)
            <input type="hidden" id="canal_init_value"
                   value="{{json_encode((new \App\Http\Services\PriceActivityService())->getCanalInitValue('L0015730'))}}">
        @endif
        <div class="layui-form-item" id="canal_select_div" style="
        @if(!empty($coupon) && strpos($coupon['supplier_ids'],strval(17)) === false) display:none; @endif
        @if(empty($coupon)) display:none; @endif
        ">
            <label class="layui-form-label">
                渠道标签</label>
            <div class="layui-input-inline">
                <div id="canal_selector" class="layui-input-inline" value="" style="width: 700px;">
                </div>
                @if($org_id==1)
                    <input type="hidden" name="canals" value="{{$coupon['canals'] ?? ''}}" id="canals">
                @endif
                @if($org_id==3)
                    <input type="hidden" name="canals" value="{{$coupon['canals'] ?? 'L0015730'}}" id="canals">
                @endif
            </div>
            <div style="margin-left: 530px" class="layui-form-mid layui-word-aux">只有供应商为专卖的时候,渠道标签才会起作用,因为其它供应商没有渠道标签
            </div>
        </div>
        <div class="layui-form-item" id="brand_ids_div">
            <div class="layui-row">
                @inject('brandNameListPresenter','App\Presenters\BrandNameListPresenter')
                {!! $brandNameListPresenter->render('brand_ids','参与的品牌:',$coupon['brand_ids'] ?? '',1) !!}
            </div>
            <div class="layui-row">
                @inject('brandNameListPresenter','App\Presenters\BrandNameListPresenter')
                {!! $brandNameListPresenter->render('exclude_brand_ids','不参与的品牌:',$coupon['exclude_brand_ids'] ?? '',1) !!}
            </div>
        </div>
        <div class="layui-form-item" id="self_brand_ids_div">
            <div class="layui-row">
                @inject('brandNameListPresenter','App\Presenters\BrandNameListPresenter')
                {!! $brandNameListPresenter->render('self_brand_ids','参与的品牌:',$coupon['self_brand_ids'] ?? '',2) !!}
            </div>
            <div class="layui-row">
                @inject('brandNameListPresenter','App\Presenters\BrandNameListPresenter')
                {!! $brandNameListPresenter->render('exclude_self_brand_ids','不参与的品牌:',$coupon['exclude_self_brand_ids'] ?? '',2) !!}
            </div>
        </div>
        <div class="layui-form-item" id="class_ids_div">
            <label class="layui-form-label">
                {{--                <span style="color: red">*</span>--}}
                二级分类ID : </label>
            <div class="layui-input-inline">
                <div class="layui-input-inline">
                    <input type="text" name="class_ids" autocomplete="off"
                           class="layui-input"
                           value="{{$coupon['class_ids'] ?? ''}}">
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="self_supplier_id_div">
            <label class="layui-form-label">
                {{--                <span style="color: red">*</span>--}}
                供应商ID : </label>
            <div class="layui-input-inline">
                <div class="layui-input-inline">
                    <input type="text" name="self_supplier_id" autocomplete="off"
                           class="layui-input"
                           value="{{$coupon['self_supplier_id'] ?? ''}}">
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="goods_name_div" style="display: none">
            <label class="layui-form-label">
                参与商品型号</label>
            <div class="layui-input-block">
                <div class="layui-col-md1" style="margin-top: 5px">
                    <a target="_blank" style="color: dodgerblue"
                       href="/template/优惠券参与商品模板.csv">下载模板</a>
                </div>
                <div class="layui-col-md2" style="margin-left: -60px">
                    <button type="button" class="layui-btn layui-btn-sm" id="uploadSku">点击上传</button>
                    <input type="hidden" value="{{$coupon['sku_file_url'] ?? ''}}"
                           id="sku_file_url" name="sku_file_url">
                    <a id="sku_file_url_href"
                       @if(empty($coupon['sku_file_url']))
                           style="display: none"
                       @endif
                       target="_blank" href="{{$coupon['sku_file_url'] ?? ''}}"><img
                            style="width: 35px;height: 35px" src="/assets/images/u1080.svg"></a>
                </div>
            </div>
        </div>
        @if(!empty($coupon))
            {{--            @if($coupon['end_time']>time())--}}
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" lay-submit lay-filter="saveForm">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary cancel">取消
                    </button>
                </div>
            </div>
            {{--            @endif--}}
        @else
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" lay-submit lay-filter="saveForm">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary cancel">取消
                    </button>
                </div>
            </div>
        @endif
        <br>
    </form>
</section>
<script type="text/javascript" src="/assets/js/coupon/saveCoupon.js?v={{time()}}"></script>

