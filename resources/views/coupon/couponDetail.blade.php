@include('css')
@include('js')
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div style="margin-left: 10px;">
            <h2><strong>优惠券 - {{$coupon['coupon_sn']}}</strong></h2>
        </div>
        <hr>
        <div style="padding: 20px">
            <div class="layui-form-item">
                <div class="layui-col-md6">
                    优惠券主体 ：{{ Arr::get(config('field.OrgList'),$coupon['org_id']) }}
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-col-md6">
                    名称 ：{{$coupon['coupon_name']}}
                </div>
                <div class="layui-col-md6">
                    描述 ：{{$coupon['coupon_desc']}}
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-col-md6">
                    有效期类型 ：{{$coupon['time_type_name']}}
                </div>
                <div class="layui-col-md6">
                    有效日期
                    ：{{$coupon['start_time']?$coupon['start_time'].' - '.$coupon['end_time']:$coupon['usable_time']}}
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-col-md6">
                    发放方式 :
                    {{$coupon['issue_type_name']}}
                </div>
                <div class="layui-col-md6">
                    发行量(张) ：{{$coupon['coupon_num']}}
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-col-md6">
                    面额 :
                    {{$coupon['amount_rule']}}
                </div>
                <div class="layui-col-md6">
                    限领规则 ：每人限领 {{$coupon['total_receive_num']}} 张,每人每天限领 {{$coupon['day_receive_num']}} 张
                </div>
            </div>
            @if($coupon['org_id']!=3)
                <div class="layui-form-item">
                    <div class="layui-col-md6">
                        领取规则：
                        {{$coupon['coupon_get_rule_name']}}
                    </div>
                    <div class="layui-col-md6">
                        选择注册日期：{{$coupon['reg_start_time']}}
                    </div>
                </div>
            @endif
            <div class="layui-form-item">
                @if($coupon['org_id']==3 || $coupon['org_id']==6)
                    <div class="layui-col-md6">
                        适用供应商：L0015730 广州华云数字科技有限公司
                    </div>
                @else
                    <div class="layui-col-md6">
                        适用供应商：
                        {{$coupon['selected_supplier'] ?: '暂无'}}
                    </div>
                @endif
                @if($coupon['org_id']!=3)
                    <div class="layui-col-md6">
                        适用品牌 ：
                        @if(!empty($coupon['coupon_brand']))
                            @foreach($coupon['coupon_brand'] as $brand)
                                {{$brand['brand']['brand_name']}} |
                            @endforeach
                        @else
                            全部品牌
                        @endif
                    </div>
                @endif
            </div>
        </div>
        <div class="layui-form-item">
            <div style="padding: 20px">
                <h2>操作日志</h2>
                <hr/>
                <table class="layui-table">
                    <colgroup>
                        <col width="150">
                        <col width="200">
                        <col>
                    </colgroup>
                    <thead>
                    <tr>
                        <th>序号</th>
                        <th>操作时间</th>
                        <th>操作类型</th>
                        <th>操作人</th>
                        <th>操作结果</th>
                        <th>备注</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($logs as $log)
                        <tr>
                            <td>{{$log['op_log_id']}}</td>
                            <td>{{$log['op_time']}}</td>
                            <td>{{$log['op_type_name']}}</td>
                            <td>{{$log['operator_name']}}</td>
                            <td>{{$log['status_name']}}</td>
                            <td>{{$log['remark']}}</td>
                        </tr>
                    @endforeach

                    </tbody>
                </table>
            </div>
        </div>
    </form>
</section>
<script type="text/javascript" src="/assets/js/coupon/reviewCoupons.js?v={{time()}}"></script>

