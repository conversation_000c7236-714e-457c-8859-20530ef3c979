@include('css')
@include('js')
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div style="margin-left: 10px;margin-bottom: 30px">
            <h2><strong>发放优惠券</strong></h2>
        </div>
        <blockquote class="layui-elem-quote" style="font-size: 15px">
            发券未完成前<span style="color: red">请勿离开或者刷新此页面</span>，否则会造成<span style="color: red">发券中断</span>
            发券无数量限制
        </blockquote>
        <div style="padding: 20px">
            <div class="layui-form-item">
                <div class="layui-col-md6">
                    批次号 ：{{$coupon['coupon_sn']}}
                </div>
                <div class="layui-col-md6">
                    名称 :{{$coupon['coupon_name']}}
                </div>
            </div>
            <div class="layui-form-item">

                <div class="layui-col-md6">
                    描述 ：{{$coupon['coupon_desc']}}
                </div>
                <div class="layui-col-md6">
                    券剩余量 :
                    {{$coupon['surplus_num']}}
                </div>
            </div>
            <input type="hidden" name="coupon_id" value="{{$coupon['coupon_id']}}">
            <div class="layui-form-item" style="margin-left: -45px">
                <label class="layui-form-label">
                    限领规则 :</label>
                <div class="layui-input-inline" style="width: 200px;margin-left: -10px;">
                    <label class="layui-form-label" style="width:80px;">每人发放</label>
                    <div class="layui-input-inline" style="width:50px;">
                        <input class="layui-input" type="text" name="issue_num" id="issue_num"
                               lay-verify="required|number">
                    </div>
                    <div class="layui-form-mid layui-word-aux" style="margin-top: -5px">张</div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-left: -45px">
                <label class="layui-form-label">
                    发放名单 :</label>
                <div class="layui-input-block">
                    <textarea rows="7" placeholder="请输入用户名单,多个用英文逗号隔开" class="layui-textarea"
                              name="send_list" id="user_account_list"></textarea>
                </div>
            </div>
            <div class="layui-form-item" style="margin-left: -45px">
                <label class="layui-form-label">
                    批量导入 :</label>
                <div class="layui-input-block">
                    <a target="_blank" href="/template/发放优惠券名单模板.csv" class="layui-btn layui-btn-primary" id="downloadTemplate">下载模板</a>
                    <button type="button" class="layui-btn" id="analysisIssueCouponFile">上传名单
                    </button>
                </div>
            </div>
            <div class="layui-form-item" style="margin-left: -45px">
                <label class="layui-form-label">
                    错误名单 :</label>
                <div class="layui-input-block" id="invalid_account_list" style="padding-top: 8px">

                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 40%;margin-top: 20px">
                    <button type="button" class="layui-btn saveForm" lay-submit lay-filter="saveForm">立即提交</button>
                    <button type="button" class="layui-btn layui-btn-primary closeForm closeForm" lay-filter="closeForm">取消
                    </button>
                </div>
            </div>
            <br>
        </div>
    </form>
</section>
<script type="text/javascript" src="/assets/js/coupon/issueCoupon.js?v={{time()}}"></script>

