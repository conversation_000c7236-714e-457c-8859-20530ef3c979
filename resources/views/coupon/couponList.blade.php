@include('css')
<section class="section-page">
    <div class="layui-form layui-box">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="coupon_name" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">批次号</label>
                <div class="layui-input-inline">
                    <input type="text" name="coupon_sn" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-inline">
                    <input type="text" name="coupon_desc" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('coupon_type','类型',request()->get('coupon_type'),config('field.CouponType')) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('status','状态',request()->get('status'),config('field.CouponStatus')) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('create_uid','创建人',request()->get('create_uid'),$createrList) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('org_id','优惠券主体',request()->get('org_id'),config('field.OrgList')) !!}
            </div>
            <div class="layui-inline">
                @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                {!! $timePresenter->render('create_time','创建时间') !!}
            </div>

            <div class="layui-inline" style="padding-left: 60px">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                </button>
                <a href="/web/coupon/couponList" class="layui-btn layui-btn-primary layui-btn-sm reload"
                   data-type="reload">重置</a>
            </div>
        </div>
    </div>


    <table class="layui-table" id="list" lay-filter="list"></table>
</section>
<script type="text/html" id="toolbar">
    <button class="layui-btn layui-btn-sm" style="margin-left: 20px" id="addCoupon"
            lay-event="add"><strong>新增</strong></button>
    <button class="layui-btn layui-btn-sm" style="margin-left: 20px" id="reviewCoupon"
            lay-event="review"><strong>审核</strong></button>
    <button class="layui-btn layui-btn-sm" style="margin-left: 20px" id="issueCoupon"
            lay-event="issue"><strong>发放优惠券</strong></button>
</script>
<script type="text/html" id="edit">
    <button class="layui-btn layui-btn-xs"
            lay-event="edit"><strong>编辑</strong></button>
    @{{#  if(d.status!=-3){ }}
    <button class="layui-btn layui-btn-xs layui-btn-danger delete" lay-event="delete" value="@{{ d.id }}">
        <strong>删除</strong></button>
    @{{# } }}
</script>


<script type="text/html" id="status">
    @{{#  if(d.status==-1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>已删除</strong></a>
    @{{# }else if(d.expired==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-primary"><strong>已过期</strong></a>
    @{{# }else if(d.status==0){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>未上线</strong></a>
    @{{# }else if(d.status==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-success"><strong>已上线</strong></a>
    @{{# } }}
</script>
@include('js')
<script type="text/javascript" src="/assets/js/coupon/couponList.js?v={{time()}}"></script>
