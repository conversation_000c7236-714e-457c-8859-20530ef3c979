@include('css')
@include('js')
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div style="margin-left: 10px;">
            <h2><strong>审核优惠券</strong></h2>
        </div>
        <input type="hidden" name="couponIds" id="couponIds" value="{{$couponIds}}">
        <div style="padding: 20px">
            @foreach($couponList as $coupon)
                <h2>{{$coupon['coupon_sn']}}</h2>
                <br>
                <div class="layui-form-item">
                    <div class="layui-col-md6">
                        名称 ：{{$coupon['coupon_name']}}
                    </div>
                    <div class="layui-col-md6">
                        描述 ：{{$coupon['coupon_desc']}}
                    </div>

                </div>
                <div class="layui-form-item">
                    <div class="layui-col-md6">
                        有效期类型 :
                        ：{{$coupon['time_type_name']}}
                    </div>
                    <div class="layui-col-md6">
                        有效日期
                        ：{{$coupon['start_time']?$coupon['start_time'].' - '.$coupon['end_time']:$coupon['usable_time']}}
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-col-md6">
                        发放方式 :
                        {{$coupon['issue_type_name']}}
                    </div>
                    <div class="layui-col-md6">
                        发行量(张) ：{{$coupon['coupon_num']}}
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-col-md6">
                        面额 :
                        {{$coupon['amount_rule']}}
                    </div>
                    <div class="layui-col-md6">
                        限领规则 ：每人限领 {{$coupon['total_receive_num']}} 张,每人每天限领 {{$coupon['day_receive_num']}} 张
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-col-md6">
                        领取规则：
                        {{$coupon['coupon_get_rule_name']}}
                    </div>
                    <div class="layui-col-md6">
                        选择注册日期：{{$coupon['reg_start_time']}}
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-col-md6">
                        适用供应商：
                        {{$coupon['selected_supplier'] ?? ''}}
                    </div>
                    <div class="layui-col-md6">
                        适用品牌 ：
                        @if(!empty($coupon['coupon_brand']))
                            @foreach($coupon['coupon_brand'] as $brand)
                                {{$brand['brand']['brand_name']}} |
                            @endforeach
                        @else
                            全部品牌
                        @endif
                    </div>
                </div>
                <hr>
            @endforeach
            <h2>审核信息</h2>
            <br>
            <div class="layui-form-item" style="">
                <label class="layui-form-label">
                    <span style="color: red">*</span>审核备注 :</label>
                <div class="layui-input-block">
                    <textarea rows="7" placeholder="请输入审核备注" class="layui-textarea"
                              name="review_remark" id="review_remark"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 40%;margin-top: 20px">
                    <button type="button" class="layui-btn" lay-submit lay-filter="reviewPass">审核通过</button>
                    <button type="button" class="layui-btn layui-btn-danger" lay-submit lay-filter="reviewReject">
                        审核不通过
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary" lay-submit lay-filter="cancel">暂不审核
                    </button>
                    </button>
                </div>
            </div>
        </div>

    </form>
</section>
<script type="text/javascript" src="/assets/js/coupon/reviewCoupons.js?v={{time()}}"></script>

