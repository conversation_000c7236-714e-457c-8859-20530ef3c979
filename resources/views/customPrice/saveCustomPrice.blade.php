@include('css')
@include('js')
<style>

</style>
<script type="text/javascript" src="/assets/module/jqueryCsv/jquery.csv.min.js?v={{ time() }}"></script>
<style>
    .layui-form-label {
        width: 110px;
    }

    xm-select {
        height: auto !important;
    }

    .layui-table {
        width: 500px !important;
    }
</style>
<section class="section-page">
    <form class="layui-form" action="">
        <input type="hidden" name="id" value="{{ $customPrice['id'] ?? 0 }}">
        <div class="layui-form-item">
            @inject('statusPresenter', 'App\Presenters\StatusPresenter')
            {!! $statusPresenter->render('org_id', '应用组织', $customPrice['org_id'] ?? 3, config('field.OrgListIEdgeFirst'), [
                'required' => true,
            ]) !!}
        </div>
        <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
            <legend>阶梯设置</legend>
        </fieldset>
        <div style="padding:0 25px 25px 25px;width: 60%;">

            @if (!empty($customPrice['price_list']))
                <blockquote class="layui-elem-quote">
                    <h3 style="color: #009688;">当前生效的阶梯设置</h3>
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>价格名称</th>
                                <th>人民币利润系数(%)</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($customPrice['price_list'] as $price)
                                <tr>
                                    <td>{{ $price['price_name'] }}</td>
                                    <td>{{ $price['ratio'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </blockquote>
            @endif

            @if (request()->is_audit == 1)
                <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
                    <legend>申请审核的阶梯设置</legend>
                </fieldset>
            @endif
            @if (request()->is_audit == 0)
                <div class="layui-form-item">
                    <button type="button" class="layui-btn layui-btn-sm" id="addRow">添加行</button>
                </div>
                <table class="layui-table" lay-data="{id: 'priceTable'}" lay-filter="priceTable">
                    <thead>
                        <tr>
                            <th lay-data="{field:'price_name', title: '价格名称', edit: 'text'}">价格名称</th>
                            <th lay-data="{field:'ratio', title: '人民币利润系数(%)', width: 200, edit: 'number'}">系数</th>
                            <th lay-data="{field:'operation', title: '操作', width: 150, toolbar: '#tableOperation'}">操作
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (!empty($customPrice['audit_price_list']))
                            @foreach ($customPrice['audit_price_list'] as $price)
                                <tr>
                                    <td>{{ $price['price_name'] }}</td>
                                    <td>{{ $price['ratio'] }}</td>
                                    <td></td>
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
                @else
                 <blockquote class="layui-elem-quote">
                    <h3 style="color: #009688;">当前审核的阶梯设置</h3>
                    <table class="layui-table">
                        <thead>
                            <tr>
                                <th>价格名称</th>
                                <th>人民币利润系数(%)</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($customPrice['audit_price_list'] as $price)
                                <tr>
                                    <td>{{ $price['price_name'] }}</td>
                                    <td>{{ $price['ratio'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </blockquote>
            @endif
        </div>


        <script type="text/html" id="tableOperation">
            <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">删除</a>
        </script>

        <div class="layui-form-item" style="margin-left: 35%;padding-bottom: 10px">
            <div class="layui-input-block">
                @if (
                    !empty($customPrice['id']) &&
                        $customPrice['audit_status'] == \App\Http\Models\Cube\CustomPriceModel::STATUS_NEED_AUDIT &&
                        request()->is_audit == 1)
                    <button type="button" class="layui-btn layui-btn-sm" lay-submit
                        lay-filter="auditPass">审核通过</button>
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" lay-submit
                        lay-filter="auditFail">审核不通过</button>
                    <button type="button" class="layui-btn layui-btn-primary" lay-submit lay-filter="closeForm">取消
                    </button>
                    <br>
                    <br>
                    <div style="margin-left: -110px;">
                        <label class="layui-form-label">
                            审核备注</label>
                        <div class="layui-input-block" style="width: 60%;">
                            <textarea rows="7" name="audit_content" id="audit_content" placeholder="请输入审核内容" class="layui-textarea"></textarea>
                        </div>
                    </div>
                @else
                    <button type="button" class="layui-btn layui-btn-sm" lay-submit
                        lay-filter="saveForm">修改并提交审核</button>
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" lay-submit
                        lay-filter="closeForm">取消
                    </button>
                @endif
            </div>

        </div>
        <div class="layui-form-item">

        </div>
        </div>
    </form>
</section>
<script type="text/javascript" src="/assets/js/customPrice/saveCustomPrice.js?v={{ time() }}"></script>
