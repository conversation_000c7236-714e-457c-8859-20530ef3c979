@include('css')
@include('js')
<section class="section-page">
    <div class="layui-form layui-box">
        <div class="layui-form-item mb0">
            <div class="layui-inline">
                @inject('statusPresenter', 'App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('status', '状态', request()->get('status'), config('field.CustomPriceStatus')) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter', 'App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('audit_status', '审核状态', request()->get('audit_status'), config('field.CustomPriceAuditStatus')) !!}
            </div>
            <div class="layui-inline">
                @inject('timePresenter', 'App\Presenters\TimeIntervalPresenter')
                {!! $timePresenter->render('create_time', '创建时间') !!}
            </div>

            <div class="layui-inline" style="padding-left: 60px">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                </button>
                <a href="/web/customPrice/customPriceList" class="layui-btn layui-btn-primary layui-btn-sm reload"
                    data-type="reload">重置</a>
            </div>
        </div>
    </div>
    <!--工具类-->
    <script type="text/html" id="toolbar">
        <button class="layui-btn layui-btn-sm" type="button" style="margin-left: 20px" id="addCustomPrice"
                lay-event="addCustomPrice"><strong>新增</strong></button>
    </script>
    <table class="layui-table" id="list" lay-filter="list"></table>
</section>
<script type="text/html" id="edit">
    @{{#  if(d.audit_status==1){ }}
    @{{# if(d.status==1){ }}
     <button type="button"
       class="layui-btn layui-btn-xs"
            lay-event="edit"><strong>编辑</strong></button>
    <button class="layui-btn layui-btn-xs layui-btn-danger disable" value="@{{ d.id }}" lay-event="disable"><strong>禁用</strong></button>
    @{{# }else if(d.status==-1){ }}
     <button type="button"
       class="layui-btn layui-btn-xs"
            lay-event="edit"><strong>编辑</strong></button>
    <button class="layui-btn layui-btn-xs layui-btn-danger enable" value="@{{ d.id }}" lay-event="enable"><strong>启用</strong></button>
    @{{# } }}
    @{{# } }}
    @{{#  if(d.audit_status==0){ }}
      @if(\App\Http\Services\PermService::hasPerm('api_customPrice_auditCustomPrice'))
          <button class="layui-btn layui-btn-xs layui-btn-info audit" lay-event="audit" value="@{{ d.id }}"><strong>审核</strong></button>
      @else

      @endif
    @{{# } }}
     @{{#  if(d.audit_status==-1){ }}
  <button type="button"
       class="layui-btn layui-btn-xs"
            lay-event="edit"><strong>重新提交审核</strong></button>
    @{{# } }}
</script>
<script type="text/html" id="add_time">
    @{{ date('Y-m-d H:i',d.add_time) }}
</script>

<script type="text/javascript" src="/assets/js/customPrice/customPriceList.js?v={{ time() }}"></script>
