@include('css')
@include('js')
<style>

</style>
<script type="text/javascript" src="/assets/module/jqueryCsv/jquery.csv.min.js?v={{time()}}"></script>
<style>
    .layui-form-label {
        width: 110px;
    }

    xm-select {
        height: auto !important;
    }
</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div style="margin-left: 10px;margin-bottom: 30px">
            <h2>活动信息</h2>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    活动名称</label>
                <div class="layui-input-inline">
                    <div class="layui-input-inline">
                        <input type="hidden" name="price_id" id="price_id" value="{{$price['id'] ?? 0}}">
                        <input type="text" name="price_name" autocomplete="off" style="width: 500px;"
                               class="layui-input"
                               value="{{$price['price_name'] ?? ''}}">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-inline">
                    <label class="layui-form-label">
                        <span style="color: red">*</span>
                        有效时间</label>
                    <div class="layui-input-inline" style="width: 150px">
                        <input type="text" name="start_time" id="start_time" autocomplete="off" class="layui-input"
                               value="{{$price['start_time_string'] ?? ''}}">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline" style="width: 150px">
                        <input type="text" name="end_time" id="end_time" autocomplete="off" class="layui-input"
                               value="{{$price['end_time_string'] ?? ''}}">
                    </div>
                </div>
            </div>
            <input type="hidden" name="status" value="{{$price['status'] ?? 0}}">

        </div>
        <div class="layui-form-item" style="max-height: 50px">
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    参与币种</label>
                <div class="layui-input-block" style="padding-top: -10px">
                    <input type="checkbox" id="currency_rmb" name="currency_rmb" lay-filter="currency_rmb"
                           value="{{$price['currency_rmb'] ?? 1}}" title="人民币"
                           @if(!empty($price['currency_rmb']) || empty($price))  checked @endif
                    >
                    <input type="checkbox" id="currency_us" name="currency_us" lay-filter="currency_us"
                           value="{{$price['currency_us'] ?? 0}}" title="美金"
                           @if(!empty($price['currency_us'])) checked @endif
                    >
                </div>
            </div>
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    折扣系数</label>
                <div class="layui-input-inline" style="width: 230px">
                    <div class="layui-col-md6" id="currency_rmb_ratio_div"
                         style="@if((isset($price['currency_rmb'])&&$price['currency_rmb']==0))
                                 display:none @endif">
                        <div class="layui-col-md2" style="color: red;font-size: 14px;margin-top: 5px"> ¥</div>
                        <div class="layui-col-md6"><input type="text" name="ratio" autocomplete="off"
                                                          class="layui-input"
                                                          value="{{$price['ratio'] ?? ''}}"></div>
                        <div class="layui-col-md3" style="font-size: 14px;margin-top: 5px;margin-left: 3px">
                            %
                        </div>
                    </div>
                    <div class="layui-col-md6" id="currency_us_ratio_div"
                         style="@if((isset($price['currency_us'])&&$price['currency_us']==0)||!isset($price))
                                 display:none @endif">
                        <div class="layui-col-md2" style="color: red;font-size: 14px;margin-top: 5px"> $</div>
                        <div class="layui-col-md6"><input type="text" name="ratio_us" autocomplete="off"
                                                          class="layui-input"
                                                          value="{{$price['ratio_us'] ?? ''}}"></div>
                        <div class="layui-col-md3" style="font-size: 14px;margin-top: 5px;margin-left: 3px">
                            %
                        </div>
                    </div>
                </div>
                <div class="layui-form-mid layui-word-aux">纯数字,如9.5折填95,支持2位小数</div>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    展示名称</label>
                <div class="layui-input-inline">
                    <div class="layui-input-inline">
                        <input type="text" name="show_name" autocomplete="off" class="layui-input"
                               value="{{$price['show_name'] ?? ''}}">
                    </div>
                </div>
                <div class="layui-form-mid layui-word-aux">（最多输入4个字符数，即2个汉字）</div>
            </div>
            <div class="layui-col-md6">

                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    营销标签</label>
                <div class="layui-input-inline">
                    <div class="layui-input-inline">
                        <input type="text" name="sign" autocomplete="off" class="layui-input"
                               value="{{$price['sign'] ?? ''}}">
                    </div>
                </div>
                <div class="layui-form-mid layui-word-aux">（最多输入8个字符数，即4个汉字）</div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    标签说明</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <div class="layui-input-inline">
                        <input type="text" style="width: 300px;" name="sign_text" autocomplete="off" class="layui-input"
                               value="{{$price['sign_text'] ?? ''}}">
                    </div>
                </div>
                <div class="layui-form-mid layui-word-aux">（最多输入32个字符数，即16个汉字）</div>
            </div>
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    标签说明</label>
                <div class="layui-input-inline" style="width: 300px;">
                    <div class="layui-input-inline">
                        <input type="text" style="width: 300px;" name="sign_text" autocomplete="off" class="layui-input"
                               value="{{$price['sign_text'] ?? ''}}">
                    </div>
                </div>
                <div class="layui-form-mid layui-word-aux">（最多输入32个字符数，即16个汉字）</div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    用户范围</label>
                <div class="layui-input-block">
                    <input type="radio" name="user_scope" value="1" title="全部用户"
                           @if(empty($price['user_scope']) || $price['user_scope']==1) checked @endif>
                    <input type="radio" name="user_scope" value="2" title="网站登陆用户"
                           @if(!empty($price['user_scope']) && $price['user_scope']==2) checked @endif>
                </div>
            </div>
        </div>
        <hr/>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    商品类型</label>
                <input type="hidden" id="goods_scope" value="{{$price['goods_scope'] ?? 0}}">
                <div class="layui-input-block">
                    <input type="radio" id="zy_type" @if(!empty($price)) disabled @endif name="goods_scope"
                           lay-filter="goods_scope" value="1" title="自营"
                           @if(isset($price['goods_scope']) && $price['goods_scope']==1) checked @endif>
                    <input type="radio" id="ly_type" @if(!empty($price)) disabled @endif name="goods_scope"
                           lay-filter="goods_scope" value="2" title="专营"
                           @if(isset($price['goods_scope']) && $price['goods_scope']==2) checked @endif>
                </div>
            </div>
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    使用优惠券</label>
                <div class="layui-input-block">
                    <input type="radio" name="allow_coupon" value="1" title="允许"
                           @if(!empty($price['allow_coupon']) && $price['allow_coupon']==1) checked @endif>
                    <input type="radio" name="allow_coupon" value="2" title="禁止"
                           @if(empty($price['allow_coupon']) || $price['allow_coupon']==2) checked @endif>
                </div>
            </div>
        </div>
        <div style="margin-left: 10px;margin-bottom: 30px">
            <h2>参与商品设置</h2>
            <blockquote class="layui-elem-quote" style="margin-top: 20px">
                <label style="font-size: 14px;">下面的条件,如果有选择的话都满足的商品才会生效</label>
            </blockquote>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    使用方式</label>
                <input type="hidden" id="use_type" value="{{$price['use_type'] ?? 0}}">
                <div class="layui-input-block">
                    <input type="radio" id="zy_type" name="use_type"
                           lay-filter="use_type" value="1" title="按品类"
                           @if(isset($price['use_type']) && $price['use_type']==1) checked @endif>
                    <input type="radio" id="ly_type" name="use_type"
                           lay-filter="use_type" value="2" title="按型号"
                           @if(isset($price['use_type']) && $price['use_type']==2) checked @endif>
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="supplier_ids_div">
            <label class="layui-form-label">
                <span style="color: red">*</span>
                供应商</label>
            <div class="layui-input-inline">
                <div id="supplier_selector" class="layui-input-inline" value="" style="width:700px;">
                </div>
                <input type="hidden" name="supplier_ids" value="{{$price['supplier_ids'] ?? ''}}" id="supplier_ids">
            </div>
        </div>
        <div class="layui-form-item" id="supplier_ziying_div">
            <label class="layui-form-label">
                <span style="color: red">*</span>
                供应商</label>
            <div class="layui-input-inline">
                <input type="radio" title="猎芯自营" checked/>
            </div>
        </div>
        <div class="layui-form-item" id="class_select_div">

            <label class="layui-form-label">
                选择分类</label>
            <div class="layui-input-inline">

                <div id="class_selector" class="layui-input-inline" value="" style="width: 700px;">
                </div>
                <input type="hidden" name="class_ids" value="{{$price['class_ids'] ?? ''}}" id="class_ids">
            </div>
        </div>
        <input type="hidden" id="canal_init_value"
               value="{{!empty($canal_init_value) ? json_encode($canal_init_value): ''}}">

        <div class="layui-form-item" id="canal_select_div" style="
        @if(!empty($price) && strpos($price['supplier_ids'],strval(17)) === false) display:none; @endif
        @if(empty($price)) display:none; @endif
        ">
            <label class="layui-form-label">
                渠道标签</label>
            <div class="layui-input-inline">
                <div id="canal_selector" class="layui-input-inline" value="" style="width: 700px;">
                </div>
                <input type="hidden" name="canals" value="{{$price['canals'] ?? ''}}" id="canals">
            </div>
            <div style="margin-left: 530px" class="layui-form-mid layui-word-aux">只有供应商为专卖的时候,渠道标签才会起作用,因为其它供应商没有渠道标签
            </div>
        </div>
        <div id="brand_select_div" style="
         @if(!empty($price)&&$price['use_type']==2) display:none; @endif
        ">
            <div class="layui-form-item">
                @inject('standardBrandNameListPresenter','App\Presenters\StandardBrandNameListPresenter')
                {!! $standardBrandNameListPresenter->render('standard_brand_ids','参与的品牌',$price['standard_brand_ids'] ?? '',$price['standard_brand_name_list'] ?? '') !!}
            </div>
            <div class="layui-form-item">
                @inject('standardBrandNameListPresenter','App\Presenters\StandardBrandNameListPresenter')
                {!! $standardBrandNameListPresenter->render('exclude_standard_brand_ids','不参与的品牌',$price['exclude_standard_brand_ids'] ?? '',$price['exclude_standard_brand_name_list'] ?? '') !!}
            </div>
        </div>
        <div id="goods_select_div" style="
        @if(!empty($price)&&$price['use_type']==1) display:none; @endif
        ">
            <div class="layui-form-item">
                <label class="layui-form-label">
                    参与商品型号</label>
                <div class="layui-input-block">
                    <div class="layui-col-md1" style="margin-top: 5px">
                        <a target="_blank" style="color: dodgerblue"
                           href="/template/活动价参与商品模板.csv">下载模板</a>
                    </div>
                    <div class="layui-col-md2" style="margin-left: -60px">
                        <button type="button" class="layui-btn layui-btn-sm" id="uploadSku">点击上传</button>
                        <input type="hidden" value="{{$price['sku_file_url'] ?? ''}}"
                               id="sku_file_url" name="sku_file_url">
                        <a id="sku_file_url_href"
                           @if(empty($price['sku_file_url']))
                               style="display: none"
                           @endif
                           target="_blank" href="{{$price['sku_file_url'] ?? ''}}"><img
                                style="width: 35px;height: 35px" src="/assets/images/u1080.svg"></a>
                    </div>
                </div>
            </div>
        </div>
        {{--        <div class="layui-form-item">--}}
        {{--            <label class="layui-form-label">--}}
        {{--                不参与商品ID</label>--}}
        {{--            <div class="layui-input-block">--}}
        {{--                <div class="layui-col-md1" style="margin-top: 5px">--}}
        {{--                    <a target="_blank" style="color: dodgerblue"--}}
        {{--                       href="/template/活动价不参与商品ID模板.csv">下载模板</a>--}}
        {{--                </div>--}}
        {{--                <div class="layui-col-md2" style="margin-left: -60px">--}}
        {{--                    <button type="button" class="layui-btn layui-btn-sm" id="uploadExcludeSkuIds">点击上传</button>--}}
        {{--                    <input type="hidden" value="{{$price['exclude_sku_ids'] ?? ''}}" id="exclude_sku_ids"--}}
        {{--                           name="exclude_sku_ids">--}}
        {{--                    <input type="hidden" value="{{$price['exclude_sku_ids_file_url'] ?? ''}}"--}}
        {{--                           id="exclude_sku_ids_file_url" name="exclude_sku_ids_file_url">--}}
        {{--                    <a id="exclude_sku_ids_file_url_href"--}}
        {{--                       @if(empty($price['exclude_sku_ids_file_url']))--}}
        {{--                           style="display: none"--}}
        {{--                       @endif--}}
        {{--                       target="_blank" href="{{$price['exclude_sku_ids_file_url'] ?? ''}}"><img--}}
        {{--                            style="width: 35px;height: 35px" src="/assets/images/u1080.svg"></a>--}}
        {{--                </div>--}}
        {{--            </div>--}}
        {{--        </div>--}}
        @if(!empty($price))
            @if($price['end_time']>time())
                <div class="layui-form-item" style="margin-left: 35%;padding-bottom: 10px">
                    <div class="layui-input-block">
                        <button type="button" class="layui-btn" lay-submit lay-filter="saveForm">立即提交</button>
                    </div>
                </div>
            @endif
        @else
            <div class="layui-form-item" style="margin-left: 35%;padding-bottom: 10px">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" lay-submit lay-filter="saveForm">立即提交</button>
                    <button type="button" class="layui-btn layui-btn-primary" lay-submit lay-filter="closeForm">取消
                    </button>
                </div>
            </div>
        @endif
        <br>
    </form>
</section>
<script type="text/javascript" src="/assets/js/priceprice/savePriceprice.js?v={{time()}}"></script>

