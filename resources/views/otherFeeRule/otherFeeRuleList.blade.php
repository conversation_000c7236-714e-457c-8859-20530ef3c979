@include('css')
@include('js')
<section class="section-page">
    <div class="layui-form">
        <div class="layui-form-item">
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('supplier_id','渠道 : ',0,$supplierList,['width'=>'200px','radio'=>true]) !!}
            </div>
              <div class="layui-inline">
                @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                {!! $multiSelectorPresenter->render('supplier_code','供应商 : ',0,$supplierListForXmSelect,['width'=>'200px','radio'=>true]) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('status','状态 : ',0,[1=>'启用',-1=>'禁用'],['width'=>'200px','radio'=>true]) !!}
            </div>
            <div class="layui-inline">
                @inject('timeInterval','App\Presenters\TimeIntervalPresenter')
                {!! $timeInterval->render('create_time','创建时间') !!}
            </div>
            <div class="layui-inline" style="padding-left: 60px">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                </button>
                <button class="layui-btn layui-btn-sm layui-btn-primary" lay-filter="reset" lay-submit lay-event="reset">重置</button>
            </div>
        </div>
    </div>

    <script type="text/html" id="toolbar">
        <div class="layui-btn-container">
            <button class="layui-btn layui-btn-sm" lay-event="add">新增</button>
        </div>
    </script>

    <table class="layui-table" id="list" lay-filter="list"></table>
</section>
<script type="text/html" id="edit">
    <button class="layui-btn layui-btn-xs" lay-event="edit"><strong>编辑</strong></button>
    @{{# if(d.status == 1){ }}
        <button class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable"><strong>禁用</strong></button>
        @{{# }else if(d.status == -1){ }}
            <button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="enable"><strong>启用</strong></button>
            @{{# } }}
                <button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="log"><strong>日志</strong></button>
</script>

<script type="text/html" id="status">
    @{{# if(d.status == 1){ }}
        <span class="layui-badge layui-bg-green">启用</span>
        @{{# }else if(d.status == -1){ }}
            <span class="layui-badge layui-btn-danger">禁用</span>
            @{{# } }}
</script>
<script type="text/javascript" src="/assets/js/otherFeeRule/otherFeeRuleList.js?v={{time()}}"></script>
