@include('css')
<section class="section-page">
    <h2 style="margin-left: 10px;margin-bottom: 20px">操作日志</h2>
    <input type="hidden" id="id" value="{{ $id }}">
    <table class="layui-table" id="log-list" lay-filter="log-list"></table>
</section>
@include('js')
<script>
    layui.use(['table', 'form'], function () {
        const table = layui.table;
        const id = $('#id').val();

        table.render({
            elem: '#log-list',
            url: '/api/otherFeeRule/getOperationLogs',
            method: 'post',
            where: {id: id},
            size: 'sm',
            cellMinWidth: 80,
            cols: [[
                {field: 'operation_time', title: '操作时间', align: 'center', width: 180},
                {field: 'operator_name', title: '操作人', align: 'center', width: 120},
                {field: 'content', title: '内容', align: 'center',templet: function(d){
                    return d.content;
                }},
            ]],
            page: false
        });
    });
</script>
