@include('css')
@include('js')
<style>
    .layui-form-label {
        width: 100px;
    }

    .layui-input-block {
        margin-left: 130px;
    }

    .fee-table th,
    .fee-table td {
        text-align: center;
    }

    .fee-table input {
        text-align: center;
    }

</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div style="margin-left: 10px;margin-bottom: 30px">
            <h2>
                @if(empty($rule))
                新增
                @else
                修改
                @endif
                其他费用规则配置
            </h2>
        </div>
        <input type="hidden" name="id" id="id" value="{{ $rule->id ?? '' }}">
        <div class="layui-form-item">
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('supplier_id','渠道 : ',!empty($rule) ? $rule->supplier_id : 0,$supplierList,['width'=>'100px','radio'=>true,'required'=>true , 'disabled'=> !empty($rule)? true:false ]) !!}
            </div>
        </div>
        <div class="layui-form-item" id="supplier_code_selector" style="
        @if(empty($rule))
        display: none
        @endif
        @if(!empty($rule) && !$rule->supplier_code)
        display: none
        @endif
        ">
            <div class="layui-inline">
                @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                {!! $multiSelectorPresenter->render('supplier_code','供应商 : ',!empty($rule) ? $rule->supplier_code : 0,$supplierListForXmSelect,['width'=>'300px','radio'=>true,'required'=>true,'disabled'=> !empty($rule)? true:false]) !!}
            </div>
        </div>

        <!-- 打卷费区域 -->
        <fieldset class="layui-elem-field layui-field-title">
            <legend>打卷费</legend>
        </fieldset>

        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color: red">*</span> 是否收费</label>
            <div class="layui-input-block">
                <input type="radio" name="rolling_rule[is_charge]" value="1" title="是" {{ (!empty($rule) && !empty($rule->rolling_rule) && !empty($rule->rolling_rule['is_charge'])) ? 'checked' : '' }} lay-filter="rolling_is_charge">
                <input type="radio" name="rolling_rule[is_charge]" value="0" title="否" {{ (empty($rule) || empty($rule->rolling_rule) || empty($rule->rolling_rule['is_charge'])) ? 'checked' : '' }} lay-filter="rolling_is_charge">
            </div>
        </div>

        <div id="rolling_config_section" style="@if(!empty($rule) && !empty($rule->rolling_rule) && !empty($rule->rolling_rule['is_charge']))
            @else
            display: none
        @endif">
            <div class="layui-form-item">
                <label class="layui-form-label" style='width: 120px'><span style="color: red">*</span> 费用配置方式</label>
                <div class="layui-input-block">
                    <input type="radio" name="rolling_rule[config_type]" value="1" title="应用全渠道商品" {{ (empty($rule) || empty($rule->rolling_rule) || empty($rule->rolling_rule['config_type']) || $rule->rolling_rule['config_type'] == 1) ? 'checked' : '' }} lay-filter="rolling_config_type">
                    <input type="radio" name="rolling_rule[config_type]" value="2" title="按包装方式匹配" {{ (!empty($rule) && !empty($rule->rolling_rule) && !empty($rule->rolling_rule['config_type']) && $rule->rolling_rule['config_type'] == 2) ? 'checked' : '' }} lay-filter="rolling_config_type">
                </div>
            </div>
            <div class="layui-form-item" style="width:700px">
                <table class="layui-table fee-table" id="rolling-package-table">
                    <thead>
                        <tr>
                            <th class="rolling-package-keyword-column" style="{{ (empty($rule) || empty($rule->rolling_rule) || empty($rule->rolling_rule['config_type']) || $rule->rolling_rule['config_type'] == 1) ? 'display:none' : '' }}">
                                <span style="color: red">*</span> 包装方式关键词
                            </th>
                            <th>人民币费用/单个型号</th>
                            <th>美金费用/单个型号</th>
                        </tr>
                    </thead>
                    <tbody id="rolling-package-body">
                        <tr class="rolling-package-item">
                            <td class="rolling-package-keyword-column" style="{{ (empty($rule) || empty($rule->rolling_rule) || empty($rule->rolling_rule['config_type']) || $rule->rolling_rule['config_type'] == 1) ? 'display:none' : '' }}">
                                <input type="text" name="rolling_rule[package_keyword]" value="{{ ($rule->rolling_rule['package_keyword'] ?? '') }}" class="layui-input" placeholder="多个逗号隔开">
                            </td>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <span style="font-weight: bold;margin-right: 5px">￥</span>
                                    <input type="number" name="rolling_rule[cny_fee]" value="{{ ($rule->rolling_rule['cny_fee'] ?? '') }}" class="layui-input" step="0.01" min="0" placeholder="请输入人民币费用">
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <span style="font-weight: bold;margin-right: 5px">$</span>
                                    <input type="number" name="rolling_rule[usd_fee]" value="{{ ($rule->rolling_rule['usd_fee'] ?? '') }}" class="layui-input" step="0.01" min="0" placeholder="请输入美金费用">
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 操作费区域 -->
        <fieldset class="layui-elem-field layui-field-title">
            <legend>操作费</legend>
        </fieldset>

        <div class="layui-form-item">
            <label class="layui-form-label"><span style="color: red">*</span> 是否收费</label>
            <div class="layui-input-block">
                <input type="radio" name="operation_rule[is_charge]" value="1" title="是" {{ (!empty($rule) && !empty($rule->operation_rule) && !empty($rule->operation_rule['is_charge'])) ? 'checked' : '' }} lay-filter="operation_is_charge">
                <input type="radio" name="operation_rule[is_charge]" value="0" title="否" {{ (empty($rule) || empty($rule->operation_rule) || empty($rule->operation_rule['is_charge'])) ? 'checked' : '' }} lay-filter="operation_is_charge">
            </div>
        </div>

        <div id="operation_config_section" style="@if(!empty($rule) && !empty($rule->operation_rule) && !empty($rule->operation_rule['is_charge']))
            @else
            display: none
        @endif">
            <div class="layui-form-item">
                <label class="layui-form-label" style='width: 120px'><span style="color: red">*</span> 费用配置方式</label>
                <div class="layui-input-block">
                    <input type="radio" name="operation_rule[config_type]" value="1" title="应用全渠道商品" {{ (empty($rule) || empty($rule->operation_rule) || empty($rule->operation_rule['config_type']) || $rule->operation_rule['config_type'] == 1) ? 'checked' : '' }} lay-filter="operation_config_type">
                    <input type="radio" name="operation_rule[config_type]" value="2" title="按包装方式匹配" {{ (!empty($rule) && !empty($rule->operation_rule) && !empty($rule->operation_rule['config_type']) && $rule->operation_rule['config_type'] == 2) ? 'checked' : '' }} lay-filter="operation_config_type">
                </div>
            </div>

            <div class="layui-form-item" style="width:700px">
                <table class="layui-table fee-table" id="operation-package-table">
                    <thead>
                        <tr>
                            <th class="operation-package-keyword-column" style="{{ (empty($rule) || empty($rule->operation_rule) || empty($rule->operation_rule['config_type']) || $rule->operation_rule['config_type'] == 1) ? 'display:none' : '' }}">
                                <span style="color: red">*</span> 包装方式关键词
                            </th>
                            <th>人民币费用/单个型号</th>
                            <th>美金费用/单个型号</th>
                        </tr>
                    </thead>
                    <tbody id="operation-package-body">
                        <tr class="operation-package-item">
                            <td class="operation-package-keyword-column" style="{{ (empty($rule) || empty($rule->operation_rule) || empty($rule->operation_rule['config_type']) || $rule->operation_rule['config_type'] == 1) ? 'display:none' : '' }}">
                                <input type="text" name="operation_rule[package_keyword]" value="{{ ($rule->operation_rule['package_keyword'] ?? '') }}" class="layui-input" placeholder="多个逗号隔开">
                            </td>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <span style="font-weight: bold;margin-right: 5px">￥</span>
                                    <input type="number" name="operation_rule[cny_fee]" value="{{ ($rule->operation_rule['cny_fee'] ?? '') }}" class="layui-input" step="0.01" min="0" placeholder="请输入人民币费用">
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; align-items: center;">
                                    <span style="font-weight: bold;margin-right: 5px">$</span>
                                    <input type="number" name="operation_rule[usd_fee]" value="{{ ($rule->operation_rule['usd_fee'] ?? '') }}" class="layui-input" step="0.01" min="0" placeholder="请输入美金费用">
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="layui-form-item" style="text-align: center;padding-top: 20px">
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-sm saveForm" lay-submit lay-filter="saveForm">确定</button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm cancel">取消</button>
            </div>
        </div>
        <br>
    </form>
</section>
<script type="text/javascript" src="/assets/js/otherFeeRule/saveOtherFeeRule.js?v={{time()}}"></script>
