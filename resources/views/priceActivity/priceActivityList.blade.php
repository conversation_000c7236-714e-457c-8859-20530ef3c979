@include('css')
<section class="section-page">

<input type="hidden" id="activity_name" value="{{request()->input('activity_name')}}">
    <div class="layui-form layui-box">
        <div class="layui-form-item mb0">
            <div class="layui-inline">
                <label class="layui-form-label">活动ID</label>
                <div class="layui-input-inline">
                    <input type="text" name="id" placeholder="多个用英文逗号隔开" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">活动名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="activity_name" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input" value="{{request()->input('activity_name')}}">
                </div>
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('user_scope','用户范围',request()->get('user_scope'),[1=>'全站用户',2=>'登陆用户']) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('goods_scope','商品类型',request()->get('goods_scope'),[1=>'自营',2=>'专营']) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('currency','参与币种',request()->get('currency'),[1=>'人民币',2=>'美金',3=>'人民币和美金']) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('status','状态',request()->get('status'),[1=>'已上线',0=>'未上线',-1=>'已删除',-2=>'已过期']) !!}
            </div>
            <div class="layui-inline">
                @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                {!! $timePresenter->render('activity_time','活动时间') !!}
            </div>

            <div class="layui-inline" style="padding-left: 60px">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                </button>
                <a href="/web/priceActivity/priceActivityList" class="layui-btn layui-btn-primary layui-btn-sm reload"
                   data-type="reload">重置</a>
            </div>
        </div>
    </div>
    <!--工具类-->
    <script type="text/html" id="toolbar">
        <button class="layui-btn layui-btn-sm" type="button" style="margin-left: 20px" id="addActivity"
                lay-event="addActivity"><strong>新增</strong></button>
    </script>
    <table class="layui-table" id="list" lay-filter="list"></table>
</section>
<script type="text/html" id="edit">
    @{{# if(d.expired==0 && d.status!=-1){ }}
    <button type="button"
       class="layui-btn layui-btn-xs"
            lay-event="edit"><strong>编辑</strong></button>
    <a href="/api/priceActivity/exportPriceActivityStatistics?activity_id=@{{ d.id }}" target="_blank" class="layui-btn layui-btn-xs layui-btn-primary" lay-event="statistics"><strong>活动统计</strong>
    </a>
    @{{# }else{ }}
{{--    <a href="/priceActivity/savePriceActivity?id=@{{ d.id }}" target="_blank"--}}
{{--       class="layui-btn layui-btn-xs layui-btn-primary"--}}
{{--       lay-event="edit"><strong>查看活动</strong></a>--}}
    <a href="/api/priceActivity/exportPriceActivityStatistics?activity_id=@{{ d.id }}" target="_blank" class="layui-btn layui-btn-xs layui-btn-primary" lay-event="statistics"><strong>活动统计</strong>
    @{{# } }}

    @{{# if(d.status==0 && d.expired!=1){ }}
    <button class="layui-btn layui-btn-xs publish" value="@{{ d.id }}" lay-event="publish"><strong>发布上线</strong>
    </button>
    <button class="layui-btn layui-btn-xs layui-btn-danger delete" value="@{{ d.id }}"><strong>删除</strong></button>
    @{{# }else if((d.status==0 || d.status==1) && d.expired != 1){ }}
    <button class="layui-btn layui-btn-xs layui-btn-danger delete" value="@{{ d.id }}"><strong>删除</strong></button>
    @{{# } }}
</script>
<script type="text/html" id="add_time">
    @{{ date('Y-m-d H:i',d.add_time) }}
</script>
<script type="text/html" id="is_new_reg">
    @{{# if(d.is_new_reg==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-primary"><strong>是</strong></a>
    @{{# }else if(d.is_new_reg==2){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-success"><strong>否</strong></a>
    @{{# } }}
</script>

<script type="text/html" id="status">
    @{{#  if(d.status==-1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>已删除</strong></a>
    @{{# }else if(d.expired==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-primary"><strong>已过期</strong></a>
    @{{# }else if(d.status==0){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>未上线</strong></a>
    @{{# }else if(d.status==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-success"><strong>已上线</strong></a>
    @{{# } }}
</script>
@include('js')
<script type="text/javascript" src="/assets/js/priceActivity/priceActivityList.js?v={{time()}}"></script>