@include('css')
<style>
    .priceboxs {
    }

    .priceboxs .row span {
        width: 90px;
        text-align: left;
        padding-left: 10px;
    }

    .priceboxs.h360 {
        height: 60px;
        overflow: hidden;
    }

    .jgsq {
        cursor: pointer;
        color: #1E9FFF;
        float: right;
    }
</style>
<section class="section-page">

    <input type="hidden" id="activity_name" value="{{request()->input('activity_name')}}">
    <form class="layui-form" action="" lay-filter="filter_form">
        <div class="layui-form layui-box">
            <div class="layui-form-item mb0">
                <div class="layui-inline">
                    <label class="layui-form-label">活动编号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="activity_no" placeholder="请输入活动编号" autocomplete="off"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">活动名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="activity_name" placeholder="模糊匹配" autocomplete="off"
                               class="layui-input" value="{{request()->input('activity_name')}}">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">客户账号</label>
                    <div class="layui-input-inline">
                        <input type="text" name="mobile" placeholder="模糊匹配" autocomplete="off"
                               class="layui-input" value="{{request()->input('mobile')}}">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">公司名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="company_name" placeholder="模糊匹配" autocomplete="off"
                               class="layui-input" value="{{request()->input('company_name')}}">
                    </div>
                </div>
                <div class="layui-inline">
                    @inject('statusPresenter','App\Presenters\StatusPresenter')
                    {!! $statusPresenter->render('is_new_reg','是否新注册',request()->get('is_new_reg'),[1=>'是',-1=>'否']) !!}
                </div>
                <div class="layui-inline">
                    @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                    {!! $timePresenter->render('create_time','填写时间') !!}
                </div>
                {{--            <div class="layui-inline">--}}
                {{--                @inject('timePresenter','App\Presenters\TimeIntervalPresenter')--}}
                {{--                {!! $timePresenter->render('activity_time','活动时间') !!}--}}
                {{--            </div>--}}
                <div class="layui-inline" style="padding-left: 60px">
                    <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                    </button>
                    <a href="/web/customFormData/customFormDataList"
                       class="layui-btn layui-btn-primary layui-btn-sm reload"
                       data-type="reload">重置</a>
                </div>
            </div>
        </div>
    </form>
    <!--工具类-->
    <script type="text/html" id="toolbar">
    </script>
    <br>
    <button class="layui-btn layui-btn-sm" id="export" type="button">导出</button>
    <table class="layui-table" id="list" lay-filter="list"></table>
</section>
<script type="text/html" id="edit">
    <button type="button"
            class="layui-btn layui-btn-xs"
            lay-event="view"><strong>查看</strong></button>
</script>
<script type="text/html" id="add_time">
    @{{ date('Y-m-d H:i',d.add_time) }}
</script>
<script type="text/html" id="is_new_reg">
    @{{# if(d.is_new_reg==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn"><strong>是</strong></a>
    @{{# }else if(d.is_new_reg==-1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-danger"><strong>否</strong></a>
    @{{# } }}
</script>

@include('js')
<script type="text/javascript" src="/assets/js/customFormData/customFormDataList.js?v={{time()}}"></script>
