@include('css')
<style>
    .layui-form-label {
        width: 114px;
    }

    .layui-table, .layui-table-view {
        margin-top: 0px;
    }

    .layui-textarea {
        min-height: 60px;
    }

    .layui-layer-content {
        height: auto !important;
    }
</style>
<div class="lay-box">
    asd
</div>

@verbatim
    <!-- 工具栏 -->
    <script type="text/html" id="toolbar">
        <div class="layui-btn-container">
            <a class="layui-btn layui-btn-sm btn-color" ew-href="/web/purchasePlan/addPurchasePlan" ew-title="采购计划新增">新增</a>
            <a class="layui-btn layui-btn-sm btn-color" lay-event="invalid">作废</a>
            <a class="layui-btn layui-btn-sm btn-color" lay-event="delete">删除</a>
        </div>
    </script>
    <!-- 状态 -->
    <script type="text/html" id="statusTpl">
        {{# if (d.status== -3) { }}
        <span style="color: #F70808;" title="{{d.remark}}">{{d.status_name}}</span>
        {{# } else { }}
        {{d.status_name}}
        {{# } }}
    </script>
    <!-- 时间选择 -->
    <script type="text/html" id="timeChangeHtml">
        {{# if (d== 1) { }}
        <input type="text" name="create_time" value="" autocomplete="off" placeholder="选择时间" class="layui-input" id="time_start" readonly>
        {{# } else { }}
        <input type="text" name="update_time" value="" autocomplete="off" placeholder="最近修改时间" class="layui-input" id="time_start" readonly>
        {{# } }}
    </script>
@endverbatim

@include('js')
<script type="text/javascript" src="/assets/js/activity/list.js?v={{time()}}"></script>
