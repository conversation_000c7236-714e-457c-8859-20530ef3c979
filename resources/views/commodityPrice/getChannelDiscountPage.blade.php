@include('css')
<style>
    .layui-form-label {
        width: 150px;
    }
    .rtext{
        color:#000;
        height:30px;
        line-height: 30px;
    }
</style>

<section class="section-page">
    <form class="layui-form" onsubmit="return false" lay-filter="getlist">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>渠道/供应商：
                </label>
                <div class="layui-input-inline">
                    <div class="rtext">
                        {{$info["supplier_name"]}}
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>是否设为默认折扣：
                </label>
                <div class="layui-input-inline">
                    <div class="rtext">
                        @if($info["supplier_name"] == 1)
                        是
                        @else
                        否
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>优先级：
                </label>
                <div class="layui-input-inline">
                    <div class="rtext">{{$info["order"]}}</div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>人民币渠道折扣：
                </label>
                <div class="layui-input-inline">
                    <div class="rtext">
                        ¥{{$info["ration"]*100}}%
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>美金渠道折扣：
                </label>
                <div class="layui-input-inline">
                    <div class="rtext">
                        ${{$info["ration"]*100}}%
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label"> 参与的型号：</label>
                <div class="layui-input-inline">
                    <div class="rtext">
                        @if(!empty($info["step_price_extend"]))
                        <a id="exclude_sku_ids_file_url_href" style="" target="_blank" href="{{$info["step_price_extend"]["file_url"]}}"><img style="width: 35px;height: 35px" src="/assets/images/u1080.svg"></a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label"> 参与的品牌：</label>
                <div class="layui-input-inline">
                    <div class="rtext">
                        @if(!empty($info["step_price_extend"]))
                        {{$info["step_price_extend"]["brand"]}}
                        @endif
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label"> 备注：</label>
                <div class="layui-input-inline">
                    <div class="rtext">{{$info["remark"]}}</div>
                </div>
            </div>
        </div>
        <div class="layui-form-item row rowCenter">
            <a class="layui-btn layui-btn-sm btn-color" ew-href="/web/price/upateChannelDiscount?channel_disct_id={{$info["channel_disct_id"]}}" ew-title="编辑{{$info["channel_disct_sn"]}}">编辑</a>
        </div>
    </form>

</section>

@include('js')
<!-- <script type="text/javascript" src="/assets/js/commodityPrice/getChannelDiscountPage.js?v={{time()}}"></script> -->