@include('css')
@include('js')
<style>
.layui-form-label{width:110px;}
.layui-table-cell{word-break:break-all}
.priceboxs{}
.priceboxs .row span{width:70px;}
.priceboxs.h360{height:60px;overflow: hidden;}
.jgsq{cursor: pointer;color:#1E9FFF;float: right;}
</style>


<section class="section-page">
<input type="hidden" id="sppe_sn" value="{{request()->input('sppe_sn')}}">
    <form class="layui-form" onsubmit="return false" lay-filter="getlist">
        <div class="layui-form-item mb0">
            <div class="layui-inline">
                <label class="layui-form-label">售价组编号</label>
                <div class="layui-input-inline">
                    <input type="text" name="sppe_sn" value="{{request()->input('sppe_sn')}}" placeholder="精准匹配" autocomplete="off" class="layui-input">
                </div>
            </div>
             <div class="layui-inline">
                  @inject('statusPresenter','App\Presenters\StatusPresenter')
                  {!! $statusPresenter->render('org_id','组织','',config('field.OrgList')) !!}
             </div>
            <div class="layui-inline" style="padding-top: 20px">
                @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                {!! $multiSelectorPresenter->render('supplier_id','供应商名称/编码 : ',0,$supplierList,['width'=>'200px','radio'=>true]) !!}
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status">
                        <option value="">全部</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">是否默认</label>
                <div class="layui-input-inline">
                    <select name="is_default">
                        <option value="">全部</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline" style="padding-left: 60px">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
            </div>
        </div>
    </form>
    <table id="list" lay-filter="list"></table>
</section>

@verbatim
<!--工具类-->
<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <a class="layui-btn layui-btn-sm btn-color" ew-href="/web/price/addGoodsSalePriceGroup" ew-title="新增商品售价组">新增</a>
    </div>
</script>

@endverbatim

<script type="text/javascript" src="/assets/js/commodityPrice/goodsSalePriceGroup.js?v={{time()}}"></script>
