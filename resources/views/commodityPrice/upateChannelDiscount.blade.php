@include('css')
<style>
    .layui-form-label {
        width: 140px;
    }

    .layui-col-md7 {
        width: 50%;
    }

    .layui-col-md5 {
        height: 60px;
    }

    .layui-textarea {
        height: 60px;
    }

    .tipsxx {
        background: #fff8eb;
        color: #F98119;
        padding: 10px;
        margin-bottom: 20px;
    }

    .lrsbox {
        line-height: 30px;
    }

    .lrsbox span {
        font-size: 16px;
    }

    .f-red {
        color: red;
    }

    .layui-col-md1 {
        width: 60px !important;
    }

    .layui-col-md2 {
        margin-left: 0px !important;
    }

    .layui-layer-msg {
        top: 100px !important;
    }

    .layui-col-md5 p {
        margin-top: 5px !important;
    }
    .inuqd {width: 300px!important;}
    .inuqd font{
        line-height: 30px;
    }
    .inuqd div{width:210px;margin-left: 10px;line-height: 30px;}
    .inuqd input{width:90px;}
</style>
@include('js')
<section class="section-page">
    <div class="tipsxx"><i class="layui-icon layui-icon-about"></i> 成本价 = 各渠道官价 * 渠道折扣</div>
    <form class="layui-form" onsubmit="return false" lay-filter="getlist">
        <div class="layui-form-item ">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>渠道/供应商：
                </label>
                <div class="layui-input-inline xhyuanbox">
                    <select name="supplier_value" lay-search="" lay-filter="supplier_value" class="supplier_value" disabled>
                        <option value="">请选择</option>
                        <optgroup label="代购供应商" class="zzbox">
                            @foreach($daiGouSupList as $item)
                            <option value="{{$item["supplier_id"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="1">{{$item["supplier_name"]}}</option>
                            @endforeach
                        </optgroup>
                        <optgroup label="专营供应商" class="lzbox">
                            @foreach($zhuanYingSupList as $item)
                            <option value="{{$item["supplier_code"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="2">{{$item["supplier_code"]}}-{{$item["supplier_name"]}}</option>
                            @endforeach
                        </optgroup>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>是否默认：
                </label>
                <div class="layui-input-inline">
                    <select name="is_default" class="is_default" lay-filter="is_default">
                        <option value="1">是</option>
                        <option value="0" selected>否</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline order_bysort" >
                <label class="layui-form-label"><font style="color:red;">*</font>优先级：</label>
                <div class="layui-input-inline row " style="width:600px">
                    <select name="order" >
                        <option value="">请选择</option>
                    </select>
                    <span style="height:30px;margin-left:5px;line-height:30px;">最大值255，数值越大越优先匹配，数值全部相等时，随机匹配</span>
                </div>
            </div>
        </div>
        <div class="layui-form-item ">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>人民币渠道折扣：
                </label>
                <div class="layui-input-inline row inuqd">
                    <font class="f-red" style="padding: 0px 5px 0px 5px;background-color: #F0F0F0">&nbsp;￥&nbsp;</font>
                    <input type="text" class="layui-input inuqdinput ration" autofocus="autofocus" onkeyup="value=value.replace(/[^\d^\.]+/g,'')"//>
                    <font class="" style="padding: 0px 5px 0px 5px;background-color: #F0F0F0">&nbsp;%&nbsp;</font>
                    <div>RMB渠道利润：<font>0%</font>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item ">
            <div class="layui-inline">
                <label class="layui-form-label">
                    <font style="color:red;">*</font>美金渠道折扣：
                </label>
                <div class="layui-input-inline row inuqd">
                    <font class="f-red" style="padding: 0px 5px 0px 5px;background-color: #F0F0F0">&nbsp;$&nbsp;</font>
                    <input type="text" class="layui-input inuqdinput ration_usd" autofocus="autofocus" onkeyup="value=value.replace(/[^\d^\.]+/g,'')"//>
                    <font class="" style="padding: 0px 5px 0px 5px;background-color: #F0F0F0">&nbsp;%&nbsp;</font>
                    <div>USD渠道利润：<font>0%</font>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item cybbox">
            <label class="layui-form-label">
                参与的型号：</label>
            <div class="layui-input-block">
                <div class="layui-col-md1" style="margin-top: 5px">
                    <a target="_blank" style="color: dodgerblue" href="/template/goods_name_upload.csv">下载模板</a>
                </div>
                <div class="layui-col-md2" style="margin-left: -60px">
                    <button type="button" class="layui-btn layui-btn-sm" id="uploadExcludeSkuIds">点击上传</button>
                    <input type="hidden" value="{{$activity['exclude_sku_ids'] ?? ''}}" id="exclude_sku_ids" name="exclude_sku_ids">
                    <input type="hidden" value="{{$activity['exclude_sku_ids_file_url'] ?? ''}}" id="exclude_sku_ids_file_url" name="exclude_sku_ids_file_url">
                    <a id="exclude_sku_ids_file_url_href" @if(empty($activity['exclude_sku_ids_file_url'])) style="display: none" @endif target="_blank" href="{{$activity['exclude_sku_ids_file_url'] ?? ''}}"><img style="width: 35px;height: 35px" src="/assets/images/u1080.svg"></a>
                </div>
            </div>
        </div>

        <div class="layui-form-item cybbox">

            @inject('standardBrandNameListPresenter','App\Presenters\StandardBrandNameListPresenter')
            {!! $standardBrandNameListPresenter->render('exclude_standard_brand_ids','参与的品牌：',$activity['exclude_standard_brand_ids'] ?? '',$activity['exclude_standard_brand_name_list'] ?? '') !!}
        </div>
        <div class="layui-form-item ">
            <label class="layui-form-label">备注：</label>
            <div class="layui-input-inline">
                <textarea placeholder="" class="layui-textarea remark" style="width:640px;"></textarea>
            </div>
        </div>
    </form>
    <div class="jtcons">
        <fieldset class="layui-elem-field layui-field-title" style="margin-top: 30px;">
            <legend>此渠道已存在的折扣信息</legend>
        </fieldset>
        <table id="list" lay-filter="list"></table>
    </div>
    <div class="row rowCenter">
        <button  class="layui-btn  layui-btn-sm  layui-btn-normal savebtn" >保存</button>
        <a  class="layui-btn  layui-btn-sm layui-btn-primary "  onclick="closeCurrentPageJumpOne('渠道折扣管理', '/web/price/channelDiscount', 1000)">取消</a>
     </div>
</section>

@verbatim


@endverbatim

<script type="text/javascript" src="/assets/module/jqueryCsv/jquery.csv.min.js?v={{time()}}"></script>
<script type="text/javascript" src="/assets/js/commodityPrice/upateChannelDiscount.js?v={{time()}}"></script>