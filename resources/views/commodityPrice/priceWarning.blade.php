@include('css')
<style>
    .rpxk .layui-form-label {
        width: 110px;
    }

    .layui-table-cell {
        word-break: break-all
    }

    .priceboxs {}

    .priceboxs .row span {
        width: 70px;
    }

    .priceboxs.h360 {
        height: 60px;
        overflow: hidden;
    }

    .jgsq {
        cursor: pointer;
        color: #1E9FFF;
        float: right;
    }
    .tipsxx{background: #fff8eb;color:#F98119;padding:10px;margin-bottom: 20px;}
</style>

<section class="section-page">
    <form class="layui-form rpxk" onsubmit="return false" lay-filter="getlist">
        <div class="layui-form-item mb0">

            <div class="layui-inline">
                <label class="layui-form-label">活动ID</label>
                <div class="layui-input-inline">
                    <input type="text" name="price_activity_id" placeholder="多个用引英文逗号隔开" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">活动名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="activity_name" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">售价组编号</label>
                <div class="layui-input-inline">
                    <input type="text" name="sppe_sn" placeholder="精准匹配" autocomplete="off" class="layui-input">
                </div>
            </div>
            <!-- <div class="layui-inline">
                <label class="layui-form-label">类型</label>
                <div class="layui-input-inline">
                    <select name="is_default">
                        <option value="">全部</option>
                        <option value="1">发布活动</option>
                        <option value="2">定时检测</option>
                    </select>
                </div>
            </div> -->
            <div class="layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status">
                        <option value="">全部</option>
                        <option value="1">待处理</option>
                        <option value="2">已忽略</option>
                        <option value="3">已处理</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">预警时间</label>
                <div class="layui-input-inline " >
                    <input type="text" name="warn_time" id="warn_time" placeholder="" autocomplete="off" readonly class="layui-input">
                </div>
            </div>
            <div class="layui-inline" style="padding-left: 60px">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
            </div>
        </div>
    </form>
    
    <div class="tipsxx"><i class="layui-icon layui-icon-about"></i> 以下售价组存在亏本风险
规则：
活动价 = 成本 * (1 + 销售利润）* 活动折扣
盈利率 = (活动价-成本)÷成本×100%  =  [  (1 + 销售利润) * 活动折扣 - 1 ] * 100%</div>
    <table id="list" lay-filter="list"></table>
</section>

@verbatim
<!--工具类-->
<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <a class="layui-btn layui-btn-sm btn-color" lay-event="plcl" >批量标记已处理</a>
    </div>
</script>
 <!--新增-编辑活动-->
 <script type="text/html" id="testPop">
        <form class="layui-form layer-box-padding" onsubmit="return false;" >
            <div class="layui-form-item">
                <label class="layui-form-label required">状态：</label>
                <div class="layui-input-block">
                    <select name="status" lay-verify="required" lay-reqtext="请选择状态" lay-vertype="tips">
                    <option value="" >全部</option>
                        <option value="2">忽略</option>
                        <option value="3">已处理</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required">备注：</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入备注" name="remark" class="layui-textarea bzsg" lay-verify="required" lay-reqtext="请输入备注" ></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="submit" class="layui-btn layui-btn-sm activityAddSubmit" lay-submit="" lay-filter="testPopgo">保存</button>
                    <button class="layui-btn layui-btn-sm  layui-btn-primary" onclick="layer.closeAll()">取消</button>
                </div>
            </div>
        </form>
    </script>
@endverbatim

@include('js')
<script type="text/javascript" src="/assets/js/commodityPrice/priceWarning.js?v={{time()}}"></script>