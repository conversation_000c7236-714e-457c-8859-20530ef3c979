@include('css')
<style>
.layui-form-label{width:110px;}
.layui-table-cell{word-break:break-all}
.priceboxs{}
.priceboxs .row span{width:70px;}
.priceboxs.h360{height:60px;overflow: hidden;}
.jgsq{cursor: pointer;color:#1E9FFF;float: right;}
</style>

<section class="section-page">
    <form class="layui-form" onsubmit="return false" lay-filter="getlist">
        <div class="layui-form-item mb0">
<!--            <div class="layui-inline">-->
<!--                <label class="layui-form-label">供应商名称</label>-->
<!--                <div class="layui-input-inline">-->
<!--                    <input type="text" name="supplier_keyword" placeholder="模糊匹配" autocomplete="off" class="layui-input">-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="layui-inline">-->
<!--                <label class="layui-form-label">供应商编码</label>-->
<!--                <div class="layui-input-inline">-->
<!--                    <input type="text" name="supplier_code" placeholder="精确匹配 多个用英文逗号,分隔" autocomplete="off" class="layui-input">-->
<!--                </div>-->
<!--            </div>-->
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 130px;">供应商名称/编码</label>
                <div class="layui-input-inline xhyuanbox">
                    <select name="supplier_id" lay-search xm-select="supplier_id" id="supplier_id" xm-select-show-count="1"
                            xm-select-search="" xm-select-search-type="dl">
                        <option value="">请选择</option>
                        <optgroup label="代购供应商" class="zzbox">
                            @foreach($daiGouSupList as $item)
                            <option value="{{$item["supplier_id"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="1">{{$item["supplier_name"]}}</option>
                            @endforeach
                        </optgroup>
                        <optgroup label="专营供应商" class="lzbox">
                            @foreach($zhuanYingSupList as $item)
                            <option value="{{$item["supplier_code"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="2">{{$item["supplier_code"]}}-{{$item["supplier_name"]}}</option>
                            @endforeach
                        </optgroup>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">标准品牌</label>
                <div class="layui-input-inline">
                    <div id="brandSelect"></div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status">
                        <option value="">全部</option>
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">是否默认</label>
                <div class="layui-input-inline">
                    <select name="is_default">
                        <option value="">全部</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline" style="padding-left: 60px">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                </button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
            </div>
        </div>
    </form>
    <table id="list" lay-filter="list"></table>
</section>

@verbatim
<!--工具类-->
<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <a class="layui-btn layui-btn-sm btn-color" ew-href="/web/price/addChannelDiscount" ew-title="新增渠道折扣">新增</a>
    </div>
</script>

@endverbatim

@include('js')
<script type="text/javascript" src="/assets/js/commodityPrice/channelDiscount.js?v={{time()}}"></script>