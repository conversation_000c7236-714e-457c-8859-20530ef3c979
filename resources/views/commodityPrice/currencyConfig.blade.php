@include('css')
<style>
    .tipsxx {
        background: #fff8eb;
        color: #F98119;
        padding: 10px;
        margin-bottom: 20px;
    }
    .biglabel .layui-form-label{
        width:150px;
    }
    .layui-input[disabled]{
        background: #f3f3f3;
    }
</style>
<section class="section-page">
    <form class="layui-form" onsubmit="return false" lay-filter="getlist">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">供应商</label>
                <div class="layui-input-inline xhyuanbox">
                    <select name="supplier_keyword" lay-search="">
                        <option value="">请选择供应商</option>
                        <optgroup label="代购供应商">
                            @foreach($daiGouSupList as $item)
                            <option value="{{$item["supplier_name"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="1">{{$item["supplier_name"]}}</option>
                            @endforeach
                        </optgroup>
                        <optgroup label="专营供应商">
                            @foreach($zhuanYingSupList as $item)
                            <option value="{{$item["supplier_name"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="2">{{$item["supplier_code"]}}-{{$item["supplier_name"]}}</option>
                            @endforeach
                        </optgroup>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">原币</label>
                <div class="layui-input-inline">
                    <select name="currency">
                        <option value="">全部</option>
                        <option value="1">人民币</option>
                        <option value="3">港币</option>
                        <option value="4">欧元</option>
                        <option value="2">美元</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <button lay-submit lay-filter="getList" class="layui-btn layui-btn-sm reload" data-type="reload">查询</button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
            </div>
        </div>
    </form>
    <!-- <div class="tipsxx row" style="margin-bottom: 0" >
        <i class="layui-icon layui-icon-about"></i>
        <div style="margin-left: 10px;">
            当接入数据非人民币和美金时才需在此处配置，换算公式：<br/>
            欧元转美金：（欧元 * 欧元转人民币汇率）÷ 美元转人民币汇率<br/>
            港币转美金：（港币 * 港币转人民币汇率）÷ 美元转人民币汇率
        </div>
    </div> -->
    <table id="list" lay-filter="list"></table>
</section>

@verbatim
<!--工具类-->
<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <a class="layui-btn layui-btn-sm btn-color" lay-event="addCurrencyConfig">新增</a>
    </div>
</script>
@endverbatim
<!--新增-->

<script type="text/html" id="addCurrencyConfigHtml">
    <form class="layui-form layer-box-padding biglabel" onsubmit="return false;" lay-filter="addCurrencyConfigForm">
        <input type="hidden" name="supplier_name" value="">
        <input type="hidden" name="sup_type" value="">
        <div class="layui-form-item">
            <label class="layui-form-label required">供应商：</label>
            <div class="layui-input-inline">
                <select name="supplier_value" lay-search="" lay-filter="supplierChange" lay-verify="required">
                    <option value="">请选择供应商</option>
                    <optgroup label="代购供应商">
                        @foreach($daiGouSupList as $item)
                        <option value="{{$item["supplier_id"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="1">{{$item["supplier_name"]}}</option>
                        @endforeach
                    </optgroup>
                    <optgroup label="专营供应商">
                        @foreach($zhuanYingSupList as $item)
                        <option value="{{$item["supplier_code"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="2">{{$item["supplier_code"]}}-{{$item["supplier_name"]}}</option>
                        @endforeach
                    </optgroup>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">原币种：</label>
            <div class="layui-input-inline">
                <select name="currency" lay-verify="required"  lay-filter="curencychange"  lay-reqtext="请选择原币种" lay-vertype="tips">
                    <option value="">全部</option>
                    <option value="1">人民币</option>
                    <option value="3">港币</option>
                    <option value="4">欧元</option>
                    <option value="2">美元</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item hsboxik" >
            <label class="layui-form-label required">是否含税：</label>
            <div class="layui-input-inline">
                <input type="radio" name="is_tax" value="0" title="否" >
                <input type="radio" name="is_tax" value="1" title="是">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">原币转人民币汇率：</label>
            <div class="layui-input-inline" style="width:90px;">
                <select name="customize_rate_rmb" lay-verify="required"  lay-filter="curencychange11"  >
                <option value="">全部</option>
                    <option value="1">系统默认</option>
                    <option value="2">自定义</option>
                </select>

            </div>
            <div class="layui-input-inline" style="width:90px;">
            <input type="number" class="layui-input customize_rate_rmb_val" name="customize_rate_rmb_val" step="0.01"  autocomplete="off"  style="display:none;"/>
            </div>

        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">原币转美元汇率：</label>
            <div class="layui-input-inline" style="width:90px;">
                <select name="customize_rate_usd"  lay-verify="required"  lay-filter="curencychange22"  >
                    <option value="">全部</option>
                    <option value="1">系统默认</option>
                    <option value="2">自定义</option>
                </select>
            </div>
            <div class="layui-input-inline" style="width:90px;">
            <input type="number" class="layui-input customize_rate_usd_val" name="customize_rate_usd_val" step="0.01"  autocomplete="off"   style="display:none;"/>
            </div>
        </div>
        <div class="layui-form-item us_to_cn" style="display:none;">
            <label class="layui-form-label ">美金转人民币汇率：</label>
            <div class="layui-input-inline" style="width:90px;">
                <input type="checkbox" lay-skin="switch" lay-filter="us_to_cn" value="1" name="us_to_cn" lay-text="是|否"  autocomplete="off" />
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn layui-btn-sm activityAddSubmit" lay-submit="" lay-filter="addCurrencyConfigSubmit">保存</button>
                <button class="layui-btn layui-btn-sm  layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</script>
@include('js')
<script type="text/javascript" src="/assets/js/commodityPrice/currency.js?v={{time()}}"></script>