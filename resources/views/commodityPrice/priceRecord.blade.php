@include('css')
<style>
    .layui-form-label {
        width: 108px;
    }

    .layui-table-cell {
        word-break: break-all
    }

    .priceboxs {}

    .priceboxs .row span {
        width: 70px;
    }

    .priceboxs.h360 {
        height: 60px;
        overflow: hidden;
    }

    .jgsq {
        cursor: pointer;
        color: #1E9FFF;
        float: right;
    }
</style>

<section class="section-page">
    <form class="layui-form" onsubmit="return false" lay-filter="getlist">
        <div class="layui-form-item mb0">
            <div class="layui-inline">
                <label class="layui-form-label">单据ID</label>
                <div class="layui-input-inline">
                    <input type="text" name="obj_id" placeholder="请输入单据ID" autocomplete="off" class="layui-input" />
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">创建人</label>
                <div class="layui-input-inline">
                    <div id="operator"></div>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">操作时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="create_time" placeholder="请选择操作时间" autocomplete="off" class="layui-input" id="create_time" />
                </div>
            </div>
            <div class="layui-inline">
                <button class="layui-btn layui-btn-sm searchBtn" lay-submit lay-filter="getList">查询</button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
            </div>
        </div>
    </form>
    <table id="list" lay-filter="list"></table>
</section>



@include('js')
<script type="text/javascript" src="/assets/js/commodityPrice/priceRecord.js?v={{time()}}"></script>