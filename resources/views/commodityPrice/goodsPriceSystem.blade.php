@include('css')
<style>
    .layui-table-cell {
        word-break: break-all
    }

    .priceboxs {
    }

    .priceboxs .row span {
        width: 90px;
        text-align: left;
        padding-left: 10px;
    }

    .priceboxs.h360 {
        height: 60px;
        overflow: hidden;
    }

    .jgsq {
        cursor: pointer;
        color: #1E9FFF;
        float: right;
    }
    .soul-table-child-wrapper .layui-tab-title{display: none;}
    #brandSelect{position: relative;}
    #brandSelect.dis::after{
        content:"";
        position: absolute;
        width:170px;
        height:30px;
        background: #f3f3f3;
        top:0px;
    }
    .tipsxx{background: #fff8eb;color:#F98119;padding:10px;}
    .aart .layui-form-label{width:90px;}
</style>

<section class="section-page">
    
    <form class="layui-form  aart" onsubmit="return false" lay-filter="getlist" >
        <div class="layui-form-item mb0">
            <div class="layui-inline">
                <label class="layui-form-label">SKUID</label>
                <div class="layui-input-inline">
                    <input type="text" name="skuids" placeholder="多个逗号隔开" autocomplete="off" class="layui-input skuidsp">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">型号</label>
                <div class="layui-input-inline">
                    <input type="text" name="goods_name" placeholder="模糊匹配" autocomplete="off" class="layui-input goods_namefg">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">标准品牌</label>
                <div class="layui-input-inline">
                    <div id="brandSelect"></div>
                </div>
            </div>
            <div class="layui-inline">
                    <label class="layui-form-label" style="width: 130px;">供应商名称/编码</label>
                    <div class="layui-input-inline xhyuanbox">
                        <select name="supplier_id" lay-search xm-select="supplier_id" id="supplier_id" xm-select-show-count="1"
                                xm-select-search="" xm-select-search-type="dl">
                            <option value="">请选择</option>
                            <optgroup label="代购供应商" class="zzbox">
                                @foreach($daiGouSupList as $item)
                                <option value="{{$item["supplier_id"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="1">{{$item["supplier_name"]}}</option>
                                @endforeach
                            </optgroup>
                            <optgroup label="专营供应商" class="lzbox">
                                @foreach($zhuanYingSupList as $item)
                                <option value="{{$item["supplier_code"]}}" supplier_name="{{$item["supplier_name"]}}" sup_type="2">{{$item["supplier_code"]}}-{{$item["supplier_name"]}}</option>
                                @endforeach
                            </optgroup>
                        </select>
                    </div>
                </div>
            
            <div class="layui-inline">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询</button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm resets">重置</button>
            </div>
        </div>
        <div class="tipsxx"><i class="layui-icon layui-icon-about"></i>&nbsp;&nbsp;&nbsp;美金转人民币汇率：{{$rate}}</div>
    </form>
    <table id="list" lay-filter="list"></table>
    
</section>


@include('js')
<script type="text/javascript" src="/assets/js/commodityPrice/goodsPriceSystem.js?v={{time()}}"></script>
