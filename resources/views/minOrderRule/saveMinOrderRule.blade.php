@include('css')
@include('js')
{{ Autograph() }}
<style>
    .layui-form-label {
        width: 110px;
    }

</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div style="margin-left: 10px;margin-bottom: 30px">
            <h2>
                @if(empty($rule))
                新增
                @else
                修改
                @endif
                订单规则配置
            </h2>
        </div>
        <input type="hidden" name="rule_id" id="rule_id" value="{{ $rule->rule_id ?? '' }}">
        <div class="layui-form-item">
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('supplier_id','渠道 : ',!empty($rule) ? $rule->supplier_id : 0,$supplierList,['width'=>'100px','radio'=>true,'required'=>true , 'disabled'=> !empty($rule)? true:false ]) !!}
            </div>
        </div>
        <div class="layui-form-item" id="supplier_code_selector" style="
        @if(empty($rule))
        display: none
        @endif
        @if(!empty($rule) && !$rule->supplier_code)
        display: none
        @endif
        ;">
            <div class="layui-inline">
                @inject('multiSelectorPresenter','App\Presenters\MultiSelectorPresenter')
                {!! $multiSelectorPresenter->render('supplier_code','供应商 : ',!empty($rule) ? $rule->supplier_code : 0,$supplierListForXmSelect,['width'=>'300px','radio'=>true,'required'=>true , 'disabled'=> !empty($rule)? true:false]) !!}
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <table class="layui-table">
                    <thead>
                        <tr>
                            <th>币种</th>
                            <th>订单最小金额</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>人民币</td>
                            <td>
                                <input type="number" name="cny_amount" value="{{ $rule->cny_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                            </td>
                        </tr>
                        <tr>
                            <td>美金</td>
                            <td>
                                <input type="number" name="usd_amount" value="{{ $rule->usd_amount ?? '' }}" class="layui-input" step="0.01" min="0">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <blockquote class="layui-elem-quote" style="margin-top: 30px">
            <p>订单最小金额：下单时，该供应商/渠道所有型号相加的总金额需大于等于订单最小金额</p>
        </blockquote>

        <div class="layui-form-item" style="text-align: center;padding-top: 20px">
            <div class="layui-input-block">
                <button type="button" class="layui-btn layui-btn-sm saveForm" lay-submit lay-filter="saveForm">确定</button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm cancel">取消</button>
            </div>
        </div>
        <br>
    </form>
</section>
<script type="text/javascript" src="/assets/js/minOrderRule/saveMinOrderRule.js?v={{time()}}"></script>
