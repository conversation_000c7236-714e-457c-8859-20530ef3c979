@include('css')
<section class="section-page">
    <h2 style="margin-left: 10px;margin-bottom: 20px">操作日志</h2>
    <input type="hidden" id="rule_id" value="{{ $ruleId }}">
    <table class="layui-table" id="log-list" lay-filter="log-list"></table>
</section>
@include('js')
<script>
    layui.use(['table', 'form'], function () {
        const table = layui.table;
        const ruleId = $('#rule_id').val();

        table.render({
            elem: '#log-list',
            url: '/api/minOrderRule/getOperationLogs',
            method: 'post',
            where: {rule_id: ruleId},
            size: 'sm',
            cellMinWidth: 80,
            cols: [[
                {field: 'operation_time', title: '操作时间', align: 'center', width: 180},
                {field: 'operator_name', title: '操作人', align: 'center', width: 120},
                {field: 'content', title: '内容', align: 'center'},
            ]],
            page: false
        });
    });
</script>
