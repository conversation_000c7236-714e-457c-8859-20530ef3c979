@include('css')
<section class="section-page">
    <form class="layui-form layui-box" lay-filter="user_coupon_list_form">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">用户账号</label>
                <div class="layui-input-inline">
                    <input type="text" name="user_account" placeholder="用户账号" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">抽奖名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="lottery_name" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">奖品名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="prize_name" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('prize_type','奖品类型',request()->get('prize_type'),config('field.FilterPrizeType')) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('draw_type','抽奖类型',request()->get('draw_type'),config('field.PrizeDrawType')) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('is_test','测试账号',request()->get('is_test'),[1=>'否',2=>'是']) !!}
            </div>
            <div class="layui-inline">
                @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                {!! $timePresenter->render('prize_get_time','中奖时间') !!}
            </div>
            <div class="layui-inline" style="padding-left: 60px">
                <button type="button" lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload"
                        data-type="reload">查询
                </button>
                <a href="/web/userLottery/userLotteryList" class="layui-btn layui-btn-primary layui-btn-sm reload"
                   data-type="reload">重置</a>
            </div>
        </div>
    </form>
    <script type="text/html" id="toolbar">
        @if(request()->user->org_id == 1)
            <button class="layui-btn layui-btn-sm" style="margin-left: 20px" id="add"
                    lay-event="add"><strong>新增中奖名单</strong></button>
        @endif
    </script>
    <table class="layui-table" id="list" lay-filter="list"></table>
</section>
<script type="text/html" id="edit">
    @{{#  if(d.prize_type==2 && d.is_sent==2){ }}
    @{{# }else if(d.prize_type==2 ){ }}
    <span style="color: green">已发放</span>
    @{{# }else{ }}
    <button type="button" class="layui-btn layui-btn-xs" lay-event="issue"><strong>发放</strong></button>
    @{{# } }}
</script>


<script type="text/html" id="status">
    @{{#  if(d.status==-1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>已删除</strong></a>
    @{{# }else if(d.expired==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-primary"><strong>已过期</strong></a>
    @{{# }else if(d.status==0){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>未上线</strong></a>
    @{{# }else if(d.status==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-success"><strong>已上线</strong></a>
    @{{# } }}
</script>
@include('js')
<script type="text/javascript" src="/assets/js/userLottery/userLotteryList.js?v={{time()}}"></script>
