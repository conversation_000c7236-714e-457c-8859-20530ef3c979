@include('css')
@include('js')
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div class="layui-form-item" style="margin-top: 20px">
            <label class="layui-form-label" style="width: 100px">用户信息:</label>
            <div class="layui-input-inline" style="margin-left: 10px">
                <input id="user-info" type="text" name="title" style="width: 200px" required  lay-verify="required" placeholder="手机号或邮箱" autocomplete="off" class="layui-input" value="{{ $user_info ?? '' }}">
            </div>
            <div class="layui-form-mid layui-word-aux" style="margin-left: 30px">接受用户手机号或邮箱</div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px">选择活动:</label>
            <div class="layui-input-block" style="width: 250px">
                <select id="choose-activity" name="choose-activity" lay-verify="required" lay-filter="choose-activity">
                    <option value="">请选择活动</option>
                    @foreach($activity_info as $activity)
                        <option value="{{ $activity->lottery_id }}" @if($choose_act==$activity->lottery_id) selected @endif>{{ $activity->lottery_name }}</option>
                    @endforeach
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px">选择奖品:</label>
            <div class="layui-input-block" style="width: 250px">
                <select id="choose-prize" name="choose-prize" lay-verify="required">
                    <option value="">请选择奖品</option>
                    @foreach($prize_info as $prize)
                        <option value="{{ $prize->prize_id }}">{{ $prize->prize_name }}</option>
                    @endforeach
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" type="button" id="submit-btn" lay-submit lay-filter="saveForm">提交</button>
                <button type="button" class="layui-btn layui-btn-primary cancel" lay-filter="cancel">取消</button>
            </div>
        </div>
    </form>

</section>
<script type="text/javascript" src="/assets/js/userLottery/addUserLottery.js?v={{time()}}"></script>

