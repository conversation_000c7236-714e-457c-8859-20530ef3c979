@include('css')
@include('js')
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
<section class="section-page">

    <form class="layui-form" action="">
        <input id="user-id" name="user_id" type="hidden" value="{{ $info->user_id }}"/>
        <input id="prize-id" name="prize_id" type="hidden" value="{{ $info->prize_id }}"/>
        <input id="lottery-id" name="lottery_id" type="hidden" value="{{ $info->lottery_id }}"/>
        <input id="draw-type" name="draw_type" type="hidden" value="{{ $info->draw_type }}"/>
        <input id="platform" name="platform" type="hidden" value="{{ $info->platform }}"/>
        <input id="user-prize-id" name="user_prize_id" type="hidden" value="{{ $info->user_prize_id }}"/>

        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px">收货人</label>
            <div class="layui-input-block">
                <input id="consignee" type="text" name="consignee" required placeholder="请输入收货人姓名" autocomplete="off"
                       class="layui-input" style="width: 500px" value="{{ $info->consignee ?? '' }}">
            </div>
        </div>
        <div class="layui-form-item" style="margin-left: 10px">
            <label class="layui-form-label" style="width: 100px">所在地区</label>
            <div class="layui-input-inline" style="width: 150px">
                <select name="province" lay-filter="choose-province" id="province">
                    <option value="">请选择省</option>
                    @if(!empty($provinces) && is_array($provinces) && count($provinces)>0)
                        @foreach($provinces as $k=>$v)
                            <option value="{{ $v->region_id }}"
                                    @if($v->region_id==$info->province) selected @endif>{{ $v->region_name }}</option>
                        @endforeach
                    @endif
                </select>
            </div>
            <div class="layui-input-inline" style="width: 150px">
                <select name="city" lay-filter="choose-city" id="city">
                    <option value="">请选择市</option>
                    @if(!empty($cities) && is_array($cities) && count($cities)>0)
                        @foreach($cities as $k=>$v)
                            <option value="{{ $v->region_id }}"
                                    @if($v->region_id==$info->city) selected @endif>{{ $v->region_name }}</option>
                        @endforeach
                    @endif
                </select>
            </div>
            <div class="layui-input-inline" style="width: 150px">
                <select name="district" lay-filter="choose-district" id="district">
                    <option value="">请选择县/区</option>
                    @if(!empty($districts) && is_array($districts) && count($districts)>0)
                        @foreach($districts as $k=>$v)
                            <option value="{{ $v->region_id }}"
                                    @if($v->region_id==$info->district) selected @endif>{{ $v->region_name }}</option>
                        @endforeach
                    @endif
                </select>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label" style="width: 100px">详细地址</label>
            <div class="layui-input-block">
                <textarea id="detail-address" name="detail_address" placeholder="请输入详细地址" class="layui-textarea"
                          style="width: 500px">{{ $info->detail_address ?? ''}}</textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px">手机号码</label>
            <div class="layui-input-block">
                <input id="mobile" type="text" name="mobile" required placeholder="请输入手机号码" autocomplete="off"
                       class="layui-input" style="width: 500px" value="{{ $info->mobile ?? '' }}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 100px">快递单号</label>
            <div class="layui-input-block">
                <input id="awb_no" type="text" name="awb_no" required placeholder="请输入快递单号" autocomplete="off"
                       class="layui-input" style="width: 500px" value="{{ $info->awb_no ?? '' }}">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block" style="text-align: center">
                <button class="layui-btn" lay-submit lay-filter="issue-button">确认发货</button>
            </div>
        </div>
    </form>
</section>
<script type="text/javascript" src="/assets/js/userLottery/issueUserLottery.js?v={{time()}}"></script>

