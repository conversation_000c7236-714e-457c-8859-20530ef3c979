@include('css')
<section class="section-page">
    <div class="layui-form layui-box">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">抽奖ID</label>
                <div class="layui-input-inline">
                    <input type="text" name="lottery_id" placeholder="ID" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="lottery_name" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>

            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('status','状态',request()->get('status'),config('field.LotteryStatus')) !!}
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('publisher_id','创建人',request()->get('publisher_id'),$createrList) !!}
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                {!! $timePresenter->render('create_time','创建时间') !!}
            </div>

            <div class="layui-inline" style="padding-left: 60px">
                <button lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload" data-type="reload">查询
                </button>
                <a href="/web/lottery/lotteryList" class="layui-btn layui-btn-primary layui-btn-sm reload"
                   data-type="reload">重置</a>
            </div>
        </div>
    </div>
    <script type="text/html" id="toolbar">
        <button class="layui-btn layui-btn-sm" style="margin-left: 20px" id="add"
                lay-event="add"><strong>新增</strong></button>
    </script>
    <table class="layui-table" id="list" lay-filter="list"></table>
</section>
<script type="text/html" id="edit">
    <button class="layui-btn layui-btn-xs"
            lay-event="edit"><strong>编辑</strong></button>
    <button class="layui-btn layui-btn-xs copy" lay-event="copy" value="@{{ d.id }}">
        <strong>复制</strong></button>
    <button class="layui-btn layui-btn-xs layui-btn-danger delete" lay-event="delete" value="@{{ d.id }}">
        <strong>删除</strong></button>

</script>


<script type="text/html" id="status">
    @{{#  if(d.status==-1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>已删除</strong></a>
    @{{# }else if(d.expired==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-primary"><strong>已过期</strong></a>
    @{{# }else if(d.status==0){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>未上线</strong></a>
    @{{# }else if(d.status==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-success"><strong>已上线</strong></a>
    @{{# } }}
</script>
@include('js')
<script type="text/javascript" src="/assets/js/lottery/lotteryList.js?v={{time()}}"></script>