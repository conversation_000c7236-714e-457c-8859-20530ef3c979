@include('css')
@include('js')
{{ Autograph() }}
<style>
    .layui-form-label {
        width: 110px;
    }
</style>
<section class="section-page">
    <form class="layui-form" action="">
        <div style="margin-left: 10px;margin-bottom: 30px">
            <h2>
                @if($is_copy==1)
                    复制
                @else
                    @if(empty($lottery))
                        新增
                    @else
                        编辑
                    @endif
                @endif

                抽奖活动
            </h2>
        </div>
        <input type="hidden" name="op_type" value="{{empty($lottery)?1:2}}">
        <div class="layui-form-item">
            <div class="layui-col-md12">
                <div style="display: flex;">
                    <div style="width: 270px;margin-bottom: 20px">
                        @inject('singleSelectPresenter','App\Presenters\SingleSelectPresenter')
                        {!! $singleSelectPresenter->render('org_id','活动主体:',!empty($lottery)?$lottery['org_id']:request()->user->org_id,config('field.MainOrgList'),['required'=>true,'disabled' => true,'hide_not_select'=>true]) !!}
                    </div>
                    <div id="iedge_org_select_div" style="width: 100px;display: none">
                        <input type="hidden" id="iedge_org_id" name="iedge_org_id" value="{{$lottery['org_id']??''}}">
                        <select id="iedge_org_id_select" lay-verify="required" lay-filter="iedge_org_id_select"
                                @if(!empty($lottery) && !$is_copy)
                                    disabled
                            @endif>
                            <option value="3" @if(Arr::get($lottery??[],'org_id',1)==3) selected='selected' @endif>爱智中文站
                            </option>
                            {{--                            <option value="6" @if(Arr::get($lottery??[],'org_id',1)==6) selected='selected' @endif>爱智英文站--}}
                            {{--                            </option>--}}
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    名称:</label>
                <div class="layui-input-inline">
                    <div class="layui-input-inline">
                        <input type="hidden" name="lottery_id" id="lottery_id" value="{{$lottery['lottery_id'] ?? 0}}">
                        <input type="hidden" name="is_copy" id="is_copy" value="{{$is_copy ?? 0}}">
                        <input type="text" name="lottery_name" autocomplete="off" style="width: 500px;"
                               class="layui-input"
                               value="{{$lottery['lottery_name'] ?? ''}}">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    有效日期:</label>
                <div class="layui-input-block">
                    <div class="layui-col-md3">
                        <input type="text" name="start_time" id="start_time" placeholder="开始时间" autocomplete="off"
                               class="layui-input" value="{{$lottery['start_time'] ?? ''}}">
                    </div>
                    <div class="layui-col-md1" style="padding-top: 5px;padding-left: 15px">
                        ---
                    </div>
                    <div class="layui-col-md3">
                        <input type="text" name="end_time" id="end_time" placeholder="结束时间" autocomplete="off"
                               class="layui-input" value="{{$lottery['end_time'] ?? ''}}">
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-col-md6 liexin_div">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    红包发放最低限制金额:</label>
                <div class="layui-input-inline">
                    <div class="layui-input-inline">
                        <input type="text" name="limit_get_amount" autocomplete="off" style="width: 500px;"
                               class="layui-input" placeholder="单位:元"
                               value="{{$lottery['limit_get_amount'] ?? ''}}">
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="display: none">
                <label class="layui-form-label">红包发放方式</label>
                <div class="layui-input-block">
                    <input type="radio" name="money_get_type" lay-filter="money_get_type" value="1" title="钱包发放"
                           @if(empty($lottery['money_get_type']) || $lottery['money_get_type']==1) checked=""
                           @endif @if(!empty($lottery['lottery_id'])) disabled @endif>
                    <input type="radio" name="money_get_type" lay-filter="money_get_type" value="2" title="扫码发放"
                           @if(!empty($lottery['money_get_type']) && $lottery['money_get_type']==2) checked=""
                           @endif @if(!empty($lottery['lottery_id'])) disabled @endif>
                </div>
            </div>

            <div class="layui-form-item" id="apply_amount_div" style="display: none">
                <label class="layui-form-label">钱包发放限制</label>
                <div class="layui-inline">
                    <div class="layui-form-mid layui-word-aux">活动期间最多可发放</div>
                    <div class="layui-input-inline" style="width: 60px;">
                        <input type="text" name="apply_amount" id="apply_amount" autocomplete="off"
                               class="layui-input number-input" value="{{ $lottery['apply_amount'] ?? 0}}"
                               @if($is_online) disabled @endif>
                    </div>
                    <div class="layui-form-mid layui-word-aux" style="margin-top: -10px;">元红包。
                        <button class="layui-btn layui-btn-sm" id="validate_btn">验证金额</button>
                    </div>
                </div>
            </div>


            <div class="layui-col-md6">
                <label class="layui-form-label">
                    <span style="color: red">*</span>
                    中奖限制:</label>
                <div class="layui-input-inline" style="width: 150px;height: 30px;margin-left: -20px;">
                    <div class="layui-unselect layui-form-checkbox" lay-skin="primary">
                        <span style="font-size: 12px">每个账号每天最多中</span></div>
                </div>
                <div class="layui-input-inline" style="width: 60px">
                    <input type="text" name="win_limit_per_day" id="win_limit_per_day"
                           lay-verify="required|number" autocomplete="off" class="layui-input"
                           value="{{$lottery['win_limit_per_day'] ?? 0}}">
                </div>
                <div class="layui-form-mid layui-word-aux">次</div>
                <div class="layui-input-inline" style="width: 150px;height: 30px">
                    <div class="layui-unselect layui-form-checkbox" lay-skin="primary">
                        <span style="font-size: 12px">每个账号累计最多中</span></div>
                </div>
                <div class="layui-input-inline" style="width: 60px">
                    <input type="text" name="win_limit_in_all" id="win_limit_in_all"
                           lay-verify="required|number" autocomplete="off" class="layui-input"
                           value="{{$lottery['win_limit_in_all'] ?? 0}}">
                </div>
                <div class="layui-form-mid layui-word-aux">次</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">抽奖资格获取渠道</label>
            <div class="layui-input-block" id="qualify_get_ruler_div">
                <input type="checkbox" name="qualify_get_rule_register" lay-filter="qualify_get_rule" title="注册"
                       value="1"
                       @if(!empty($lottery['qualify_get_rule']) && in_array(1,$lottery['qualify_get_rule'])) checked @endif>
                <input type="checkbox" name="qualify_get_rule_login" lay-filter="qualify_get_rule" title="登陆"
                       value="2"
                       @if(!empty($lottery['qualify_get_rule']) && in_array(2,$lottery['qualify_get_rule'])) checked @endif>
                <input type="checkbox" name="qualify_get_rule_share" lay-filter="qualify_get_rule" title="分享"
                       value="3"
                       @if(!empty($lottery['qualify_get_rule']) && in_array(3,$lottery['qualify_get_rule'])) checked @endif>
                <input type="checkbox" name="qualify_get_rule_order" lay-filter="qualify_get_rule" title="下单"
                       value="4"
                       @if(!empty($lottery['qualify_get_rule']) && in_array(4,$lottery['qualify_get_rule'])) checked @endif>
                <input type="checkbox" name="qualify_get_rule_follow" lay-filter="qualify_get_rule" title="关注"
                       value="5"
                       @if(!empty($lottery['qualify_get_rule']) && in_array(5,$lottery['qualify_get_rule'])) checked @endif>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">次数限制</label>
            <div class="layui-inline" id="qualify_register_div_id"
                 @if(empty($lottery['qualify_get_rule']) || !in_array(1,$lottery['qualify_get_rule'])) style="display: none" @endif>
                <div class="layui-form-mid layui-word-aux">注册最多送</div>
                <div class="layui-input-inline" style="width: 60px;">
                    <input type="text" name="qualify_register" id="qualify_register" autocomplete="off"
                           class="layui-input number-input" value="{{ $lottery['qualify_register']??0}}">
                </div>
                <div class="layui-form-mid layui-word-aux">次</div>
            </div>
            <div class="layui-inline" id="qualify_login_div_id"
                 @if(empty($lottery['qualify_get_rule']) || !in_array(2,$lottery['qualify_get_rule'])) style="display: none" @endif>
                <div class="layui-form-mid layui-word-aux">登陆最多送</div>
                <div class="layui-input-inline" style="width: 60px;">
                    <input type="text" name="qualify_login" id="qualify_login" autocomplete="off"
                           class="layui-input number-input" value="{{ $lottery['qualify_login']??0}}">
                </div>
                <div class="layui-form-mid layui-word-aux">次</div>
            </div>
            <div class="layui-inline" id="qualify_login_day_div_id"
                 @if(empty($lottery['qualify_get_rule']) || !in_array(2,$lottery['qualify_get_rule'])) style="display: none" @endif>
                <div class="layui-form-mid layui-word-aux">登陆每天送</div>
                <div class="layui-input-inline" style="width: 60px;">
                    <input type="text" name="qualify_login_day" id="qualify_login_day" autocomplete="off"
                           class="layui-input number-input" value="{{ $lottery['qualify_login_day']??0}}">
                </div>
                <div class="layui-form-mid layui-word-aux">次</div>
            </div>
            <div class="layui-inline" id="qualify_share_div_id"
                 @if(empty($lottery['qualify_get_rule']) || !in_array(3,$lottery['qualify_get_rule'])) style="display: none" @endif>
                <div class="layui-form-mid layui-word-aux">分享最多送</div>
                <div class="layui-input-inline" style="width: 60px;">
                    <input type="text" name="qualify_share" id="qualify_share" autocomplete="off"
                           class="layui-input number-input" value="{{ $lottery['qualify_share']??0}}">
                </div>
                <div class="layui-form-mid layui-word-aux">次</div>
            </div>
            <div class="layui-inline" id="qualify_order_div_id"
                 @if(empty($lottery['qualify_get_rule']) || !in_array(4,$lottery['qualify_get_rule'])) style="display: none" @endif>
                <div class="layui-form-mid layui-word-aux">下单最多送</div>
                <div class="layui-input-inline" style="width: 60px;">
                    <input type="text" name="qualify_order" id="qualify_order" autocomplete="off"
                           class="layui-input number-input" value="{{ $lottery['qualify_order']?? 0 }}">
                </div>
                <div class="layui-form-mid layui-word-aux">次</div>
            </div>
            <div class="layui-inline" id="activity_url_div_id"
                 @if(empty($lottery['qualify_get_rule']) || !in_array(4,$lottery['qualify_get_rule'])) style="display: none" @endif>
                <div class="layui-form-mid layui-word-aux">活动地址</div>
                <div class="layui-input-inline" style="width: 200px;">
                    <input type="text" name="activity_url" id="activity_url" autocomplete="off" class="layui-input"
                           value="{{ $lottery['activity_url'] ?? ''}}">
                </div>
            </div>
            <div class="layui-inline" id="qualify_follow_div_id"
                 @if(empty($lottery['qualify_get_rule']) || !in_array(5,$lottery['qualify_get_rule'])) style="display: none" @endif>
                <div class="layui-form-mid layui-word-aux">关注最多送</div>
                <div class="layui-input-inline" style="width: 60px;">
                    <input type="text" name="qualify_follow" id="qualify_follow" autocomplete="off"
                           class="layui-input number-input" value="{{ $lottery['qualify_follow'] ?? 0 }}">
                </div>
                <div class="layui-form-mid layui-word-aux">次</div>
            </div>
        </div>
        <div class="layui-form-item" id="pay_suc_banner_div_id"
             @if(empty($lottery['qualify_get_rule']) || !in_array(4,$lottery['qualify_get_rule'])) style="display: none" @endif>
            <label class="layui-form-label">支付成功页面banner图</label>
            <div class="layui-input-block">
                <div class="layui-col-md2" style="padding-top: 7px">
                    <a class="layui-btn layui-btn-sm upload-img" data-obj="obj-paysucimg"
                       preview="preview-paysucimg">上传图片</a>
                    <p style="margin-top: 10px">建议尺寸1190*200</p>
                </div>
                <div class="layui-col-md10" style="margin-left: -120px">
                    <img class="layui-upload-img" id="preview-paysucimg" style=""
                         @if(!empty($lottery['pay_suc_img_url'])) src="{{ $lottery['pay_suc_img_url'] }}"
                         @endif width="595" height="100"/>
                    <input type="hidden" name="paysucimg" value="{{ $lottery['pay_suc_img_url'] ?? '' }}"
                           id="obj-paysucimg">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">中奖后弹窗中banner:</label>
            <div class="layui-input-block">
                <input type="checkbox" name="show_success_banner" lay-skin="switch" lay-text="是|否"
                       @if((!empty($lottery['show_success_banner'])&&$lottery['show_success_banner']==1)||empty($lottery))
                           checked
                       @endif
                       lay-filter="show_success_banner">
            </div>
        </div>
        <div class="layui-form-item"
             style="padding-left: 110px;margin-top: -20px; @if(!empty($lottery['show_success_banner']) && $lottery['show_success_banner']!=1) display:none; @endif"
             id="success_banner_div">
            <div class="layui-upload">
                <button type="button" class="layui-btn layui-btn-sm" id="upload">选择图片</button>
                <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="clear">清除</button>
                <span>建议尺寸 : 200x200</span>
                <div class="layui-upload-list">
                    <img class="layui-upload-img" id="success_banner_image_preview" style="max-width: 200px;max-height: 200px;"
                         src="{{$lottery['success_banner_image']??''}}">
                </div>
            </div>
            <input type="hidden" name="success_banner_image" id="success_banner_image"
                   value="{{$lottery['success_banner_image']??''}}">
        </div>
{{--        <div class="layui-form-item iedge_div">--}}
{{--            <label class="layui-form-label"><span style="color: red">* </span>抽奖入口:</label>--}}
{{--            <div class="layui-inline">--}}
{{--                <input type="radio" value="1" title="弹窗" checked>--}}
{{--            </div>--}}
{{--        </div>--}}
        <div class="layui-form-item iedge_div" id="is_first_login_div" style="display: none">
            <div class="layui-col-md2">
                <label class="layui-form-label">首次登录弹窗</label>
                <div class="layui-input-block">
                    <input type="checkbox"
                           @if(!empty($lottery)&&$lottery['is_first_login']==1)
                               checked
                           @endif
                           name="is_first_login" lay-skin="switch"
                           title="开关">
                </div>
            </div>
            <div class="layui-col-md5"
                 style=""
                 id="lottery_img_div">
                <label class="layui-form-label">抽奖活动图:</label>
                <div class="layui-input-block">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn layui-btn-sm" id="lottery_img_upload">选择图片</button>
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="clear_lottery_img">
                            清除
                        </button>
                        <span>仅支持jpg、png、jpeg、gif格式，尺寸最大为800*1000，大小不超过10M</span>
                        <div class="layui-upload-list">
                            <img class="layui-upload-img" id="lottery_img_preview"
                                 src="{{$lottery['lottery_img']??''}}" style="max-height: 250px;max-width: 250px">
                        </div>
                    </div>
                </div>
                <input type="hidden" name="lottery_img" id="lottery_img"
                       value="{{$lottery['lottery_img']??''}}">
            </div>
            <div class="layui-col-md5"
                 style="margin-top: -10px;"
                 id="animation_img_div">
                <label class="layui-form-label">
{{--                    <span style="color: red">* </span>--}}
                    抽奖动效:</label>
                <div class="layui-input-block">
                    <div class="layui-upload">
                        <button type="button" class="layui-btn layui-btn-sm" id="animation_img_upload">选择图片</button>
                        <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" id="clear_animation_img">
                            清除
                        </button>
                        <span>仅支持jpg、png、jpeg、gif格式，尺寸最大为800*1000，大小不超过10M</span>
                        <div class="layui-upload-list">
                            <img class="layui-upload-img" id="animation_img_preview"
                                 src="{{$lottery['animation_img']??''}}" style="max-height: 300px;max-width: 300px">
                        </div>
                    </div>
                </div>
                <input type="hidden" name="animation_img" id="animation_img"
                       value="{{$lottery['animation_img']??''}}">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="ibox-content" style="margin-top: 30px">
                <div class="layui-row" style="width: 100%">
                    <div style="margin-left: 25px;margin-right: 25px">
                        <blockquote class="layui-elem-quote" style="margin-top: 20px">
                            <h3>中奖规则</h3>
                        </blockquote>
                        <table class="layui-table" style="margin-top: 10px">
                            <thead>
                            <tr role="row">
                                <th style="width:50px;text-align: center">序号</th>
                                <th style="width:100px;text-align: center">等级</th>
                                <th style="width:70px;text-align: center">奖品类型</th>
                                <th style="width:150px;text-align: center">奖品名称</th>
                                <th style="width:120px;text-align: center">奖品价值</th>
                                <th style="width:100px;text-align: center">奖品数量</th>
                                <th style="width:120px;text-align: center">每天抽中数量</th>
                                <th style="width:100px;text-align: center">中奖概率</th>
                                <th style="width:100px;text-align: center">中奖标题</th>
                                <th style="width:130px;text-align: center">中奖提示</th>
                                <th class="text-center is_first_login_div" id="upload-head-id" style="width:230px;text-align: center;"
                                   >首次登录弹窗-中奖图
                                </th>
                            </tr>
                            </thead>
                            <tbody id="add-coupon-body">
                            @if(!empty($lottery['lottery_id']) && !empty($prizeInfo))
                                @for($i=0;$i<8;$i++)
                                    <tr role="row" align="center" valign="middle">
                                        <td width="5%"
                                            style="text-align: center">{{ $prizeInfo[$i]['prize_id'] ?? '' }}</td>
                                        <td width="5%">
                                            <input class="layui-input" style="text-align: center" type="text"
                                                   name="level" placeholder="等级"
                                                   value="{{ $prizeInfo[$i]['level'] ?? '' }}"/>
                                            <input type="hidden" class="hidden" id="{{ 'ex_str'.$i }}"
                                                   value="{{ $prizeInfo[$i]['ex_str'] ?? '' }}">
                                        </td>
                                        <td width="7%" valign="middle">
                                            <select name="prize-type"/>
                                            <option value="1"
                                                    @if($prizeInfo[$i]['prize_type']==1) selected @endif>实物
                                            </option>
                                            <option value="2"
                                                    @if($prizeInfo[$i]['prize_type']==2) selected @endif>优惠券
                                            </option>
                                            <option value="3"
                                                    @if($prizeInfo[$i]['prize_type']==3) selected @endif>其他
                                            </option>
                                            <option value="4"
                                                    @if($prizeInfo[$i]['prize_type']==4) selected @endif>未中奖
                                            </option>
                                            </select>
                                        </td>
                                        <td width="11%">
                                            <input class="layui-input" style="text-align: center"
                                                   type="text" name="prize-name" placeholder="奖品名称"
                                                   value="{{ $prizeInfo[$i]['prize_name'] ?? '' }}"/>
                                        </td>
                                        <td width="8%">
                                            <input class="layui-input"
                                                   style="text-align: center; width: 90%"
                                                   type="text" name="prize-value" placeholder="奖品价值"
                                                   value="{{ $prizeInfo[$i]['prize_value'] ?? '' }}"
                                                   @if(1 == $lottery['money_get_type']) disabled @endif/>
                                        </td>
                                        <td width="8%">
                                            <input class="layui-input"
                                                   style="text-align: center; width: 90%"
                                                   type="text" name="prize-num" placeholder="奖品数量"
                                                   value="{{ $prizeInfo[$i]['prize_num'] ?? '' }}"/>
                                        </td>
                                        <td width="8%">
                                            <input class="layui-input"
                                                   style="text-align: center; width: 90%"
                                                   type="text" name="prize-send-num-day"
                                                   placeholder="每天抽中数量"
                                                   value="{{ $prizeInfo[$i]['prize_send_num_day'] ?? '' }}"/>
                                        </td>
                                        <td width="8%">
                                            <input class="layui-input"
                                                   style="text-align: center; width: 90%"
                                                   type="text" name="prize-chance"
                                                   placeholder="中奖概率"
                                                   value="{{ $prizeInfo[$i]['chance'] ?? '' }}"/>
                                        </td>
                                        <td width="11%">
                                            <input class="layui-input"
                                                   style="text-align: center; width: 90%"
                                                   type="text" name="prize-title" placeholder="中奖标题"
                                                   value="{{ $prizeInfo[$i]['prize_title'] ?? '' }}"/>
                                        </td>
                                        <td width="11%">
                                            <input class="layui-input"
                                                   style="text-align: center; width: 90%"
                                                   type="text" name="prize-tips" placeholder="中奖提示"
                                                   value="{{ $prizeInfo[$i]['prize_tips'] ?? '' }}"/>
                                        </td>
                                        <td width="17%" class="upload-square iedge_div is_first_login_div" style="text-align: center; ">
                                            <input class="layui-btn layui-btn-xs upload-img"
                                                   data-obj="obj-{{ $i }}" style="width: 60px;text-align: center; "
                                                   value="选择图片"/>
                                            <a href="{{ $prizeInfo[$i]['prize_img'] }}"
                                               style="color: dodgerblue;margin-left: 5px;" target="_blank"
                                               id="obj-{{ $i }}-href">{{ $prizeInfo[$i]['prize_img_file_name'] }}</a>
                                            <input type="hidden" class="prize_img_file_name"
                                                   name="prize_img_file_name"
                                                   value="{{ $prizeInfo[$i]['prize_img_file_name'] }}"
                                                   id="obj-{{ $i }}-name">
                                            <input type="hidden" class="prize_img" name="prize_img"
                                                   value="{{ $prizeInfo[$i]['prize_img'] }}"
                                                   id="obj-{{ $i }}">
                                        </td>
                                    </tr>
                                @endfor
                            @else
                                @for($i=0;$i<8;$i++)
                                    <tr role="row" align="center" valign="middle">
                                        <td width="5%" style="text-align: center">{{ '' }}</td>
                                        <td width="5%">
                                            <input class="layui-input" style="text-align: center" type="text"
                                                   name="level" placeholder="等级" value=""/>
                                            <input type="hidden" class="hidden" id="{{ 'ex_str'.$i }}"
                                                   value="{{ $prizeInfo[$i]['ex_str'] ?? '' }}">
                                        </td>
                                        <td width="7%" valign="middle">
                                            <select name="prize-type">
                                                <option value="1">实物</option>
                                                <option value="2">优惠券</option>
                                                <option value="3">其他</option>
                                                <option value="4">未中奖</option>
                                            </select>
                                        </td>
                                        <td width="11%"><input class="layui-input" style="text-align: center"
                                                               type="text" name="prize-name" placeholder="奖品名称"
                                                               value=""/></td>
                                        <td width="8%"><input class="layui-input"
                                                               style="text-align: center; width: 90%"
                                                               type="text" name="prize-value" placeholder="奖品价值"
                                                               value=""/></td>
                                        <td width="8%"><input class="layui-input"
                                                               style="text-align: center; width: 90%"
                                                               type="text" name="prize-num" placeholder="奖品数量"
                                                               value=""/></td>
                                        <td width="8%"><input class="layui-input"
                                                               style="text-align: center; width: 90%"
                                                               type="text" name="prize-send-num-day"
                                                               placeholder="每天抽中数量" value=""/></td>
                                        <td width="8%"><input class="layui-input"
                                                               style="text-align: center; width: 90%"
                                                               type="text" name="prize-chance"
                                                               placeholder="中奖概率" value=""/></td>
                                        <td width="11%"><input class="layui-input"
                                                                                  style="text-align: center; width: 90%"
                                                                                  type="text" name="prize-title"
                                                                                  placeholder="中奖标题"
                                                                                  value=""/></td>
                                        <td width="11%"><input class="layui-input"
                                                                                  style="text-align: center; width: 90%"
                                                                                  type="text" name="prize-tips"
                                                                                  placeholder="中奖提示"
                                                                                  value=""/></td>
                                        <td width="17%" class="upload-square is_first_login_div" style="text-align: center; ">
                                            <input class="layui-btn layui-btn-xs upload-img"
                                                   data-obj="obj-{{ $i }}" style="width: 60px;"
                                                   value="选择图片"/>
                                            <a href="" style="color: dodgerblue;margin-left: 5px;" target="_blank"
                                               id="obj-{{ $i }}-href"></a>
                                            <input type="hidden" class="prize_img_file_name"
                                                   name="prize_img_file_name" value=""
                                                   id="obj-{{ $i }}-name">
                                            <input type="hidden" class="prize_img" name="prize_img" value=""
                                                   id="obj-{{ $i }}">
                                        </td>
                                    </tr>
                                @endfor
                            @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <blockquote class="layui-elem-quote" style="margin-top: 20px">
            <p>说明:</p>
            <p>1、奖品等级可配置1-8中任意等级，只要保证最大等级的奖品为中奖率最高的奖品即可。</p>
            <p>2、奖品名称展示在中奖信息滚动列表中。</p>
            <p>3、中奖提示名称及中奖提示内容根据所录入的内容，展示在中奖后的弹窗中。</p>
            <p>4、如有扫码领取的奖品，请配置在“其他”类奖品中。</p>
            <p>5、如果新增奖品类型为优惠券类型，或有其他类型奖品改为优惠券类型，在填写奖品名称时格式为
                id+优惠券id。eg:id12345</p>
        </blockquote>
        <div class="layui-form-item" style="padding-left: 40%">
            <div class="layui-input-block">
                <button type="button" class="layui-btn saveForm" lay-submit lay-filter="saveForm">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary cancel">取消
                </button>
            </div>
        </div>
        <br>
    </form>
</section>
<script type="text/javascript" src="/assets/js/lottery/saveLottery.js?v={{time()}}"></script>

