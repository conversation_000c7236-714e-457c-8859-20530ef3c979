<script type="text/javascript" src="/assets/libs/jquery/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js?v={{time()}}"></script>
<script type="text/javascript" src="/assets/js/common.js?v={{time()}}"></script>
<?php
$api_upload_k1 = time();
$api_upload_k2 = MD5(MD5($api_upload_k1) . Config('website.UploadKey'));
?>
<script>
  var search_url = "{{ config('website.search_domain') }}" //搜索网址
  var api_url = "{{ config('website.api_domain') }}" //api
  var oss_url="{{ config('website.file_domain') }}"
  var api_upload_key = "{{ config('website.UploadKey') }}" //api上传key
  var api_upload_k1 = "{{ $api_upload_k1 }}" //api上传k1
  var api_upload_k2 = "{{ $api_upload_k2 }}" //api上传k2
  var order_domain = "{{ Config('website.order_domain') }}" //order_domain
</script>