<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="assets/images/favicon.ico" rel="icon">
    <title>魔方系统管理</title>
    <link rel="stylesheet" href="assets/libs/layui/css/layui.css?v={{time()}}"/>
    <link rel="stylesheet" href="assets/module/admin.css?v={{time()}}"/>
    <link rel="stylesheet" href="/assets/css/font/iconfont.css?v={{time()}}">
    <!--[if lt IE 9]>
    <script src="assets/js/html5shiv.min.js"></script>
    <script src="assets/js/respond.min.js"></script>
    <![endif]-->
</head>

<body class="layui-layout-body">
<div class="layui-layout layui-layout-admin">
    <!--头部logo-->
    <div class="layui-header">
        <div class="layui-logo" id="nav-logo">
            <a href="{{ Config::get('website.login')['dashboard'] }}" target="_blank" class="logo-v">
                <img src="assets/images/u6.png"/>
                <cite style="color: #ffffff;font-size: 12px">魔方系统管理</cite>
            </a>
            <a class="flexible" ew-event="flexible" title="侧边伸缩"><i class="layui-icon layui-icon-shrink-right"></i></a>
        </div>
    </div>
    <!--头部登录信息-->
    <div class="layui-header-right-bar">
        <ul class="layui-nav">
            <li class="layui-nav-item" lay-unselect>
                <a href="javascript:;">
                    <div id="user-logo">
                        <img src="{{ request()->user->header ?? 'assets/images/logo-.png' }}" class="layui-nav-img">
                        <cite title="{{ request()->user->name }}">{{ request()->user->name }}</cite>
                    </div>
                    <span class="layui-nav-more"></span>
                </a>
                <dl class="layui-nav-child">
                    <dd lay-unselect><a ew-event="logout" data-url="{{ Config::get('website.login')['logout'] }}">退出</a></dd>
                </dl>
            </li>
        </ul>
        <div class="deal-with"></div>
    </div>
    <!-- 侧边栏 -->
    <div class="layui-side">
        <div class="layui-side-scroll">
            <ul class="layui-nav layui-nav-tree arrow2" lay-filter="admin-side-nav" lay-shrink="_all">
                @foreach ($menu_list as $menu_item)
                    <li class="layui-nav-item">
                        <!-- 一级菜单，没有下拉 -->
                        @if (empty($menu_item["childs"]))
                            <a lay-href="{{$menu_item['href']}}"><i class="layui-icon {{$menu_item['class']}}"></i>&emsp;<cite>{{ $menu_item['title'] }}</cite></a>
                        @else
                            <a><i class="layui-icon {{$menu_item['class']}}"></i>&emsp;<cite>{{ $menu_item['title'] }}</cite></a>
                            <dl class="layui-nav-child">
                                @foreach ($menu_item['childs'] as $child_info)
                                    <dd><a lay-href="{{$child_info['href']}}">{{ $child_info['title'] }}</a></dd>
                                @endforeach
                            </dl>
                        @endif
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
    <!-- 主体部分 -->
    <div class="layui-body"></div>
    <!-- 底部 -->
    <div class="layui-footer layui-text">
        copyright © 2022 <a href="http://easyweb.vip" target="_blank">ichunt.com</a> all rights reserved.
        <span class="pull-right">Version 3.1.8</span>
    </div>
</div>

<!-- 加载动画 -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>
<script type="text/javascript" src="/assets/libs/jquery/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js?v={{time()}}"></script>
<script type="text/javascript" src="/assets/js/common.js?v={{time()}}"></script>
<script>
  layui.use(['index', 'admin'], function () {
    var $ = layui.jquery;
    var index = layui.index;
    var admin = layui.admin;


    // 默认加载主页
    var jumpUrl = getUrlParam("jumpUrl");
    if (jumpUrl == "" || jumpUrl == null) {
      index.loadHome({
        title: '活动列表管理',
        menuPath: "/web/activity/list",
        menuName: '<i class="layui-icon layui-icon-home"></i>'
      });
    } else {
      $(this).find("span.title").text("处理数据页面");
      index.loadHome({
        menuPath: getProtoColOkUrl("{{ config('website.frq_url')}}" + jumpUrl + location.search),
        menuName: '<i class="layui-icon layui-icon-home"></i>'
      });
    }

    //获取url中的参数
    function getUrlParam(name) {
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
      var r = window.location.search.substr(1).match(reg); //匹配目标参数
      if (r != null) return unescape(r[2]);
      return ""; //返回参数值
    }

    // 获取符合当前协议的url，可能当前页面是https，那么就需要把http的转换为https的，这样才能展示。
    // 如果当前页面是http的，那么即使是https的，也能展示，所以不用转换
    function getProtoColOkUrl(url) {
      if (isHttps()) {
        return httpUrlToHttpsUrl(url)
      } else {
        return httpsUrlToHttpUrl(url)
      }
    }

    function isHttps() {
      return window.location.protocol.indexOf("https") !== -1
    }

    function httpUrlToHttpsUrl(url) {
      if (url.indexOf('https') !== -1) {
        return url
      }
      return url.replace("http", "https")
    }

    function httpsUrlToHttpUrl(url) {
      return url.replace("https", "http")
    }

  });
</script>
</body>
</html>

