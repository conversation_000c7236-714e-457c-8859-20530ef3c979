@include('css')
<section class="section-page">
    <form class="layui-form layui-box" lay-filter="user_coupon_list_form">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">客户账号</label>
                <div class="layui-input-inline">
                    <input type="text" name="user_account" placeholder="客户账号" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">批次号</label>
                <div class="layui-input-inline">
                    <input type="text" name="coupon_sn" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">优惠券名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="coupon_name" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-inline">
                    <input type="text" name="coupon_desc" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">渠道来源</label>
                <div class="layui-input-inline">
                    <input type="text" name="adtag" placeholder="模糊匹配" autocomplete="off"
                           class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                @inject('statusPresenter','App\Presenters\StatusPresenter')
                {!! $statusPresenter->render('status','使用状态',request()->get('status'),config('field.UserCouponStatus')) !!}
            </div>
            <div class="layui-inline">
                @inject('timePresenter','App\Presenters\TimeIntervalPresenter')
                {!! $timePresenter->render('create_time','领取时间') !!}
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="min-width: 80px">使用时间</label>
                <div class="layui-input-inline" style="min-width: 260px">
                    <input type="text" name="use_time" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline" style="padding-left: 60px">
                <button type="button" lay-submit lay-filter="load" class="layui-btn layui-btn-sm reload"
                        data-type="reload">查询
                </button>
                <a href="/web/userCoupon/userCouponList" class="layui-btn layui-btn-primary layui-btn-sm reload"
                   data-type="reload">重置</a>
            </div>
        </div>
    </form>
    <script type="text/html" id="toolbar">

        <button class="layui-btn layui-btn-sm" style="margin-left: 20px" id="export"
                lay-event="export"><strong>导出</strong></button>
    </script>
    <table class="layui-table" id="list" lay-filter="list"></table>
</section>
<script type="text/html" id="edit">
    <button class="layui-btn layui-btn-xs"
            lay-event="edit"><strong>编辑</strong></button>
    <button class="layui-btn layui-btn-xs layui-btn-danger delete" value="@{{ d.id }}"><strong>删除</strong></button>

</script>


<script type="text/html" id="status">
    @{{#  if(d.status==-1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>已删除</strong></a>
    @{{# }else if(d.expired==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-primary"><strong>已过期</strong></a>
    @{{# }else if(d.status==0){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-danger"><strong>未上线</strong></a>
    @{{# }else if(d.status==1){ }}
    <a class="layui-btn layui-btn-xs layui-btn-outline layui-btn-success"><strong>已上线</strong></a>
    @{{# } }}
</script>
@include('js')
<script type="text/javascript" src="/assets/js/userCoupon/userCouponList.js?v={{time()}}"></script>