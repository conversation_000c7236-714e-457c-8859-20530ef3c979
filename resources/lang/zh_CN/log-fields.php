<?php
return [
    'email' => '邮箱',
    'purchase_plan_sn' => '计划单号',
    'company_name' => '采购组织名称',
    'currency' => '币别',
//    'delivery_warehouse_id' => '交货仓ID',
    'delivery_warehouse_name' => '交货仓名称',
//    'goods_sn' => '商品编码',
    'brand_name' => '品牌名称',
    'goods_name' => '商品型号',
    'plan_qty' => '备货计划数量',
    'goods_unit' => '商品单位',
    'price_without_tax' => '未税单价',
    'price_in_tax' => '含税单价',
//    'purchase_uid' => '采购员UID',
    'purchase_name' => '采购员名字',
//    'frq_time' => '需求日期',
//    'purchase_sn' => '采购单号',
//    'remark' => '备注',
//    'purchase_plan_id' => '采购计划ID',
    'purchase_plan_items' => '采购计划详情',
    'return_qty' => '退货数量',
    'return_without_tax' => '未税退货单价',
    'return_in_tax' => '含税退货单价',
    'address' => '退货收货人地址',
    'tel' => '退货收货人电话',
    'consignee' => '退货收货人',
    'supplier_name' => '供应商名称',
    'supp_brand_name' => '供应商品牌名',
    'supp_goods_sn' => '供应商商品唯一编码',
];
