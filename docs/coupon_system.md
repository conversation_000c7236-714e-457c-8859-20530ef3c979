# 优惠券系统文档

## 数据库表结构

### lie_coupon 表（优惠券表）

存储优惠券的基本信息，包括优惠券名称、类型、使用条件、有效期等。

主要字段说明：
- `coupon_id`: 优惠券ID，主键
- `coupon_sn`: 优惠券编码
- `coupon_name`: 优惠券名称
- `coupon_type`: 优惠券类型（1:抵扣券 2:折扣券）
- `require_amount`: 可使用券金额下限
- `sale_amount`: 优惠金额/折扣率
- `coupon_mall_type`: 适用商城（1:全站 2:自营商城 3:联营商城）
- `coupon_goods_range`: 适用商品范围（1:全站 2:供应商 3:品牌 4:分类 5:供应商id 6:商品名称）
- `max_preferential_amount`: 最大优惠金额（针对折扣券）

### lie_user_coupon 表（用户优惠券表）

存储用户领取的优惠券信息，包括领取时间、使用状态等。

主要字段说明：
- `user_coupon_id`: 用户优惠券ID，主键
- `user_id`: 用户ID
- `coupon_id`: 优惠券ID，关联到lie_coupon表
- `coupon_type`: 券类型（冗余字段）
- `create_time`: 领取时间
- `end_time`: 失效时间
- `use_time`: 使用时间
- `status`: 状态（-3:已删除 -2:已失效 -1:待使用 1:已使用）

## 获取最佳优惠券接口

### 接口说明

该接口用于根据商品列表和用户ID，获取最适合用户的优惠券。

### 请求方式

```
POST /api/open/coupon/getBestCoupon
```

### 请求参数

请求体为JSON格式，如下：

```json
{
  "user_id": 70248,
  "goods_list": [
    {
      "goods_id": 1170418131729525209,
      "price": 0.7495
    }
  ],
  "org_id": 1
}
```

| 参数名 | 类型 | 必填 | 说明 |
| --- | --- | --- | --- |
| user_id | int | 是 | 用户ID |
| goods_list | array | 是 | 商品列表，每个商品包含 goods_id 和 price 字段 |
| org_id | int | 否 | 组织ID，默认为1（猪芯） |

### 响应结果

成功响应：

```json
{
  "status": true,
  "data": {
    "user_coupon_id": 123,
    "coupon_id": 456,
    "coupon_name": "满100减10",
    "coupon_type": 1,
    "sale_amount": 10,
    "preferential": 10
  }
}
```

失败响应：

```json
{
  "status": false,
  "message": "没有可用的优惠券"
}
```

## 优惠券匹配逻辑

1. 获取用户所有可用的优惠券（未使用且未过期）
2. 获取商品详细信息（通过商品服务）
3. 检查每张优惠券是否适用于当前商品列表：

### 商城类型匹配

根据优惠券的 `coupon_mall_type` 字段进行匹配：

- 1：全站 - 所有商品都可用
- 2：自营商城 - 只能用于自营商品（商品类型为3吆4）
- 3：联营商城 - 只能用于联营商品（商品类型为1〆2和6）

### 商品范围匹配

根据优惠券的 `coupon_goods_range` 字段进行匹配：

- 1：全站 - 所有商品都可用（但还要符合商城类型）
- 2：供应商 - 只能用于指定供应商的商品
- 3：品牌 - 只能用于指定品牌的商品
- 4：分类 - 只能用于指定分类的商品
- 5：供应商ID - 只能用于指定供应商ID的商品
- 6：商品名称 - 只能用于指定名称的商品

### 品牌匹配逻辑

1. 如果设置了 `selected_brand_id`，则商品的品牌ID必须在这个列表中
2. 如果设置了 `exclude_brand_ids`，则商品的品牌ID不能在这个列表中
3. 如果设置了 `supplier_ids` 和 `canals`，则商品的供应商ID和渠道编码必须匹配

### 金额要求

如果优惠券设置了 `require_amount`（最低使用金额），则符合条件的商品总金额必须大于等于该值才能使用优惠券。

### 优惠金额计算

- 抵扣券（coupon_type=1）：直接返回 sale_amount 值
- 折扣券（coupon_type=2）：计算公式为 `订单总金额 - (sale_amount * 订单总金额)`
  - 例如：订单总金额为100元，折扣为0.8（即八折），则优惠金额为100 - (0.8 * 100) = 20元
- 如果设置了最高优惠限额（max_preferential_amount），则取优惠金额和最高限额的较小值

### 最佳优惠券选择

在所有可用的优惠券中，选择优惠金额最高的作为最佳优惠券返回。
