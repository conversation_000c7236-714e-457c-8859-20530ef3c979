## lie_activity_new
//id(int),活动编号(varchar),活动名称(varchar),活动分类(unint8)(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动),活动地址(varchar 255),活动开始时间(uint),活动结束时间(uint),网页类型(uint8)(1-网页,2-h5,3-全部都有),活动状态(utinyint)(1待配置,2已配置未上线,3已上线,-1已过期),创建时间(int),创建人id(int),创建人名称(varchar 32),活动启用状态(unint8)(1启用,-1未启用),网页标题(varchar),网页关键词(varchar),网页描述(varchar),web网页html(text),web网页html配置项(text),h5网页html(text),h5网页配置项(text),更新时间(int)
```sql
CREATE TABLE `lie_activity_new` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `activity_no` varchar(32) NOT NULL DEFAULT '' COMMENT '活动编号',
  `activity_name` varchar(255) NOT NULL DEFAULT '' COMMENT '活动名称',
  `activity_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '活动分类(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动)',
  `activity_url` varchar(255) NOT NULL DEFAULT '' COMMENT '活动地址',
  `activity_start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动开始时间',
  `activity_end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动结束时间',
  `web_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '网页类型(1-网页,2-h5,3-全部都有)',
  `activity_status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '活动状态(1待配置,2已配置未上线,3已上线,-1已过期)',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `create_user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建人id',
  `create_user_name` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人名称',
  `activity_enable` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '活动启用状态(1启用,-1未启用)',
  `web_title` varchar(255) NOT NULL DEFAULT '' COMMENT '网页标题',
  `web_keywords` varchar(255) NOT NULL DEFAULT '' COMMENT '网页关键词',
  `web_description` varchar(255) NOT NULL DEFAULT '' COMMENT '网页描述',
  `web_html` text NOT NULL COMMENT 'web网页html',
  `web_html_config` text NOT NULL COMMENT 'web网页html配置项',
  `h5_html` text NOT NULL COMMENT 'h5网页html',
  `h5_html_config` text NOT NULL
    `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `activity_no` (`activity_no`),
    KEY `activity_type` (`activity_type`),
    KEY `activity_status` (`activity_status`),
    KEY `activity_enable` (`activity_enable`),
    KEY `activity_start_time` (`activity_start_time`),
    KEY `activity_end_time` (`activity_end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='新活动表';

```

## 活动元件配置表 lie_activity_element
//id,模板名,视图标记,状态(1启用,-1禁用),创建时间
```sql
CREATE TABLE `lie_activity_element` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `element_name` varchar(32) NOT NULL DEFAULT '' COMMENT '模板名',
  `element_code` varchar(32) NOT NULL DEFAULT '' COMMENT '视图标记',
  `element_status` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '状态(1启用,-1禁用)',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `element_code` (`element_code`),
  KEY `element_status` (`element_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='活动元件配置表';

```
