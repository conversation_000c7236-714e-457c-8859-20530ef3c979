<?php

define("DIGITS_TWO", 2);
define("DIGITS_FOUR", 4);
define("DIGITS_SIX", 6);


/**
 * 是否为多维数组
 * @param array $arr
 * @return bool
 */
function isMultipleArray(array &$arr): bool
{
    if (count($arr) <= 0) {
        return false;
    }

    if (count($arr) == count($arr, COUNT_RECURSIVE)) {
        foreach ($arr as $tempArr) {
            if (is_array($tempArr)) {
                return true;
            }
        }
        return false;
    }

    return true;
}

//判断是否有对应的权限
//request()->perms是Permission中间件过来的
function checkPerm($perm): bool
{
    $perms = request()->perms ?: [];
    return in_array($perm, $perms);
}


/**
 * 价格格式化
 * @param  [type]  $price [description]
 * @param integer $sign [description]
 * @param integer $num [description]
 * @return [type]         [description]
 */
function price_format($price, $sign = 0, $num = 2, $sep = '')
{
    $minus = $price < 0 ? '-' : '';
    $price = floatval(strval($price));
    $price = number_format(abs($price), $num, '.', $sep);
    $sign = \Arr::get(config("field.currency_sign"), intval($sign), "");
    if (!empty($sign)) {
        $price = $sign . $price;
    }
    if ($minus) {
        return $minus . $price;
    }
    return $price;
}


function arrayGet($arr, $key, $default = "", $func = "")
{
    if (isset($arr[$key])) {
        if ($func && is_callable($func)) {
            try {
                return $func($arr[$key]);
            } catch (\Throwable $e) {
                return $arr[$key];
            }
        } else {
            return $arr[$key];
        }

    } else {
        return $default;
    }
}

/*
 * 构建时间查询
 */
function buildQueryTimeRange($time = "")
{
    $time = explode("~", $time);
    $buildTimeQueryData["begin_time"] = isset($time[0]) ? $time[0] : "";
    $buildTimeQueryData["end_time"] = isset($time[1]) ? $time[1] : "";
    return $buildTimeQueryData;
}


/*
 * 排序
 */
//function _arraySort($array,$keys,$sort='asc') {
//    $newArr = $valArr = array();
//    foreach ($array as $key=>$value) {
//        $valArr[$key] = $value[$keys];
//    }
//    ($sort == 'asc') ?  asort($valArr) : arsort($valArr);
//    reset($valArr);
//    foreach($valArr as $key=>$value) {
//        $newArr[$key] = $array[$key];
//    }
//    return $newArr;
//}

function arraySort($list, $keys, $sort = "asc")
{
    if ($sort == "asc") {
        $sort = SORT_ASC;
    } else {
        $sort = SORT_DESC;
    }
    array_multisort(array_column($list, $keys), $sort, $list);
    return $list;
}


/*
 * 去重数组
 * 过滤数组
 */
function array_filter_unique($arr)
{
    return array_unique(array_filter($arr));
}


/*
 * 截取字符串
 */
//如果字符串长度超过10，则截取并以省略号结尾
function truncStr($str, $len = 100, $endStr = "")
{
    $str = (string)$str;
    if (mb_strlen($str, 'utf-8') > $len) {
        return mb_substr($str, 0, $len, 'utf-8') . $endStr;
    } else {
        return $str;
    }
}

/*
 * 获取登录者的信息
 */
function getAdminUser()
{
    $admin = request()->get("user");
    if (!$admin) {
        throw new \App\Exceptions\InvalidRequestException("没找到登录相关信息,请先登录~_~");
    }
    $arr = [];
    $arr["userId"] = $admin->userId;
    $arr["name"] = $admin->name;
    $arr["email"] = $admin->email;
    $arr["engName"] = $admin->engName;
    return $arr;
}

/*
 * 获取登录者用户id
 */
function getAdminUserId()
{
    $admin = request()->get("user");
    if (!$admin) {
        throw new \App\Exceptions\InvalidRequestException("没找到登录相关信息,请先登录~_~");
    }
    return $admin->userId;
}

/*
 * 获取登录者的名字
 */
function getAdminUserName()
{
    $admin = request()->get("user");
    if (!$admin) {
        throw new \App\Exceptions\InvalidRequestException("没找到登录相关信息,请先登录~_~");
    }
    return $admin->name;
}


/*
 * 遍历数组中某个字段的值 作为 键   返回新数组
 */
function arrayChangeKeyByField($list, $searchKey)
{
    $arr = [];
    if (!$searchKey) {
        return $list;
    }
    foreach ($list as $k => $v) {
        if (isset($v[$searchKey])) {
            $arr[$v[$searchKey]] = $v;
        }
    }
    return $arr ? $arr : $list;
}


/*
 * 把数组中null的字符串转为空
 */
function conversionArray($arr)
{
    if (empty($arr)) {
        return $arr;
    }

    foreach ($arr as $k => $v) {
        if (is_array($v)) {
            $arr[$k] = conversionArray($v);
        } else {
            if ($v === null) {
                $arr[$k] = "";
            }
        }
    }
    return $arr;
}

function dateDefault($time)
{
    $time = intval($time);
    if ($time) {
        return date("Y-m-d H:i:s", $time);
    }
    return "";
}

/*
 * 格式化数字 保留两位小数
 * 应领导要求封装成了一个函数，次函数已废弃 参考函数decimal_number_format
 */
if (!function_exists('sprintf2')) {
    function sprintf2($amount)
    {
        $amount = floatval(strval($amount));
        $tmp = number_format($amount, 2, ".", "");
        return $tmp;
    }
}


/*
 * 格式化数字 保留6位小数
 * 应领导要求封装成了一个函数，次函数已废弃 参考函数decimal_number_format
 */
if (!function_exists('sprintf6')) {
    function sprintf6($amount)
    {
        $amount = floatval(strval($amount));
        $tmp = number_format($amount, 6, ".", "");
        return $tmp;
    }
}

/*
 * 格式化数字 保留4位小数
 * 应领导要求封装成了一个函数，次函数已废弃  参考函数decimal_number_format
 */
if (!function_exists('sprintf4')) {
    function sprintf4($amount)
    {
        $amount = floatval(strval($amount));
        $tmp = number_format($amount, 4, ".", "");
        return $tmp;
    }
}


/**
 * 通用格式化字符串为数字金额
 * @$amount   需要格式化的数字或者字符串 1.2345
 * @$digits  保留小数位数   1.23
 * @$currency  币别 ￥12003.4567
 * @$thousandsSymbol 千分位字符串隔开  1,200,3.4567
 *
 *  define("DIGITS_TWO",2);
 * define("DIGITS_FOUR",4);
 * define("DIGITS_SIX",6);
 */
if (!function_exists('decimal_number_format')) {
    function decimal_number_format($amount, $digits = DIGITS_TWO, $currency = "", $thousandsSymbol = "")
    {
        $amount = floatval(strval($amount));
        //格式化币别
        if ($currency) {
            $minus = $amount < 0 ? '-' : '';
            $numerical = number_format(abs($amount), $digits, ".", $thousandsSymbol);
            $sign = \Arr::get(config("field.currency_sign"), intval($currency), "");
            if (!empty($sign)) {
                $numerical = $sign . $numerical;
            }
            return $minus ? $minus . $numerical : $numerical;
        } else {
            $numerical = number_format($amount, $digits, ".", "");
            return $numerical;
        }
    }
}

if (!function_exists('printJson')) {
    function printJson($data)
    {
        print_r(is_array($data) ? json_encode($data) : $data);
        die;
    }
}

if (!function_exists('drawLetter')) {
    /*
 * 格式化型号, echo DrawLetter("LMGAGA 质量 &&*****") 输出：LMGAGA
 * @param $g string 关键词
 */
    function drawLetter($g)
    {
        $g = preg_replace('/[\x{4e00}-\x{9fff}]+/u', '', $g);
        $g = preg_replace('/[^A-Za-z0-9]+/', '', $g);
        return strtoupper($g);
    }
}

if (!function_exists('buildQuery')) {
    function buildQuery($query, $where)
    {
        foreach ($where as $subWhere) {
            if (count($subWhere) == 2) {
                $fiels = $exp = $subWhere[0] ?? "";
                $value = $subWhere[1] ?? null;
                if (empty($fiels) || $value === null) {
                    continue;
                }
                if ($exp == "or") {
                    $query->orWhere(function ($q) use ($value) {
                        buildQuery($q, $value);
                    });
                } else {
                    $query->where($subWhere[0], $subWhere[1]);
                }
            } else {
                if (count($subWhere) == 3) {
                    $fiels = $subWhere[0] ?? "";
                    $exp = $subWhere[1] ?? null;
                    $value = $subWhere[2] ?? null;
                    if (empty($fiels) || $exp === null || $value === null) {
                        continue;
                    }
                    if ($exp == "in" && is_array($value) && !empty($value)) {
                        $query->whereIn($fiels, $value);
                    } else {
                        $query->where($fiels, $exp, $value);
                    }
                }
            }
        }
        return $query;
    }
}

if (!function_exists('echoToSql')) {
    function echoToSql($query)
    {
        $tmp = str_replace('?', '"' . '%s' . '"', $query->toSql());
        $tmp = vsprintf($tmp, $query->getBindings());
        dd($tmp);
    }
}

/**
 * Notes:提取数组中某个字段$searchkey作为键值，然后从数组中检索给定的键的所有值
 * User: sl
 * Date: 2022/5/25 15:39
 * @param $arr
 * @param $key
 * @param $val
 * @return array
 */
function flipArrayPluck($arr, $newKey, $seachKey)
{
    $newArr = [];
    foreach ($arr as $v) {
        $newArr[$v[$newKey]][] = $v[$seachKey];
    }
    return $newArr;
}


if (!function_exists('checkArrayValuesNotEmpty')) {
    function checkArrayValuesNotEmpty($arr = [])
    {
        $result = true;
        if (is_array($arr)) {
            foreach ($arr as $val) {
                if (empty($val)) {
                    $result = false;
                    break;
                }
            }
        } else {
            $result = false;
        }
        return $result;
    }
}

if (!function_exists('pwdhash')) {
    function pwdhash($pwd, $salt)
    {
        return md5(md5($pwd) . $salt);
    }
}

//获取api加密钥匙
if (!function_exists('authkey')) {
    function authkey()
    {
        return array(
            'k1' => $_SERVER['REQUEST_TIME'],
            'k2' => pwdhash($_SERVER['REQUEST_TIME'], 'fh6y5t4rr351d2c3bryi'),
            'pf' => 1
        );
    }
}

if (!function_exists('fputcsv2')) {
    function fputcsv2($handle, array $fields, $delimiter = ",", $enclosure = '"', $escape_char = "\\")
    {
        foreach ($fields as $k => $v) {
            $fields[$k] = iconv("UTF-8", "GB2312//IGNORE", $v);  // 这里将UTF-8转为GB2312编码
        }
        fputcsv($handle, $fields, $delimiter, $enclosure, $escape_char);
    }
}

//导出csv
if (!function_exists('exportCsv')) {
    function exportCsv($data, $filename, $column)
    {
        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');

        $file = fopen('php://output', 'w');
        fputcsv2($file, $column);
        foreach ($data as $row) {
            fputcsv2($file, $row);
        }
        exit();
    }
}

if (!function_exists('generateCouponSN')) {
    //生成批次编号
    function generateCouponSN($str = '')
    {
        return $str . date('Ymd') . substr(implode(null, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0,
                8);
    }
}

if (!function_exists('channelDiscountRound')) {
    function channelDiscountRound($ratio)
    {
        $ratio = floatval(strval($ratio));
        $ratio = number_format($ratio, 4);
        return (float)$ratio;
    }
}

if (!function_exists('channelDiscountFormatStr')) {
    function channelDiscountFormatStr($ratio)
    {
        $ratio = floatval(strval($ratio));
        $ratio = number_format($ratio, 4);
        return $ratio;
    }
}

if (!function_exists('my_array_sort')) {
    //数组排序
    function my_array_sort($array, $on, $order = SORT_ASC)
    {
        $new_array = array();
        $sortable_array = array();

        if (count($array) > 0) {
            foreach ($array as $k => $v) {
                if (is_array($v)) {
                    foreach ($v as $k2 => $v2) {
                        if ($k2 == $on) {
                            $sortable_array[$k] = $v2;
                        }
                    }
                } else {
                    $sortable_array[$k] = $v;
                }
            }

            switch ($order) {
                case SORT_ASC:
                    asort($sortable_array);
                    break;
                case SORT_DESC:
                    arsort($sortable_array);
                    break;
            }

            foreach ($sortable_array as $k => $v) {
                $new_array[$k] = $array[$k];
            }
        }

        return $new_array;
    }
}

if (!function_exists('objectToArray')) {
    /*
 * 对象转数组
 * */
    function objectToArray($d)
    {
        if (is_object($d)) {
            // Gets the properties of the given object
            // with get_object_vars function
            $d = get_object_vars($d);
        }

        if (is_array($d)) {
            /*
            * Return array converted to object
            * Using __FUNCTION__ (Magic constant)
            * for recursive call
            */
            return array_map(__FUNCTION__, $d);
        } else {
            // Return array
            return $d;
        }
    }
}

if (!function_exists('curlWithCookie')) {
    function curlWithCookie($url, $params = false, $isPost = 0, $https = 0, $cookie = '', $timeout = 1000)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_USERAGENT,
            'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.118 Safari/537.36');
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_REFERER, Config('website.data'));
        curl_setopt($ch, CURLOPT_COOKIE, $cookie);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 对认证证书来源的检查
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
        if ($isPost) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
            curl_setopt($ch, CURLOPT_URL, $url);
        } else {
            if ($params) {
                if (is_array($params)) {
                    $params = http_build_query($params);
                }
                curl_setopt($ch, CURLOPT_URL, $url . '?' . $params);
            } else {
                curl_setopt($ch, CURLOPT_URL, $url);
            }
        }

        $response = curl_exec($ch);
        if ($response === false) {
            echo "cURL Error: " . curl_error($ch);
            return false;
        }
        curl_close($ch);
        return $response;
    }
}

if (!function_exists('curl')) {
    function curl($url, $params = false, $ispost = 0, $https = 0)
    {
        $httpInfo = array();
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/41.0.2272.118 Safari/537.36');
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);

        if ($https) {
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); // 对认证证书来源的检查
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); // 从证书中检查SSL加密算法是否存在
        }
        if ($ispost) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
            curl_setopt($ch, CURLOPT_URL, $url);
        } else {
            if ($params) {
                if (is_array($params)) {
                    $params = http_build_query($params);
                }
                curl_setopt($ch, CURLOPT_URL, $url . '?' . $params);
            } else {
                curl_setopt($ch, CURLOPT_URL, $url);
            }
        }

        $response = curl_exec($ch);
        if ($response === FALSE) {
//        echo "cURL Error: " . curl_error($ch);
            return false;
        }
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $httpInfo = array_merge($httpInfo, curl_getinfo($ch));
        curl_close($ch);
        return $response;
    }

    if (!function_exists('Autograph')) {
        //上传文件接口签名
        function Autograph()
        {
            $url = '';
            $current_domain = $_SERVER['HTTP_HOST'];
            if ($current_domain === "cube.liexindev.net" || $current_domain === "cube.liexin.net") {
                $url = 'http://api.liexin.com';
            } elseif ($current_domain === "szcube.ichunt.net") {
                $url = 'http://api.liexin.com';
            } elseif ($current_domain === "cube.ichunt.net") {
                $url = 'https://api.ichunt.com';
            }
            $data['k1'] = time();
            $data['k2'] = MD5(MD5($data['k1']) . Config('marketingconfig.UploadKey'));
            echo '<script>
            k1="' . $data['k1'] . '";
            k2="' . $data['k2'] . '";
            UploadImgUrl="' . $url . '"
        </script>';
        }
    }

    if (!function_exists('isStrictInteger')) {
        function isStrictInteger($value)
        {
            return preg_match('/^-?\d+$/', $value);
        }
    }

}
