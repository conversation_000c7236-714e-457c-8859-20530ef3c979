<?php

namespace App\Exports;

use App\Http\Models\AlikeSpuModel;
use App\Http\Models\StandardBrandModel;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;

class PriceActivityStatisticsExport implements FromCollection
{

    private $activityId;

    public function __construct($activityId)
    {
        $this->activityId = $activityId;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        //去获取相关的订单和用户信息
        $data = DB::connection('mongodb')->collection('price_activity_statistics')->where('activity_id', '=',
            (int)$this->activityId)->get();
        $excelData = [];
        $excelData[] = ["订单ID", "用户ID", "用户账号", "注册来源(PC/H5)", "订单总额"];
        foreach ($data as $key => $item) {
            $pf = $item["pf"] == 1 ? "PC" : "H5";
            if (empty($item['order_amount'])) {
                $item['order_amount'] = "0";
            }
            $excelItem = [$item["order_id"], $item["user_id"], $item["mobile"], $pf, $item['order_amount']];
            $excelData[] = $excelItem;
        }
        return $excelData;
    }
}
