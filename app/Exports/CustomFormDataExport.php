<?php

namespace App\Exports;

use App\Http\Models\AlikeSpuModel;
use App\Http\Models\Cube\CustomFormDataModel;
use App\Http\Models\StandardBrandModel;
use App\Http\Services\CustomFormDataService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class CustomFormDataExport implements FromCollection, ShouldAutoSize
{

    private $params;

    public function __construct($params)
    {
        $this->params = $params;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return new Collection($this->createData());
    }

    public function createData()
    {
        $params = json_decode($this->params, true);
        $model = new CustomFormDataModel();
        $query = $model->with(['activity' => function ($q) {
            $q->select(['id', 'activity_no', 'activity_name']);
        }])->orderBy('id', 'desc');
        $query = (new CustomFormDataService())->filter($query, $params);
        $data = $query->get()->toArray();
        $excelData = [];
        $excelData[] = ["序号", "活动编号", "活动名称", "客户账号", "公司名称", "联系人", "岗位", "是否新注册", "填写时间"];

        foreach ($data as $key => $item) {
            $formData = htmlspecialchars_decode($item['form_data']);
            $formData = json_decode($formData, true);
            $formDataText = '';
            $contactName = $position = '';
            if (!empty($formData)) {
                foreach ($formData as $k => $value) {
                    $formDataText .= $value['input_name'] . " : " . $value['input_value'] . ' ';
                    if ($k < count($formData) - 1) {
                        $formDataText .= PHP_EOL;
                    }
                    if ($k == 0) {
                        $contactName = $value['input_value'];
                    }
                    if ($k == 1) {
                        $position = $value['input_value'];
                    }
                }
            }

            $excelItem = [
                $item['id'],
                $item['activity']['activity_no'] ?? '',
                $item['activity']['activity_name'] ?? '',
                $item['mobile'] . "\t",
                $item['company_name'],
                $contactName,
                $position,
                $item['is_new_reg'] == 1 ? '是' : '否',
                date('Y-m-d H:i:s', $item['create_time']),
            ];
            $excelData[] = $excelItem;
        }
        return $excelData;
    }
}
