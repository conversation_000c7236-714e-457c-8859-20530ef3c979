<?php
namespace App\Imports;

use App\Exceptions\InvalidRequestException;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Collection;

class FrqImport implements ToArray{


    public static $titleArr = [
        0 => "采购需求ID(*)",
        1 => "商品编码",
        2 => "商品型号",
        3 => "品牌",
        4 => "采购数量(*)",
        5 => "采购单价(*)",
        6 => "预计交货时间",
        7 => "生产批次D/C",
        8 => "下单备注",
    ];

    public static $fieldArr = [
        0 => "frq_id",
        1 => "goods_sn",
        2 => "goods_name",
        3 => "brand_name",
        4 => "procured_qty",
        5 => "price",
        6 => "estimat_delivery_time",
        7 => "date_code",
        8 => "order_remark",
    ];

    public function Array(Array $tables){

    }





}