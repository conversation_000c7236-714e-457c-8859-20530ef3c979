<?php

namespace App\Http\Services;


use App\Http\Models\Cms\CmsDepartmentModel;
use App\Http\Models\Cms\CmsUserDepartmentModel;
use App\Http\Models\Cms\CmsUserInfoModel;

class CmsUserService
{


    //模糊检索用户
    public function getUserByPrefixName($name="",$type=1){
        $department_id = 0;
        if($type == "1"){//采购
            $department_id = CmsDepartmentModel::PURCHASE_DEPARTMENT_ID;
        }elseif($type == "2"){//销售
            $department_id = CmsDepartmentModel::SALES_DEPARTMENT_ID;
        }
        return CmsUserInfoModel::getSalesByPrefixName($name,$department_id);
    }

    public static function getCmsUserNameMap($user_ids)
    {
        $user_name_map = [];
        $user_list = \App\Http\Models\Cms\CmsUserInfoModel::getUsers($user_ids);
        if ($user_list) {
            foreach ($user_list as $user) {
                $user_name_map[$user['userId']] = $user['name'];
            }
        }
        return $user_name_map;
    }

    public static function getCmsUserInfoById($id){
        $userInfo = CmsUserInfoModel::getUserInfoById($id);
        return $userInfo;
    }

    public static function getUserList(){
        $on = CmsUserInfoModel::getUserList(0);
        $off = CmsUserInfoModel::getUserList(4);
        $onArr = [];
        $offArr = [];
        foreach($on as $k=>$v){
            $arr["value"] = $k;
            $arr["name"] = $v;
            $onArr[] = $arr;
        }
        foreach($off as $k=>$v){
            $arr["value"] = $k;
            $arr["name"] = $v;
            $offArr[] = $arr;
        }
        return [
            "on"=>(array)$onArr,
            "off"=>(array)$offArr,
        ];
    }
}
