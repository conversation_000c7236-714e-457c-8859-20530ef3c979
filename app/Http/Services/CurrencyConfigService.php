<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\StepPriceExtendModel;
use App\Http\Models\Cube\StepPriceNewModel;
use App\Http\Models\Cube\SupCurrencyNewModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;

class CurrencyConfigService  extends  BaseService
{

    public static function addCurrencyConfig($requestParams)
    {
        $requestParams["create_user_id"] = getAdminUserId();
        $requestParams["create_user"] = getAdminUserName();
        $requestParams["create_time"] = time();
        $requestParams["update_time"] = 0;
        //        dd($requestParams);
        $isExists = SupCurrencyNewModel::isExistsSameInfo($requestParams["sup_type"],  $requestParams["supplier_name"], $requestParams["supplier_id"], $requestParams["supplier_code"]);
        if ($isExists) {
            throw new InvalidRequestException("该供应商数据已经存在，请勿重复添加");
        }
        try {
            (new self)->startTransaction();
            SupCurrencyNewModel::insertData($requestParams);
            $bk = self::addCurrencyConfigCache($requestParams);
            if (!$bk) {
                throw new InvalidRequestException("新增数据缓存失败");
            }
            (new self)->commitTransaction();
        } catch (\Throwable $e) {
            (new self)->rollBackTransaction();
            throw new InvalidRequestException($e->getMessage());
        }
    }

    public static function getCurrencyConfigCache($data)
    {
        if ($data["sup_type"] == SupCurrencyNewModel::SUPTYPE_DAIGOU) {
            $key = $data["supplier_id"];
        } else {
            $key = $data["supplier_code"];
        }
        if (empty($key)) {
            throw new InvalidRequestException("新增缓存失败:supplier_id|supplier_code不能为空");
        }
        $res = Redis::connection(config("config.ladder_price_redis_connection_name"))
            ->hget(
                "magic_cube_supplier_currency",
                trim(strtoupper($key))
            );
        return $res;
    }

    public static function addCurrencyConfigCache($data)
    {
        if ($data["sup_type"] == SupCurrencyNewModel::SUPTYPE_DAIGOU) {
            $key = $data["supplier_id"];
        } else {
            $key = $data["supplier_code"];
        }
        if (empty($key)) {
            throw new InvalidRequestException("新增缓存失败:supplier_id|supplier_code不能为空");
        }
        $supCurrencyCacheValue = [];
        $supCurrencyCacheValue["currency"] = intval($data["currency"]);
        $supCurrencyCacheValue["customize_rate_rmb"] = floatval($data["customize_rate_rmb"]) > 0.000001 ? (float)$data["customize_rate_rmb"] : 0;
        $supCurrencyCacheValue["customize_rate_usd"] = floatval($data["customize_rate_usd"]) > 0.000001 ? (float)$data["customize_rate_usd"] : 0;
        $supCurrencyCacheValue["has_tax"] = intval($data["is_tax"]);
        $supCurrencyCacheValue["us_to_cn"] = isset($data["us_to_cn"]) ? intval($data["us_to_cn"]) : 0;
        $supCurrencyCacheValue["symbol"] = arrayGet(config("field.currency_sign"), intval($data["currency"]), "");
        return Redis::connection(config("config.ladder_price_redis_connection_name"))
            ->hset(
                "magic_cube_supplier_currency",
                trim(strtoupper($key)),
                json_encode($supCurrencyCacheValue)
            );
    }

    public static function delCurrencyConfigCache($data)
    {
        if ($data["sup_type"] == SupCurrencyNewModel::SUPTYPE_DAIGOU) {
            $key = $data["supplier_id"];
        } else {
            $key = $data["supplier_code"];
        }
        return Redis::connection(config("config.ladder_price_redis_connection_name"))
            ->hdel(
                "magic_cube_supplier_currency",
                trim(strtoupper($key))
            );
    }

    public static function getCurrencyConfigList($reqParams)
    {
        $data["supplier_keyword"] =  arrayGet($reqParams, "supplier_keyword", "", "trim");
        $data["currency"] =  arrayGet($reqParams, "currency");
        $pageInfo["page"] = arrayGet($reqParams, "page", 1, "intval");
        $pageInfo["limit"] = arrayGet($reqParams, "limit", static::_LIMIT_, "intval");
        $query = SupCurrencyNewModel::query();
        $buildEqualQueryData["currency"] = $reqParams["currency"] ?? "";

        if ($data["supplier_keyword"]) {
            $query->where(function ($q) use ($data) {
                $q->where("supplier_code", "like", "%" . $data["supplier_keyword"] . "%")->orWhere("supplier_name", "like", "%" . $data["supplier_keyword"] . "%");
            });
        }

        $list = $query->select("*")->buildEqualQuery($buildEqualQueryData)->orderBy("id", "desc")->orderBy("status", "desc")->getList($pageInfo["page"], $pageInfo["limit"]);
        $list = $list->toArray() ?? [];

        $arr = $list["data"] ?? [];
        foreach ($arr as $k => $v) {
            $arr[$k]["currency_cn"] =  arrayGet(SupCurrencyNewModel::$CURRENCY, $v["currency"], "");
            $arr[$k]["status_cn"] =  arrayGet(SupCurrencyNewModel::$STATUS_FORMAT, $v["status"], "");
            $arr[$k]["create_time"] =  dateDefault($v["create_time"]);
            $arr[$k]["update_time"] =  dateDefault($v["update_time"]);
            $arr[$k]["is_tax_cn"] = "";
            if ($arr[$k]["currency"] == 1) {
                $arr[$k]["is_tax_cn"] =   arrayGet(SupCurrencyNewModel::$IS_TAX_FORMAT, $v["is_tax"], "");
            }
            if (floatval($arr[$k]["customize_rate_rmb"]) == 0) {
                $arr[$k]["customize_rate_rmb"] = "系统";
            }

            if (floatval($arr[$k]["customize_rate_usd"]) == 0) {
                $arr[$k]["customize_rate_usd"] = "系统";
            }

            // 设置美金转人民币开关状态，默认为0
            $arr[$k]["us_to_cn"] = isset($v["us_to_cn"]) ? intval($v["us_to_cn"]) : 0;
        }
        return [$arr, $list["total"]];
    }

    public static function disableCurrencyConfig($reqParams)
    {
        $id = arrayGet($reqParams, "id");
        $type = arrayGet($reqParams, "type");
        if (!in_array($type, [-1, 1])) {
            throw new InvalidRequestException("参数 type|1启用 2禁用");
        }
        $info = SupCurrencyNewModel::getOne(intval($id));
        if (empty($info)) {
            throw new InvalidRequestException("没找到相关信息");
        }

        if ($type == $info["status"]) {
            return;
        }

        $updateStatus = intval($type);
        try {
            (new self)->startTransaction();
            if ($updateStatus == SupCurrencyNewModel::$STATUS["status_enable"]) {
                //启用
                $isExists = SupCurrencyNewModel::isExistsSameInfo($info["sup_type"], $info["supplier_name"], $info["supplier_id"], $info["supplier_code"]);
                if ($isExists) {
                    throw new InvalidRequestException("该供应商数据已经存在，无法启用 ");
                }
            }

            SupCurrencyNewModel::updateData(["id" => $id], ["status" => $updateStatus, "update_time" => time(), "update_user" => getAdminUserName()]);
            if ($updateStatus == SupCurrencyNewModel::$STATUS["status_enable"]) {
                //启用  新增缓存
                $bk = self::addCurrencyConfigCache($info);
                if (!$bk) {
                    throw new InvalidRequestException("新增数据缓存失败");
                }
            } else {
                //禁用   删除缓存
                if (self::getCurrencyConfigCache($info)) {
                    $bk = self::delCurrencyConfigCache($info);
                    if (!$bk) {
                        throw new InvalidRequestException("禁用后清除缓存失败");
                    }
                }
            }

            (new self)->commitTransaction();
        } catch (\Throwable $e) {
            (new self)->rollBackTransaction();
            throw new InvalidRequestException($e->getMessage());
        }
    }

    public static function updateUsToCnStatus($reqParams)
    {
        $id = arrayGet($reqParams, "id");
        $status = arrayGet($reqParams, "status");
        if (!in_array($status, [0, 1])) {
            throw new InvalidRequestException("参数 status|0关闭 1开启");
        }
        $info = SupCurrencyNewModel::getOne(intval($id));
        if (empty($info)) {
            throw new InvalidRequestException("没找到相关信息");
        }

        // 如果supplier_id=17，不允许修改
        if ($info["supplier_id"] == 17) {
            throw new InvalidRequestException("该供应商不允许修改美金转人民币状态");
        }

        if ($status == $info["us_to_cn"]) {
            return;
        }

        try {
            (new self)->startTransaction();

            // 更新数据库
            SupCurrencyNewModel::updateData(["id" => $id], ["us_to_cn" => $status, "update_time" => time(), "update_user" => getAdminUserName()]);

            // 更新缓存
            $info["us_to_cn"] = $status;
            self::addCurrencyConfigCache($info);
            (new self)->commitTransaction();
        } catch (\Throwable $e) {
            (new self)->rollBackTransaction();
            throw new InvalidRequestException($e->getMessage());
        }
    }
}
