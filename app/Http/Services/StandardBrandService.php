<?php


namespace App\Http\Services;


use App\Http\Models\Spu\BrandModel;
use App\Http\Models\Spu\StandardBrandModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class StandardBrandService
{
    public function checkStandardBrandNameList($standardBrandNameList = [])
    {
        //去数据库查询
        $validStandardBrandList = StandardBrandModel::whereIn('brand_name',
            $standardBrandNameList)
            ->pluck('brand_name', 'standard_brand_id')->toArray();
        $validStandardBrandMap = array_flip($validStandardBrandList);
        $invalidBrandNameList = [];
        $validStandardBrandNameList = [];
        $validStandardBrandIds = [];
        //判断哪些是错误的,哪些是正确的
        $redis = Redis::connection('sku');
        foreach ($standardBrandNameList as $key => $standardBrandName) {
            if (in_array($standardBrandName, array_values($validStandardBrandList))) {
                $validStandardBrandIds[] = $validStandardBrandMap[$standardBrandName];
                $validStandardBrandNameList[] = $standardBrandName;
                unset($standardBrandNameList[$key]);
            }
        }
        //剩下没有能直接找到标准品牌的,先当作普通品牌处理,然后去找出标准品牌,如果还是找不到,那就是无效品牌了
        foreach ($standardBrandNameList as $key => $checkStandardBrandName) {
            //先去找对应的标准品牌
            $brandId = BrandModel::where('brand_name', $checkStandardBrandName)->value('brand_id');
            $standardBrandId = $redis->hget('standard_brand_mapping', $brandId);
            if (empty($standardBrandId)) {
                $invalidBrandNameList[] = $checkStandardBrandName;
                continue;
            }
            $standardBrand = $redis->hget('standard_brand', $standardBrandId);
            $standardBrand = json_decode($standardBrand, true);
            $standardBrandName = Arr::get($standardBrand, 'brand_name', '');
            $standardBrandId = Arr::get($standardBrand, 'standard_brand_id', '');
            if (empty($standardBrandName)) {
                $invalidBrandNameList[] = $checkStandardBrandName;
                continue;
            }
            $validStandardBrandIds[] = $standardBrandId;
            $validStandardBrandNameList[] = $standardBrandName;
        }
        //整合数据,返回标准的数据和无效的数据
        return [
            'invalid_brand_name_list' => $invalidBrandNameList,
            'valid_brand_name_list' => $validStandardBrandNameList,
            'valid_brand_ids' => $validStandardBrandIds,
        ];
    }

    //根据品牌id获取标准品牌名字列表
    public function getStandardBrandNameListByBrandIds($brandIds)
    {
        $brandIds = explode(',', trim($brandIds, ','));
        if (empty($brandIds)) {
            return '';
        }
        $redis = Redis::connection('sku');
        $brands = $redis->hmget('standard_brand', $brandIds);
        $standardBrandNameList = [];
        foreach ($brands as $brand) {
            $brand = json_decode($brand, true);
            $standardBrandNameList[] = $brand['brand_name'];
        }
        return $standardBrandNameList ? implode(',', $standardBrandNameList) : '';
    }

    //搜索标准品牌
    public function searchStandardBrand($brandName)
    {
        $query = StandardBrandModel::where('brand_name', 'like', '%' . $brandName . '%');
        $count = $query->count();
        $brandList = $query->select(DB::raw('brand_name,standard_brand_id as brand_id'))->paginate(17);
        $brandList = $brandList ? $brandList->toArray() : [];
        $data = Arr::get($brandList, 'data');
        $lastPage = Arr::get($brandList, 'last_page');
        return [
            'errcode' => 0,
            'errmsg' => 'ok',
            'total' => $count,
            'count' => $count,
            'data' => $data,
            'last_page' => $lastPage
        ];
    }

    public function searchStandardBrandByOldBrand($brandName)
    {
        $brandList = DB::Connection("spu")->table("brand")->select(DB::raw("lie_brand_standard.brand_name,lie_brand_standard.standard_brand_id  as brand_id"))->rightJoin("brand_standard_mapping","brand.brand_id","=","brand_standard_mapping.brand_id")->rightJoin("brand_standard","brand_standard_mapping.standard_brand_id","=","brand_standard.standard_brand_id")->where('brand.brand_name', 'like', '%' . $brandName . '%')->groupBy("brand_standard.standard_brand_id")->paginate(17);
        $brandList = $brandList ? $brandList->toArray() : [];
        $data = Arr::get($brandList, 'data');
        $lastPage = Arr::get($brandList, 'last_page');

        return [
            'errcode' => 0,
            'errmsg' => 'ok',
            'total' => $brandList["total"],
            'count' => $brandList["total"],
            'data' => $data,
            'last_page' => $lastPage
        ];
    }

    public function searchStandardBrandEq($brandName)
    {
        if(empty($brandName)){
            return [];
        }

        $brandList = DB::Connection("spu")->table("brand")->select(DB::raw("lie_brand_standard.brand_name,lie_brand_standard.standard_brand_id  as brand_id"))->rightJoin("brand_standard_mapping","brand.brand_id","=","brand_standard_mapping.brand_id")->rightJoin("brand_standard","brand_standard_mapping.standard_brand_id","=","brand_standard.standard_brand_id")->where('brand.brand_name', $brandName)->groupBy("brand_standard.standard_brand_id")->first();


//        $brandList = BrandModel::where("brand_name",$brandName)->select("brand_id","brand_name")->first();
        return $brandList ;
    }
}