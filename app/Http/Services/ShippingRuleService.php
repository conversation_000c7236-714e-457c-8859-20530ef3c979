<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use Illuminate\Support\Facades\DB;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Models\Cube\ShippingRuleModel;
use App\Http\Models\Cube\OperationLogModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class ShippingRuleService
{
    /**
     * 获取运费规则列表
     * @param array $map
     * @return array
     */
    public function getShippingRuleList($map = [])
    {
        $query = ShippingRuleModel::where('status', '!=', ShippingRuleModel::STATUS_DELETED);

        if (!empty($map['supplier_id'])) {
            $query->where('supplier_id', $map['supplier_id']);
        }

        if (!empty($map['supplier_code'])) {
            $query->where('supplier_code', $map['supplier_code']);
        }

        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        if (!empty($map['type'])) {
            $query->where('type', $map['type']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $total = $query->count();
        $data = $query->orderBy('id', 'desc')
            ->with(['user_info', 'supplier_code', 'supplier'])
            ->paginate($map['limit'] ?? 10)
            ->toArray();
        foreach ($data['data'] as $key => &$value) {
            $value['create_time'] = $value['create_time'] ? date('Y-m-d H:i:s', $value['create_time']) : '';
            $value['update_time'] = $value['update_time'] ? date('Y-m-d H:i:s', $value['update_time']) : '';
            $value['type_name'] = config('field.ShippingRuleType')[$value['type']] ?? '';
            $value['rule_format'] = $this->formatRuleContent($value['type'], $value['rule']);
        }

        return [
            'data' => $data['data'],
            'total' => $data['total']
        ];
    }

    /**
     * 格式化运费规则内容
     * @param int $type 规则类型
     * @param array $ruleData 规则数据
     * @return string
     */
    public function formatRuleContent($type, $ruleData)
    {
        if (empty($ruleData)) {
            return '';
        }

        $content = '';
        switch ($type) {
            case 1: // 按订单金额配置
                if (!empty($ruleData['free_shipping_cny_amount']) || !empty($ruleData['shipping_cny_amount'])) {
                    $content .= "人民币：满{$ruleData['free_shipping_cny_amount']}元包邮，不满收取运费";
                    if (!empty($ruleData['is_change_rate_cny']) && $ruleData['is_change_rate_cny'] == 1) {
                        $content .= "按汇率转换";
                    } else {
                        $content .= "{$ruleData['shipping_cny_amount']}元";
                    }
                    $content .= "<br>";
                } else {
                    $content .= "人民币 : 按汇率转换" . "<br>";
                }
                if (!empty($ruleData['free_shipping_usd_amount']) || !empty($ruleData['shipping_usd_amount'])) {
                    $content .= "美金：满{$ruleData['free_shipping_usd_amount']}美元包邮，不满收取运费";
                    if (!empty($ruleData['is_change_rate_usd']) && $ruleData['is_change_rate_usd'] == 1) {
                        $content .= "按汇率转换";
                    } else {
                        $content .= "{$ruleData['shipping_usd_amount']}美元";
                    }
                    $content .= "<br>";
                } else {
                    $content .= "美金 : 按汇率转换" . "<br>";
                }
                break;

            case 2: // 按订单金额阶梯配置
                if (!empty($ruleData['base_cny_amount'])) {
                    $content .= "人民币：基础运费";
                    if (!empty($ruleData['ladder_is_change_rate_cny']) && $ruleData['ladder_is_change_rate_cny'] == 1) {
                        $content .= "（按汇率转换），每满{$ruleData['ladder_cny_amount']}元，加收（按汇率转换）运费";
                    } else {
                        $content .= "{$ruleData['base_cny_amount']}元，每满{$ruleData['ladder_cny_amount']}元，加收{$ruleData['ladder_additional_cny_amount']}元运费";
                    }
                    $content .= "<br>";
                } else {
                    $content .= "人民币 : 按汇率转换" . "<br>";
                }
                if (!empty($ruleData['base_usd_amount'])) {
                    $content .= "美金：基础运费";
                    if (!empty($ruleData['ladder_is_change_rate_usd']) && $ruleData['ladder_is_change_rate_usd'] == 1) {
                        $content .= "（按汇率转换），每满{$ruleData['ladder_usd_amount']}美元，加收（按汇率转换）运费";
                    } else {
                        $content .= "{$ruleData['base_usd_amount']}美元，每满{$ruleData['ladder_usd_amount']}美元，加收{$ruleData['ladder_additional_usd_amount']}美元运费";
                    }
                } else {
                    $content .= "美金 : 按汇率转换" . "<br>";
                }
                break;

            case 3: // 按订单型号数量配置
                if (!empty($ruleData['sku_count'])) {
                    foreach ($ruleData['sku_count'] as $index => $countRule) {
                        $maxText = $index == count($ruleData['sku_count']) - 1 ? "个以上" : "-{$countRule['max_count']}个";
                        $content .= "{$countRule['min_count']}{$maxText}";

                        // 人民币部分
                        $content .= "人民币：";
                        if (!empty($ruleData['count_is_change_rate_cny']) && $ruleData['count_is_change_rate_cny'] == 1) {
                            $content .= "按汇率转换";
                        } else {
                            $content .= "{$countRule['cny_fee']}元";
                        }
                        $content .= "<br>";

                        // 美金部分
                        $content .= "美金：";
                        if (!empty($ruleData['count_is_change_rate_usd']) && $ruleData['count_is_change_rate_usd'] == 1) {
                            $content .= "按汇率转换";
                        } else {
                            $content .= "{$countRule['usd_fee']}美元";
                        }

                        if ($index < count($ruleData['sku_count']) - 1) {
                            $content .= "<br>";
                        }
                    }
                }
                break;

            case 4: // 按订单型号重量配置
                if (!empty($ruleData['sku_weight'])) {
                    foreach ($ruleData['sku_weight'] as $index => $weightRule) {
                        $maxText = $index == count($ruleData['sku_weight']) - 1 ? "kg以上" : "-{$weightRule['max_weight']}kg";
                        $content .= "{$weightRule['min_weight']}{$maxText}<br>";

                        // 人民币部分
                        $content .= "人民币：";
                        if (!empty($ruleData['weight_is_change_rate_cny']) && $ruleData['weight_is_change_rate_cny'] == 1) {
                            $content .= "按汇率转换";
                        } else {
                            $content .= "{$weightRule['cny_fee']}元";
                        }
                        $content .= "<br>";

                        // 美金部分
                        $content .= "美金：";
                        if (!empty($ruleData['weight_is_change_rate_usd']) && $ruleData['weight_is_change_rate_usd'] == 1) {
                            $content .= "按汇率转换";
                        } else {
                            $content .= "{$weightRule['usd_fee']}美元";
                        }

                        if ($index < count($ruleData['sku_weight']) - 1) {
                            $content .= "<br><br>";
                        }
                    }
                }
                break;
        }

        return rtrim($content, "<br>");
    }

    /**
     * 根据ID获取运费规则
     * @param $id
     * @return mixed
     */
    public function getShippingRuleById($id)
    {
        return ShippingRuleModel::find($id);
    }

    /**
     * 保存运费规则
     * @param $data
     * @return bool
     * @throws \Exception
     */
    public function saveShippingRule($data)
    {
        $userId = request()->user->userId;
        $createName = request()->user->name;

        // 检查规则是否已存在
        $query = ShippingRuleModel::where('supplier_id', $data['supplier_id'])
            ->where('status', '!=', ShippingRuleModel::STATUS_DELETED);

        // 如果是更新操作，排除当前记录
        if (!empty($data['id'])) {
            $query->where('id', '!=', $data['id']);
        }

        // 如果supplier_id为17，还需要检查supplier_code
        if ($data['supplier_id'] == 17) {
            $query->where('supplier_code', $data['supplier_code']);

            // 检查是否存在
            if ($query->exists()) {
                throw new InvalidRequestException('该供应商编码已存在运费规则');
            }
        } else {
            // 检查是否存在
            if ($query->exists()) {
                throw new InvalidRequestException('该供应商已存在运费规则');
            }
        }

        //不同type取得字段不一样
        $ruleContent = [];
        switch ($data['type']) {
            case 1: //按订单金额配置
                $ruleContent['free_shipping_cny_amount'] = (float)$data['free_shipping_cny_amount'];
                $ruleContent['shipping_cny_amount'] = (float)$data['shipping_cny_amount'];
                $ruleContent['is_change_rate_cny'] = (int)$data['is_change_rate_cny'];
                $ruleContent['free_shipping_usd_amount'] = (float)$data['free_shipping_usd_amount'];
                $ruleContent['shipping_usd_amount'] = (float)$data['shipping_usd_amount'];
                $ruleContent['is_change_rate_usd'] = (int)$data['is_change_rate_usd'];
                break;

            case 2: //按订单金额阶梯配置
                # code..
                $ruleContent['base_cny_amount'] = (float)$data['base_cny_amount'];
                $ruleContent['ladder_cny_amount'] = (float)$data['ladder_cny_amount'];
                $ruleContent['ladder_additional_cny_amount'] = (float)$data['ladder_additional_cny_amount'];
                $ruleContent['ladder_is_change_rate_cny'] = (int)$data['ladder_is_change_rate_cny'];
                $ruleContent['base_usd_amount'] = (float)$data['base_usd_amount'];
                $ruleContent['ladder_usd_amount'] = (float)$data['ladder_usd_amount'];
                $ruleContent['ladder_additional_usd_amount'] = (float)$data['ladder_additional_usd_amount'];
                $ruleContent['ladder_is_change_rate_usd'] = (int)$data['ladder_is_change_rate_usd'];
                break;
            case 3: //按订单型号数量配置
                # code..
                $ruleContent['count_is_change_rate_cny'] = (int)$data['count_is_change_rate_cny'];
                $ruleContent['count_is_change_rate_usd'] = (int)$data['count_is_change_rate_usd'];
                foreach ($data['sku_count'] as $key => &$value) {
                    $value['min_count'] = (int)$value['min_count'];
                    $value['max_count'] = (int)$value['max_count'];
                    $value['cny_fee'] = (float)$value['cny_fee'];
                    $value['usd_fee'] = (float)$value['usd_fee'];
                }
                $ruleContent['sku_count'] = $data['sku_count'];
                break;
            case 4: //按订单型号重量配置
                # code..
                $ruleContent['weight_is_change_rate_cny'] = (int)$data['weight_is_change_rate_cny'];
                $ruleContent['weight_is_change_rate_usd'] = (int)$data['weight_is_change_rate_usd'];
                foreach ($data['sku_weight'] as $key => &$value) {
                    $value['min_weight'] = (float)$value['min_weight'];
                    $value['max_weight'] = (float)$value['max_weight'];
                    $value['cny_fee'] = (float)$value['cny_fee'];
                    $value['usd_fee'] = (float)$value['usd_fee'];
                }
                $ruleContent['sku_weight'] = $data['sku_weight'];
                break;
        }
        $ruleContent = \json_encode($ruleContent);
        // 开启事务
        DB::beginTransaction();
        try {
            if (!empty($data['id'])) {
                // 获取原规则数据，用于记录日志
                $oldRule = ShippingRuleModel::find($data['id']);
                if (empty($oldRule)) {
                    throw new InvalidRequestException('运费规则不存在');
                }

                // 记录修改前的数据，用于记录日志
                $oldRuleData = $oldRule->rule;
                $oldRuleFormatted = $this->formatRuleContent($oldRule->type, $oldRuleData);

                // 记录操作日志
                $logContent = '修改运费规则，原数据：' . $oldRuleFormatted;
                OperationLogModel::insert([
                    'rule_id' => $data['id'],
                    'operator_id' => $userId,
                    'operation_time' => time(),
                    'type' => OperationLogModel::TYPE_SHIPPING_RULE,
                    'content' => $logContent,
                    'create_uid' => $userId,
                    'create_time' => time(),
                    'create_name' => $createName,
                ]);
                // 更新规则
                $rule['id'] = $data['id'];
                $rule['supplier_code'] = $data['supplier_id'] == 17 ? $data['supplier_code'] : '';
                $rule['type'] = $data['type'];
                $rule['supplier_id'] = $data['supplier_id'];
                $rule['rule'] = $ruleContent;
                $rule['update_name'] = request()->user->name;
                $rule['update_time'] = time();
                $result = ShippingRuleModel::where('id', $data['id'])->update($rule);
            } else {
                // 新增规则
                $rule['supplier_id'] = $data['supplier_id'];
                $rule['supplier_code'] = $data['supplier_id'] == 17 ? $data['supplier_code'] : '';
                $rule['create_name'] = request()->user->name;
                $rule['create_time'] = time();
                $rule['status'] = ShippingRuleModel::STATUS_ENABLE;
                $rule['type'] = $data['type'];
                $rule['rule'] = $ruleContent;
                $result = ShippingRuleModel::insert($rule);

                // 获取新插入的ID
                $newRuleId = DB::getPdo()->lastInsertId();

                // 记录操作日志
                OperationLogModel::insert([
                    'rule_id' => $newRuleId,
                    'operator_id' => $userId,
                    'operation_time' => time(),
                    'type' => OperationLogModel::TYPE_SHIPPING_RULE,
                    'content' => '新建运费规则',
                    'create_uid' => $userId,
                    'create_time' => time(),
                    'create_name' => $createName,
                ]);
            }

            DB::commit();
            return $result;
        } catch (\Exception $e) {
            dd($e->getMessage());
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除运费规则
     * @param $id
     * @return bool
     * @throws \Exception
     */
    public function deleteShippingRule($id)
    {
        $userId = request()->user->userId;
        $createName = request()->user->name;
        $now = time();

        // 开启事务
        DB::beginTransaction();
        try {
            $rule = ShippingRuleModel::find($id);
            if (empty($rule)) {
                throw new InvalidRequestException('运费规则不存在');
            }

            // 软删除
            $rule->status = ShippingRuleModel::STATUS_DELETED;
            $rule->update_uid = $userId;
            $rule->update_name = $createName;
            $rule->update_time = $now;
            $rule->save();

            // 记录操作日志
            OperationLogModel::insert([
                'rule_id' => $rule->id,
                'operator_id' => $userId,
                'operation_time' => $now,
                'type' => OperationLogModel::TYPE_SHIPPING_RULE,
                'content' => '删除运费规则',
                'create_uid' => $userId,
                'create_time' => $now,
                'create_name' => $createName,
            ]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 修改运费规则状态
     * @param $id
     * @param $status
     * @return bool
     * @throws \Exception
     */
    public function changeStatus($id, $status)
    {
        $userId = request()->user->userId;
        $createName = request()->user->name;
        $now = time();

        // 开启事务
        DB::beginTransaction();
        try {
            $rule = ShippingRuleModel::find($id);
            if (empty($rule)) {
                throw new InvalidRequestException('运费规则不存在');
            }

            // 修改状态
            $rule->status = $status;
            $rule->update_name = $createName;
            $rule->update_time = $now;
            $rule->save();

            // 记录操作日志
            $content = $status == ShippingRuleModel::STATUS_ENABLE ? '启用运费规则' : '禁用运费规则';
            OperationLogModel::insert([
                'rule_id' => $rule->id,
                'operator_id' => $userId,
                'operation_time' => $now,
                'type' => OperationLogModel::TYPE_SHIPPING_RULE,
                'content' => $content,
                'create_uid' => $userId,
                'create_time' => $now,
                'create_name' => $createName,
            ]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 获取操作日志
     * @param $id
     * @return mixed
     */
    public function getOperationLogs($id)
    {
        return OperationLogModel::where('id', $id)
            ->where('type', OperationLogModel::TYPE_SHIPPING_RULE)
            ->orderBy('operation_time', 'desc')
            ->with('operator')
            ->get()
            ->map(function ($item) {
                $item->operator_name = $item->operator->name ?? '';
                return $item;
            });
    }
}
