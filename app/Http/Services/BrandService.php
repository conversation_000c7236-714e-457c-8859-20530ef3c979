<?php


namespace App\Http\Services;


use App\Http\Models\Self\SelfBrandModel;
use App\Http\Models\Spu\BrandModel;
use App\Http\Models\Spu\StandardBrandModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class BrandService
{
    public function checkBrandNameList($brandNameList = [], $brandType = 1)
    {
        $invalidBrandNameList = [];
        $validBrandNameList = [];
        $validStandardBrandIds = [];
        foreach ($brandNameList as $key => $checkBrandName) {
            if ($brandType == 1) {
                //先去找对应的品牌
                $brandId = BrandModel::where('brand_name', $checkBrandName)->value('brand_id');
                if (empty($brandId)) {
                    $invalidBrandNameList[] = $checkBrandName;
                    continue;
                }
                $validStandardBrandIds[] = $brandId;
                $validBrandNameList[] = $checkBrandName;
            } else {
                $brandId = SelfBrandModel::where('brand_name', $checkBrandName)->value('brand_id');
                if (empty($brandId)) {
                    $invalidBrandNameList[] = $checkBrandName;
                    continue;
                }
                $validStandardBrandIds[] = $brandId;
                $validBrandNameList[] = $checkBrandName;
            }
        }
        //整合数据,返回标准的数据和无效的数据
        return [
            'invalid_brand_name_list' => $invalidBrandNameList,
            'valid_brand_name_list' => $validBrandNameList,
            'valid_brand_ids' => $validStandardBrandIds,
        ];
    }

    //根据品牌id获取标准品牌名字列表
    public function getBrandNameListByBrandIds($brandIds)
    {
        $brandIds = explode(',', trim($brandIds, ','));
        if (empty($brandIds)) {
            return '';
        }
        $redis = Redis::connection('sku');
        $brands = $redis->hmget('brand', $brandIds);
        $brandNameList = [];
        foreach ($brands as $brand) {
            $brandNameList[] = $brand;
        }
        return $brandNameList ? implode(',', $brandNameList) : '';
    }
}