<?php


namespace App\Http\Services;


use App\Http\Models\Spu\BrandModel;
use App\Http\Models\Spu\StandardBrandModel;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class SelfBrandService
{
    //根据品牌id获取标准品牌名字列表
    public function getBrandNameListByBrandIds($brandIds)
    {
        $brandIds = explode(',', trim($brandIds, ','));
        if (empty($brandIds)) {
            return '';
        }
        $redis = Redis::connection('sku');
        $brands = $redis->hmget('Self_Brand', $brandIds);
        $brandNameList = [];
        foreach ($brands as $brand) {
            $brand = json_decode($brand, true);
            $brandNameList[] = $brand['brand_name'];
        }
        return $brandNameList ? implode(',', $brandNameList) : '';
    }
}
