<?php


namespace App\Http\Services;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\CouponModel;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Models\Cube\UserCouponModel;
use App\Http\Models\Liexin\UserMainModel;
use Illuminate\Support\Facades\Redis;

class UserCouponService
{
    public function getUserCouponList($map)
    {
        $model = new UserCouponModel();
        $orgId = request()->user->org_id;
        $query = $model->orderBy('create_time', 'desc');
        //
        if (!PermService::hasPerm('cube_coupon_viewAllCoupon') && request()->user->userId != 1000) {
            if (in_array($orgId, config('field.IedgeOrgIdList'))) {
                $query->whereIn('org_id', config('field.IedgeOrgIdList'));
            } else {
                $query->where('org_id', $orgId);
            }
        }
        if (!empty($map['user_account'])) {
            $userId = UserService::getUserIdByAccount($map['user_account']);
            $query->where('user_id', $userId);
        }

        if (!empty($map['coupon_name'])) {
            $query->whereHas('coupon', function ($q) use ($map) {
                $q->where('coupon_name', 'like', "%${map['coupon_name']}%");
            });
        }
        if (!empty($map['coupon_sn'])) {
            $query->whereHas('coupon', function ($q) use ($map) {
                $q->where('coupon_sn', 'like', "%${map['coupon_sn']}%");
            });
        }
        if (!empty($map['coupon_desc'])) {
            $query->whereHas('coupon', function ($q) use ($map) {
                $q->where('coupon_desc', 'like', "%${map['coupon_desc']}%");
            });
        }

        if (!empty($map['adtag'])) {
            $query->where('adtag', 'like', "%${map['adtag']}%");
        }

        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        if (!empty($map['use_time'])) {
            $startTime = strtotime(explode('~', $map['use_time'])[0]);
            $endTime = strtotime(explode('~', $map['use_time'])[1]);
            $query->whereBetween('use_time', [$startTime, $endTime]);
        }

        $limit = !empty($map['limit']) ? $map['limit'] : 10;
        if (!empty($map['is_export'])) {
            $count = $query->count();
            if ($count > 10000) {
                throw new InvalidRequestException('导出数量不能超过10000条');
            }
            $result['data'] = $query->with([
                'user',
                'coupon',
            ])->get()->toArray();
            $result['total'] = 0;
        } else {
            $result = $query->with([
                'user',
                'coupon',
            ])->paginate($limit)->toArray();
        }
        return $result;
    }

}
