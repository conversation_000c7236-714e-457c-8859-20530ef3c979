<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\IdSender\IdSender;
use App\Http\Models\Cube\ChannelDiscountModel;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Models\Cube\PriceWarningModel;
use App\Http\Models\Cube\StepPriceExtendModel;
use App\Http\Models\Cube\StepPriceNewModel;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use Illuminate\Support\Facades\Redis;

class PriceWarningService extends  BaseService
{
    const ZHUAN_YING = 2;
    const DAI_GOU = 1;


    /**
     * Notes:活动增删改查触发  计算盈利率 目前同步计算  以后有时间改为异步
     * User: sl
     * Date: 2023-03-13 11:35
     * @param $priceActivityId
     * @throws InvalidRequestException
     */
    public static function activityTriggerPriceWarning($priceActivityId){
        try{

            self::calculatePriceWarningByActivity($priceActivityId);
        }catch (\Throwable $e){
            //记录日志 告警提醒
            PriceWarningModel::delPriceWarningBypriceActivityId($priceActivityId);

        }

    }

    /**
     * Notes:售价组增删改查触发  计算盈利率 目前同步计算  以后有时间改为异步
     * User: sl
     * Date: 2023-03-13 11:35
     * @param $priceActivityId
     * @throws InvalidRequestException
     */
    public static function salePriceGroupTriggerPriceWarning($sppeId){
        try{
            self::calculatePriceWarningBySalePriceGroup($sppeId);
        }catch (\Throwable $e){
            //记录日志 告警提醒


        }

    }

    /**
     * Notes:根据活动 统计盈利率
     * User: sl
     * Date: 2023-03-11 14:00
     */
    public static function calculatePriceWarningByActivity($priceActivityId){
        $activityInfo = PriceActivityModel::where("id",intval($priceActivityId))->first();
        $activityInfo = $activityInfo ? $activityInfo->toArray() : [];
        if(empty($activityInfo)){
           throw  new InvalidRequestException("没有找到该活动相关信息");
        }
        if($activityInfo["status"] == -1 || ($activityInfo["end_time"] < time())){
            //删除活动关联的盈利率
            PriceWarningModel::delPriceWarningBypriceActivityId($priceActivityId);
            return ;
        }
        $supplierCodes = !empty($activityInfo["canals"]) ? explode(",",$activityInfo["canals"]) : [];
        $supplierIds = !empty($activityInfo["supplier_ids"]) ? explode(",",$activityInfo["supplier_ids"]) : [];
        $activityRatio = round($activityInfo["ratio"]/100,4);//0.9525
        $standard_brand_ids = !empty($activityInfo["standard_brand_ids"]) ? explode(",",$activityInfo["standard_brand_ids"]) : [];
        $supplierCodes = array_filter_unique($supplierCodes);//供应商编码
        $supplierIds = array_filter_unique($supplierIds);//供应商id
        $activityStandardBrandIds = array_filter_unique($standard_brand_ids);//标准品牌列表

        $supType = 0;//代购
        //猎芯专营活动
        if(!empty($supplierIds) && in_array(config("config.liexin_zhuanying_supplier_id"),$supplierIds) && !empty($supplierCodes)){
            $supType = self::ZHUAN_YING;//专营
        }
        //渠道代购活动
        if(!empty($supplierIds) && !in_array(config("config.liexin_zhuanying_supplier_id"),$supplierIds)){
            $supType = self::DAI_GOU;//代购
        }
        if($supType == 0){
            throw  new InvalidRequestException("该活动无法判断是专营还是代购");
        }
        try{
            switch ($supType){
                case self::DAI_GOU://代购
                    self::channelDaiGouRateOfReturn($supplierIds,$activityStandardBrandIds,$activityRatio,$priceActivityId,$activityInfo);
                    break;
                case self::ZHUAN_YING://专营
                    self::ZhuanYingRateOfReturn($supplierCodes,$activityStandardBrandIds,$activityRatio,$priceActivityId,$activityInfo);
                    break;
            }

        }catch (\Throwable $e){
            throw new InvalidRequestException($e->getMessage());
        }

    }


    /**
     * Notes:通过商品售价组计算活动盈利率
     * User: sl
     * Date: 2023-03-13 10:27
     * @param $sppeId
     */
    public static function calculatePriceWarningBySalePriceGroup($sppeId){
        //售价组信息
        $stepPriceNewInfo = StepPriceNewModel::getStepPriceInfoById($sppeId);
//        dd($stepPriceNewInfo);
        if(empty($stepPriceNewInfo)){
            throw new InvalidRequestException("没有找到相关的售价组信息");
        }
        if($stepPriceNewInfo["status"] == StepPriceNewModel::$STATUS["status_disable"]){
            //禁用的售价组 则删除预警记录
            PriceWarningModel::delPriceWarningBySppeId($sppeId);
            return ;
        }
        $supplierId = $stepPriceNewInfo["supplier_id"];
        $supplierCode = $stepPriceNewInfo["supplier_code"];

        $priceActivityQuery =  PriceActivityModel::where("status",1)->where("end_time",">=",time());
        //代购
        if($stepPriceNewInfo["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU){

            $priceActivityQuery->whereRaw(sprintf(" FIND_IN_SET(%s,supplier_ids)",intval($supplierId)));
        }else{
            //专营
            $priceActivityQuery->whereRaw(sprintf("FIND_IN_SET('%s',canals)",strtoupper($supplierCode)));
        }
        $activityList = $priceActivityQuery->get()->toArray();
        foreach($activityList as $activity){
            try{
                self::calculatePriceWarningByActivity($activity["id"]);
            }catch (\Throwable $e){
                //记录日志和告警
            }

        }


    }

    /**
     * [ ( 1 + 销售利润 ) * 活动折扣 - 1 ] *100%
     * Notes:计算渠道盈利率 代购
     * User: sl
     * Date: 2023-03-11 14:43
     */
    public static function channelDaiGouRateOfReturn($supplierIds,$activityStandardBrandIds,$activityRatio,$priceActivityId,$activityInfo){
        //获取所有售价组信息
        $stepPriceNewList = StepPriceNewModel::where("status",1)
            ->whereIn("supplier_id",$supplierIds)
            ->with("stepPriceExtend")
            ->get()->toArray();
        //如果活动参与品牌不在售价组中 则不计算该售价组
        $finalstepPriceNewList = self::computBrand($stepPriceNewList,$activityStandardBrandIds);
        self::calculateProfit($priceActivityId,$activityRatio,$finalstepPriceNewList,
            [
                "currency_rmb"=>$activityInfo["currency_rmb"],
                "currency_us"=>$activityInfo["currency_us"],
            ]
        );
    }





    /**
     * [ ( 1 + 销售利润 ) * 活动折扣 - 1 ] *100%
     * Notes:计算专营盈利率
     * User: sl
     * Date: 2023-03-11 14:44
     */
    public static function ZhuanYingRateOfReturn($supplierCodes,$activityStandardBrandIds,$activityRatio,$priceActivityId,$activityInfo){
        //获取所有售价组信息
        $stepPriceNewList = StepPriceNewModel::where("status",1)
            ->whereIn("supplier_code",$supplierCodes)
            ->with("stepPriceExtend")
            ->get()->toArray();
        ////如果活动参与品牌不在售价组中 则不计算该售价组
        $finalstepPriceNewList = self::computBrand($stepPriceNewList,$activityStandardBrandIds);
        self::calculateProfit($priceActivityId,$activityRatio,$finalstepPriceNewList,[
            "currency_rmb"=>$activityInfo["currency_rmb"],
            "currency_us"=>$activityInfo["currency_us"],
        ]);
    }

    ////如果活动参与品牌不在售价组中 则不计算该售价组
    public static function computBrand($stepPriceNewList,$activityStandardBrandIds){
        $finalstepPriceNewList = $stepPriceNewList;
        if(!empty($activityStandardBrandIds)){
            foreach($stepPriceNewList as $k=>$item){
                $brandIds = $item["step_price_extend"]["brand_ids"] ?? "";
                if(empty($brandIds)){
                    //售价组所有品牌都参与 需要计算盈利率
                    continue;
                }
                $brandIds = explode(",",$brandIds);
                $intersectBrandIds = array_intersect($brandIds,$activityStandardBrandIds);
                $intersectBrandIds = array_filter_unique($intersectBrandIds);
                if(count($intersectBrandIds) <= 0){
                    unset($finalstepPriceNewList[$k]);
                }
            }
        }
        return $finalstepPriceNewList;
    }

    /**
     * Notes:
     * User: sl
     * Date: 2023-03-11 17:40
     * @param $priceActivityId  //活动id
     * @param $activityRatio //活动折扣 0.95
     * @param $stepPriceNewList //活动关联的售价组信息
     */
    public static function  calculateProfit($priceActivityId,$activityRatio,$stepPriceNewList,$extroParams=[]){

        foreach($stepPriceNewList as $item){
            //利润阶梯
            $stepPriceData = json_decode($item["step_price_data_json"],true);
            $estimatedProfitRate= [];
            $negativeProfit = false;
            foreach($stepPriceData as $k=>$ladderRatio){
                $rateOfReturnCny = (1+$ladderRatio["ratio"]/100)*$activityRatio - 1;
                $rateOfReturnCny = round($rateOfReturnCny,4);

                $rateOfReturnUsd = (1+$ladderRatio["ratio_usd"]/100)*$activityRatio - 1;
                $rateOfReturnUsd = round($rateOfReturnUsd,4);
                if(!empty($extroParams["currency_rmb"])){
                    $estimatedProfitRate[$k]["ratio"] = round($rateOfReturnCny*100,4);
                }
                if(!empty($extroParams["currency_us"])){
                    $estimatedProfitRate[$k]["ratio_usd"] = round($rateOfReturnCny*100,4);
                }

                if(bccomp($rateOfReturnCny,0,4) != 1 || bccomp($rateOfReturnUsd,0,4) != 1){
                    $negativeProfit = true;
                    //盈利率小于等于0 告警
                }
            }
            if($negativeProfit){
                //如果是负利润 则新增或者修改预警记录
                self::updateOrCreatePriceWarnIngInfo([
                    "sppe_id"=> $item["sppe_id"],
                    "price_activity_id"=>$priceActivityId,
                    "estimated_profit_rate"=>$estimatedProfitRate,
                ]);
                //删除去掉的渠道或者供应商关联的价格预警

            }else{
                //不亏本并且数据存在的情况下  则更新状态为已处理
                if(!empty($extroParams["price_warning_id"])){
                    //单条预警记录重新验证情况下
                    PriceWarningModel::where("price_warning_id",intval($extroParams["price_warning_id"]))
                        ->update([
                            "status"=>PriceWarningModel::$STATUS["status_processed"],
                            "hander" => getAdminUserName(),
                            "hander_id"=>getAdminUserId(),
                            "warn_time"=>time(),
                            "update_time"=>time(),
                            "remark"=>"重新验证后未亏本",
                        ]);
                }else{
                    //系统处理 不亏本情况下  删除记录
                    PriceWarningModel::where("price_activity_id",$priceActivityId)
                        ->where("sppe_id",$item["sppe_id"])->update([
                            "status"=>PriceWarningModel::$STATUS["status_processed"],
                            "hander" => "系统触发",
                            "hander_id"=>"1000",
                            "warn_time"=>time(),
                            "update_time"=>time(),
                            "remark"=>"重新验证后未亏本",
                        ]);
                }
            }

        }
        if(empty($extroParams["revalidation"])){
            $spppeIds = \Arr::pluck($stepPriceNewList,"sppe_id");
            PriceWarningModel::where("price_activity_id",$priceActivityId)->whereNotIn("sppe_id",$spppeIds)->delete();
        }

    }

    /***
     * Notes:已处理=》新增，待处理=》更新和新增，忽略的跳过
     * User: sl
     * Date: 2023-03-11 16:10
     * @param $data
     */
    public static function updateOrCreatePriceWarnIngInfo($data){
        $priceActivityId = $data["price_activity_id"];
        $sppeId = $data["sppe_id"];
        $estimatedProfitRate = $data["estimated_profit_rate"];
        $priceActivityInfo = PriceActivityModel::getPriceActivityInfo($priceActivityId);
        $stepPriceNewInfo = StepPriceNewModel::getStepPriceInfoById($sppeId);
        if(empty($priceActivityInfo) || empty($stepPriceNewInfo)){
            //删除对应的预警记录
            PriceWarningModel::where("price_activity_id",$priceActivityId)
                ->where("sppe_id",$sppeId)->delete();
            return;
        }
        $originInfo = PriceWarningModel::getPriceWarning($priceActivityId,$sppeId);
        if(!empty($originInfo) && $originInfo["status"] == PriceWarningModel::statusIgnore){
            //忽略状态的盈利率告警的跳过
            return;
        }
        ////待处理,更新
        if(!empty($originInfo) && $originInfo["status"] == PriceWarningModel::statusPending){
            PriceWarningModel::where("price_activity_id",$priceActivityId)
                ->where("sppe_id",$sppeId)
                ->update([
                "activity_name"=>$priceActivityInfo["activity_name"],
                "warn_time"=>time(),
                "sale_ratio"=>$stepPriceNewInfo["step_price_data_json"],
                "estimated_profit_rate"=>json_encode($estimatedProfitRate),
                "update_time"=>time()
            ]);
        }else{
            //已处理 新增
            PriceWarningModel::insertGetId([
                "price_activity_id"=> $priceActivityId,
                "activity_name"=>$priceActivityInfo["activity_name"],
                "sppe_id"=>$sppeId,
                "sppe_sn"=>$stepPriceNewInfo["sppe_sn"],
                "supplier_name"=>$stepPriceNewInfo["supplier_name"],
                "supplier_id"=>$stepPriceNewInfo["supplier_id"],
                "supplier_code"=>$stepPriceNewInfo["supplier_code"],
                "status"=> 1,
                "warn_time"=>time(),
                "sale_ratio"=>$stepPriceNewInfo["step_price_data_json"],
                "estimated_profit_rate"=>json_encode($estimatedProfitRate),
                "create_time"=>time()
            ]);
        }

    }

    public static function getPriceWarningList($paramsData){
        $pageInfo["page"] = arrayGet($paramsData, "page", 1, "intval");
        $pageInfo["limit"] = arrayGet($paramsData, "limit", static::_LIMIT_, "intval");
        $list = self::buildPriceWarningList($paramsData, $pageInfo);
        $arr = $list["data"] ?? [];
        foreach($arr as $k=>$v){
            $arr[$k]["goods_name"] = $v["step_price_extend"]["goods_name"] ?? "";
            $arr[$k]["brand"] = $v["step_price_extend"]["brand"] ?? "";
            $arr[$k]["brand_ids"] = $v["step_price_extend"]["brand_ids"] ?? "";
            $arr[$k]["eccn"] = $v["step_price_extend"]["eccn"] ?? "";
            $arr[$k]["file_url"] = $v["step_price_extend"]["file_url"] ?? "";
            unset($arr[$k]["step_price_extend"]);
            $v["sup_type"] = $arr[$k]["sup_type"] = $v["step_price_new"]["sup_type"] ?? "";
            $v["order"]  = $arr[$k]["order"] = $v["step_price_new"]["order"] ?? "";
            unset($arr[$k]["step_price_new"]);


            $arr[$k]["supplier_id_or_code"] = "";
            if($v["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU){
                $arr[$k]["supplier_id_or_code"] =  $v["supplier_id"];
            }elseif($v["sup_type"] == StepPriceNewModel::SUPTYPE_ZHUANYING){
                $arr[$k]["supplier_id_or_code"] =  $v["supplier_code"];
            }
            $arr[$k]["is_default"] = 0;
            if($v["order"] == 0){
                $arr[$k]["is_default"] = 1;
            }

            $arr[$k]["warn_time"] =  dateDefault($v["warn_time"]);
            $arr[$k]["create_time"] =  dateDefault($v["create_time"]);
            $arr[$k]["hand_time"] =  dateDefault($v["hand_time"]);

            $saleProfit = json_decode($v["sale_ratio"],true);
            $estimatedProfitRate = json_decode($v["estimated_profit_rate"],true);

            $arr[$k]["saleProfit"] = [];
            foreach($saleProfit as $saleProfitKey=>$saleProfitVal){
                $arr[$k]["saleProfit"][$saleProfitKey]["ratio"] = $saleProfitVal["ratio"];
                $arr[$k]["saleProfit"][$saleProfitKey]["ratio_usd"] = $saleProfitVal["ratio_usd"];
            }
            $arr[$k]["estimateProfit"] = [];
            foreach($estimatedProfitRate as $estimatedProfitRateKey=>$estimatedProfitRateVal){
                $arr[$k]["estimateProfit"][$estimatedProfitRateKey]["ratio"] = $estimatedProfitRateVal["ratio"] ?? 0;
                $arr[$k]["estimateProfit"][$estimatedProfitRateKey]["ratio_usd"] = $estimatedProfitRateVal["ratio_usd"] ?? 0;
            }
            $arr[$k]["saleProfit"] = self::getSaleProfit(json_encode($arr[$k]["saleProfit"]));
            $arr[$k]["estimateProfit"] = self::getSaleProfit(json_encode($arr[$k]["estimateProfit"]));

            $arr[$k]["status_format"] = \Arr::get(PriceWarningModel::$STATUS_FORMAT,$v["status"],"");


            $arr[$k]["activity_name_href"] = sprintf('<a class="alink" ew-href="/web/priceActivity/priceActivityList?activity_name=%s" ew-title="%s">%s</a>',$v["activity_name"],$v["activity_name"],$v["activity_name"]);

            $arr[$k]["sppe_sn_href"] = sprintf('<a class="alink" ew-href="/web/price/goodsSalePriceGroup?sppe_sn=%s" ew-title="%s">%s</a>',$v["sppe_sn"],$v["sppe_sn"],$v["sppe_sn"]);
        }

        return [$arr,$list["total"]];
    }

    public static function getSaleProfit($step_price_data_json){
        $step_price_data_json = json_decode($step_price_data_json,true);
        $arr = [];
        foreach ($step_price_data_json as $k=>$v){
            $arr[$k]["order"] = \Arr::get(config("config.num_format"),$k,"");
            $arr[$k]["ratio"] = sprintf("%s",$v["ratio"]);
            $arr[$k]["ratio_usd"] = sprintf("%s",$v["ratio_usd"]);
        }
        return array_reverse($arr);
    }

    public static function buildPriceWarningList($reqParams, $pageInfo){
        $query = PriceWarningModel::select("*");
        $buildEqualQueryData = [];
        if($reqParams["sppe_sn"] !== ""){
            $buildEqualQueryData["sppe_sn"] = $reqParams["sppe_sn"];
        }

        if($reqParams["status"] !== ""){
            $buildEqualQueryData["status"] = (int)$reqParams["status"];
        }

        $buildTimeQueryData = [];
        if ($reqParams["warn_time"] != "") {
            $warn_time = explode("~", $reqParams["warn_time"]);
            $buildTimeQueryData["warn_time"]["begin_time"] = isset($warn_time[0]) ? $warn_time[0] : "";
            $buildTimeQueryData["warn_time"]["end_time"] = isset($warn_time[1]) ? $warn_time[1] : "";
        }


        if($reqParams["activity_name"] != ""){
            $activityNames = explode(",",$reqParams["activity_name"]);
            $query->whereIn("activity_name",$activityNames);
        }

        if($reqParams["price_activity_id"] != ""){
            $activityIds = explode(",",$reqParams["price_activity_id"]);
            $query->whereIn("price_activity_id",$activityIds);
        }
        $list = $query->buildEqualQuery($buildEqualQueryData)
            ->buildTimeQuery($buildTimeQueryData)->orderBy("price_warning_id", "desc")
            ->with("stepPriceExtend")->with("stepPriceNew")
            ->getList($pageInfo["page"], $pageInfo["limit"]);
        return $list->toArray();
    }

    public static function dealWithPriceWarning($requestParams){
        $data["price_warning_id"] =  arrayGet($requestParams, "price_warning_id", 0, "intval");
        $data["status"] =  arrayGet($requestParams, "status", 0, "intval");
        $data["remark"] =  arrayGet($requestParams, "remark", "", "trim");
        $priceWarnInfo = PriceWarningModel::getPriceWarningById(intval($data["price_warning_id"]));
        if(empty($priceWarnInfo)){
            throw  new InvalidRequestException("没找到相关的价格预警信息");
        }
        if(!in_array($data["status"],[2,3])){
            throw  new InvalidRequestException("请选择处理的状态后再提交");
        }
        PriceWarningModel::where("price_warning_id",intval($data["price_warning_id"]))->update([
            "status"=>$data["status"],
            "remark"=>truncStr($data["remark"],80),
            "hander"=>getAdminUserName(),
            "hander_id"=>getAdminUserId(),
            "hand_time"=>time(),
            "update_time"=>time(),
        ]);
        \Log::channel("priceWarning")->info("---------------价格预警标记------------------");
        \Log::channel("priceWarning")->info(sprintf("处理结果：%s",json_encode($data)));

        ActionLogService::addLog(ActionLogService::TYPE_ACTION_PRICE_WARNING_SIGN,$data["price_warning_id"],
            [
                "message"=>sprintf("价格预警标记"),
                "data"=>sprintf("标记状态:%s,备注:%s",\Arr::get(PriceWarningModel::$STATUS_FORMAT,$data["status"],"-"),$data["remark"])
            ]);

    }

    public static function recertifyPriceWarning($priceWarningId){
        $priceWarnInfo = PriceWarningModel::getPriceWarningById($priceWarningId);
        if(empty($priceWarnInfo)){
            throw  new InvalidRequestException("没找到相关的价格预警信息");
        }
        if($priceWarnInfo["status"] != PriceWarningModel::$STATUS["status_pending"]){
            throw  new InvalidRequestException("只有待处理状态的记录才能操作重新验证");
        }
        $priceActivityId = $priceWarnInfo["price_activity_id"];//活动id
        $sppeId = $priceWarnInfo["sppe_id"];//售价组id
        //活动相关信息
        $activityInfo = PriceActivityModel::where("id",intval($priceActivityId))->first();
        $activityInfo = $activityInfo ? $activityInfo->toArray() : [];
        if(empty($activityInfo)){
            throw  new InvalidRequestException("没有找到该活动相关信息");
        }
        if($activityInfo["status"] == -1 || ($activityInfo["end_time"] < time())){
            //删除活动关联的盈利率
            PriceWarningModel::delPriceWarningBypriceActivityId($priceActivityId);
            throw  new InvalidRequestException("活动已删除或已结束");
        }


        $activityRatio = round($activityInfo["ratio"]/100,4);//0.9525
        $standard_brand_ids = !empty($activityInfo["standard_brand_ids"]) ? explode(",",$activityInfo["standard_brand_ids"]) : [];
        $activityStandardBrandIds = array_filter_unique($standard_brand_ids);//标准品牌列表


        //获取所有售价组信息
        $stepPriceNewList = StepPriceNewModel::where("status",1)
            ->where("sppe_id",$sppeId)
            ->with("stepPriceExtend")
            ->get()->toArray();


        ////如果活动参与品牌不在售价组中 则不计算该售价组
        $finalstepPriceNewList = self::computBrand($stepPriceNewList,$activityStandardBrandIds);
        $extroParams["price_warning_id"] = $priceWarningId;
        $extroParams["revalidation"] = 1;
        $extroParams["currency_rmb"] = $activityInfo["currency_rmb"] ?? 0;
        $extroParams["currency_us"] = $activityInfo["currency_us"] ?? 0;
        self::calculateProfit($priceActivityId,$activityRatio,$finalstepPriceNewList,$extroParams);
    }

}