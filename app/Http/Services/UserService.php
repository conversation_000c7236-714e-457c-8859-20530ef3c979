<?php

namespace App\Http\Services;

use App\Http\Models\Crm\UserModel;
use App\Http\Models\Liexin\UserMainModel;
use App\Http\Models\Ucenter\UserSubModel;
use Illuminate\Support\Facades\Redis;

class UserService
{
    public static function getList($params)
    {
        $list = UserModel::getListByWhere($params);

        return [
            'list' => $list,
            'total' => $list['total'],
        ];
    }

    public static function buildListWhere($params)
    {

        return [];
    }

    public static function checkAccount($account)
    {
        return UserModel::checkAccount($account);
    }

    public static function addUserInfo($params)
    {
        //注册用户
        //新增基础信息
        //新增发票/合同信息
        //新增收货信息
        //新增标签
        //新增客户备注?


    }

    public static function getUserInfo($userId)
    {
        $data = [
            'user_base_info',
            'tax_no_list',
            'address_list',
            'user_tag_list',
            'user_remark',
        ];
        return $data;
    }

    //获取用户id列表
    public static function getUserIdByAccount($account)
    {
        $orgId = request()->user->org_id;
        if ($orgId == 1) {
            $userId = UserMainModel::where('mobile', $account)->value('user_id');
            if (empty($userId)) {
                $userId = UserMainModel::where('email', $account)->value('user_id');
            }
        } else {
            $redis = Redis::connection('user_new');
            $userId = $redis->hget('ucenter_mobile', $account . '_' . $orgId);
            if (empty($userId)) {
                $userId = $redis->hget('ucenter_email', $account . '@' . $orgId);
            }
        }
        return $userId;
    }

    //获取所有的用户id列表
    public static function getAllUserIdByAccount($account)
    {
        //猎芯的
        $userId = UserMainModel::where('mobile', $account)->value('user_id');
        if (empty($userId)) {
            $userId = UserMainModel::where('email', $account)->value('user_id');
        }

        $ucId = \App\Http\Models\Ucenter\UserModel::where('mobile', $account)->value('uc_id');
        $otherUserIds = \App\Http\Models\Ucenter\UserSubModel::where('uc_id', $ucId)->pluck('org_user_id');
        $otherUserIds[] = $userId;
        return $otherUserIds;
    }
}
