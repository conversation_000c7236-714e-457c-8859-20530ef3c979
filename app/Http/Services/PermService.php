<?php
namespace App\Http\Services;

use App\Http\Models\Cms\CmsBusinessConfigModel;
use App\Http\Models\Cms\CmsRolePermModel;
use App\Http\Models\Cms\CmsUserDepartmentModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Cms\CmsUserPermModel;

use Illuminate\Support\Facades\Log;

class PermService
{
    const ROLE_ADMIN = 1; // 管理员角色

    const ROLE_ONLINE_ALL = 2; // 线上查看下级

    const ROLE_ONLINE_SELF = 3; // 线上查看自己

    const ROLE_OPERATORS = 4; // 运营

    const ROLE_OFFLINE_ALL = 5; // 线下查看下级

    const ROLE_OFFLINE_SELF = 6; // 线下查看自己

    const ROLE_COMPANY_MANAGER = 7; // 公司管理员

    const ROLE_KA_MAMAGER_ALL = 8; // 大客户查看下级

    const ROLE_KA_MAMAGER_SELF = 9; // 大客户查看自己

    const ROLE_SUZHOU_ALL = 10;  // 苏州查看下级

    const ROLE_SUZHOU_SELF = 11; // 苏州查看自己

    const ROLE_NULL = 0;  // 未设置角色

    private static $role_name_map = [
		'管理员'        => self::ROLE_ADMIN,
		'线上查看下级'	=> self::ROLE_ONLINE_ALL,
		'线上查看下级' 	=> self::ROLE_ONLINE_SELF,
		'运营'        	=> self::ROLE_OPERATORS,
		'线下查看下级' 	=> self::ROLE_OFFLINE_ALL,
		'线下查看自己' 	=> self::ROLE_OFFLINE_SELF,
		'公司管理员' 	=> self::ROLE_COMPANY_MANAGER,
		'大客户查看下级' => self::ROLE_KA_MAMAGER_ALL,
		'大客户查看自己' => self::ROLE_KA_MAMAGER_SELF,
		'苏州查看下级' 	=> self::ROLE_SUZHOU_ALL,
		'苏州查看自己' 	=> self::ROLE_SUZHOU_SELF,
	];

    // 获取当前用户角色
    public static function getUserRoles($uid = 0, $email = "")
    {
        $admin = request()->get("user");
        $uid = $uid ? $uid : $admin->userId;
        $email = $email ? $email : $admin->email;

        // 如果是管理员邮箱，直接返回管理员角色
        if ($email == '<EMAIL>') {
            return [self::ROLE_ADMIN];
        }

        // 根据域名查询系统业务ID
        $business = self::getBusinessInfo();
        if (!$business) {
            return [];
        }
        $bid = $business['bid'];

        // 权限系统配置的管理帐号， 如果是权限系统中配置的管理员邮箱，直接返回管理员角色
        $adminAccount = json_decode($business['admin'], true);
        if (in_array($email, $adminAccount)) {
            return [self::ROLE_ADMIN];
        }

        // 根据用户ID和业务ID查看角色
        $userPerm = CmsUserPermModel::getUserPermByUidAndBid($uid, $bid);
        if (empty($userPerm)) {
            return [];
        }

        // 没有选择角色
        if ($userPerm['roles'] == 'null') {
            return [];
        }

        $role_list = [];
        $role_ids = json_decode($userPerm['roles'], true);
        if ($role_ids) {
            $role_perm_list = CmsRolePermModel::getRolePermInfosByRoleIds($role_ids, $bid);
            if ($role_perm_list) {
                $role_name_list = array_column($role_perm_list, 'name');
                foreach ($role_name_list as $role_name) {
                    if (isset(self::$role_name_map[$role_name])) {
                        $role_list[] = self::$role_name_map[$role_name];
                    }
                }
            }
        }

        return $role_list;
    }

    // 判断用户是否拥有某角色
    public static function hasRole($role, $role_list)
    {
        return in_array($role, $role_list);
    }

    // 获取用户所有权限
    public static function getUserPerms()
    {
        $userAllPermList = [];
        $business = self::getBusinessInfo();
        //获取用户单独的权限
        if (isset($business['bid'])){
            $bid = $business['bid'];
            $userPerm = CmsUserPermModel::getUserPermByUidAndBid(request()->user->userId, $bid);//获取用户权限信息
            if ($userPerm && isset($userPerm['perms'])){
                $userPermList = json_decode($userPerm['perms']);
                if (is_array($userPermList)){
                    $userAllPermList = array_merge($userAllPermList,$userPermList);
                }
            }
        }

        //获取用户所在角色的权限
        $rolePermList = [];
        if (isset($userPerm['roles'])) {
            $roleIds = json_decode($userPerm['roles'], true);//用户所有的角色
            if ($roleIds) {
                $rolePermData = CmsRolePermModel::getRolePermInfosByRoleIds($roleIds, $bid);
                if ($rolePermData) {
                    foreach ($rolePermData as $param) {//获取用户的角色权限
                        $arrParam = json_decode($param['perms']);
                        $rolePermList = array_merge($rolePermList, $arrParam);
                    }
                }
            }
        }
        $userAllPermList = array_merge($userAllPermList,$rolePermList);
        if (empty($userAllPermList)){
            Log::error("该用户未配置权限，请联系管理员,uid:" . request()->user->userId);
        }
        return $userAllPermList;
    }

    // 获取permId
    public static function getPermId($url)
    {
        $perm_id = '';
        $url_info = parse_url($url);
        if (isset($url_info['path'])) {
            $path_arr = explode("/", $url_info['path']);
            $path_arr = array_filter($path_arr);
            $perm_id = implode("_", $path_arr);
        }
        return $perm_id;
    }

    // 判断用户是否拥有某权限
    public static function hasPerm($perm_id)
    {
        // if (request()->user->userId == 1000) {
        //     return true;
        // }
        $user_perms = self::getUserPerms();
        return in_array($perm_id, $user_perms);
    }

    // 系统名称
    public static function getBusinessConfigName()
    {
        return get_resource_config_section('app', 'cube')['APP_CONFIG_NAME'] ?? '魔方-重构';
    }

    // 获取系统信息
    public static function getBusinessInfo()
    {
        $app_config_name = self::getBusinessConfigName();
        return CmsBusinessConfigModel::getInfoByTitle($app_config_name);
    }

    // 获取指定用户下级所有人员
    public static function getSubUserId($userId)
    {
        $sub_user_ids = [];
        array_unshift($sub_user_ids, $userId); // 将当前用户添加到数组

        $user_info = CmsUserInfoModel::getUserInfoById($userId);

        if (!$user_info['department_id']) {
            return $sub_user_ids;
        }

        // 获取所有下级部门
        $department_ids = self::_getDepartmentIds($user_info['department_id']);
        // 获取下级部门的人员
        $sub_user_ids = CmsUserInfoModel::getUserIdsByDepartmentIds($department_ids);
        return array_unique($sub_user_ids);
    }

    // 获取查询的部门id，查询销售和采购部门下所有子部门的ids
    // 这里要使用循环的查询方法，如果改部门下面还有子部门，那么一并查询，最终合并用户子部门id集
    public static function _getDepartmentIds($top_department_id)
    {
        $all_department_ids = $next_department_ids = [$top_department_id];
        while ($next_department_ids) {
            $next_department_ids = CmsUserDepartmentModel::getDepartmentIdsParrentIds($next_department_ids);
            $all_department_ids = array_merge($all_department_ids, $next_department_ids);
        }
        return $all_department_ids;
    }





}
