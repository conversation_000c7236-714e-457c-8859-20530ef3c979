<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use Illuminate\Support\Facades\DB;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Models\Cube\MinOrderRuleModel;
use App\Http\Models\Cube\OperationLogModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class MinOrderRuleService
{
    /**
     * 获取最小订单规则列表
     * @param array $map
     * @return array
     */
    public function getMinOrderRuleList($map = [])
    {
        $query = MinOrderRuleModel::where('status', '!=', MinOrderRuleModel::STATUS_DELETED);

        if (!empty($map['supplier_id'])) {
            $query->where('supplier_id', $map['supplier_id']);
        }

        if (!empty($map['supplier_code'])) {
            $query->where('supplier_code', $map['supplier_code']);
        }

        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        $total = $query->count();
        $list = $query->orderBy('rule_id', 'desc')
            ->with('user_info')
            ->offset(($map['page'] - 1) * $map['limit'])
            ->limit($map['limit'])
            ->get()
            ->toArray();

        return [
            'data' => $list,
            'total' => $total
        ];
    }

    /**
     * 保存最小订单规则
     * @param array $data
     * @return bool
     */
    public function saveMinOrderRule($data)
    {
        $data['supplier_code'] = empty($data['supplier_code']) ? '' : $data['supplier_code'];
        $ruleId = $data['rule_id'] ?? 0;
        $now = time();
        $userId = request()->user->userId;
        $createName = request()->user->name;
        $updateName = request()->user->name;

        if ($ruleId) {

            //判断是否存在
            $rule = MinOrderRuleModel::where('rule_id','!=', $ruleId)
            ->where('supplier_id', $data['supplier_id'])
            ->where('supplier_code', $data['supplier_code'])
            ->first();
            if ($rule) {
                throw new InvalidRequestException('渠道/供应商已存在订单规则');
            }

            // 修改
            $rule = MinOrderRuleModel::find($ruleId);
            if (!$rule) {
                throw new InvalidRequestException('订单规则不存在');
            }

            // 记录操作日志
            $logContent = '修改订单规则，原数据：人民币：' . $rule->cny_amount . '元；美金：' . $rule->usd_amount . '美元';
            OperationLogModel::insert([
                'rule_id' => $ruleId,
                'operator_id' => $userId,
                'operation_time' => $now,
                'type' => OperationLogModel::TYPE_MIN_ORDER_RULE,
                'content' => $logContent,
                'create_uid' => $userId,
                'create_time' => $now,
                'create_name' => $createName,
            ]);

            // 更新规则
            $rule->supplier_id = $data['supplier_id'];
            $rule->supplier_code = $data['supplier_code'];
            $rule->cny_amount = $data['cny_amount'];
            $rule->usd_amount = $data['usd_amount'];
            $rule->status = $data['status'] ?? MinOrderRuleModel::STATUS_ENABLE;
            $rule->update_uid = $userId;
            $rule->update_name = $updateName;
            $rule->update_time = $now;
            $rule->save();
        } else {
            //判断是否存在
            $rule = MinOrderRuleModel::where('supplier_id', $data['supplier_id'])
            ->where('supplier_code', $data['supplier_code'])
            ->first();
            if ($rule) {
                throw new InvalidRequestException('渠道/供应商已存在订单规则');
            }

            // 新增
            $rule = new MinOrderRuleModel();
            $rule->supplier_id = $data['supplier_id'];
            $rule->supplier_code = $data['supplier_code'];
            $rule->cny_amount = $data['cny_amount'];
            $rule->usd_amount = $data['usd_amount'];
            $rule->status = $data['status'] ?? MinOrderRuleModel::STATUS_ENABLE;
            $rule->create_uid = $userId;
            $rule->create_name = $createName;
            $rule->create_time = $now;
            $rule->update_uid = $userId;
            $rule->update_name = $updateName;
            $rule->update_time = $now;
            $rule->save();
            // 记录操作日志
            OperationLogModel::insert([
                'rule_id' => $rule->rule_id,
                'operator_id' => $userId,
                'operation_time' => $now,
                'type' => OperationLogModel::TYPE_MIN_ORDER_RULE,
                'content' => '新建订单规则',
                'create_uid' => $userId,
                'create_time' => $now,
                'create_name' => $createName,
                'update_uid' => $userId,
                'update_time' => $now,
                'update_name' => $updateName,
            ]);
        }
    }

    /**
     * 根据ID获取最小订单规则
     * @param int $ruleId
     * @return array|null
     */
    public function getMinOrderRuleById($ruleId)
    {
        return MinOrderRuleModel::where('rule_id', $ruleId)
            ->first();
    }

    /**
     * 获取供应商列表
     * @return array
     */
    public function getSupplierList()
    {
        return SupplierChannelModel::where('status', SupplierChannelModel::STATUS_PASS)
            ->pluck('supplier_name', 'supplier_id')
            ->toArray();
    }
}
