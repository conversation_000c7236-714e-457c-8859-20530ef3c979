<?php


namespace App\Http\Services;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\ActivityAndCouponSkuModel;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use Illuminate\Support\Facades\Redis;

class PriceActivityService
{
    //转换请求数据
    public function transformRequestData($request)
    {
        $data = $request->only([
            'activity_name',
            'start_time',
            'end_time',
            'status',
            'currency_rmb',
            'currency_us',
            'user_scope',
            'goods_scope',
            'show_name',
            'sign',
            'sign_text',
            'ratio',
            'ratio_us',
            'allow_coupon',
            'supplier_ids',
            'class_ids',
            'canals',
            'standard_brand_ids',
            'sku_ids',
            'exclude_standard_brand_ids',
            'exclude_sku_ids',
            'exclude_sku_ids_file_url',
            'use_type',
            'sku_file_url',
            'sign_url',
            'org_id'
        ]);
        //先去判断是自营还是专营,如果是自营,则忽略渠道标签字段,如果是专营,则忽略分类id
        $data['class_ids'] = $data['goods_scope'] == 1 ? $data['class_ids'] : '';
        $data['canals'] = $data['goods_scope'] == 2 ? $data['canals'] : '';

        //去除逗号
        $data['supplier_ids'] = trim($data['supplier_ids'], ',');
        $data['class_ids'] = trim($data['class_ids'], ',');
        $data['canals'] = trim($data['canals'], ',');
        $data['standard_brand_ids'] = trim($data['standard_brand_ids'], ',');
        $data['exclude_standard_brand_ids'] = trim($data['exclude_standard_brand_ids'], ',');
        if (!empty($data['can_admin_order'])) {
            $data['can_admin_order'] = (int)$data['can_admin_order'];
        }
        $data['use_type'] = intval($data['use_type']);
        //补全信息
        $data['add_time'] = time();
        $data['admin_id'] = request()->user->userId;
        $data['admin_name'] = request()->user->email;
        $data['status'] = (int)$data['status'];
        $data['user_scope'] = (int)$data['user_scope'];
        $data['goods_scope'] = (int)$data['goods_scope'];
        $data['allow_coupon'] = (int)$data['allow_coupon'];
        $data['supplier_type'] = (int)$data['goods_scope'];
        $data['start_time'] = strtotime($data['start_time']);
        $data['end_time'] = strtotime($data['end_time']);
        if (!empty($data['currency_rmb'])) {
            $data['ratio'] = (float)$data['ratio'];
        }
        if (!empty($data['currency_us'])) {
            $data['ratio_us'] = (float)$data['ratio_us'];
        }

        if (empty($data['currency_rmb'])) {
            $data['ratio'] = 0;
        }

        if (empty($data['currency_us'])) {
            $data['ratio_us'] = 0;
        }

        $data['currency_rmb'] = !empty($data['currency_rmb']) ? (int)$data['currency_rmb'] : 0;
        $data['currency_us'] = !empty($data['currency_us']) ? (int)$data['currency_us'] : 0;

        if ($data['use_type'] == 1) {
            $data['sku_file_url'] = '';
        } else {
            $data['standard_brand_ids'] = '';
            $data['exclude_standard_brand_ids'] = '';
        }

        if ($data['org_id'] == PriceActivityModel::ORG_IEDGE) {
            $data['currency_us'] = 0;
            $data['ratio_us'] = 0;
        }
        return $data;
    }

    //保存活动到redis
    public function saveActivityToRedis($activityId, $activity, $type)
    {
        $orgId = $activity['org_id'] ?? 1;
        $redisKey = $type == 'price' ? 'lie_price_activity' : 'lie_gift_activity';
        //将data里面的supplier_ids拆分出来,比如supplier_ids是1,2,3,则在redis存3条数据代表3个供应商
        $supplierIdList = explode(',', $activity['supplier_ids']);
        $activity['activity_id'] = (int)$activityId;
        //如果下面都是空的话,则代表是整个供应商在搞活动
        $activity['entire_supplier_activity'] = false;
        if (
            empty($activity['standard_brand_ids']) && empty($activity['canals']) && empty($activity['class_ids'])
            && empty($activity['exclude_standard_brand_ids']) && empty($activity['exclude_sku_ids']) && empty($activity['sku_file_url'])
        ) {
            $activity['entire_supplier_activity'] = true;
        }
        $activity['ratio'] = (float)$activity['ratio'];
        $activity['ratio_us'] = (float)$activity['ratio_us'];
        //如果有item_list,则要排序
        if (!empty($activity['item_list'])) {
            $sortKey = [];
            foreach ($activity['item_list'] as $k => $v) {
                $sortKey[$k] = $v['amount'];
            }
            array_multisort($activity['item_list'], SORT_DESC, $sortKey);
        }
        unset($activity['supplier_ids']);
        $redis = Redis::connection('sku');
        foreach ($supplierIdList as $supplierId) {
            // 使用包含组织ID的键名格式
            $redisKeyName = $orgId == 1 ? $supplierId : $supplierId . '_' . $orgId;

            // 从Redis获取现有活动
            $activities = $redis->hget($redisKey, $redisKeyName);
            $activities = $activities ? json_decode($activities, true) : [];

            // 过滤掉当前活动的旧版本
            $activities = array_filter($activities, function ($activity) use ($activityId) {
                return $activity['activity_id'] != $activityId;
            });

            // 添加新活动并保存
            $activities[] = $activity;
            $activities = array_values($activities);
            $redis->hset($redisKey, $redisKeyName, json_encode($activities));
        }
        PriceWarningService::activityTriggerPriceWarning($activityId);
        if ($redisKey == 'lie_price_activity') {
            //找出所有redis活动,然后找出没有对应供应商的数据进行删除
            $allPriceActivities = $redis->hgetAll('lie_price_activity');
            foreach ($allPriceActivities as $redisSupplierKey => $activities) {
                $activities = json_decode($activities, true);
                $hasInvalidActivity = false;

                // 从Redis键名中解析出supplier_id和org_id
                $keyParts = explode('_', $redisSupplierKey);
                $supplierId = $keyParts[0]; // 第一部分始终是supplier_id
                $keyOrgId = count($keyParts) > 1 ? (int)end($keyParts) : 1; // 如果有下划线，最后一部分是org_id，否则默认为1

                foreach ($activities as $key => $activity) {
                    //过期的不管
                    if ($activity['end_time'] < time()) {
                        continue;
                    }

                    //判断当前活动还有没有这个供应商,没有的话,可以删掉了
                    $supplierIds = PriceActivityModel::where('id', $activity['activity_id'])->value('supplier_ids');
                    $supplierIds = explode(',', trim($supplierIds, ','));
                    // 检查活动中的供应商ID是否还存在
                    if (!in_array($supplierId, $supplierIds)) {
                        $hasInvalidActivity = true;
                        unset($activities[$key]);
                    }

                    // 检查活动的组织ID是否与键名中的组织ID匹配
                    $activityOrgId = PriceActivityModel::where('id', $activity['activity_id'])->value('org_id') ?? 1;
                    if ($keyOrgId != $activityOrgId) {
                        $hasInvalidActivity = true;
                        unset($activities[$key]);
                    }
                }

                $activities = array_values($activities);
                if ($hasInvalidActivity) {
                    if (empty($activities)) {
                        $redis->hdel('lie_price_activity', $redisSupplierKey);
                    } else {
                        $redis->hset('lie_price_activity', $redisSupplierKey, json_encode($activities));
                    }
                }
            }
        }
    }

    //获取活动信息
    public function getActivityInfo($id)
    {
        $model = new PriceActivityModel();
        $activity = $model->where('id', $id)->first();
        if (!$activity) {
            return [];
        }
        $activity = $activity->toArray();
        $activity['start_time_string'] = date("Y-m-d H:i:s", $activity['start_time']);
        $activity['end_time_string'] = date("Y-m-d H:i:s", $activity['end_time']);
        return $activity;
    }

    //获取活动列表
    public function getActivityList($map)
    {
        $model = new PriceActivityModel();
        $query = $model->with('statistics');
        if (!empty($map['activity_name'])) {
            $query->where('activity_name', 'like', "%" . $map['activity_name'] . "%");
        }
        if (!empty($map['id'])) {
            $ids = explode(',', trim($map['id'], ''));
            $query->whereIn('id', $ids);
        }
        if (!empty($map['add_time'])) {
            $startTime = strtotime(explode('~', $map['add_time'])[0]);
            $endTime = strtotime(explode('~', $map['add_time'])[1]);
            $query->whereBetween('add_time', [$startTime, $endTime]);
        }
        if (!empty($map['activity_time'])) {
            $startTime = strtotime(explode('~', $map['activity_time'])[0]);
            $endTime = strtotime(explode('~', $map['activity_time'])[1]);
            $query->whereBetween('start_time', [$startTime, time()]);
            $query->whereBetween('end_time', [time(), $endTime]);
        }
        if (!empty($map['user_scope'])) {
            $query->where('user_scope', $map['user_scope']);
        }
        if (!empty($map['goods_scope'])) {
            $query->where('goods_scope', $map['goods_scope']);
        }
        if (!empty($map['currency'])) {
            if ($map['currency'] == 1) {
                $query->where('currency_rmb', 1);
            }
            if ($map['currency'] == 2) {
                $query->where('currency_us', 1);
            }
            if ($map['currency'] == 3) {
                $query->where('currency_rmb', 1)->where('currency_us', 1);
            }
        }
        $query = $this->getStatusMap($query, $map);
        $limit = !empty($map['limit']) ? $map['limit'] : 10;
        $result = $query->orderBy('id', 'desc')->paginate($limit);
        $result = !empty($result) ? $result->toArray() : [];
        return $result;
    }

    //获取状态查询条件
    public function getStatusMap($query, $map)
    {
        if (isset($map['status']) && $map['status'] !== "") {
            if ($map['status'] == 1) {
                $query->where('end_time', '>', time());
                $query->where('status', $map['status']);
            } elseif ($map['status'] == -2) {
                //过期
                $query->where('end_time', '<', time());
            } elseif ($map['status'] == -1) {
                //删除
                $query->where('status', $map['status']);
                $query->where('end_time', '>', time());
            } else {
                $query->where('status', $map['status']);
                $query->where('end_time', '>', time());
            }
        }
        return $query;
    }

    //处理上传的csv文件
    public function dealWithSkuNameCsv($activity, $activityId)
    {
        $redis = Redis::connection('sku');
        ActivityAndCouponSkuModel::where('main_id', $activityId)->where('type', 1)->delete();
        $redis->del('lie_activity_and_coupon_sku_' . $activityId);
        // 读取 CSV 文件内容
        $file = file_get_contents($activity['sku_file_url']);
        $rows = explode(PHP_EOL, $file);
        $rows = array_unique($rows);
        $data = [];
        $now = time();
        $skuNameList = [];
        foreach ($rows as $key => $row) {
            $row = str_getcsv($row);
            $row = array_map(function ($item) {
                return mb_convert_encoding($item, 'UTF-8', 'GBK'); // 将每个元素转换为 UTF-8 编码
            }, $row);
            if (empty($row[0]) || $row[0] == '型号') {
                continue;
            }
            if ($key == 0) {
                continue;
            }
            $skuNameList[] = $row[0];
        }
        //组装数据
        foreach ($skuNameList as $skuName) {
            $data[] = [
                'sku_name' => $skuName,
                'supplier_ids' => $activity['supplier_ids'],
                'canals' => $activity['canals'],
                'main_id' => $activityId,
                'type' => ActivityAndCouponSkuModel::TYPE_PRICE_ACTIVITY,
                'create_time' => $now,
            ];
        }
        foreach (array_chunk($data, 1000) as $items) {
            ActivityAndCouponSkuModel::insert($items);
            $data = [];
            foreach ($items as $item) {
                $data[$item['sku_name']] = 1;
            }
            $redis->hmset('lie_activity_and_coupon_sku_' . $activityId, $data);
        }

        //还要插入到redis给成意那边去跑定时任务
        $redis->hset('activity_to_es', $activityId, json_encode([
            'main_id' => $activityId,
            'update_time' => $now,
        ]));
    }

    public function getCanalInitValue($canals)
    {
        if (!trim($canals, ',')) {
            return [];
        }
        $canals = explode(',', $canals);
        $canalList = SupplierChannelModel::whereIn('supplier_code', $canals)->select([
            'supplier_code',
            'supplier_name',
        ])->get();
        $canalList = !empty($canalList) ? $canalList->toArray() : [];
        return $canalList;
    }
}
