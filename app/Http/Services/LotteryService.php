<?php


namespace App\Http\Services;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\LotteryModel;
use App\Http\Models\Cube\PrizeModel;
use App\Http\Models\Cube\PrizeWinnerModel;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Transformers\LotteryTransformer;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;

class LotteryService
{
    public function getLotteryList($map)
    {
        $model = new LotteryModel();
        $query = $model->with([
            'user_info',
        ])->where('status', '!=', LotteryModel::STATUS_DELETED)->orderBy('lottery_id', 'desc');
        $orgId = request()->user->org_id;
        //查看权限控制
        if (!PermService::hasPerm('cube_coupon_viewAllLottery') && request()->user->userId != 1000) {
            if (in_array($orgId, config('field.IedgeOrgIdList'))) {
                $query->whereIn('org_id', config('field.IedgeOrgIdList'));
            } else {
                $query->where('org_id', $orgId);
            }
        }

        if (!empty($map['lottery_name'])) {
            $query->where('lottery_name', 'like', "%" . $map['lottery_name'] . "%");
        }
        if (!empty($map['lottery_id'])) {
            $query->where('lottery_id', $map['lottery_id']);
        }
        if (isset($map['status']) && $map['status'] === 0 || !empty($map['status'])) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['publisher_id'])) {
            $query->where('publisher_id', $map['publisher_id']);
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        $limit = !empty($map['limit']) ? $map['limit'] : 10;
        $result = $query->paginate($limit);
        $result = !empty($result) ? $result->toArray() : [];
        return $result;
    }

    public function getLotteryById($lotteryId)
    {
        $lottery = LotteryModel::where('lottery_id', $lotteryId)->first();
        $lottery = $lottery ? $lottery->toArray() : [];
        $lottery['create_time'] = $lottery['create_time'] ? date('Y-m-d H:i:s', $lottery['create_time']) : '';
        $lottery['start_time'] = $lottery['start_time'] ? date('Y-m-d', $lottery['start_time']) : '';
        $lottery['end_time'] = $lottery['end_time'] ? date('Y-m-d', $lottery['end_time']) : '';
        $lottery['qualify_get_rule'] = $lottery ? explode(',', $lottery['qualify_get_rule']) : [];
        return $lottery;
    }

    public function getLotteryListByIds($lotteryIds = [])
    {
        $lotteryList = LotteryModel::whereIn('lottery_id', $lotteryIds)->get()->toArray();
        $lotteryList = (new LotteryTransformer())->listTransform($lotteryList);
        return $lotteryList;
    }


    public function saveLottery($data)
    {

    }

    /*
        抽奖时创建奖品缓存数据(防抽超)
    */
    public function saveDrawInfo($lotteryModel, $couponId, $prizes)
    {
        $redis = Redis::connection('user');
        //过期时间
        $expired = $lotteryModel['end_time'] - time();
        foreach ($prizes as $key => $value) {
            //redis名字　＝　lie_price + 抽奖id + 中奖等级
            $name = 'lie_prize_' . $couponId . '_' . $value['level'];
            $redisExists = $redis->exists($name);
            //检查是否存在　如果存在则删除后重新添加数据
            if ($redisExists) {
                //删除队列
                $redis->del($name);
            }

            for ($i = 0; $i < $value['prize_num']; $i++) {
                //创造奖品数据以存储到redis防止抽超
                $redis->lpush($name, 1);
            }

            //设置过期时间
            $redis->expire($name, $expired);
        }
    }

    //获取 获得抽奖资格渠道
    public function qualifyGetRules($data)
    {
        $rules = '';
        $rules .= !empty($data['qualify_get_rule_register']) ? $data['qualify_get_rule_register'] . ',' : '';
        $rules .= !empty($data['qualify_get_rule_login']) ? $data['qualify_get_rule_login'] . ',' : '';
        $rules .= !empty($data['qualify_get_rule_share']) ? $data['qualify_get_rule_share'] . ',' : '';
        $rules .= !empty($data['qualify_get_rule_order']) ? $data['qualify_get_rule_order'] . ',' : '';
        $rules .= !empty($data['qualify_get_rule_follow']) ? $data['qualify_get_rule_follow'] : '';
        return $rules;
    }

    //验证活动余额是否充足
    public function checkBalance($money)
    {

        $params = array_merge([], authkey());
        $apiDomain = config('website.api_domain');
        $balance = json_decode(curl($apiDomain . '/wallet/getliexinbalance', $params));
        if (empty($balance) || $balance->err_code != 0) {
            throw new InvalidRequestException('获取可用余额失败');
        } else {
            //猎芯账户可用余额
            $availBalance = $balance->data;
            //计算在线活动余额(申请-已用) + 当前申请额度 是否 <= 猎芯余额
            //求在线活动余额
            $onlineBalance = $this->onlineActivityBalance();
            if ($availBalance < $money + $onlineBalance) {
                return false;
            }
            return true;
        }
    }

    //计算在线活动还需要多少经费(申请-已用) 有效期+status
    private function onlineActivityBalance()
    {
        $lotteryActs = LotteryModel::select('lottery_id', 'apply_amount')
            ->where('status', 1)->where('money_get_type', 1)->where('apply_amount', '>', 0)
            ->where('start_time', '<=', time())->where('end_time', '>=', time())->get();
        $onlineAvailBalance = 0;
        if ($lotteryActs) {
            foreach ($lotteryActs as $lotteryAct) {
                $lotteryAct = objectToArray($lotteryAct);
                //获奖表
                $winnerPrizeIds = PrizeWinnerModel::select('prize_id')->where('lottery_id',
                    $lotteryAct['lottery_id'])
                    ->where('prize_type', 3)->where('is_sent', 1)->get();
                if ($winnerPrizeIds) {
                    $winnerPrizeIdsArr = [];
                    foreach ($winnerPrizeIds as $winner_prize_id) {
                        $winnerPrizeIdsArr[] = $winner_prize_id->prize_id;
                    }
                    //奖品表
                    $prizeIds = PrizeModel::select('prize_id', 'prize_value')
                        ->where('lottery_id', $lotteryAct['lottery_id'])->where('prize_type', 3)->get();
                    $prizeIdsArr = [];
                    foreach ($prizeIds as $prizeId) {
                        $prizeId = objectToArray($prizeId);
                        $prizeIdsArr[$prizeId['prize_id']] = $prizeId['prize_value'];
                    }
                    $sumPrizeValue = 0;
                    foreach ($winnerPrizeIdsArr as $winner) {
                        //规避没有数据的从而报错的情况
                        if ($winner > count($winnerPrizeIdsArr)) {
                            continue;
                        }

                        $sumPrizeValue += $prizeIdsArr[$winner];
                    }
                    $onlineAvailBalance += $lotteryAct['apply_amount'] - $sumPrizeValue;
                }
            }
        }
        return $onlineAvailBalance;
    }

}
