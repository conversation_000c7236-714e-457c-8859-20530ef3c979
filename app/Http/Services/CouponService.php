<?php


namespace App\Http\Services;


use Illuminate\Support\Facades\DB;
use App\Repository\CouponRepository;
use Illuminate\Support\Facades\Http;
use App\Http\Models\Cube\CouponModel;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Models\Cube\CouponBrandModel;
use App\Http\Models\Cube\CouponClassModel;
use App\Http\Models\Cube\CouponOpLogModel;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Transformers\CouponTransformer;
use App\Http\Models\Cube\ActivityAndCouponSkuModel;
use App\Http\Models\Cube\UserCouponModel;

class CouponService
{

    public function getCouponList($map)
    {
        $model = new CouponModel();
        $orgId = request()->user->org_id;
        $query = $model->orderBy('coupon_id', 'desc');
        //查看权限控制
        if (!PermService::hasPerm('cube_coupon_viewAllCoupon') && request()->user->userId != 1000) {
            if (in_array($orgId, config('field.IedgeOrgIdList'))) {
                $query->whereIn('org_id', config('field.IedgeOrgIdList'));
            } else {
                $query->where('org_id', $orgId);
            }
        }
        if (!empty($map['coupon_name'])) {
            $query->where('coupon_name', 'like', "%" . $map['coupon_name'] . "%");
        }
        if (!empty($map['coupon_desc'])) {
            $query->where('coupon_desc', 'like', "%" . $map['coupon_desc'] . "%");
        }
        if (!empty($map['coupon_sn'])) {
            $query->where('coupon_sn', 'like', "%" . $map['coupon_sn'] . "%");
        }
        if (!empty($map['coupon_ids'])) {
            $couponIds = explode(',', trim($map['coupon_ids'], ''));
            $query->whereIn('coupon_id', $couponIds);
        }
        if (!empty($map['coupon_type'])) {
            $query->where('coupon_type', $map['coupon_type']);
        }
        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['org_id'])) {
            //if ($map['org_id'] != request()->user->org_id && request()->user->userId != 1000) {
            //    $map['org_id'] = -1;
            //}
            $query->where('org_id', $map['org_id']);
        }
        if (!empty($map['create_uid'])) {
            $query->where('create_uid', $map['create_uid']);
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        $limit = !empty($map['limit']) ? $map['limit'] : 10;
        $result = $query->paginate($limit);
        $result = !empty($result) ? $result->toArray() : [];
        return $result;
    }

    public function getCouponById($couponId)
    {
        $coupon = CouponModel::with(['coupon_brand', 'coupon_class'])
            ->where('coupon_id', $couponId)->first();
        $coupon = $coupon ? $coupon->toArray() : [];
        $coupon['class_ids'] = '';
        if ($coupon['coupon_class']) {
            $classIds = [];
            foreach ($coupon['coupon_class'] as $class) {
                $classIds[] = $class['class_id'];
            }
            $coupon['class_ids'] = implode(',', $classIds);
        }
        $coupon['brand_ids'] = '';
        if ($coupon['coupon_brand']) {
            $classIds = [];
            foreach ($coupon['coupon_brand'] as $class) {
                $classIds[] = $class['brand_id'];
            }
            $coupon['brand_ids'] = implode(',', $classIds);
        }
        if ($coupon['coupon_mall_type'] == 2) {
            $coupon['self_brand_ids'] = $coupon['brand_ids'];
            $coupon['exclude_self_brand_ids'] = $coupon['exclude_brand_ids'];
            $coupon['brand_ids'] = '';
            $coupon['exclude_brand_ids'] = '';
        } else {
            $coupon['self_brand_ids'] = '';
            $coupon['exclude_self_brand_ids'] = '';
        }
        $coupon['reg_start_time'] = $coupon['reg_start_time'] ? date('Y-m-d H:i:s', $coupon['reg_start_time']) : '';
        $coupon['start_time'] = $coupon['start_time'] ? date('Y-m-d H:i:s', $coupon['start_time']) : '';
        $coupon['end_time'] = $coupon['end_time'] ? date('Y-m-d H:i:s', $coupon['end_time']) : '';
        $coupon['effect_start_time'] = !empty($coupon['effect_start_time']) ? date(
            'Y-m-d H:i:s',
            $coupon['effect_start_time']
        ) : '';
        $coupon['effect_end_time'] = !empty($coupon['effect_end_time']) ? date('Y-m-d H:i:s', $coupon['effect_end_time']) : '';
        $coupon['self_supplier_id'] = $coupon['coupon_mall_type'] == 2 ? $coupon['selected_supplier_id'] : '';
        //        $coupon['supplier_ids'] = $coupon['coupon_mall_type'] == 3 ? $coupon['selected_supplier_id'] : '';
        return $coupon;
    }

    public function getCouponListByIds($couponIds = [])
    {
        $couponList = CouponModel::with([
            'coupon_brand' => function ($q) {
                $q->with('brand');
            }
        ])->whereIn('coupon_id', $couponIds)->get()->toArray();
        $couponList = (new CouponTransformer())->listTransform($couponList);
        return $couponList;
    }

    //生成请求参数
    private function generateParamsWithCouponId($couponId)
    {
        $params = [];
        $params['k1'] = time();
        $params['k2'] = pwdhash($params['k1'], 'fh6y5t4rr351d2c3bryi');
        $params['id'] = $couponId;
        return $params;
    }

    public function saveCoupon($data)
    {
        $coupon['coupon_id'] = $data['coupon_id'];
        //如果是华云的组织,还可以选多一次组织,所以要下面这种操作
        $coupon['org_id'] = $data['iedge_org_id'] ?: $data['org_id'];
        $coupon['coupon_name'] = $data['coupon_name'];
        $coupon['coupon_desc'] = $data['coupon_desc'];
        $coupon['coupon_type'] = $data['coupon_type'];
        $coupon['sku_file_url'] = $data['sku_file_url'];
        $coupon['require_amount'] = $data['require_amount'];
        $coupon['sale_amount'] = $data['sale_amount'];
        $coupon['coupon_num'] = $data['coupon_num'];
        $coupon['is_activity_center_show'] = $data['is_activity_center_show'];
        if (empty($data['coupon_id'])) {
            $coupon['surplus_num'] = $data['coupon_num'];
        }
        $coupon['limit_time'] = 0;
        $coupon['coupon_mall_type'] = $data['coupon_mall_type'];
        $coupon['coupon_goods_range'] = $data['coupon_goods_range'];
        //现在只有专营才有选择供应商的概念
        //如果是自营,那么忽略供应商
        if ($data['coupon_mall_type'] == 3) {
            $coupon['selected_supplier_id'] = trim($data['selected_supplier_id'], ',');
            $coupon['selected_supplier'] = (new SupplierModel())->getSupplierNameBySupplierId($coupon['selected_supplier_id']);
        } elseif ($data['coupon_mall_type'] == 2) {
            $coupon['selected_supplier_id'] = trim($data['self_supplier_id'], ',');
        }
        if (empty($coupon['selected_supplier'])) {
            unset($coupon['selected_supplier']);
        }

        //如果是根据商品型号来过滤,则要清除其它区域的数据
        if ($coupon['coupon_mall_type'] == 6) {
            unset($coupon['brand_ids']);
            unset($coupon['exclude_brand_ids']);
            unset($coupon['selected_supplier']);
            unset($coupon['class_ids']);
            //如果是自营的,没有渠道和供应商编码
            if ($coupon['coupon_goods_range'] == 2) {
                unset($coupon['supplier_ids']);
                unset($coupon['canals']);
            }
        }

        $coupon['issue_type'] = $data['issue_type'];
        $coupon['time_type'] = $data['time_type'];
        $coupon['start_time'] = $data['start_time'] ? strtotime($data['start_time']) : '';
        $coupon['end_time'] = $data['end_time'] ? strtotime($data['end_time']) : '';

        //如果是注册可领取,并且是相对时间,那么一定要有截止时间
        if ($data['coupon_get_rule'] == 1 && $data['time_type'] == 2) {
            if (empty($data['register_end_time'])) {
                throw new InvalidRequestException('当选择注册可领取，且设置有效期类型为相对时间时，需填写发放截止时间');
            }
            $coupon['end_time'] = strtotime($data['register_end_time']);
            $coupon['effect_end_time'] = strtotime($data['register_end_time']);
        }

        $coupon['usable_time'] = $data['usable_time'];
        $coupon['total_receive_num'] = $data['total_receive_num'];
        $coupon['day_receive_num'] = $data['day_receive_num'];
        $coupon['coupon_get_rule'] = $data['coupon_get_rule'];
        $coupon['reg_start_time'] = $data['reg_start_time'] ? strtotime($data['reg_start_time']) : '';
        if ($coupon['coupon_get_rule'] == 3 && !empty($data['coupon_id'])) {
            $coupon['effect_start_time'] = !empty($data['effect_start_time']) ? strtotime($data['effect_start_time']) : '';
            $coupon['effect_end_time'] = !empty($data['effect_end_time']) ? strtotime($data['effect_end_time']) : '';
        }
        $coupon['max_preferential_amount'] = $data['max_preferential_amount'];
        $coupon['order_num'] = empty($data['order_num']) ? 1 : $data['order_num'];
        $coupon['supplier_ids'] = trim($data['supplier_ids'], ',');
        $coupon['canals'] = trim($data['canals'], ',');
        //日志中需要的操作类型
        $opType = $data['op_type'];
        //过滤下品牌id,自营取自营的
        $data['brand_ids'] = $coupon['coupon_mall_type'] == 2 ? $data['self_brand_ids'] : $data['brand_ids'];
        $data['exclude_brand_ids'] = $coupon['coupon_mall_type'] == 2 ? $data['exclude_self_brand_ids'] : $data['exclude_brand_ids'];

        if (empty($coupon['coupon_id'])) {
            //插入新优惠券到表
            $coupon['coupon_sn'] = generateCouponSN('SN');
            $coupon['create_uid'] = request()->user->userId;
            $coupon['create_time'] = time();
            $coupon['status'] = CouponModel::STATUS_NEED_REVIEW;
            $insertCouponId = CouponModel::insertGetId($coupon);
            if (!$insertCouponId) {
                throw new InvalidRequestException('新增优惠券失败');
            }

            //插入新优惠券记录到日志表
            $couponLog['coupon_id'] = $insertCouponId;
            $couponLog['op_type'] = $opType; //1
            $couponLog['operator_uid'] = request()->user->userId;
            $couponLog['status'] = 1;
            $couponLog['remark'] = '';
            $couponLog['op_time'] = time();
            $result = CouponOpLogModel::insertGetId($couponLog);
            if (!$result) {
                throw  new InvalidRequestException('插入操作日志失败');
            }
        } else {
            $originCoupon = CouponModel::where('coupon_id', $coupon['coupon_id'])->first()->toArray();
            //更新优惠券信息
            $result = CouponModel::where('coupon_id', $coupon['coupon_id'])->update($coupon);
            if (!$result && $result !== 0) {
                throw new InvalidRequestException('编辑优惠券失败');
            }
            $isChangeGetRule = false;
            //插入更新优惠券状态信息到日志表
            if (($data['coupon_get_rule'] != $originCoupon['coupon_get_rule']) && $originCoupon['status'] == 1) {
                $isChangeGetRule = true;
                $opType = 2;
            }
            $couponLog['coupon_id'] = $coupon['coupon_id'];
            $couponLog['op_type'] = $opType;
            $couponLog['operator_uid'] = request()->user->userId;
            $couponLog['status'] = 1;
            $couponLog['op_time'] = time();
            $result = CouponOpLogModel::insert($couponLog);
            if (!$result) {
                throw new InvalidRequestException('写入优惠券日志失败');
            }
            //如果是审核通过新创建的优惠券有效/或审核通过后(即优惠券为有效状态)再编辑限领规则  则调用api接口初始化队列
            if ($isChangeGetRule) {
                $this->initCoupon($coupon['coupon_id']);
            }
        }

        $couponId = $insertCouponId ?? $data['coupon_id'];

        //如果填写了二级分类则入库
        if (!empty($data['class_ids'])) {
            CouponClassModel::where('coupon_id', $couponId)->delete();
            $secondClassId = explode(',', $data['class_ids']);
            foreach ($secondClassId as $x => $y) {
                $couponClass = ['class_id' => $y, 'coupon_id' => $couponId];
                CouponClassModel::insertGetId($couponClass);
            }
        }
        //如果此优惠券仅限于某个品牌则进行优惠券品牌表的更新或添加操作
        if (!empty($data['brand_ids'])) {
            //查询是否有这个优惠券关联的id 有就删除
            $brandResult = CouponBrandModel::where('coupon_id', $couponId)->count();
            if ($brandResult > 0) {
                CouponBrandModel::where('coupon_id', $couponId)->delete();
            }
            //添加优惠券关联的品牌
            $data['brand_ids'] = explode(',', $data['brand_ids']);
            foreach ($data['brand_ids'] as $k => $v) {
                $brand['coupon_id'] = $couponId;
                $brand['brand_id'] = $v;
                CouponBrandModel::insert($brand);
            }
        } else {
            CouponBrandModel::where('coupon_id', $couponId)->delete();
        }

        //修改缓存
        $coupon = CouponModel::where('coupon_id', $couponId)->first()->toArray();
        $redis = Redis::connection('sku');
        $redis->hset('api_coupon', $couponId, json_encode($coupon));

        //这里为什么不把排除的品牌也放到coupon_brands里面呢,因为老的接口也是直接用这个表的,简单加个类型老接口不知道,所以还是单独用一个字段存起来了
        //而且一般排除的也不多,所以用一个字段也可以
        CouponModel::where('coupon_id', $couponId)->update([
            'exclude_brand_ids' => $data['exclude_brand_ids'],
        ]);
        //插入到数据库
        if ($data['upload_change']) {
            $this->dealWithSkuNameCsv($data, $couponId);
        }

        return true;
    }

    //审核优惠券,支持批量
    public function reviewCoupons($couponIds, $status, $reviewRemark)
    {
        foreach ($couponIds as $couponId) {
            $originStatus = CouponModel::where('coupon_id', $couponId)->value('status');
            $result = CouponModel::where('coupon_id', $couponId)->where('status', CouponModel::STATUS_NEED_REVIEW)
                ->update([
                    'status' => $status,
                    'review_remark' => $reviewRemark,
                    'review_time' => time(),
                    'review_uid' => request()->user->userId,
                ]);
            if ($result) {
                (new CouponOpLogModel())->insertCouponLog($couponId, CouponOpLogModel::OP_TYPE_APPEND_REVIEW);
                if ($originStatus == CouponModel::STATUS_NEED_REVIEW && $status != CouponModel::STATUS_CANCEL) {
                    $this->initCoupon($couponId);
                }
            }
        }

        return true;
    }

    //初始化优惠券
    public function initCoupon($couponId)
    {
        $params = $this->generateParamsWithCouponId($couponId);
        $apiDomain = config('website.api_domain');
        $initCouponResult = curl("$apiDomain/coupon/init", $params);
        $initCouponResult = json_decode($initCouponResult, true);
        if (\Arr::get($initCouponResult, 'err_code') != 0) {
            throw new InvalidRequestException('初始化优惠券队列失败 : ' . \Arr::get($initCouponResult, 'err_msg'));
        }
    }

    //处理上传的csv文件
    public function dealWithSkuNameCsv($coupon, $couponId)
    {
        $redis = Redis::connection('sku');
        ActivityAndCouponSkuModel::where('main_id', $couponId)->where('type', ActivityAndCouponSkuModel::TYPE_COUPON)->delete();
        $redis->del('lie_coupon_sku_' . $couponId);
        // 读取 CSV 文件内容
        $file = file_get_contents($coupon['sku_file_url']);
        $rows = explode(PHP_EOL, $file);
        $rows = array_unique($rows);
        $data = [];
        $now = time();
        $skuNameList = [];
        foreach ($rows as $key => $row) {
            $row = str_getcsv($row);
            $row = trim($row[0]);
            if (empty($row) || $row == '型号') {
                continue;
            }
            if ($key == 0) {
                continue;
            }
            $skuNameList[] = trim($row);
        }
        //组装数据
        foreach ($skuNameList as $skuName) {
            $data[] = [
                'sku_name' => $skuName,
                'main_id' => $couponId,
                'canals' => $coupon['canals'],
                'supplier_ids' => $coupon['supplier_ids'],
                'type' => ActivityAndCouponSkuModel::TYPE_COUPON,
                'create_time' => $now,
            ];
        }

        foreach (array_chunk($data, 1000) as $items) {
            ActivityAndCouponSkuModel::insert($items);
            $data = [];
            foreach ($items as $item) {
                $data[$item['sku_name']] = 1;
            }
            $redis->hmset('lie_coupon_sku_' . $couponId, $data);
        }
    }


    /**
     * 获取最佳优惠券
     *
     * @param array $goodsList 商品列表，格式为 [{'goods_id': 123, 'price': 100, 'num': 1}]
     * @param int $userId 用户ID
     * @param int $orgId 组织ID，默认为1（猪芯）
     * @return array 返回结果
     */
    public function getBestCoupon($goodsList, $userId, $orgId = 1)
    {
        try {
            // 验证商品列表格式
            if (empty($goodsList) || !is_array($goodsList)) {
                throw new InvalidRequestException('商品列表不能为空');
            }

            // 获取可用优惠券
            $coupons = (new CouponRepository())->getAvailableCoupons($userId, $orgId);
            if ($coupons->isEmpty()) {
                throw new InvalidRequestException('没有可用的优惠券');
            }
            // 提取商品ID列表
            $goodsIds = array_column($goodsList, 'goods_id');

            // 获取商品详细信息
            $goodsInfo = $this->getGoodsInfo($goodsIds);
            if (empty($goodsInfo)) {
                throw new InvalidRequestException('无法获取商品信息');
            }

            // 构建商品SKU列表，包含价格信息
            $skuList = $this->buildSkuList($goodsList, $goodsInfo);
            // 计算每个优惠券的适用性和优惠金额
            $usableCoupons = [];
            foreach ($coupons as $coupon) {
                $couponInfo = $this->checkCouponUsability($coupon, $skuList);
                if ($couponInfo['isUsable']) {
                    $usableCoupons[] = [
                        'coupon' => $coupon,
                        'preferential' => $couponInfo['preferential']
                    ];
                }
            }
            // 按优惠金额降序排序
            usort($usableCoupons, function ($a, $b) {
                return $b['preferential'] <=> $a['preferential'];
            });

            // 获取最佳优惠券
            if (empty($usableCoupons)) {
                throw new InvalidRequestException('没有适用的优惠券');
            }

            $bestCoupon = $usableCoupons[0];
            $usableCoupons = array_map(function ($coupon) {
                return $this->formatCouponResponse($coupon);
            }, $usableCoupons);
            return [
                'status' => true,
                'data' => ['best_coupon' => $this->formatCouponResponse($bestCoupon), 'coupon_list' => $usableCoupons],
            ];
        } catch (InvalidRequestException $e) {
            throw new InvalidRequestException('系统错误: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
        } catch (\Exception $e) {
            throw new InvalidRequestException('系统错误: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
        }
    }

    /**
     * 获取商品详细信息
     *
     * @param array $goodsIds 商品ID列表
     * @return array 商品信息
     */
    public function getGoodsInfo($goodsIds)
    {
        if (empty($goodsIds)) {
            return [];
        }

        // 将商品ID数组转为逗号分隔的字符串
        $goodsIdsStr = is_array($goodsIds) ? implode(',', $goodsIds) : $goodsIds;

        // 调用商品服务获取商品信息
        $response = Http::get(trim(config('website.goods_server'), '/') . '/synchronization?goods_id=' . $goodsIdsStr);
        $result = json_decode($response->body(), true);

        if (!isset($result['errcode']) || $result['errcode'] != 0 || empty($result['data'])) {
            return [];
        }

        return $result['data'];
    }

    /**
     * 构建SKU列表，包含价格信息
     *
     * @param array $goodsList 原始商品列表
     * @param array $goodsInfo 商品详细信息
     * @return array 构建后的SKU列表
     */
    public function buildSkuList($goodsList, $goodsInfo)
    {
        $skuList = [];

        // 构建商品ID到价格的映射
        $priceMap = [];
        $numMap = [];
        foreach ($goodsList as $item) {
            $priceMap[$item['goods_id']] = $item['price'];
            $numMap[$item['goods_id']] = $item['num'];
        }

        // 构建SKU列表
        foreach ($goodsInfo as $goodsId => $goods) {
            if (empty($goods)) {
                continue;
            }

            $price = $priceMap[$goodsId] ?? 0;

            $skuList[] = [
                'goods_id' => $goodsId,
                'price' => $price,
                'total_amount' => $price * $numMap[$goodsId],  // 单价 * 数量，这里默认数量为1
                'supplier_id' => $goods['supplier_id'] ?? 0,
                'canal' => $goods['canal'] ?? '',
                'brand_id' => $goods['brand_id'] ?? 0,
                'class_id' => $goods['class_id'] ?? 0,
                'goods_type' => $goods['goods_type'] ?? 0,
            ];
        }

        return $skuList;
    }

    /**
     * 检查优惠券是否可用于给定的商品列表
     *
     * @param object $coupon 优惠券对象
     * @param array $skuList 商品SKU列表
     * @return array 可用性信息
     */
    public function checkCouponUsability($userCoupon, $skuList): array
    {
        $coupon = CouponModel::where('coupon_id', $userCoupon->coupon_id)->first();
        // 定义自营和联营商品类型
        $lyGoodsTypes = [1, 2, 6]; // 联营商品类型
        $zyGoodsTypes = [3, 4];    // 自营商品类型

        // 初始化结果
        $result = [
            'isUsable' => false,
            'preferential' => 0,
            'remark' => 0 // 0:可用 -1:不可用 正数:需要凑单的金额
        ];

        // 计算适用金额
        $amount = 0;
        $zyAmount = 0; // 自营商品金额
        $lyAmount = 0; // 联营商品金额

        // 将品牌字符串转换为数组
        $selectedBrandIds = !empty($coupon->selected_brand_id) ? explode(',', $coupon->selected_brand_id) : [];
        $excludeBrandIds = !empty($coupon->exclude_brand_ids) ? explode(',', $coupon->exclude_brand_ids) : [];
        $supplierIds = !empty($coupon->supplier_ids) ? explode(',', $coupon->supplier_ids) : [];
        $canals = !empty($coupon->canals) ? explode(',', $coupon->canals) : [];
        // 根据优惠券的商城类型和商品范围计算适用金额
        switch ($coupon->coupon_mall_type) {
            case 1: // 全站
                // 全站商品都可用
                foreach ($skuList as $sku) {
                    $amount += $sku['total_amount'];
                }
                break;

            case 2: // 自营商城
                // 根据商品范围进一步筛选
                if ($coupon->coupon_goods_range == 1) { // 全站范围
                    foreach ($skuList as $sku) {
                        // 只计算自营商品
                        $goodsType = $sku['goods_type'] ?? 0;
                        if (in_array($goodsType, $zyGoodsTypes)) {
                            $amount += $sku['total_amount'];
                        }
                    }
                } elseif ($coupon->coupon_goods_range == 3) { // 品牌范围
                    foreach ($skuList as $sku) {
                        $goodsType = $sku['goods_type'] ?? 0;
                        $brandId = $sku['brand_id'] ?? 0;

                        // 如果在排除的品牌列表中，跳过
                        if (!empty($excludeBrandIds) && in_array($brandId, $excludeBrandIds)) {
                            continue;
                        }

                        // 如果在指定的品牌列表中且是自营商品，计算金额
                        if (in_array($brandId, $selectedBrandIds) && in_array($goodsType, $zyGoodsTypes)) {
                            $amount += $sku['total_amount'];
                        }
                    }
                } elseif ($coupon->coupon_goods_range == 6) { // 商品名称范围
                    // 这里需要实现商品名称匹配逻辑
                    // 由于没有足够信息，暂时返回不可用
                    $result['isUsable'] = false;
                    return $result;
                }
                break;
            case 3: // 联营商城
                if ($coupon->coupon_goods_range == 1) { // 全站范围
                    foreach ($skuList as $sku) {
                        // 只计算联营商品
                        $goodsType = $sku['goods_type'] ?? 0;
                        if (in_array($goodsType, $lyGoodsTypes)) {
                            $amount += $sku['total_amount'];
                        }
                    }
                } elseif ($coupon->coupon_goods_range == 2) { // 供应商范围
                    // 指定供应商的优惠券
                    if (!empty($coupon->selected_supplier_id) && empty($selectedBrandIds)) {
                        foreach ($skuList as $sku) {
                            $goodsType = $sku['goods_type'] ?? 0;
                            if ($sku['supplier_id'] == $coupon->selected_supplier_id && in_array($goodsType, $lyGoodsTypes)) {
                                $amount += $sku['total_amount'];
                            }
                        }
                    } elseif (!empty($coupon->selected_supplier_id) && !empty($selectedBrandIds)) {
                        // 指定供应商+品牌的优惠券
                        foreach ($skuList as $sku) {
                            $goodsType = $sku['goods_type'] ?? 0;
                            if (
                                $sku['supplier_id'] == $coupon->selected_supplier_id &&
                                in_array($sku['brand_id'], $selectedBrandIds) &&
                                in_array($goodsType, $lyGoodsTypes)
                            ) {
                                $amount += $sku['total_amount'];
                            }
                        }
                    }
                } elseif ($coupon->coupon_goods_range == 3) { // 品牌范围
                    // 兼容旧数据
                    if (!empty($selectedBrandIds) && empty($supplierIds)) {
                        foreach ($skuList as $sku) {
                            $goodsType = $sku['goods_type'] ?? 0;
                            if (in_array($sku['brand_id'], $selectedBrandIds) && in_array($goodsType, $lyGoodsTypes)) {
                                $amount += $sku['total_amount'];
                            }
                        }
                    } elseif (!empty($supplierIds)) {
                        // 新版本的品牌判断
                        foreach ($skuList as $sku) {
                            $goodsType = $sku['goods_type'] ?? 0;

                            // 不属于对应的供应商ID，跳过
                            if (!in_array($sku['supplier_id'], $supplierIds)) {
                                continue;
                            }

                            // 供应商编码不为空，并且不在里面，跳过
                            if (!empty($canals) && !in_array($sku['canal'], $canals)) {
                                continue;
                            }

                            // 在排除的品牌里面，跳过
                            if (!empty($excludeBrandIds) && in_array($sku['brand_id'], $excludeBrandIds)) {
                                continue;
                            }

                            if (!empty($selectedBrandIds)) {
                                // 在设置的品牌里面，并且是联营商品，才算价格
                                if (in_array($sku['brand_id'], $selectedBrandIds) && in_array($goodsType, $lyGoodsTypes)) {
                                    $amount += $sku['total_amount'];
                                }
                            } elseif (in_array($goodsType, $lyGoodsTypes)) {
                                // 没有设置品牌的话，就不去判断品牌了
                                $amount += $sku['total_amount'];
                            }
                        }
                    }
                } elseif ($coupon->coupon_goods_range == 6) { // 商品名称范围
                    // 这里需要实现商品名称匹配逻辑
                    // 由于没有足够信息，暂时返回不可用
                    $result['isUsable'] = false;
                    return $result;
                }
                break;

            default:
                $result['isUsable'] = false;
                return $result;
        }

        // 根据总金额决定是否可用及优惠金额
        if ($amount == 0) {
            // 没有符合条件的商品
            $result['isUsable'] = false;
            $result['remark'] = -1;
        } elseif ($amount < $coupon->require_amount) {
            // 金额不足，需要凑单
            $result['isUsable'] = false;
            $result['remark'] = $coupon->require_amount - $amount; // 还需要凑多少金额
        } else {
            // 可用，计算优惠金额
            $result['isUsable'] = true;
            $result['remark'] = 0;

            // 根据优惠券类型计算优惠金额
            if ($coupon->coupon_type == 1) { // 抵扣券
                $result['preferential'] = $coupon->sale_amount;
            } else { // 折扣券
                $result['preferential'] = $amount - $this->price_format($coupon->sale_amount * $amount, 0, 2);

                // 检查最高优惠限额
                if ($coupon->max_preferential_amount > 0 && $result['preferential'] > $coupon->max_preferential_amount) {
                    $result['preferential'] = $coupon->max_preferential_amount;
                }
            }
        }

        return $result;
    }

    /**
     * 格式化价格
     *
     * @param float $price 价格
     * @param int $type 类型（0为四舍五入，1为四舍六入）
     * @param int $decimals 小数位数
     * @return float 格式化后的价格
     */
    private function price_format($price, $type = 0, $decimals = 2): float
    {
        if ($type == 0) {
            return round($price, $decimals, PHP_ROUND_HALF_UP);
        } else {
            return ceil($price * pow(10, $decimals)) / pow(10, $decimals);
        }
    }

    /**
     * 检查商品类型是否匹配优惠券
     *
     * @param object $coupon 优惠券对象
     * @param array $skuList 商品SKU列表
     * @return bool 是否匹配
     */
    private function checkMallType($coupon, $skuList): bool
    {
        // 如果优惠券适用于全站，则直接返回true
        if ($coupon->coupon_mall_type == 1) {
            return true;
        }

        // 检查每个商品是否符合优惠券的商城类型
        foreach ($skuList as $sku) {
            $mallType = $sku['mall_type'] ?? 0;

            // 如果优惠券只适用于自营商城，但商品不是自营的
            if ($coupon->coupon_mall_type == 2 && $mallType != 2) {
                return false;
            }

            // 如果优惠券只适用于联营商城，但商品不是联营的
            if ($coupon->coupon_mall_type == 3 && $mallType != 3) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查商品范围是否匹配优惠券
     *
     * @param object $coupon 优惠券对象
     * @param array $skuList 商品SKU列表
     * @return bool 是否匹配
     */
    private function checkGoodsRange($coupon, $skuList): bool
    {
        // 如果优惠券适用于全站商品，则直接返回true
        if ($coupon->coupon_goods_range == 1) {
            return true;
        }

        // 根据不同的商品范围类型进行检查
        switch ($coupon->coupon_goods_range) {
            case 2: // 供应商
                return $this->checkSupplier($coupon, $skuList);

            case 3: // 品牌
                return $this->checkBrand($coupon, $skuList);

            case 4: // 分类
                return $this->checkCategory($coupon, $skuList);

            case 5: // 供应商ID
                return $this->checkSupplierId($coupon, $skuList);

            case 6: // 商品名称
                // 这里需要实现商品名称匹配逻辑，但由于没有足够信息，暂时返回true
                return true;

            default:
                return false;
        }
    }

    /**
     * 检查供应商是否匹配
     */
    private function checkSupplier($coupon, $skuList): bool
    {
        if (empty($coupon->selected_supplier)) {
            return true;
        }

        $supplierNames = explode(',', $coupon->selected_supplier);

        foreach ($skuList as $sku) {
            $supplierName = $sku['supplier_name'] ?? '';
            if (!in_array($supplierName, $supplierNames)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查品牌是否匹配
     */
    private function checkBrand($coupon, $skuList): bool
    {
        if (empty($coupon->selected_brand_id)) {
            return true;
        }

        $brandIds = explode(',', $coupon->selected_brand_id);

        // 检查排除的品牌
        $excludeBrandIds = !empty($coupon->exclude_brand_ids) ? explode(',', $coupon->exclude_brand_ids) : [];

        foreach ($skuList as $sku) {
            $brandId = $sku['brand_id'] ?? 0;

            // 如果商品品牌在排除列表中，则不可用
            if (!empty($excludeBrandIds) && in_array($brandId, $excludeBrandIds)) {
                return false;
            }

            // 如果指定了品牌，但商品品牌不在列表中，则不可用
            if (!empty($brandIds) && !in_array($brandId, $brandIds)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查分类是否匹配
     */
    private function checkCategory($coupon, $skuList): bool
    {
        // 获取优惠券关联的分类ID
        $classIds = [];
        if ($coupon->coupon_class && is_array($coupon->coupon_class)) {
            foreach ($coupon->coupon_class as $class) {
                $classIds[] = $class['class_id'];
            }
        }

        if (empty($classIds)) {
            return true;
        }

        foreach ($skuList as $sku) {
            $classId = $sku['class_id'] ?? 0;
            if (!in_array($classId, $classIds)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查供应商ID是否匹配
     */
    private function checkSupplierId($coupon, $skuList): bool
    {
        if (empty($coupon->selected_supplier_id)) {
            return true;
        }

        $supplierIds = explode(',', $coupon->selected_supplier_id);

        foreach ($skuList as $sku) {
            $supplierId = $sku['supplier_id'] ?? 0;
            if (!in_array($supplierId, $supplierIds)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 计算优惠金额
     *
     * @param object $coupon 优惠券对象
     * @param float $totalAmount 订单总金额
     * @return float 优惠金额
     */
    private function calculatePreferential($coupon, $totalAmount): float
    {
        if ($coupon->coupon_type === 1) { // 抵扣券
            return $coupon->sale_amount;
        }

        // 折扣券 (sale_amount是折扣率，如0.8表示8折)
        $preferential = $totalAmount - ($coupon->sale_amount * $totalAmount);

        // 检查最高优惠限额
        if ($coupon->max_preferential_amount > 0) {
            $preferential = min($preferential, $coupon->max_preferential_amount);
        }

        return round($preferential, 2);
    }

    /**
     * 格式化优惠券响应
     *
     * @param array $bestCoupon 最佳优惠券信息
     * @return array 格式化后的响应
     */
    private function formatCouponResponse(array $bestCoupon): array
    {
        return [
            'user_coupon_id' => $bestCoupon['coupon']->user_coupon_id,
            'coupon_id' => $bestCoupon['coupon']->coupon_id,
            'coupon_name' => $bestCoupon['coupon']->coupon->coupon_name ?? $bestCoupon['coupon']->coupon_name ?? '',
            'coupon_type' => $bestCoupon['coupon']->coupon_type,
            'sale_amount' => $bestCoupon['coupon']->sale_amount ?? (float)$bestCoupon['coupon']->coupon->sale_amount ?? 0,
            'preferential' => (float) $bestCoupon['preferential']
        ];
    }


}
