<?php
namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use SoapClient;

//调用erp接口, 统一数组 array
class ThirdErpService
{
    const ERP_QUEUE_NAME = 'lie_queue_erp'; //erp 队列名称

    /*
   * 队列推送队列数据
    *@param  mix $types 类型：
                 1:队列接口soap推送;
                 2:直连soap 推送
                3:队列原生推送，自己拼接json推送到接口

   * @param  array $param  推送数据,如：["CURRENCY"=>'"民币","CUSTOMER"=>"深圳小明电子科技"]
                                      如果 $types=3 ,则是原生推送，自己拼接json推送到接口队列：
   * @param  string $erp_fuc  如果是soap，则是 erp的函数：getCreditAmount 检测账期
   * @param  string $callback_path  相对回调路径：/open/testa
   * @param  string $search_key  日志搜索字段，自定义可以存订单号 ，售后单号 等
   * @param  int $main_time_out  主推送超时时间
   * @param  int $callback_timeout 回调推送时间
   * @param  array $callback_timeout  推送数据
   * @return  array
   */
    private function push($types = 1,$erp_fuc="",$param = [],$callback_path="",$search_key="",$main_time_out=50,$callback_timeout=10)
    {
        $types =  data_get($_REQUEST,"debuga") == 1 ? 2:$types;  //调试模式开始直连

        $exportConfig = get_resource_config('domain');
        $rabmqUrl = $exportConfig["domain"]["api_push_data"];
        $res = ""; //请求返回结果
        switch ($types){
            case 1: #队列推送
                $uk =  md5(json_encode($param).time());
                $data = [
                    "queue_name"=> self::ERP_QUEUE_NAME,
                    "mq_data"=> [
                        "__from"=>  $this->getFromUrl() ?: gethostname(),
                        "__insert_time"=> time(),
                        "__timeout"=>$main_time_out,
                        "__route_key"=> $erp_fuc,
                        "__type"=> "soap",
                        "__uk"=> $uk,
                        "data"=> (object)$param,
                    ]
                ];
                if ($search_key){
                    $data["mq_data"]["__search_key"] = $search_key;
                }
                if ($callback_path){ //存在回调
                    $nowDomain =  str_replace(request()->path(),"",request()->url());
                    $data["mq_data"]["__callback"] =   [                                  // 选填字段,有回调时必填
                        "__callback_url"=> $nowDomain.$callback_path,    // 回调的url路由地址，暂不支持回调soap接口列
                        "__callback_timeout" => $callback_timeout,            // 回调该接口的超时时间，默认为10秒，选填字段
                        "__callback_type" =>"http",              // 回调该接口的请求方式，同"__type"，选填字段
                        //"__callback_verify"=>"verify"         // 若回调的接口需要校验，如请求龙哥的接口需要该字段，选填字段
                        "__callback_search_key" => $search_key,
                    ];
                }

                $response = Http::asJson()->post($rabmqUrl,$data);
                $res = $response->body();
                break;
            case 2: #直连 soap 推送
                $data = $param;
                try {
                    ini_set('soap.wsdl_cache_enabled', '0');
                    libxml_disable_entity_loader(false);
                    $erp =  new SoapClient(Config('config.erp_domain').'/ormrpc/services/WSIchuntjKFacade?wsdl');
                    $res = $erp->$erp_fuc(json_encode($data, JSON_UNESCAPED_SLASHES));
                } catch (\Exception $e) {
                    throw new InvalidRequestException('ERP接口异常，请联系技术查看日志，错误信息：'.$e->getMessage());
                }
                break;
            case 3: #原生推送，自己组织数据
                $data = $param;
                $response = Http::asJson()->post($rabmqUrl,$data);
                $res = $response->body();
                break;
        }

        if (data_get($_REQUEST,"debuga")) {
            print_r("队列接口：<br/>");
            print_r($rabmqUrl);
            print_r("<br/>请求soap函数：".$erp_fuc);
            print_r("<br/>请求数据：");
            print_r(json_encode($data));
            print_r("<br/>返回结果：");
            print_r($res);
            print_r("<br/>");
        }
        Log::info("--- 推送erp数据：".json_encode($data, JSON_UNESCAPED_SLASHES)." 返回数据：".$res);

        $arr = json_decode($res,true);

        if(!array_key_exists("code",$arr)){ //兼容erp旧接口
            if (isset($arr['0000'])) {
                $arrTemp["code"] = 0;
                 $arrTemp["msg"] = "成功";
            } else if (isset($arr['3333'])) { // 用于账期客户额度不够或者有超期的情况
                $arrTemp["code"] = 2;
                $arrTemp["msg"]  = $res;
            } else {
                $arrTemp["code"] = 1;
                $arrTemp["msg"]  = data_get($arr,"4444");
            }

            // $arrTemp["code"] = isset($arr['0000']) ? 0 : 1;
            // $arrTemp["msg"]  = $arrTemp["code"] == 0 ? "成功" : data_get($arr,"4444");
            $arrTemp["data"] = $arr;
            return $arrTemp;
        }else{
            return $arr;
        }
    }

    /*
     * 账期订单 - 检查客户账期额度
     */
    public function checkCustomerAccount($input)
    {
         return $this->push(2, "getCreditAmount", $input);
    }

    // 返回校验发货通知单和预收款单接口
    public function checkBill($data)
    {

        return $this->push(2, 'checkBill', $data);
    }

    /*
   * 售后单，审核通过，已经发货的需要同步售后单到金蝶的（退货申请单）。
       {
       "SERVICE_SN":"F-**************-0",--售后单号
      "TYPE":"5",---已出库的售后  生成销售退货申请单
      "ERPORDERID":"YErgwq33113rkkk",---ERP销售订单ID
       "REMARK":"",//售后备注
        "ITEMS":[ //售后明细
        {
		     "FENTRYID":"6gYMIZtcQ4eNwECbbbAo+4iIKlg=",--erp销售订单明细id
            "FQTY":"20000"--售后数量
            "RMARK":"批次不符合-不符合",//申请原因+ 原因备注
        }
        ]
  }
   */
    public function pushServiceToErp($input)
    {
        return $this->push(1,"updateOrderStatus",$input,"open/erpResultService",$input["SERVICE_SN"]);
    }

    /*
     * erp取消售后单
        订单-post:
           {
                 "TYPE":"3",--固定默认写死
                  "SERVICE_SN":"F-**************-0",--售后单号
          }
     */
    public function cancelServiceOrder($input)
    {
        $input["TYPE"] = 3;
        return $this->push(2,"deleteOrder",$input,"",$input["SERVICE_SN"]);
    }

    /*
 * erp 稀放账期
      {    "SERVICE_SN":"F-12021101876378-0",--售后单号
            "TYPE":"6",--未出库的售后
            "ERPORDERID":"YErgwq33113rkkk",--ERP销售ID
            "ENTRY":[
            {   "FENTRYID":"hwFwjgKESnSER4rAN3jUXIiIKlg=",---ERP明细ID
                 "FQTY":"57"--售后数量
            }
      ]}
 */
    public function offsetUserPayment($input)
    {
        return $this->push(1,"updateOrderStatus",$input);
    }




    // 异常操作 推送到ERP
    public function pushExceptionUpdateToErp($params)
    {
        return $this->push(2, 'updateOrderStatus', $params);
    }

    // 删除ERP单
    public function delErpOrder($params)
    {
        return $this->push(2, 'deleteOrder', $params);
    }

    private function getFromUrl()
    {
        $domain = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
        $requestUrl = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        $fromUrl = '';
        if ($domain && $requestUrl) {
            $fromUrl = $domain . $requestUrl;
        }
        return $fromUrl;
    }

    /*
   *获取erp币种所有数据
   */
    public function getExchangeRateList($BIZDATE = "" ){

        $param = ["BIZDATE"=>$BIZDATE ? $BIZDATE: date("Y-m-d")];
        return $this->push(2, 'getExchangeRateList',$param);
    }

    // createJKmaterial
    public function createJKmaterial($params)
    {
        return $this->push(2, 'createJKmaterial', $params);
    }


}
