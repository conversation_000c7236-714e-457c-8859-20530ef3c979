<?php

namespace App\Http\Services;

use App\Enum\CompanyEnum;
use App\Http\Models\Cube\ActivityModel;
use Carbon\Carbon;

class ActivityService
{
    public static function getList($params)
    {
        $where = self::buildListWhere($params);
        $listData = ActivityModel::getListByWhere($where, $params['page'] ?? 1, $params['limit'] ?? 10);
        foreach ($listData['data'] as &$datum) {
            if ($datum['org_id']==CompanyEnum::LiexXinCompanyId){
                $datum['web_url'] = (get_resource_config_section('app',
                            'cube')['PCActivityUrl'] ?? "") . "/{$datum['activity_url']}";
                $datum['h5_url'] = (get_resource_config_section('app',
                            'cube')['H5ActivityUrl'] ?? "") . "/{$datum['activity_url']}";
            }elseif($datum['org_id']==CompanyEnum::HuaYunCompanyId){
                $datum['web_url'] = (config("website.iedge_domain")??"") . "/activity/{$datum['activity_url']}";
            }
            $datum['org_name'] = CompanyEnum::$COMPANY_LIST[$datum['org_id']] ?? "";

//            if ($datum['activity_status'] != ActivityModel::ACTIVITY_STATUS_ONLINE) {
//                $datum['web_url'] .= "?is_preview=1";
//                $datum['h5_url'] .= "?is_preview=1";
//            }
        }

        $data = [
            'total' => $listData['total'],
            'list'  => $listData['data'],
        ];

        return $data;
    }


    /**
     * buildWhere
     * @param $params = [
     * 'page'        => $request->input("page",1),//页数
     * 'limit'       => $request->input("limit",10),//每页数量
     * 'activity_no' => $request->input("activity_no"),//活动编号
     * 'activity_url' => $request->input("activity_url"),//活动地址
     * 'activity_type' => $request->input("activity_type"),//活动分类(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动)
     * 'activity_status' => $request->input("activity_status"),//活动状态(1待配置,2已配置未上线,3已上线,-1已过期)
     * 'activity_time' => $request->input("activity_time"),//活动时间
     * 'create_time' => $request->input("create_time"),//创建时间
     * ];
     * @return array
     * <AUTHOR>
     * Time: 9:48 AM
     */
    public static function buildListWhere($params)
    {
        $where = [];
        if (isset($params['activity_no']) && $params['activity_no']) {
            $where[] = [
                'activity_no',
                '=',
                $params['activity_no']
            ];
        }
        if (isset($params['activity_url']) && $params['activity_url']) {
            $where[] = [
                'activity_url',
                '=',
                $params['activity_url']
            ];
        }
        if (isset($params['activity_type']) && $params['activity_type']) {
            $where[] = [
                'activity_type',
                '=',
                $params['activity_type']
            ];
        }
        if (isset($params['activity_status']) && $params['activity_status']) {
            $where[] = [
                'activity_status',
                '=',
                $params['activity_status']
            ];
        }
        if (isset($params['activity_enable']) && $params['activity_enable']) {
            $where[] = [
                'activity_enable',
                '=',
                $params['activity_enable']
            ];
        }
        if (isset($params['activity_time']) && $params['activity_time']) {
            list($start, $end) = explode('~', $params['activity_time']);
            $where[] = ['activity_start_time', '<=', Carbon::parse(trim($end))->startOfDay()->timestamp];
            $where[] = ['activity_end_time', '>=', Carbon::parse(trim($start))->endOfDay()->timestamp];
        }
        if (isset($params['create_time']) && $params['create_time']) {
            list($start, $end) = explode('~', $params['create_time']);
            $where[] = ['create_time', '>=', Carbon::parse(trim($start))->startOfDay()->timestamp];
            $where[] = ['create_time', '<=', Carbon::parse(trim($end))->endOfDay()->timestamp];
        }
        // 如果有查看所有的权限，则直接返回
        if (!PermService::hasPerm(ActivityModel::CUBE_ACTIVITY_VIEW_ALL)&&request()->user->email!="<EMAIL>") {
            $where[] = [
                'org_id',
                '=',
                request()->user->org_id
            ];
        }


        return $where;
    }


    public static function add($params)
    {
        list($start, $end) = explode('~', $params['activity_time']);
        //查询活动是否存在
        $info = ActivityModel::getOneByUrl($params['activity_url']);
        if ($info) {
            throw new \InvalidArgumentException("活动地址已存在");
        }

        $data = [
            "org_id"                  => $params['org_id'] ?? CompanyEnum::LiexXinCompanyId,
            "activity_no"             => self::generateActivityNo(),
            //活动编号
            "activity_name"           => $params['activity_name'] ?? "",
            //活动名称
            "activity_type"           => $params['activity_type'] ?? "",
            //活动分类(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动)
            "activity_url"            => $params['activity_url'] ?? "",
            //活动地址
            "activity_start_time"     => strtotime($start),
            //活动开始时间
            "activity_end_time"       => strtotime($end),
            //活动结束时间
            "web_type"                => $params['web_type'] ?? "",
            "activity_status"         => $params['activity_status'] ?? ActivityModel::ACTIVITY_STATUS_CONFIG_NOT_ONLINE,
            //活动状态(1待配置,2已配置未上线,3已上线,-1已过期)
            "create_time"             => time(),
            "create_user_id"          => request()->user->userId,
            "create_user_name"        => request()->user->name,
            "activity_enable"         => ActivityModel::ACTIVITY_ENABLE_DISABLE,
            "web_title"               => $params['web_title'] ?? "",
            "web_keywords"            => $params['web_keywords'] ?? "",
            "web_description"         => $params['web_description'] ?? "",
            "web_html"                => $params['web_html'] ?? "",
            "web_html_config"         => $params['web_html_config'] ?? "",
            "h5_html"                 => $params['h5_html'] ?? "",
            "h5_html_config"          => $params['h5_html_config'] ?? "",
            "update_time"             => time(),
            "is_activity_center_show" => $params['is_activity_center_show'],
            "image_pc"                => $params['image_pc'],
            "image_h5"                => $params['image_pc'],
        ];
        $data['id'] = ActivityModel::insertData($data);
        return $data;
    }

    public static function update($id, $params)
    {
        $info = self::getInfo($id);
        if (!$info) {
            throw new \Exception("活动不存在");
        }
        if ($info['activity_status'] == ActivityModel::ACTIVITY_STATUS_EXPIRED) {
            throw new \Exception("活动已过期,不可修改");
        }

        list($start, $end) = explode('~', $params['activity_time']);
        $updateData = [];
        $updateData['activity_name'] = $params['activity_name'] ?? null;
        $updateData['activity_type'] = $params['activity_type'] ?? null;
        $updateData['activity_url'] = $params['activity_url'] ?? null;
        $updateData['activity_start_time'] = strtotime($start);
        $updateData['activity_end_time'] = strtotime($end);
        $updateData['web_type'] = $params['web_type'] ?? null;
        $updateData['web_title'] = $params['web_title'] ?? null;
        $updateData['web_keywords'] = $params['web_keywords'] ?? null;
        $updateData['web_description'] = $params['web_description'] ?? null;
        $updateData['is_activity_center_show'] = $params['is_activity_center_show'] ?? null;
        $updateData['image_pc'] = $params['image_pc'] ?? null;
        $updateData['image_h5'] = $params['image_h5'] ?? null;
        $updateData = array_filter($updateData);
        if ($info['activity_status'] == ActivityModel::ACTIVITY_STATUS_ONLINE) {
            unset($updateData['activity_url']);
        }

        if ($updateData) {
            $updateData['update_time'] = time();
            ActivityModel::updateById($id, $updateData);
        }

        //更新活动状态
        self::checkAndUpdateStatus($id);
        return $updateData;
    }

    public static function delete($id)
    {
        $info = self::getInfo($id);
        if (!$info) {
            throw new \Exception("活动不存在");
        }
        ActivityModel::deleteById($id);
        return true;
    }

    public static function getInfo($id)
    {
        $info = ActivityModel::getOneById($id);
        if ($info) {
            if ($info['org_id']==CompanyEnum::LiexXinCompanyId){
                $info['web_url'] = (get_resource_config_section('app',
                            'cube')['PCActivityUrl'] ?? "") . "/{$info['activity_url']}";
                $info['h5_url'] = (get_resource_config_section('app',
                            'cube')['H5ActivityUrl'] ?? "") . "/{$info['activity_url']}";
            }elseif($info['org_id']==CompanyEnum::HuaYunCompanyId){
                $info['web_url'] = (config("website.iedge_domain")??"") . "/activity/{$info['activity_url']}";
            }
        }
        return $info;
    }


    public static function getInfoByCode($code)
    {
        $info = ActivityModel::getOneByUrl($code);
        return $info;
    }

    public static function copy($id)
    {
        $info = self::getInfo($id);
        if (!$info) {
            throw new \Exception("活动不存在");
        }
        $data = [
            "activity_no"         => self::generateActivityNo(),//活动编号
            "activity_name"       => $info['activity_name'] . "-复制" ?? "",//活动名称
            "org_id"              => $info['org_id'],//组织
            "activity_type"       => $info['activity_type'] ?? "",//活动分类(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动)
            "activity_url"        => $info['activity_url'] . "-cp" ?? "",//活动地址
            "activity_start_time" => time(),//活动开始时间
            "activity_end_time"   => time() + 86400 * 365,//活动结束时间
            "web_type"            => $info['web_type'] ?? "",
            "activity_status"     => ActivityModel::ACTIVITY_STATUS_WAIT_CONFIG,//活动状态(1待配置,2已配置未上线,3已上线,-1已过期)
            "create_time"         => time(),
            "create_user_id"      => request()->user->userId,
            "create_user_name"    => request()->user->name,
            "activity_enable"     => $info['activity_enable'] ?? "",
            "web_title"           => $info['web_title'] ?? "",
            "web_keywords"        => $info['web_keywords'] ?? "",
            "web_description"     => $info['web_description'] ?? "",
            "web_html"            => $info['web_html'] ?? "",
            "web_html_config"     => $info['web_html_config'] ?? "",
            "h5_html"             => $info['h5_html'] ?? "",
            "h5_html_config"      => $info['h5_html_config'] ?? "",
            "update_time"         => time(),
        ];
        $data['id'] = ActivityModel::insertData($data);
        return $data;
    }

    public static function activityEnable($id, $status)
    {
        $info = self::getInfo($id);
        if (!$info) {
            throw new \Exception("活动不存在");
        }

        ActivityModel::updateById($id, ['activity_enable' => $status]);

        //更新活动状态
        self::checkAndUpdateStatus($id);
        return true;
    }

    public static function updatePcData($id, $html, $configJsonData)
    {
        $info = self::getInfo($id);
        if (!$info) {
            throw new \Exception("活动不存在");
        }
        $updateData = [];
        if (!empty($html)) {
            $updateData['web_html'] = $html;
        }
        if (!empty($configJsonData)) {
            $updateData['web_html_config'] = $configJsonData;
        }
//        var_dump($html);die;
        if (!empty($updateData)) {
            ActivityModel::updateById($id, $updateData);
        }
        return true;
    }

    public static function updateH5Data($id, $html, $configJsonData)
    {
        $info = self::getInfo($id);
        if (!$info) {
            throw new \Exception("活动不存在");
        }
        $updateData = [];
        if (!empty($html)) {
            $updateData['h5_html'] = $html;
        }
        if (!empty($configJsonData)) {
            $updateData['h5_html_config'] = $configJsonData;
        }
        if (!empty($updateData)) {
            ActivityModel::updateById($id, $updateData);
        }
        return true;
    }

    /**
     * 生成活动编码,A+YYMMDD+4位自增编码
     * <AUTHOR>
     * Time: 9:58 AM
     */
    public static function generateActivityNo()
    {
        $date = date('ymd');
        $activityNo = 'A' . $date;
        $activityNoCount = ActivityModel::getTodayTaskCount();
        $activityNoCount = $activityNoCount + 1;
        $activityNoCount = str_pad($activityNoCount, 4, '0', STR_PAD_LEFT);
        $activityNo = $activityNo . $activityNoCount;
        return $activityNo;
    }

    public static function checkAndUpdateStatus($id)
    {
        $info = self::getInfo($id);
        if (!$info) {
            throw new \Exception("活动不存在");
        }
        //如果是禁用状态,则直接下架
        if ($info['activity_enable'] == ActivityModel::ACTIVITY_ENABLE_DISABLE) {
            ActivityModel::updateById($id, ['activity_status' => ActivityModel::ACTIVITY_STATUS_CONFIG_NOT_ONLINE]);
            return true;
        }
        //如果是启用状态,则判断是否过期,没过期则自动上线,否则自动下架
        if ($info['activity_enable'] == ActivityModel::ACTIVITY_ENABLE_ENABLE) {
            $nowTime = time();
            //活动的时间要strtotime转换一下
            $startTime = strtotime($info['activity_start_time']);
            $endTime = strtotime($info['activity_end_time']);
            if ($nowTime < $startTime) {
                //未开始
                ActivityModel::updateById($id, ['activity_status' => ActivityModel::ACTIVITY_STATUS_CONFIG_NOT_ONLINE]);
            } elseif ($nowTime >= $startTime && $nowTime <= $endTime) {
                //进行中
                ActivityModel::updateById($id, ['activity_status' => ActivityModel::ACTIVITY_STATUS_ONLINE]);
            } elseif ($nowTime > $endTime) {
                //已过期
                ActivityModel::updateById($id, ['activity_status' => ActivityModel::ACTIVITY_STATUS_EXPIRED]);
            }
        }
    }

}
