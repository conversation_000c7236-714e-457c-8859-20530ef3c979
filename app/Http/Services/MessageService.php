<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class MessageService
{

    /**
     * 发送短信
     * template 消息系统模板名称
     * data     数据
     * toUser   收件人
     */
    public static function sendSms($template, $data, $toUser, $isIgnore = 1)
    {
        Log::info('发送短信日志， 模板：' . $template . '，请求数据：' . json_encode($data) . '，收件人：' . json_encode($toUser));

        //调用消息接口
        $url = 'https://api.ichunt.com/msg/sendmessagebyauto';

        $msgInfo = [];
        $msgInfo['data'] = $data;

        $msgInfo = json_encode($msgInfo['data'], JSON_UNESCAPED_UNICODE);

        $msgData = [
            "pf" => 1,
            'data' => $msgInfo,
        ];

        $msgData['keyword'] = $template;

        if (is_array($toUser)) {
            $msgData['touser'] = json_encode($toUser);
        } else {
            $msgData['touser'] = $toUser;
        }

        $msgData['touser'] = json_encode($toUser);
        $msgData['is_ignore'] = $isIgnore;
        $msgData['is_liexin'] = 1; // 过滤csrf校验
        $res = self::requestApi($url, $msgData);
        if (!$res || $res['err_code'] != 0) return false;

        return true;
    }

    /**
     * 发送邮件
     * template 消息系统模板名称
     * data     数据
     * toUser   收件人
     */
    public static function sendMail($template, $data, $toUser, $isIgnore = 1)
    {
        Log::info('发送邮件日志， 模板：' . $template . '，请求数据：' . json_encode($data) . '，收件人：' . json_encode($toUser));

        //调用消息接口
        $url = rtrim(Config('website.api_domain'), '/') . '/msg/sendmessagebyauto';

        $msgInfo = [];
        $msgInfo['data'] = $data;

        $msgInfo = json_encode($msgInfo['data'], JSON_UNESCAPED_UNICODE);

        $msgData = [
            "pf" => 1,
            'data' => $msgInfo,
        ];

        $msgData['keyword'] = $template;

        if (is_array($toUser)) {
            $msgData['touser'] = json_encode($toUser);
        } else {
            $msgData['touser'] = $toUser;
        }

        $msgData['touser'] = json_encode($toUser);
        $msgData['is_ignore'] = $isIgnore;
        $msgData['is_liexin'] = 1; // 过滤csrf校验
        $res = self::requestApi($url, $msgData);
        if (!$res || $res['err_code'] != 0) return false;

        return true;
    }


    /**
     * 发送钉钉消息
     * template 消息系统模板名称
     * data     数据
     * toUser   收件人
     */
    public static function sendDinDinAndEmailById($template, $data, $id)
    {
        Log::info('发送钉钉日志， 模板：' . $template . '，请求数据：' . json_encode($data) . '，收件人：' . json_encode($id));

        $msgInfo = [];
        $msgInfo['data'] = $data;
        $msgInfo = json_encode($msgInfo['data'], JSON_UNESCAPED_UNICODE);

        $msgData = [
            "keyword" => $template,
            "pf" => 1,
            'touser' => json_encode($id),
            'data' => $msgInfo,
        ];
        //调用消息接口
        $url = rtrim(Config('website.api_domain'), "/") . 'msg/sendmessagebyauto';
        $msgData['is_ignore'] = 0;
        $msgData['is_liexin'] = 1; // 过滤csrf校验
        $res = self::requestApi($url, $msgData);
        if (!$res || $res['err_code'] != 0) return false;
        return true;
    }

    // 调用ichuntapi项目接口
    public static function requestApi($url, $data)
    {
        $data['k1'] = time();
        $data['k2'] = md5(md5($data['k1']) . Config('website.UploadKey'));

        try {
            $response = Http::asForm()->post($url, $data);
        } catch (Exception $e) {
            throw new InvalidRequestException('发送消息失败，异常原因：' . $e->getMessage());
        }

        return json_decode($response->body(), true);
    }


}

