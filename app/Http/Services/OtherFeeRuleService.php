<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use Illuminate\Support\Facades\DB;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Models\Cube\OtherFeeRuleModel;
use App\Http\Models\Cube\OperationLogModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class OtherFeeRuleService
{
    /**
     * 获取其他费用规则列表
     * @param array $map
     * @return array
     */
    public function getOtherFeeRuleList($map = [])
    {
        $query = OtherFeeRuleModel::where('status', '!=', OtherFeeRuleModel::STATUS_DELETED);

        if (!empty($map['supplier_code'])) {
            $query->where('supplier_code', $map['supplier_code']);
        }

        if (!empty($map['supplier_id'])) {
            $query->where('supplier_id', $map['supplier_id']);
        }

        if (!empty($map['status'])) {
            $query->where('status', $map['status']);
        }

        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        $total = $query->count();
        $list = $query->orderBy('id', 'desc')
            ->with(['user_info', 'supplier', 'supplier_code'])
            ->offset(($map['page'] - 1) * $map['limit'])
            ->limit($map['limit'])
            ->get()
            ->toArray();

        // 处理返回结果，将JSON规则展开为字段
        if (!empty($list)) {
            foreach ($list as &$item) {
                if (!empty($item['rolling_rule']) && is_array($item['rolling_rule'])) {
                    foreach ($item['rolling_rule'] as $key => $value) {
                        $item['rolling_' . $key] = $value;
                    }
                }

                if (!empty($item['operation_rule']) && is_array($item['operation_rule'])) {
                    foreach ($item['operation_rule'] as $key => $value) {
                        $item['operation_' . $key] = $value;
                    }
                }
                $item['create_time'] = $item['create_time']?date('Y-m-d H:i:s', $item['create_time']):'';
                $item['update_time'] = $item['update_time']?date('Y-m-d H:i:s', $item['update_time']):'';
                $item['rolling_rule_format'] = $this->formatRuleContent($item['rolling_rule']);
                $item['operation_rule_format'] = $this->formatRuleContent($item['operation_rule']);
            }
        }

        return [
            'data' => $list,
            'total' => $total
        ];
    }

    /**
     * 根据ID获取其他费用规则
     * @param $id
     * @return mixed
     */
    public function getOtherFeeRuleById($id)
    {
        return OtherFeeRuleModel::find($id);
    }

    /**
     * 保存其他费用规则
     * @param $data
     * @return bool
     * @throws \Exception
     */
    public function saveOtherFeeRule($data)
    {
        $userId = request()->user->userId;
        $createName = request()->user->name;
        $updateName = request()->user->name;
        $now = time();

        // 检查规则是否已存在
        $query = OtherFeeRuleModel::where('supplier_id', $data['supplier_id'])
            ->where('status', '!=', OtherFeeRuleModel::STATUS_DELETED);

        // 如果是更新操作，排除当前记录
        if (!empty($data['id'])) {
            $query->where('id', '!=', $data['id']);
        }

        // 如果supplier_id为17，还需要检查supplier_code
        if ($data['supplier_id'] == 17) {
            $query->where('supplier_code', $data['supplier_code']);
            // 检查是否存在
            if ($query->exists()) {
                throw new InvalidRequestException('该供应商编码已存在其他费用规则');
            }
        } else {
            // 检查是否存在
            if ($query->exists()) {
                throw new InvalidRequestException('该供应商已存在其他费用规则');
            }
        }

        // 开启事务
        DB::beginTransaction();
        try {
            // 准备打卷费规则数据
            $rollingRule = [
                'is_charge' => (int)$data['rolling_rule']['is_charge'] ?? 0,
            ];

            // 如果收费，则处理费用配置
            $rollingRule['config_type'] = (int) $data['rolling_rule']['config_type'] ?? OtherFeeRuleModel::CONFIG_TYPE_ALL;
            if ($data['rolling_rule']['is_charge'] == 1) {
                $rollingRule['package_keyword'] = $data['rolling_rule']['package_keyword'] ?? '';
                $rollingRule['cny_fee'] = (float) $data['rolling_rule']['cny_fee'] ?? 0;
                $rollingRule['usd_fee'] = (float) $data['rolling_rule']['usd_fee'] ?? 0;
            } else {
                $rollingRule['config_type'] = OtherFeeRuleModel::CONFIG_TYPE_ALL;
                $rollingRule['cny_fee'] = 0;
                $rollingRule['usd_fee'] = 0;
                $rollingRule['package_keyword'] = '';
            }

            // 准备操作费规则数据
            $operationRule = [
                'is_charge' => (int)$data['operation_rule']['is_charge'] ?? 0,
            ];

            // 如果收费，则处理费用配置
            $operationRule['config_type'] = (int) $data['operation_rule']['config_type'] ?? OtherFeeRuleModel::CONFIG_TYPE_ALL;
            if ($data['operation_rule']['is_charge'] == 1) {
                $operationRule['package_keyword'] = $data['operation_rule']['package_keyword'] ?? '';
                $operationRule['cny_fee'] = (float) $data['operation_rule']['cny_fee'] ?? 0;
                $operationRule['usd_fee'] = (float) $data['operation_rule']['usd_fee'] ?? 0;
            } else {
                $operationRule['config_type'] = OtherFeeRuleModel::CONFIG_TYPE_ALL;
                $operationRule['cny_fee'] = 0;
                $operationRule['usd_fee'] = 0;
                $operationRule['package_keyword'] = '';
            }

            // 如果有ID，则是修改
            if (!empty($data['id'])) {
                $rule = OtherFeeRuleModel::find($data['id']);
                if (empty($rule)) {
                    throw new InvalidRequestException('规则不存在');
                }

                // 记录修改前的数据，用于记录日志
                $oldRollingRule = $this->formatRuleContent($rule->rolling_rule);
                $oldOperationRule = $this->formatRuleContent($rule->operation_rule);

                $logContent = '修改其他费用规则，原数据：' . "\n";
                $logContent .= '打卷费规则：' . $oldRollingRule . "\n";
                $logContent .= '操作费规则：' . $oldOperationRule;

                // 记录操作日志
                OperationLogModel::insert([
                    'rule_id' => $rule->id,
                    'operator_id' => $userId,
                    'operation_time' => $now,
                    'type' => OperationLogModel::TYPE_OTHER_FEE_RULE,
                    'content' => $logContent,
                    'create_uid' => $userId,
                    'create_time' => $now,
                    'create_name' => $createName,
                ]);

                // 更新数据
                $rule->supplier_id = $data['supplier_id'];
                $rule->supplier_code = $data['supplier_code'] ?? '';
                $rule->rolling_rule = $rollingRule;
                $rule->operation_rule = $operationRule;
                $rule->update_uid = $userId;
                $rule->update_name = $updateName;
                $rule->update_time = time();
                $rule->save();
            } else {
                // 新增
                $rule = new OtherFeeRuleModel();
                $rule->supplier_id = $data['supplier_id'];
                $rule->supplier_code = $data['supplier_code'] ?? '';
                $rule->rolling_rule = $rollingRule;
                $rule->operation_rule = $operationRule;
                $rule->status = $data['status'] ?? OtherFeeRuleModel::STATUS_ENABLE;
                $rule->create_uid = $userId;
                $rule->create_name = $createName;
                $rule->create_time = $now;
                $rule->update_uid = $userId;
                $rule->update_name = $updateName;
                $rule->update_time = $now;
                $rule->save();

                // 记录操作日志
                OperationLogModel::insert([
                    'rule_id' => $rule->id,
                    'operator_id' => $userId,
                    'operation_time' => $now,
                    'type' => OperationLogModel::TYPE_OTHER_FEE_RULE,
                    'content' => '新建其他费用规则',
                    'create_uid' => $userId,
                    'create_time' => $now,
                    'create_name' => $createName,
                ]);
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 删除其他费用规则
     * @param $id
     * @return bool
     * @throws \Exception
     */
    public function deleteOtherFeeRule($id)
    {
        $userId = request()->user->userId;
        $createName = request()->user->name;
        $now = time();

        // 开启事务
        DB::beginTransaction();
        try {
            $rule = OtherFeeRuleModel::find($id);
            if (empty($rule)) {
                throw new InvalidRequestException('规则不存在');
            }

            // 软删除
            $rule->status = OtherFeeRuleModel::STATUS_DELETED;
            $rule->update_uid = $userId;
            $rule->update_name = $createName;
            $rule->update_time = $now;
            $rule->save();

            // 记录操作日志
            OperationLogModel::insert([
                'rule_id' => $rule->id,
                'operator_id' => $userId,
                'operation_time' => $now,
                'type' => OperationLogModel::TYPE_OTHER_FEE_RULE,
                'content' => '删除其他费用规则',
                'create_uid' => $userId,
                'create_time' => $now,
                'create_name' => $createName,
            ]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 修改其他费用规则状态
     * @param $id
     * @param $status
     * @return bool
     * @throws \Exception
     */
    public function changeStatus($id, $status)
    {
        $userId = request()->user->userId;
        $createName = request()->user->name;
        $now = time();

        // 开启事务
        DB::beginTransaction();
        try {
            $rule = OtherFeeRuleModel::find($id);
            if (empty($rule)) {
                throw new InvalidRequestException('规则不存在');
            }

            // 修改状态
            $rule->status = $status;
            $rule->update_uid = $userId;
            $rule->update_name = $createName;
            $rule->update_time = $now;
            $rule->save();

            // 记录操作日志
            $content = $status == OtherFeeRuleModel::STATUS_ENABLE ? '启用其他费用规则' : '禁用其他费用规则';
            OperationLogModel::insert([
                'rule_id' => $rule->id,
                'operator_id' => $userId,
                'operation_time' => $now,
                'type' => OperationLogModel::TYPE_OTHER_FEE_RULE,
                'content' => $content,
                'create_uid' => $userId,
                'create_time' => $now,
                'create_name' => $createName,
            ]);

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 格式化规则内容
     * @param array $rule 规则数据
     * @return string
     */
    public function formatRuleContent($rule)
    {
        if (empty($rule) || empty($rule['is_charge'])) {
            return '不收费';
        }

        $content = '';
        if ($rule['is_charge'] == 1) {
            if ($rule['config_type'] == 1) {
                $content = '应用全渠道商品<br>';
            } else {
                $content = '按包装方式匹配<br>';
                $content .= '包装方式：' . ($rule['package_keyword'] ?? '') . '；';
            }

            $content .= '人民币：' . ($rule['cny_fee'] ?? 0) . '元；';
            $content .= '美金：' . ($rule['usd_fee'] ?? 0) . '美元';
        }

        return $content;
    }

}
