<?php

namespace App\Http\Services;

use App\Exceptions\InvalidRequestException;
use App\Http\IdSender\IdSender;
use App\Http\Models\Cube\ChannelDiscountModel;
use App\Http\Models\Cube\StepPriceExtendModel;
use App\Http\Models\Cube\StepPriceNewModel;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use Illuminate\Support\Facades\Redis;

class ChannelDiscountService extends  BaseService
{

    public static function getChannelDiscountList($paramsData=[]){
        $pageInfo["page"] = arrayGet($paramsData, "page", 1, "intval");
        $pageInfo["limit"] = arrayGet($paramsData, "limit", static::_LIMIT_, "intval");
        $list = self::buildChannelDiscountList($paramsData, $pageInfo);
        $arr = $list["data"] ?? [];
        foreach($arr as $k=>$item){
            $arr[$k]["goods_name"] = $item["step_price_extend"]["goods_name"] ?? "";
            $arr[$k]["brand"] = $item["step_price_extend"]["brand"] ?? "";
            if(!empty($item["step_price_extend"]["brand_ids"])){
                $arr[$k]["brand"] =  (new StandardBrandService())->getStandardBrandNameListByBrandIds($item["step_price_extend"]["brand_ids"]);
            }
            $arr[$k]["eccn"] = $item["step_price_extend"]["eccn"] ?? "";
            $arr[$k]["file_url"] = $item["step_price_extend"]["file_url"] ?? "";
            unset($arr[$k]["step_price_extend"]);
            $arr[$k]["supplier_id_or_code"] = "";
            if($item["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU){
                $arr[$k]["supplier_id_or_code"] =  $item["supplier_id"];
            }elseif($item["sup_type"] == StepPriceNewModel::SUPTYPE_ZHUANYING){
                $arr[$k]["supplier_id_or_code"] =  $item["supplier_code"];
            }

            $arr[$k]["ration"] =  decimal_number_format($item["ration"]*100);
            $arr[$k]["ration_usd"] =  decimal_number_format($item["ration_usd"]*100);

            $arr[$k]["ration_format"] =  sprintf("¥%s%%",decimal_number_format($item["ration"]*100));
            $arr[$k]["ration_usd_format"] =  sprintf("$%s%%",decimal_number_format($item["ration_usd"]*100));
            $arr[$k]["status_format"] = \Arr::get(ChannelDiscountModel::$STATUS_FORMAT,$item["status"],"");


            $arr[$k]["create_time"] =  dateDefault($item["create_time"]);
        }
        return [$arr,$list["total"]];
    }

    public static function buildChannelDiscountList($reqParams, $pageInfo){
        $query = ChannelDiscountModel::query();
        $buildEqualQueryData = [];

        if($reqParams["supplier_id"]){
            $query->where(function($q) use($reqParams) {
                $q->where(function ($q1) use($reqParams){
                    $q1->where("supplier_code",trim($reqParams["supplier_id"]))->where("supplier_id",0);
                })->orWhere(function ($q2) use($reqParams){
                    $q2->where("supplier_id",intval($reqParams["supplier_id"]))->where("supplier_code","");
                });
            });
        }

        if($reqParams["status"] !== ""){
            $buildEqualQueryData["status"] = (int)$reqParams["status"];
        }

        if($reqParams["is_default"] !== ""){
            $buildEqualQueryData["is_default"] = (int)$reqParams["is_default"];
        }

        $supplierCode = arrayGet($reqParams,"supplier_code");
        if(!empty($supplierCode)){
            $supplierCodeList = explode(",",$supplierCode);
            array_map(function($v){
                return strtoupper($v);
            },$supplierCodeList);

            $query->where(function($q) use($supplierCodeList){
                $q->where(function($q)use($supplierCodeList){
                    $q->whereIn("supplier_code",$supplierCodeList)->where("sup_type",2);
                })->orWhere(function($q)use($supplierCodeList){
                    $q->whereIn("supplier_id",$supplierCodeList)->where("sup_type",1);
                });
            });

        }
        $brandNames = arrayGet($reqParams,"brand_name");
        if(!empty($brandNames)){
            $brandNames = explode(",",$brandNames);
            $brandNames = array_filter_unique($brandNames);
            foreach($brandNames as $brandName){
                $query->whereHasIn("stepPriceExtend",function($q) use($brandName){
                $q->where("brand","like","%".trim($brandName)."%");
            });
            }

        }

        $query = $query->select("*")->buildEqualQuery($buildEqualQueryData)->with(["stepPriceExtend"=>function($q){
            $q->where("type",StepPriceExtendModel::PRICE_EXTEND_TYPE_DISCOUNT);
        }]);
        $list = $query->orderBy("channel_disct_id", "desc")->getList($pageInfo["page"], $pageInfo["limit"]);
        return $list->toArray();
    }

    public static function getChannelDisctById($channelDisctId){
        return ChannelDiscountModel::getChannelDisctById($channelDisctId);
    }


    /**
     * Notes:启用 禁用
     * User: sl
     * Date: 2023-03-10 14:12
     * @param $channelDisctId
     * @param $update
     * @return mixed
     * @throws InvalidRequestException
     */
    public static function updatechannelDisctStatus($channelDisctId,$update){
        $update["update_time"] = time();
        $channelDisctInfo = ChannelDiscountService::getChannelDisctById($channelDisctId);
        if(empty($channelDisctInfo)){
            throw new InvalidRequestException("没找到关联的渠道折扣信息");
        }
        //启用  需要判断是否能启用 可能相关信息已经存在了
        if($update["status"] == ChannelDiscountModel::$STATUS["status_enable"]){
            $isExistsSameInfo =  ChannelDiscountModel::isExistsSameInfo(
                $channelDisctInfo["sup_type"],
                $channelDisctInfo["order"],
                $channelDisctInfo["supplier_name"],
                $channelDisctInfo["supplier_id"],
                $channelDisctInfo["supplier_code"]
            );
            if(!empty($isExistsSameInfo)){
                throw new InvalidRequestException("已经存在类似的渠道折扣信息，无法启用");
            }
            //新增缓存信息
            self::addCnannelDiscountCache($channelDisctInfo["sup_type"],[
                "supplier_id"=>$channelDisctInfo["supplier_id"] ?? 0,
                "supplier_code"=>$channelDisctInfo["supplier_code"] ?? "",
                "order"=>$channelDisctInfo["order"],
                "ration"=>$channelDisctInfo["ration"],
                "ration_usd"=>$channelDisctInfo["ration_usd"],
                "goods_name"=>$channelDisctInfo["step_price_extend"]["goods_name"] ?? "",
                "brand_ids"=> $channelDisctInfo["step_price_extend"]["brand_ids"] ?? "",
            ]);

        }else{
            //禁用  删除缓存信息
            //删除对应的售价组缓存
            self::deleteCnannelDiscountWithOrderCache($channelDisctInfo["sup_type"],[
                "supplier_id"=>$channelDisctInfo["supplier_id"] ?? 0,
                "supplier_code"=>$channelDisctInfo["supplier_code"] ?? "",
                "order"=>$channelDisctInfo["order"],
            ]);
        }

        ChannelDiscountModel::where("channel_disct_id",intval($channelDisctId))->update($update);

        \Log::channel("channelDidcount")->info("---------------禁用启用渠道折扣信息------------------");
        \Log::channel("channelDidcount")->info(sprintf("禁用/启用渠道折扣 id:%s,修改后状态：%s",$channelDisctId,
            \Arr::get(ChannelDiscountModel::$STATUS_FORMAT,$update["status"],"启用/禁用")
        ));

        ActionLogService::addLog(ActionLogService::TYPE_ACTION_CHANNEL_DISCOUNT_DISABLE,$channelDisctId,
            [
                "message"=>sprintf("%s渠道折扣",\Arr::get(ChannelDiscountModel::$STATUS_FORMAT,$update["status"],"启用/禁用")),
            ]);
    }

    public static function addCnannelDiscount($requestParams=[]){
        $isExistsSameInfo =  ChannelDiscountModel::isExistsSameInfo(
            $requestParams["sup_type"],
            $requestParams["order"],
            $requestParams["supplier_name"],
            $requestParams["supplier_id"],
            $requestParams["supplier_code"]
        );
        if(!empty($isExistsSameInfo)){
            throw new InvalidRequestException("已经存在类似的渠道折扣信息，请勿重复添加");
        }

        $requestParams["create_user_id"] =getAdminUserId();
        $requestParams["create_user"] =getAdminUserName();
        $requestParams["channel_disct_sn"] = IdSender::getSn(IdSender::TYPE_CUBE_CHANNELDISCOUNT);
        $requestParams["create_time"] =time();

        $requestParams["ration"] = round($requestParams["ration"],6);
        $requestParams["ration_usd"] = round($requestParams["ration_usd"],6);
        if($requestParams["ration"] <= 0 || $requestParams["ration_usd"] <= 0){
            throw new InvalidRequestException("渠道折扣保留4位小数后不能小于0");
        }
        (new self)->startTransaction();
        try{
            $channelDisctId =  ChannelDiscountModel::insertData($requestParams);

            if($channelDisctId && $requestParams["is_default"] != ChannelDiscountModel::DEFAULT_GOODS_PRICE_YES ){
                //参与 型号 品牌 eccn
                StepPriceExtendModel::insertData([
                    "sppe_id"=>$channelDisctId,
                    "goods_name"=> $requestParams["goods_name"] ?? "",
                    "brand"=>$requestParams["brand"] ?? "",
                    "brand_ids"=>$requestParams["brand_ids"] ?? "",
                    "file_url"=>$requestParams["file_url"] ?? "",
                    "type"=>StepPriceExtendModel::PRICE_EXTEND_TYPE_DISCOUNT,
                    "create_time"=>time(),
                ]);
            }

            //redis 阶梯价缓存
            self::addCnannelDiscountCache($requestParams["sup_type"],[
                "supplier_id"=>$requestParams["supplier_id"] ?? 0,
                "supplier_code"=>$requestParams["supplier_code"] ?? "",
                "order"=>$requestParams["order"],
                "ration"=>$requestParams["ration"],
                "ration_usd"=>$requestParams["ration_usd"],
                "goods_name"=>$requestParams["goods_name"] ?? "",
                "brand_ids"=> $requestParams["brand_ids"] ?? "",
            ]);

        }catch (\Exception $e){
            (new self)->rollBackTransaction();
            throw new InvalidRequestException(sprintf("新增渠道折扣失败:%s",$e->getMessage()));
        }
        \Log::channel("channelDidcount")->info("---------------新增渠道折扣信息------------------");
        \Log::channel("channelDidcount")->info(json_encode($requestParams, JSON_UNESCAPED_UNICODE));

        ActionLogService::addLog(ActionLogService::TYPE_ACTION_CHANNEL_DISCOUNT_CREATE,$channelDisctId,
            [
                "message"=>sprintf("新增渠道折扣"),
                "data"=>json_encode($requestParams)
            ]);



        (new self)->commitTransaction();
    }

    public static function addCnannelDiscountCache($supType,$data){
        $supplierId = $data["supplier_id"] ?? 0;
        $supplierCode = $data["supplier_code"] ?? "";
        $order = $data["order"];
        $goodsName = $data["goods_name"] ?? "";
        $brand = $data["brand_ids"] ?? "";
        $ration = $data["ration"] ? channelDiscountRound($data["ration"]) :  0;
        $rationUsd = $data["ration_usd"] ? channelDiscountRound($data["ration_usd"]) :  0;
        if(floatval($ration) <= 0 || floatval($rationUsd) <= 0){
            throw new InvalidRequestException("人民币渠道折扣或者美金渠道折扣必须在0-1质检");
        }
        if($order === ""){
            throw new InvalidRequestException("售价组优先级0-255参数不能为空");
        }

        switch ($supType) {
            case ChannelDiscountModel::SUPTYPE_DAIGOU://代购
                if($supplierId <= 0 ){
                    throw new InvalidRequestException("渠道(代购)id不能为空");
                }
                $cacheData = Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hget(config("config.channel_discount_daigou"),$supplierId);
                $cacheData = json_decode($cacheData,true);
                $cacheData = !empty($cacheData) ?  $cacheData : [];
                $cacheData["ration_usd"][$order] = $rationUsd;
                $cacheData["ration"][$order] = $ration;
                $cacheData["goods_name"][$order] = $goodsName;
                $cacheData["brand"][$order] = $brand;


                $cacheData["ration_usd"] = (object)$cacheData["ration_usd"];
                $cacheData["ration"] = (object)$cacheData["ration"];
                $cacheData["goods_name"] = (object)$cacheData["goods_name"];
                $cacheData["brand"] = (object)$cacheData["brand"];

                Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hset(config("config.channel_discount_daigou"),$supplierId,json_encode($cacheData));
                break;
            case ChannelDiscountModel::SUPTYPE_ZHUANYING://专营
                if(empty($supplierCode)){
                    throw new InvalidRequestException("供应商(专营)编码不能为空");
                }
                $cacheData = Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hget(config("config.channel_discount_zhuanying"),$supplierCode);
                $cacheData = json_decode($cacheData,true);
                $cacheData = !empty($cacheData) ?  $cacheData : [];
                $cacheData["ration_usd"][$order] = $rationUsd;
                $cacheData["ration"][$order] = $ration;
                $cacheData["goods_name"][$order] = $goodsName;
                $cacheData["brand"][$order] = $brand;

                $cacheData["ration_usd"] = (object)$cacheData["ration_usd"];
                $cacheData["ration"] = (object)$cacheData["ration"];
                $cacheData["goods_name"] = (object)$cacheData["goods_name"];
                $cacheData["brand"] = (object)$cacheData["brand"];

                Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hset(config("config.channel_discount_zhuanying"),$supplierCode,json_encode($cacheData));
                break;
        }
    }

    public static function updateCnannelDiscount($requestParams,$originalChannelDiscount=[]){
        $channelDisctId = (int)$requestParams["channel_disct_id"];
        $isExistsSameInfo =  ChannelDiscountModel::isExistsSameInfo(
            $requestParams["sup_type"],
            $requestParams["order"],
            $requestParams["supplier_name"],
            $requestParams["supplier_id"],
            $requestParams["supplier_code"]
        );
        if(!empty($isExistsSameInfo) && $isExistsSameInfo["channel_disct_id"] != $channelDisctId){
            throw new InvalidRequestException("已经存在类似的渠道折扣，请勿重复添加");
        }

        if(floatval($requestParams["ration"]) <= 0 || floatval($requestParams["ration_usd"]) <= 0){
            throw new InvalidRequestException("渠道折扣保留4位小数后不能小于0");
        }
        (new self)->startTransaction();

        try {
            //更新渠道折扣信息
            ChannelDiscountModel::where("channel_disct_id",$channelDisctId)->update([
                "update_time"=> time(),
                "is_default"=> $requestParams["is_default"],
                "order"=> $requestParams["order"],
                "remark"=> $requestParams["remark"],
                "ration_usd"=> channelDiscountRound($requestParams["ration_usd"]),
                "ration"=> channelDiscountRound($requestParams["ration"]),
            ]);
            //默认 则删除扩展信息
            if($requestParams["is_default"] == ChannelDiscountModel::DEFAULT_GOODS_PRICE_YES){
                self::delStepPriceExtend($channelDisctId);
            }else{
                //修改或者新增扩展信息
                self::updateOrCreatePriceExtend($channelDisctId,[
                    "goods_name"=> $requestParams["goods_name"] ?? "",
                    "brand"=>$requestParams["brand"] ?? "",
                    "brand_ids"=>$requestParams["brand_ids"] ?? "",
                    "file_url"=>$requestParams["file_url"] ?? "",
                    "eccn"=>$requestParams["eccn"] ?? "",
                    "update_time"=>time(),
                ]);
            }
            //如果修改了优先级order 需要先清除缓存中的相关优先级对应的数据
            if($originalChannelDiscount["order"] != $requestParams["order"]){
                self::deleteCnannelDiscountWithOrderCache($originalChannelDiscount["sup_type"],[
                    "supplier_id"=>$requestParams["supplier_id"] ?? 0,
                    "supplier_code"=>$requestParams["supplier_code"] ?? "",
                    "order"=>$originalChannelDiscount["order"],
                ]);
            }


            //新增缓存数据
            self::addCnannelDiscountCache($originalChannelDiscount["sup_type"],[
                "supplier_id"=>$requestParams["supplier_id"] ?? 0,
                "supplier_code"=>$requestParams["supplier_code"] ?? "",
                "order"=>$requestParams["order"],
                "goods_name"=>$requestParams["goods_name"] ?? "",
                "brand_ids"=> $requestParams["brand_ids"] ?? "",
                "ration"=>$requestParams["ration"],
                "ration_usd"=>$requestParams["ration_usd"],
            ]);



        } catch (\Exception $e) {
            (new self)->rollBackTransaction();
            throw new InvalidRequestException(sprintf("修改渠道折扣失败:%s", $e->getMessage()));
        }

        (new self)->commitTransaction();

        \Log::channel("goodsSalePriceGroup")->info("---------------修改渠道折扣信息------------------");
        \Log::channel("goodsSalePriceGroup")->info(sprintf("原始数据:%s",json_encode($originalChannelDiscount)));
        \Log::channel("goodsSalePriceGroup")->info(sprintf("修改后的数据:%s",json_encode($requestParams, JSON_UNESCAPED_UNICODE)));

        ActionLogService::addLog(ActionLogService::TYPE_ACTION_CHANNEL_DISCOUNT_UPDATE,$channelDisctId,
            [
                "message"=>sprintf("修改渠道折扣信息"),
                "data"=>sprintf("原始数据:%s",json_encode($originalChannelDiscount, JSON_UNESCAPED_UNICODE))
            ]);

    }

    public static function deleteCnannelDiscountWithOrderCache($supType,$data){
        $supplierId = $data["supplier_id"] ?? 0;
        $supplierCode = $data["supplier_code"] ?? "";
        $order = $data["order"];
        if($order === ""){
            throw new InvalidRequestException("渠道折扣优先级0-255参数不能为空");
        }

        switch ($supType) {
            case StepPriceNewModel::SUPTYPE_DAIGOU:
                $cacheData = Redis::connection(config("config.ladder_price_redis_connection_name"))->hget(config("config.channel_discount_daigou"), $supplierId);
                $cacheData = json_decode($cacheData, true);
                $cacheData = !empty($cacheData) ? $cacheData : [];
//                dd($cacheData,$order);
                if(empty($cacheData)){
                    return ;
                }
                if(isset($cacheData["ration"][$order])){
                    unset($cacheData["ration"][$order]);
                }
                if(isset($cacheData["ration_usd"][$order])){
                    unset($cacheData["ration_usd"][$order]);
                }
                if(isset($cacheData["goods_name"][$order])){
                    unset($cacheData["goods_name"][$order]);
                }
                if(isset($cacheData["brand"][$order])){
                    unset($cacheData["brand"][$order]);
                }

                $cacheData["ration_usd"] = isset($cacheData["ration_usd"]) ? (object)$cacheData["ration_usd"] : (object)[];
                $cacheData["ration"] = isset($cacheData["ration"]) ? (object)$cacheData["ration"] : (object)[];
                $cacheData["goods_name"] = isset($cacheData["goods_name"]) ? (object)$cacheData["goods_name"] : (object)[];
                $cacheData["brand"] = isset($cacheData["brand"]) ? (object)$cacheData["brand"] : (object)[];

                $cacheDataNew = json_encode($cacheData);
                Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hset(
                        config("config.channel_discount_daigou"),
                        trim(strtoupper($supplierId)),
                        $cacheDataNew
                    );
                break;
            case StepPriceNewModel::SUPTYPE_ZHUANYING:
                $supplierCode = trim(strtoupper($supplierCode));
                if(empty($supplierCode)){
                    throw new InvalidRequestException("供应商编码不能为空");
                }
                $cacheData = Redis::connection(config("config.ladder_price_redis_connection_name"))->hget(config("config.channel_discount_zhuanying"), $supplierCode);
                $cacheData = json_decode($cacheData, true);
                $cacheData = !empty($cacheData) ? $cacheData : [];
                if(empty($cacheData)){
                    return ;
                }
                if(isset($cacheData["ration_usd"][$order])){
                    unset($cacheData["ration_usd"][$order]);
                }
                if(isset($cacheData["ration"][$order])){
                    unset($cacheData["ration"][$order]);
                }
                if(isset($cacheData["goods_name"][$order])){
                    unset($cacheData["goods_name"][$order]);
                }
                if(isset($cacheData["brand"][$order])){
                    unset($cacheData["brand"][$order]);
                }

                $cacheData["ration_usd"] = isset($cacheData["ration_usd"]) ? (object)$cacheData["ration_usd"] : (object)[];
                $cacheData["ration"] = isset($cacheData["ration"]) ? (object)$cacheData["ration"] : (object)[];
                $cacheData["goods_name"] = isset($cacheData["goods_name"]) ? (object)$cacheData["goods_name"] : (object)[];
                $cacheData["brand"] = isset($cacheData["brand"]) ? (object)$cacheData["brand"] : (object)[];

                $cacheDataNew = json_encode($cacheData);


                Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hset(
                        config("config.channel_discount_zhuanying"),
                        $supplierCode,
                        $cacheDataNew
                    );
                break;

        }




    }

    public static function updateOrCreatePriceExtend($channelDisctId,$data=[]){
        $stepPriceExtend = StepPriceExtendModel::getStepPirceExtInfoBySppeId($channelDisctId,StepPriceExtendModel::PRICE_EXTEND_TYPE_DISCOUNT);
        if(!empty($stepPriceExtend)){
            StepPriceExtendModel::where("sppe_id",$channelDisctId)->where("type",StepPriceExtendModel::PRICE_EXTEND_TYPE_DISCOUNT)->update([
                "goods_name"=> $data["goods_name"] ?? "",
                "brand"=>$data["brand"] ?? "",
                "brand_ids"=>$data["brand_ids"] ?? "",
                "eccn"=>$data["eccn"] ?? "",
                "file_url"=>$data["file_url"] ?? "",
                "update_time"=>time(),
            ]);
        }else{
            StepPriceExtendModel::create([
                "sppe_id"=> $channelDisctId,
                "type"=> StepPriceExtendModel::PRICE_EXTEND_TYPE_DISCOUNT,
                "goods_name"=> $data["goods_name"] ?? "",
                "brand"=>$data["brand"] ?? "",
                "brand_ids"=>$data["brand_ids"] ?? "",
                "eccn"=>$data["eccn"] ?? "",
                "file_url"=>$data["file_url"] ?? "",
                "create_time"=>time(),
            ]);
        }

    }

    public static function delStepPriceExtend($channelDisctId){
        return StepPriceExtendModel::where("type",StepPriceExtendModel::PRICE_EXTEND_TYPE_DISCOUNT)->where("sppe_id",$channelDisctId)->delete();
    }


    public static function getPriorityLevel($data=[]){
        $extOrder = $data["order"];
        $query  = ChannelDiscountModel::where("sup_type",$data["sup_type"])->where("status",1);
        if($data["sup_type"] == ChannelDiscountModel::SUPTYPE_DAIGOU){
            $query->where("supplier_id",intval($data["supplier_value"]));
        }else{
            $query->where("supplier_code",trim($data["supplier_value"]));
        }
        $order = $query->pluck("order")->toArray();
        $orderList = [];
        if($extOrder !== ""){
            $orderList[] = intval($extOrder);
        }
        for ($i=1;$i<=255;$i++){
            if(in_array($i,$order) || $i == $extOrder){
                continue;
            }
            $orderList[] = $i;
        }
        sort($orderList);
        return $orderList;
    }

    /**
     * Notes:
     * User: sl
     * Date: 2023-03-07 15:09
     * @param $data
     */
    public static function getRelationChanDis($paramsData=[]){
        $pageInfo["page"] = arrayGet($paramsData, "page", 1, "intval");
        $pageInfo["limit"] = arrayGet($paramsData, "limit", static::_LIMIT_, "intval");
        $query  = ChannelDiscountModel::select("*")->where("sup_type",$paramsData["sup_type"]);
        if($paramsData["sup_type"] == ChannelDiscountModel::SUPTYPE_DAIGOU){
            $query->where("supplier_id",intval($paramsData["supplier_value"]));
        }else{
            $query->where("supplier_code",trim($paramsData["supplier_value"]));
        }
        $list = $query->orderBy("channel_disct_id", "desc")->with(["stepPriceExtend"=>function($q){
            $q->where("type",StepPriceExtendModel::PRICE_EXTEND_TYPE_DISCOUNT);
        }])->getList($pageInfo["page"], $pageInfo["limit"]);
        $list = $list->toArray();
        $arr = $list["data"];
        foreach($arr as $k=>$item){
            $arr[$k]["goods_name"] = $item["step_price_extend"]["goods_name"] ?? "";
            $arr[$k]["brand"] = $item["step_price_extend"]["brand"] ?? "";
            if (!empty($item["step_price_extend"]["brand"])) {
                $arr["brand"] = (new StandardBrandService())->getStandardBrandNameListByBrandIds($item["step_price_extend"]["brand_ids"]);
            }
            $arr[$k]["eccn"] = $item["step_price_extend"]["eccn"] ?? "";
            $arr[$k]["file_url"] = $item["step_price_extend"]["file_url"] ?? "";
            unset($arr[$k]["step_price_extend"]);
            $arr[$k]["supplier_id_or_code"] = "";
            if($item["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU){
                $arr[$k]["supplier_id_or_code"] =  $item["supplier_id"];
            }elseif($item["sup_type"] == StepPriceNewModel::SUPTYPE_ZHUANYING){
                $arr[$k]["supplier_id_or_code"] =  $item["supplier_code"];
            }
            $arr[$k]["ration_format"] =  sprintf("¥%s%%",decimal_number_format($item["ration"]*100,2));
            $arr[$k]["ration_usd_format"] =  sprintf("$%s%%",decimal_number_format($item["ration_usd"]*100,2));
            $arr[$k]["status_format"] = \Arr::get(ChannelDiscountModel::$STATUS_FORMAT,$item["status"],"");

            $arr[$k]["create_time"] =  dateDefault($item["create_time"]);
        }

        return [$arr,$list["total"]];
    }

    public static function getChannelDidInfo($channelDisctId){
        $info = ChannelDiscountModel::getChannelDidInfo($channelDisctId);
        if(empty($info)){
            throw new InvalidRequestException("没找到相关数据");
        }
        if(!empty($info["step_price_extend"])){
            $info["goods_name"] = $info["step_price_extend"]["goods_name"] ?? "";
            $info["brand"] = $info["step_price_extend"]["brand"] ?? "";
            $info["brand_ids"] = $info["step_price_extend"]["brand_ids"] ?? "";
            $info["file_url"] = $info["step_price_extend"]["file_url"] ?? "";
        }


        $arr = $info;
        $arr["supplier_id_or_code"] = "";
        if($arr["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU){
            $arr["supplier_id_or_code"] =  $arr["supplier_id"];
        }elseif($arr["sup_type"] == StepPriceNewModel::SUPTYPE_ZHUANYING){
            $arr["supplier_id_or_code"] =  $arr["supplier_code"];
        }
        $arr["ration_format"] =  sprintf("¥%s",decimal_number_format($arr["ration"],4));
        $arr["ration_usd_format"] =  sprintf("$%s",decimal_number_format($arr["ration_usd"],4));
        $arr["status_format"] = \Arr::get(ChannelDiscountModel::$STATUS_FORMAT,$arr["status"],"");

        $arr["ration"] = decimal_number_format($arr["ration"]*100);
        $arr["ration_usd"] = decimal_number_format($arr["ration_usd"]*100);

        $arr["create_time"] =  dateDefault($arr["create_time"]);

        if (!empty($arr["brand_ids"])) {
            $arr["brand"] = (new StandardBrandService())->getStandardBrandNameListByBrandIds($arr["brand_ids"]);
        }

        unset($arr["step_price_extend"]);
        return $arr;
    }


}