<?php


namespace App\Http\Services;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cms\UserInfoModel;
use App\Http\Models\Cube\CouponModel;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Models\Cube\PrizeWinnerModel;
use App\Http\Models\Liexin\RegionModel;
use App\Http\Models\Liexin\UserMainModel;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UserLotteryService
{
    public function getUserLotteryList($map)
    {
        $map['limit'] = $map['limit'] ?: 10;
        $query = PrizeWinnerModel::where(function ($query) use ($map) {
            if (!empty($map['user_account'])) {
                //$user_id = (new UserMainModel())->getUserIdWithMobileOrEmail($map['user_account']);
                $userIds = UserService::getAllUserIdByAccount($map['user_account']);
                $query->whereIn('user_id', $userIds);
            }
            if (!empty($map['lottery_name'])) {
                $query->where('lottery_name', 'like', '%' . $map['lottery_name'] . '%');
            }
            if (!empty($map['prize_get_time'])) {
                $startTime = strtotime(explode('~', $map['prize_get_time'])[0]);
                $endTime = strtotime(explode('~', $map['prize_get_time'])[1]);
                $query->whereBetween('prize_get_time', [$startTime, $endTime]);
            }
            if (!empty($map['draw_type'])) {
                $query->where('draw_type', $map['draw_type']);
            }
            if (!empty($map['is_test'])) {
                $is_test = ($map['is_test'] == 1) ? 0 : 1;
                $query->where('um.is_test', $is_test);
            }
            if (!empty($map['prize_type'])) {
                $query->where('prize_type', $map['prize_type']);
            }
            if (!empty($map['prize_name'])) {
                $query->where('prize_name', 'like', '%' . $map['prize_name'] . '%');
            }
        })->where('activity_type', 1);
        $orgId = request()->user->org_id;
        if (!PermService::hasPerm('cube_coupon_viewAllCoupon') && request()->user->userId != 1000) {
            if (in_array($orgId, config('field.IedgeOrgIdList'))) {
                $query->whereIn('org_id', config('field.IedgeOrgIdList'));
            } else {
                $query->where('org_id', $orgId);
            }
        }
        $result = $query->orderBy('prize_get_time', 'desc')->paginate($map['limit'])->toArray();
        return $result;
    }

    // 获取奖品发放的详细地址（抽奖 奖品+ 积分奖品 均适用）
    public function getPointPrizeSendAddress($addressId, $tableName = '')
    {
        $address = '';
        if (empty($addressId)) {
            return $address;
        }
        //todo:这里的bug是 只会拿到第一个用户收奖地址
        //todo:此处需要调整  抽奖活动中奖名单表中要存储 用户奖品收货地址  像积分奖品一样
        //todo:然后此处就可以统一 接收address_id 处理 无需通过表名判断
        $condition = $tableName == 'prize_consignee_info' ? 'user_id' : 'address_id';
        $info = DB::connection('web')->table($tableName)
            ->select('province', 'city', 'district', 'detail_address', 'mobile', 'consignee')
            ->where($condition, $addressId)->first();
        if (!empty($info)) {
            $regionInfo = RegionModel::select(['region_id', 'region_name'])
                ->whereIn('region_id', [$info->province, $info->city, $info->district])->get();
            $regions = [];
            foreach ($regionInfo as $k => $v) {
                $regions[] = objectToArray($v);
            }
            $sortedRegions = my_array_sort($regions, 'region_id', SORT_ASC);
            foreach ($sortedRegions as $sortedRegion) {
                $address .= $sortedRegion['region_name'];
            }
        }
        $addressInfo['address'] = $address;
        $addressInfo['address'] .= !empty($info->detail_address) ? $info->detail_address : '';
        $addressInfo['consignee'] = !empty($info->consignee) ? $info->consignee : '';
        $addressInfo['mobile'] = !empty($info->mobile) ? $info->mobile : 0;
        return $addressInfo;
    }

}
