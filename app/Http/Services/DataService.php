<?php

namespace App\Http\Services;


use App\Http\Models\Cube\PriceActivityModel;
use Illuminate\Support\Facades\Redis;

class DataService
{

    //统一修改线上的价格活动币种
    public static function updatePriceActivityCurrencyType()
    {
// 生成 CSV 文件路径
        $filePath = public_path('/test.csv');

// 要生成的数据行数
        $rowCount = 20000;

// 生成 CSV 数据
        $data = [];
        for ($i = 0; $i < $rowCount; $i++) {
            // 生成一列数据
            $column = 'Value ' . ($i + 1);

            // 将一列数据添加到总数据中
            $data[] = [$column];
        }

// 以写入模式打开 CSV 文件
        $file = fopen($filePath, 'w');

// 写入 CSV 数据行
        foreach ($data as $row) {
            fputcsv($file, $row);
        }

// 关闭文件句柄
        fclose($file);
    }

}
