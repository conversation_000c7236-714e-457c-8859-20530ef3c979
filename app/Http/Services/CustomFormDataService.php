<?php


namespace App\Http\Services;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\CustomFormDataModel;
use Illuminate\Support\Facades\Redis;

class CustomFormDataService
{
    //获取列表
    public function getCustomFormDataList($map)
    {
        $model = new CustomFormDataModel();
        $query = $model->with('activity')->orderBy('id', 'desc');
        $query = $this->filter($query, $map);
        $limit = !empty($map['limit']) ? $map['limit'] : 10;
        $result = $query->paginate($limit);
        $result = !empty($result) ? $result->toArray() : [];
        return $result;
    }

    public function filter($query, $map)
    {
        if (!empty($map['mobile'])) {
            $query->where('mobile', 'like', "%" . $map['mobile'] . "%");
        }
        if (!empty($map['activity_no'])) {
            $query->whereHasIn('activity', function ($q) use ($map) {
                $q->where('activity_no', 'like', "%" . trim($map['activity_no']) . "%");
            });
        }
        if (!empty($map['activity_name'])) {
            $query->whereHasIn('activity', function ($q) use ($map) {
                $q->where('activity_name', 'like', "%" . trim($map['activity_name']) . "%");
            });
        }
        if (!empty($map['company_name'])) {
            $query->whereHasIn('activity', function ($q) use ($map) {
                $q->where('company_name', 'like', "%" . $map['company_name'] . "%");
            });
        }
        if (!empty($map['activity_id'])) {
            $query->where('activity_id', $map['activity_id']);
        }
        if (!empty($map['is_new_reg'])) {
            $query->where('is_new_reg', $map['is_new_reg']);
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }

        return $query;
    }


}
