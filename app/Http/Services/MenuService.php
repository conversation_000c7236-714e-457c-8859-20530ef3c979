<?php

namespace App\Http\Services;

use App\Http\Models\Cms\CmsBusinessConfigModel;
use App\Http\Models\Cms\ConfigModel;

class MenuService
{
    // 获取菜单
    public static function getMenu()
    {
        // 获取系统名称
        $name = PermService::getBusinessConfigName();

        $user_menu_list = [];
        $user = request()->user;
        $userId = $user->userId;
        $email = $user->email;
        $role_list = PermService::getUserRoles($userId, $email);
        if (PermService::hasRole(PermService::ROLE_ADMIN, $role_list)) {
            return ConfigModel::getMenusByName($name);
        } else {
            $user_perms = PermService::getUserPerms();
            if ($user_perms) {
                $menu_list = ConfigModel::getMenusByName($name);
                if ($menu_list) {
                    $user_menu_list = self::getUserMenuList($menu_list, $user_perms);
                }
            }
        }
        return $user_menu_list;
    }

    // 获取用户菜单列表，会获取全部菜单，然后遍历判断用户是否有权限，如果没有权限，那么会删除
    public static function getUserMenuList($menu_list, $user_perms)
    {
        foreach ($menu_list as $i => &$menu_item) {
            // 如果有子菜单，那么循环遍历判断
            if ($menu_item['childs']) {
                foreach ($menu_item['childs'] as $j => $child) {
                    $perm_id = PermService::getPermId($child['href']);
                    if (!in_array($perm_id, $user_perms)) {
                        unset($menu_item['childs'][$j]);
                    }
                }
                if (empty($menu_item['childs'])) {
                    unset($menu_list[$i]);
                }
            } else {
                // 如果只是一级菜单，判断用户是否有权限
                $perm_id = PermService::getPermId($menu_item['href']);
                if (!in_array($perm_id, $user_perms)) {
                    unset($menu_list[$i]);
                }
            }
        }
        return array_values($menu_list);//保证索引从0开始
    }
}
