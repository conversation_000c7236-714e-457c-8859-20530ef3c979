<?php


namespace App\Http\Services;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\ActivityAndCouponSkuModel;
use App\Http\Models\Cube\CustomPriceModel;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use Illuminate\Support\Facades\Redis;

class CustomPriceService
{

    public static function getCustomPriceList($map)
    {
        $model = new CustomPriceModel();
        $query = $model->orderBy('id', 'desc');
        if (!empty($map['status']) || (isset($map['status']) && $map['status'] === '0')) {
            $query->where('status', $map['status']);
        }
        if (!empty($map['audit_status']) || (isset($map['audit_status']) && $map['audit_status'] === '0')) {
            $query->where('audit_status', $map['audit_status']);
        }
        if (!empty($map['create_time'])) {
            $startTime = strtotime(explode('~', $map['create_time'])[0]);
            $endTime = strtotime(explode('~', $map['create_time'])[1]);
            $query->whereBetween('create_time', [$startTime, $endTime]);
        }
        if (!empty($map['update_time'])) {
            $startTime = strtotime(explode('~', $map['update_time'])[0]);
            $endTime = strtotime(explode('~', $map['update_time'])[1]);
            $query->whereBetween('update_time', [$startTime, $endTime]);
        }

        $limit = !empty($map['limit']) ? $map['limit'] : 10;
        $result = $query->orderBy('id', 'desc')->paginate($limit);
        $result = !empty($result) ? $result->toArray() : [];
        return $result;
    }

    public static function saveCustomPrice($data)
    {
        $data['price_list'] = array_map(function ($item) {
            if (empty($item['price_name'])) {
                throw new InvalidRequestException("价格名称不能为空");
            }
            if (!is_numeric($item['ratio'])) {
                throw new InvalidRequestException("人民币利润系数必须为数字");
            }
            if ($item['ratio'] < 0 || $item['ratio'] > 100) {
                throw new InvalidRequestException("人民币利润系数必须在0-100之间");
            }
            return [
                'price_name' => $item['price_name'],
                'ratio' => (int)$item['ratio'],
            ];
        }, $data['price_list']);
        $db['org_id'] = $data['org_id'];
        if (!empty($data['id'])) {
            $auditStatus = CustomPriceModel::where('id', $data['id'])->value('audit_status');
            if ($auditStatus == CustomPriceModel::STATUS_AUDIT_PASS || $auditStatus == CustomPriceModel::STATUS_AUDIT_FAIL) {
                $db['audit_price_list'] = $data['price_list'];
                $db['audit_status'] = CustomPriceModel::STATUS_NEED_AUDIT;
            }
            //还允许被审核之前各种修改价格阶梯
            if ($auditStatus == CustomPriceModel::STATUS_NEED_AUDIT) {
                $db['audit_price_list'] = $data['price_list'];
            }
            $db['update_name'] = request()->user->name;
            $db['update_uid'] = request()->user->userId;
            $db['update_time'] = time();
            $result = CustomPriceModel::where('id', $data['id'])->update($db);
        } else {
            $db['audit_price_list'] = !empty($data['price_list']) ? json_encode($data['price_list']) : '';
            //先判断除了自己以外,是否存在相同的组织和阶梯设置
            $customPriceModel = new CustomPriceModel();
            $exists = $customPriceModel->where('org_id', $data['org_id'])->exists();
            if ($exists) {
                throw new InvalidRequestException("组织和阶梯设置已存在");
            }
            $db['audit_price_list'] = !empty($data['price_list']) ? json_encode($data['price_list']) : '';
            $db['status'] = CustomPriceModel::STATUS_NEED_AUDIT;
            $db['create_name'] = request()->user->name;
            $db['create_uid'] = request()->user->userId;
            $db['create_time'] = time();
            $result = CustomPriceModel::insertGetId($db);
            $data['id'] = $result;
        }
        self::saveCustomPriceToRedis($data['id']);
        return $result;
    }

    //审核
    public static function auditCustomPrice($id, $auditStatus, $content)
    {
        $customPriceModel = new CustomPriceModel();
        $result = $customPriceModel->where('id', $id)->update([
            'audit_status' => $auditStatus,
            'audit_content' => $content
        ]);
        //如果审核通过,则更新阶梯设置
        if ($auditStatus == CustomPriceModel::STATUS_AUDIT_PASS) {
            $auditPriceList = $customPriceModel->where('id', $id)->value('audit_price_list');
            $customPriceModel->where('id', $id)->update([
                'price_list' => $auditPriceList,
                'status' => CustomPriceModel::STATUS_ENABLE,
                'audit_time' => time(),
                'audit_name' => request()->user->name,
                'audit_uid' => request()->user->userId,
            ]);
        } else {
            $customPriceModel->where('id', $id)->update([
                'audit_status' => CustomPriceModel::STATUS_AUDIT_FAIL,
                'audit_time' => time(),
                'audit_name' => request()->user->name,
                'audit_uid' => request()->user->userId,
            ]);
        }
        self::saveCustomPriceToRedis($id);
        return $result;
    }

    //启用和禁用
    public static function enableCustomPrice($id, $status)
    {
        $customPriceModel = new CustomPriceModel();
        $result = $customPriceModel->where('id', $id)->update(['status' => $status]);
        $redis = Redis::connection('sku');
        //如果禁用,则删除redis缓存
        if ($status == CustomPriceModel::STATUS_DISABLE) {
            $orgId = $customPriceModel->where('id', $id)->value('org_id');
            $redis->hdel('cube_custom_price', $orgId);
        } else {
            $data = CustomPriceModel::where('id',  $id)->first()->toArray();
            $redis->hset('cube_custom_price', $data['org_id'], json_encode($data));
        }
        return $result;
    }

    public static function saveCustomPriceToRedis($id)
    {
        //存到redis
        $redis = Redis::connection('sku');
        $data = CustomPriceModel::where('id',  $id)->first()->toArray();
        $data['price_list'] = !empty($data['price_list']) ? json_decode($data['price_list'], true) : [];
        $data['audit_price_list'] = !empty($data['audit_price_list']) ? json_decode($data['audit_price_list'], true) : [];
        $redis->hset('cube_custom_price', $data['org_id'], json_encode($data));
    }
}
