<?php

namespace App\Http\Services;

use App\Http\Models\Crm\UserModel;
use App\Http\Models\Cube\CouponOpLogModel;
use App\Http\Transformers\CouponOpLogTransformer;

class CouponOpLogService
{

    public function getCouponOpLogList($couponId)
    {
        $logs = CouponOpLogModel::with('user_info')->where('coupon_id', $couponId)
            ->orderBy('op_log_id', 'desc')->get()->toArray();
        $logs = (new CouponOpLogTransformer())->listTransform($logs);

        return $logs;
    }

}
