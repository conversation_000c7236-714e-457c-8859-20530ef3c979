<?php

namespace App\Http\Services;

use App\Http\Models\BaseModel;
use App\Http\IdSender\IdSender;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Redis;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Import\SalePriceGroupImport;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\StepPriceNewModel;
use App\Http\Models\Cube\StepPriceExtendModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class GoodsSalePriceGroupService extends BaseService
{

    /**
     * Notes:添加售价组
     * User: sl
     * Date: 2023-03-03 14:03
     * @param $requestParams
     * @return mixed
     * @throws InvalidRequestException
     */
    public static function addGoodsSalePriceGroup($requestParams = [])
    {
        $requestParams["sppe_sn"] = IdSender::getSn(IdSender::TYPE_CUBE_GOODSSALEPRICEGROUP); //售价组编码
        $requestParams["create_user"] = getAdminUserId();
        $requestParams["create_time"] = time();
        $requestParams["update_time"] = 0;
        $requestParams["org_id"] = $requestParams["org_id"] ?? 1;

        $isExistsSameInfo = StepPriceNewModel::isExistsSameInfo(
            $requestParams["sup_type"],
            $requestParams["order"],
            $requestParams["supplier_name"],
            $requestParams["supplier_id"],
            $requestParams["supplier_code"],
            $requestParams["org_id"]
        );
        if (!empty($isExistsSameInfo)) {
            throw new InvalidRequestException("已经存在类似的售价组信息，请勿重复添加");
        }
        (new self)->startTransaction();
        try {
            $sppe_id = StepPriceNewModel::insertData($requestParams);
            if ($requestParams["is_default"] != StepPriceNewModel::DEFAULT_GOODS_PRICE_YES && $sppe_id) {
                //参与 型号 品牌 eccn
                StepPriceExtendModel::insertData([
                    "sppe_id" => $sppe_id,
                    "goods_name" => $requestParams["goods_name"] ?? "",
                    "brand" => $requestParams["brand"] ?? "",
                    "brand_ids" => $requestParams["brand_ids"] ?? "",
                    "file_url" => $requestParams["file_url"] ?? "",
                    "eccn" => $requestParams["eccn"] ?? "",
                    "type" => StepPriceExtendModel::PRICE_EXTEND_TYPE_GOODS_PRICE,
                    "create_time" => time(),
                ]);
            }
            //redis 阶梯价缓存
            self::addGoodsSalePriceGroupCache($requestParams["sup_type"], [
                "supplier_id" => $requestParams["supplier_id"] ?? 0,
                "supplier_code" => $requestParams["supplier_code"] ?? "",
                "order" => $requestParams["order"],
                "mini_profit_ladder" => $requestParams["mini_profit_ladder"] ?? 5,
                "step_price_data_json" => $requestParams["step_price_data_json"] ?? "",
                "goods_name" => $requestParams["goods_name"] ?? "",
                "brand_ids" => $requestParams["brand_ids"] ?? "",
                "eccn" => $requestParams["eccn"] ?? "",
                "org_id" => $requestParams['org_id'] ?? 1,
            ]);
        } catch (\Exception $e) {
            (new self)->rollBackTransaction();
            throw new InvalidRequestException(sprintf("新增售价组失败:%s", $e->getMessage() . '_' . $e->getLine()));
        }
        (new self)->commitTransaction();


        \Log::channel("goodsSalePriceGroup")->info("---------------新增售价组信息------------------");
        \Log::channel("goodsSalePriceGroup")->info(json_encode($requestParams, JSON_UNESCAPED_UNICODE));

        ActionLogService::addLog(
            ActionLogService::TYPE_ACTION_SALE_PRICE_GROUP_CREATE,
            $sppe_id,
            [
                "message" => sprintf("新增售价组"),
                "data" => json_encode($requestParams)
            ]
        );

        //触发价格预警记录
        PriceWarningService::salePriceGroupTriggerPriceWarning($sppe_id);
    }

    /**
     * Notes:删除售价组某个供应商里面的的单个售价组
     * User: sl
     * Date: 2023-03-06 15:56
     * @param $supType
     * @param $data
     * @throws InvalidRequestException
     */
    public static function deleteGoodsSalePriceGroupWithOrderCache($supType, $data)
    {
        $supplierId = $data["supplier_id"] ?? 0;
        $supplierCode = $data["supplier_code"] ?? "";
        $orgId = $data["org_id"] ?? 1;
        $order = $data["order"];
        if ($order === "") {
            throw new InvalidRequestException("售价组优先级0-255参数不能为空");
        }
        switch ($supType) {
            case StepPriceNewModel::SUPTYPE_DAIGOU:
                $cacheData = Redis::connection(config("config.ladder_price_redis_connection_name"))->hget(config("config.ladder_price_rediskey_daigou"), $supplierId);
                $cacheData = json_decode($cacheData, true);
                $cacheData = !empty($cacheData) ? $cacheData : [];
                if (empty($cacheData)) {
                    return;
                }
                if (isset($cacheData["ladder_price"][$order])) {
                    unset($cacheData["ladder_price"][$order]);
                }
                if (isset($cacheData["goods_name"][$order])) {
                    unset($cacheData["goods_name"][$order]);
                }
                if (isset($cacheData["brand"][$order])) {
                    unset($cacheData["brand"][$order]);
                }
                if (isset($cacheData["eccn"][$order])) {
                    unset($cacheData["eccn"][$order]);
                }
                $cacheData["ladder_price"] = isset($cacheData["ladder_price"]) ? (object)$cacheData["ladder_price"] : [];
                $cacheData["goods_name"] = isset($cacheData["goods_name"]) ? (object)$cacheData["goods_name"] : [];
                $cacheData["brand"] = isset($cacheData["brand"]) ? (object)$cacheData["brand"] : [];
                $cacheData["eccn"] = isset($cacheData["eccn"]) ? (object)$cacheData["eccn"] : [];


                $cacheDataNew = json_encode($cacheData);
                Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hset(
                        config("config.ladder_price_rediskey_daigou"),
                        trim(strtoupper($supplierId)),
                        $cacheDataNew
                    );
                break;
            case StepPriceNewModel::SUPTYPE_ZHUANYING:
                if (empty($supplierCode)) {
                    throw new InvalidRequestException("设置供应商(专营)售价组供应商编码不能为空");
                }

                $supplierCode = trim(strtoupper($supplierCode));
                //这里要判断是否是猎芯,如果不是,则要加上org_id去获取缓存
                $redisKey = $supplierCode;
                if ($orgId != BaseModel::ORG_ID_LIEXIN) {
                    $redisKey = $redisKey . "_" . $orgId;
                }

                $cacheData = Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hget(config("config.ladder_price_rediskey_zhuanying"), $redisKey);
                $cacheData = json_decode($cacheData, true);
                $cacheData = !empty($cacheData) ? $cacheData : [];
                if (empty($cacheData)) {
                    return;
                }
                if (isset($cacheData["cost_ladder_price_egt50_lt200"][$order])) {
                    unset($cacheData["cost_ladder_price_egt50_lt200"][$order]);
                }
                if (isset($cacheData["cost_ladder_price_egt200"][$order])) {
                    unset($cacheData["cost_ladder_price_egt200"][$order]);
                }
                if (isset($cacheData["ladder_price_egt50_lt200"][$order])) {
                    unset($cacheData["ladder_price_egt50_lt200"][$order]);
                }
                if (isset($cacheData["ladder_price_egt200"][$order])) {
                    unset($cacheData["ladder_price_egt200"][$order]);
                }
                if (isset($cacheData["goods_name"][$order])) {
                    unset($cacheData["goods_name"][$order]);
                }
                if (isset($cacheData["brand"][$order])) {
                    unset($cacheData["brand"][$order]);
                }
                if (isset($cacheData["eccn"][$order])) {
                    unset($cacheData["eccn"][$order]);
                }

                if (isset($cacheData["ladder_price_mini_profit_level"][$order])) {
                    unset($cacheData["ladder_price_mini_profit_level"][$order]);
                }

                if (isset($cacheData["is_set_lowest_profit"][$order])) {
                    unset($cacheData["is_set_lowest_profit"][$order]);
                }

                $cacheData["cost_ladder_price_egt50_lt200"] = (object)$cacheData["cost_ladder_price_egt50_lt200"];
                $cacheData["cost_ladder_price_egt200"] = (object)$cacheData["cost_ladder_price_egt200"];
                $cacheData["ladder_price_egt50_lt200"] = (object)$cacheData["ladder_price_egt50_lt200"];
                $cacheData["ladder_price_egt200"] = (object)$cacheData["ladder_price_egt200"];


                $cacheData["ladder_price_mini_profit_level"] = (object)$cacheData["ladder_price_mini_profit_level"];
                $cacheData["is_set_lowest_profit"] = (object)$cacheData["is_set_lowest_profit"];
                $cacheData["goods_name"] = (object)$cacheData["goods_name"];
                $cacheData["brand"] = (object)$cacheData["brand"];
                $cacheData["eccn"] = (object)$cacheData["eccn"];

                Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hset(
                        config("config.ladder_price_rediskey_zhuanying"),
                        $redisKey,
                        json_encode($cacheData)
                    );
                break;
        }
    }

    public static function addGoodsSalePriceGroupCache($supType, $data)
    {
        $supplierId = $data["supplier_id"] ?? 0;
        $supplierCode = $data["supplier_code"] ?? "";
        $order = $data["order"];
        $miniProfitLadder = $data["mini_profit_ladder"] ?? 5;
        $stepPriceDataJson = json_decode($data["step_price_data_json"], true);
        $goods_name = $data["goods_name"] ?? "";
        $brand = $data["brand_ids"] ?? "";
        $eccn = $data["eccn"] ?? "";
        $orgId = $data["org_id"] ?? 1;

        if ($order === "") {
            throw new InvalidRequestException("售价组优先级0-255参数不能为空");
        }
        $res = false;
        switch ($supType) {
            case StepPriceNewModel::SUPTYPE_DAIGOU:
                if ($supplierId <= 0) {
                    throw new InvalidRequestException("渠道(代购)id不能为空");
                }

                $cacheData = Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hget(config("config.ladder_price_rediskey_daigou"), $supplierId);
                $cacheData = json_decode($cacheData, true);
                $cacheData = !empty($cacheData) ? $cacheData : [];
                $ladderPriceArr = []; //redis json中ladder_price 必须是数组 不然其它golang无法解析
                foreach ($stepPriceDataJson as $val) {
                    $tmpArr["ratio"] = round(1 + round(floatval($val["ratio"]) / 100, 6), 6);
                    $tmpArr["ratio_usd"] = round(1 + round(floatval($val["ratio_usd"]) / 100, 6), 6);
                    $ladderPriceArr[] = $tmpArr;
                }
                $cacheData["ladder_price"][$order] = array_merge($ladderPriceArr);
                $cacheData["goods_name"][$order] = $goods_name;
                $cacheData["brand"][$order] = $brand;
                $cacheData["eccn"][$order] = $eccn;


                $cacheData["ladder_price"] = (object)$cacheData["ladder_price"];
                $cacheData["goods_name"] = (object)$cacheData["goods_name"];
                $cacheData["brand"] = (object)$cacheData["brand"];
                $cacheData["eccn"] = (object)$cacheData["eccn"];
                Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hset(
                        config("config.ladder_price_rediskey_daigou"),
                        trim(strtoupper($supplierId)),
                        json_encode($cacheData)
                    );
                break;
            case "备份20230815 弃用":
                if (empty($supplierCode)) {
                    throw new InvalidRequestException("供应商(专营)编码不能为空");
                }
                $cacheData = [];
                $cacheData["cost_ladder_price_egt50_lt200"] = [];
                $cacheData["cost_ladder_price_egt200"] = [];
                $cacheData["ladder_price_egt50_lt200"] = [];
                $cacheData["ladder_price_egt200"] = [];
                $cacheData["is_set_lowest_profit"] = true;
                $cacheData["ladder_price_mini_profit_level"] = (int)$miniProfitLadder;

                foreach ($stepPriceDataJson as $k => $val) {
                    $cacheData["cost_ladder_price_egt50_lt200"][$k]["purchases"] = (int)$val["ladder_price_egt50_lt200"];
                    $cacheData["cost_ladder_price_egt50_lt200"][$k]["price"] = round(1 + round(floatval($val["ratio"]) / 100, 6), 6);
                    $cacheData["cost_ladder_price_egt50_lt200"][$k]["price_usd"] = round(1 + round(floatval($val["ratio_usd"]) / 100, 6), 6);

                    $cacheData["cost_ladder_price_egt200"][$k]["purchases"] = (int)$val["ladder_price_egt200"];
                    $cacheData["cost_ladder_price_egt200"][$k]["price"] = round(1 + round(floatval($val["ratio"]) / 100, 6), 6);
                    $cacheData["cost_ladder_price_egt200"][$k]["price_usd"] = round(1 + round(floatval($val["ratio_usd"]) / 100, 6), 6);


                    $cacheData["ladder_price_egt50_lt200"][$k]["ratio"] = round(1 + round(floatval($val["ratio"]) / 100, 6), 6);
                    $cacheData["ladder_price_egt50_lt200"][$k]["ratio_usd"] = round(1 + round(floatval($val["ratio_usd"]) / 100, 6), 6);

                    $cacheData["ladder_price_egt200"][$k]["ratio"] = round(1 + round(floatval($val["ratio"]) / 100, 6), 6);
                    $cacheData["ladder_price_egt200"][$k]["ratio_usd"] = round(1 + round(floatval($val["ratio_usd"]) / 100, 6), 6);
                }
                $res = Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hset(
                        config("config.ladder_price_rediskey_zhuanying"),
                        trim(strtoupper($supplierCode)),
                        json_encode($cacheData, JSON_FORCE_OBJECT)
                    );
                break;
            case StepPriceNewModel::SUPTYPE_ZHUANYING:
                if (empty($supplierCode)) {
                    throw new InvalidRequestException("供应商(专营)编码不能为空");
                }
                $redisKey = trim(strtoupper($supplierCode));
                if ($orgId != BaseModel::ORG_ID_LIEXIN) {
                    $redisKey = $redisKey . "_" . $orgId;
                }

                $cacheData = Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hget(config("config.ladder_price_rediskey_zhuanying"), $redisKey);
                $cacheData = json_decode($cacheData, true);
                $cacheData = !empty($cacheData) ? $cacheData : [];

                $cost_ladder_price_egt50_lt200 = [];
                $cost_ladder_price_egt200 = [];
                $ladder_price_egt50_lt200 = [];
                $ladder_price_egt200 = [];
                foreach ($stepPriceDataJson as $k => $val) {
                    $cost_ladder_price_egt50_lt200[$k]["purchases"] = (int)$val["ladder_price_egt50_lt200"];
                    $cost_ladder_price_egt50_lt200[$k]["price"] = round(1 + round(floatval($val["ratio"]) / 100, 4), 4);
                    $cost_ladder_price_egt50_lt200[$k]["price_usd"] = round(1 + round(floatval($val["ratio_usd"]) / 100, 4), 4);

                    $cost_ladder_price_egt200[$k]["purchases"] = (int)$val["ladder_price_egt200"];
                    $cost_ladder_price_egt200[$k]["price"] = round(1 + round(floatval($val["ratio"]) / 100, 4), 4);
                    $cost_ladder_price_egt200[$k]["price_usd"] = round(1 + round(floatval($val["ratio_usd"]) / 100, 4), 4);

                    $ladder_price_egt50_lt200[$k]["ratio"] = round(1 + round(floatval($val["ratio"]) / 100, 4), 4);
                    $ladder_price_egt50_lt200[$k]["ratio_usd"] = round(1 + round(floatval($val["ratio_usd"]) / 100, 4), 4);

                    $ladder_price_egt200[$k]["ratio"] = round(1 + round(floatval($val["ratio"]) / 100, 4), 4);
                    $ladder_price_egt200[$k]["ratio_usd"] = round(1 + round(floatval($val["ratio_usd"]) / 100, 4), 4);
                }
                $cacheData["cost_ladder_price_egt50_lt200"][$order] = array_merge($cost_ladder_price_egt50_lt200);
                $cacheData["cost_ladder_price_egt200"][$order] = array_merge($cost_ladder_price_egt200);
                $cacheData["ladder_price_egt50_lt200"][$order] = array_merge($ladder_price_egt50_lt200);
                $cacheData["ladder_price_egt200"][$order] = array_merge($ladder_price_egt200);
                $cacheData["goods_name"][$order] = $goods_name;
                $cacheData["brand"][$order] = $brand;
                $cacheData["eccn"][$order] = $eccn;
                $cacheData["ladder_price_mini_profit_level"][$order] = (int)$miniProfitLadder;
                $cacheData["is_set_lowest_profit"][$order] = true;


                $cacheData["cost_ladder_price_egt50_lt200"] = (object)$cacheData["cost_ladder_price_egt50_lt200"];
                $cacheData["cost_ladder_price_egt200"] = (object)$cacheData["cost_ladder_price_egt200"];
                $cacheData["ladder_price_egt50_lt200"] = (object)$cacheData["ladder_price_egt50_lt200"];
                $cacheData["ladder_price_egt200"] = (object)$cacheData["ladder_price_egt200"];

                $cacheData["goods_name"] = (object)$cacheData["goods_name"];
                $cacheData["brand"] = (object)$cacheData["brand"];
                $cacheData["eccn"] = (object)$cacheData["eccn"];
                $cacheData["ladder_price_mini_profit_level"] = (object)$cacheData["ladder_price_mini_profit_level"];
                $cacheData["is_set_lowest_profit"] = (object)$cacheData["is_set_lowest_profit"];
                $res = Redis::connection(config("config.ladder_price_redis_connection_name"))
                    ->hset(
                        config("config.ladder_price_rediskey_zhuanying"),
                        $redisKey,
                        json_encode($cacheData)
                    );
                break;
        }
        return $res;
    }

    /**
     * 获取供应商数据
     *$supType  1代购 2专营
     * array:1443 [▼
     * 0 => array:3 [▼
     * "supplier_id" => ""
     * "supplier_code" => "L0000003"
     * "supplier_name" => "EBV ELEKTRONIK GmbH & Co KG"
     * ]
     * ]
     */
    public static function getSupList($supType)
    {
        $supList = [];
        switch ($supType) {
            case 1: //代购渠道
                $daiGou = SupplierModel::getSupplierList();
                foreach ($daiGou as $k => $val) {
                    $supList[$k]["supplier_id"] = $val["supplier_id"];
                    $supList[$k]["supplier_code"] = "";
                    $supList[$k]["supplier_name"] = $val["supplier_name"];
                }
                break;
            case 2: //公司开发的供应商 供应商管理系统
                $zhuanYing = (new SupplierChannelModel)->getSupplierCodeList();
                foreach ($zhuanYing as $supplier_code => $supplier_name) {
                    $arr = [];
                    $arr["supplier_id"] = "";
                    $arr["supplier_code"] = $supplier_code;
                    $arr["supplier_name"] = $supplier_name;
                    $supList[] = $arr;
                }
                break;
        }

        return $supList;
    }

    //获取供应商数据给前端的xm-select使用
    public static function getSupplierListForXmSelect()
    {
        $daiGouList = [];
        $daiGou = SupplierModel::getSupplierList();
        foreach ($daiGou as $k => $val) {
            $daiGouList[$k]["value"] = $val["supplier_id"];
            $daiGouList[$k]["name"] = $val["supplier_name"];
        }
        $zhuanYingList = [];
        $zhuanYing = (new SupplierChannelModel)->getSupplierCodeList();
        foreach ($zhuanYing as $supplierCode => $supplierName) {
            $arr = [];
            $arr["value"] = $supplierCode;
            $arr["name"] = $supplierCode . ' - ' . $supplierName;
            $zhuanYingList[] = $arr;
        }
        //组合数据
        return [
            [
                'name' => '代购供应商',
                'children' => $daiGouList,
            ],
            [
                'name' => '专营供应商',
                'children' => $zhuanYingList,
            ]
        ];
    }

    //获取供应商名称
    public static function getSupplierName($supplierCode, $supplierType)
    {
        if ($supplierType == 1) {
            return SupplierModel::where('supplier_id', $supplierCode)->value('supplier_name');
        } else {
            return SupplierChannelModel::where('supplier_code', $supplierCode)->value('supplier_name');
        }
    }

    /**
     * Notes:编辑售价组信息
     * User: sl
     * Date: 2023-03-04 14:23
     * @param $requestParams
     */
    public static function updateGoodsSalePriceGroup($requestParams, $oldGoodsSalePriceGroup)
    {
        $sppe_id = $requestParams["sppe_id"];
        $requestParams["update_time"] = time();
        $oldOrder = $oldGoodsSalePriceGroup["order"];
        $isExistsSameInfo = StepPriceNewModel::isExistsSameInfo(
            $oldGoodsSalePriceGroup["sup_type"],
            $requestParams["order"],
            $oldGoodsSalePriceGroup["supplier_name"],
            $oldGoodsSalePriceGroup["supplier_id"],
            $oldGoodsSalePriceGroup["supplier_code"],
            $requestParams["org_id"]
        );
        if (!empty($isExistsSameInfo) && $isExistsSameInfo["sppe_id"] != $requestParams["sppe_id"]) {
            throw new InvalidRequestException("已经存在类似的售价组信息，请勿重复添加");
        }
        //        dd($requestParams);
        (new self)->startTransaction();

        try {
            $sppe_id = $requestParams["sppe_id"];
            StepPriceNewModel::where("sppe_id", $sppe_id)->update([
                "step_price_data_json" => $requestParams["step_price_data_json"] ?: "",
                "mini_profit_ladder" => $requestParams["mini_profit_ladder"] ?: 5,
                "order" => $requestParams["order"],
                "update_time" => time(),
            ]);
            if ($requestParams["order"] != 0) {
                //不是默认售价组
                //参与 型号 品牌 eccn
                self::updateOrCreatePriceExtend($sppe_id, [
                    "goods_name" => $requestParams["goods_name"] ?? "",
                    "brand" => $requestParams["brand"] ?? "",
                    "brand_ids" => $requestParams["brand_ids"] ?? "",
                    "file_url" => $requestParams["file_url"] ?? "",
                    "eccn" => $requestParams["eccn"] ?? "",
                    "update_time" => time(),
                ]);
            } else {
                //默认售价组 删除关联的扩展表
                StepPriceExtendModel::where("sppe_id", $sppe_id)->where("type", StepPriceExtendModel::PRICE_EXTEND_TYPE_GOODS_PRICE)->delete();
            }

            //如果修改了优先级order 需要先清除缓存中的相关优先级对应的数据
            if ($requestParams["order"] != $oldOrder) {
                self::deleteGoodsSalePriceGroupWithOrderCache($oldGoodsSalePriceGroup["sup_type"], [
                    "supplier_id" => $oldGoodsSalePriceGroup["supplier_id"] ?? 0,
                    "supplier_code" => $oldGoodsSalePriceGroup["supplier_code"] ?? "",
                    "order" => $oldOrder,
                ]);
            }

            //新增缓存数据
            self::addGoodsSalePriceGroupCache($oldGoodsSalePriceGroup["sup_type"], [
                "supplier_id" => $oldGoodsSalePriceGroup["supplier_id"] ?? 0,
                "supplier_code" => $oldGoodsSalePriceGroup["supplier_code"] ?? "",
                "order" => $requestParams["order"],
                "mini_profit_ladder" => $requestParams["mini_profit_ladder"] ?? 5,
                "step_price_data_json" => $requestParams["step_price_data_json"] ?? "",
                "goods_name" => $requestParams["goods_name"] ?? "",
                "brand_ids" => $requestParams["brand_ids"] ?? "",
                "eccn" => $requestParams["eccn"] ?? "",
                "org_id" => $requestParams['org_id'] ?? 1,
            ]);
        } catch (\Exception $e) {
            (new self)->rollBackTransaction();
            throw new InvalidRequestException(sprintf("修改售价组失败:%s", $e->getMessage()));
        }

        (new self)->commitTransaction();

        \Log::channel("goodsSalePriceGroup")->info("---------------修改售价组信息------------------");
        \Log::channel("goodsSalePriceGroup")->info(sprintf("原始数据:%s", json_encode($oldGoodsSalePriceGroup)));
        \Log::channel("goodsSalePriceGroup")->info(sprintf("修改后的数据:%s", json_encode($requestParams, JSON_UNESCAPED_UNICODE)));

        ActionLogService::addLog(
            ActionLogService::TYPE_ACTION_SALE_PRICE_GROUP_UPDATE,
            $sppe_id,
            [
                "message" => "修改售价组",
                "data" => \json_encode([
                    "原始数据" => json_encode($oldGoodsSalePriceGroup ),
                    "修改数据" => json_encode($requestParams )
                ])
            ]
        );

        //触发价格预警记录
        PriceWarningService::salePriceGroupTriggerPriceWarning($sppe_id);
    }

    public static function getGoodsSalePriceGroupList($paramsData = [])
    {
        $pageInfo["page"] = arrayGet($paramsData, "page", 1, "intval");
        $pageInfo["limit"] = arrayGet($paramsData, "limit", static::_LIMIT_, "intval");
        $list = self::buildGoodsPriceList($paramsData, $pageInfo);
        $arr = $list["data"] ?? [];
        foreach ($arr as $k => $v) {
            $arr[$k]["goods_name"] = $v["step_price_extend"]["goods_name"] ?? "";
            $arr[$k]["brand"] = $v["step_price_extend"]["brand"] ?? "";
            if (!empty($v["step_price_extend"]["brand_ids"])) {
                $arr[$k]["brand"] = (new StandardBrandService())->getStandardBrandNameListByBrandIds($v["step_price_extend"]["brand_ids"]);
            }
            $arr[$k]["brand_ids"] = $v["step_price_extend"]["brand_ids"] ?? "";
            $arr[$k]["eccn"] = $v["step_price_extend"]["eccn"] ?? "";
            $arr[$k]["file_url"] = $v["step_price_extend"]["file_url"] ?? "";
            unset($arr[$k]["step_price_extend"]);
            $arr[$k]["supplier_id_or_code"] = "";
            if ($v["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU) {
                $arr[$k]["supplier_id_or_code"] = $v["supplier_id"];
            } elseif ($v["sup_type"] == StepPriceNewModel::SUPTYPE_ZHUANYING) {
                $arr[$k]["supplier_id_or_code"] = $v["supplier_code"];
            }
            $arr[$k]["is_default"] = 0;
            if ($v["order"] == 0) {
                $arr[$k]["is_default"] = 1;
            }

            $arr[$k]["status_format"] = \Arr::get(StepPriceNewModel::$STATUS_FORMAT, $v["status"], "");

            $arr[$k]["create_time"] = dateDefault($v["create_time"]);

            $arr[$k]["step_price_data"] = self::getSaleProfit($v["step_price_data_json"]);

            $arr[$k]["org_name"] = config('field.OrgList')[$v["org_id"]] ?? "";
        }
        return [$arr, $list["total"]];
    }

    public static function getSaleProfit($step_price_data_json)
    {
        $step_price_data_json = json_decode($step_price_data_json, true);
        $arr = [];
        foreach ($step_price_data_json as $k => $v) {
            $arr[$k]["order"] = \Arr::get(config("config.num_format"), $k, "");
            $arr[$k]["ratio"] = sprintf("¥%s%%", $v["ratio"]);
            $arr[$k]["ratio_usd"] = sprintf("$%s%%", $v["ratio_usd"]);
        }
        return array_reverse($arr);
    }

    public static function buildGoodsPriceList($reqParams, $pageInfo)
    {
        $query = StepPriceNewModel::query();
        $buildEqualQueryData["sppe_sn"] = $reqParams["sppe_sn"];

        if ($reqParams["org_id"]) {
            $query->where("org_id", $reqParams["org_id"]);
        }

        //这里还要加上权限判断
        if (PermService::hasPerm('cube_goodsSalePriceGroup_viewAllGoodsSalePriceGroup')) {
            # code...
        }

        if ($reqParams["supplier_id"]) {
            $query->where(function ($q) use ($reqParams) {
                $q->where(function ($q1) use ($reqParams) {
                    $q1->where("supplier_code", trim($reqParams["supplier_id"]))->where("supplier_id", 0);
                })->orWhere(function ($q2) use ($reqParams) {
                    $q2->where("supplier_id", intval($reqParams["supplier_id"]))->where("supplier_code", "");
                });
            });
        }

        if ($reqParams["status"] !== "") {
            $query->where("status", (int)$reqParams["status"]);
        }


        if ($reqParams["is_default"] == "0") {
            $query->where("order", "!=", 0);
        } elseif ($reqParams["is_default"] == "1") {
            $query->where("order", 0);
        }

        $list = $query->select("*")->buildEqualQuery($buildEqualQueryData)->with(["stepPriceExtend" => function ($q) {
            $q->where("type", StepPriceExtendModel::PRICE_EXTEND_TYPE_GOODS_PRICE);
        }]);
        $list = $list->orderBy("sppe_id", "desc")->getList($pageInfo["page"], $pageInfo["limit"]);
        return $list->toArray();
    }

    public static function getStepPriceInfoById($sppe_id)
    {
        return StepPriceNewModel::getStepPriceInfoById($sppe_id);
    }

    public static function getGoodsSalePriceGroupInfo($sppe_id)
    {
        $info = StepPriceNewModel::getStepPriceInfoById($sppe_id);
        if (empty($info)) {
            return [];
        }
        $arr = $info;
        $arr["goods_name"] = $arr["step_price_extend"]["goods_name"] ?? "";
        $arr["brand"] = "";
        if (!empty($arr["step_price_extend"]["brand_ids"])) {
            $arr["brand"] = (new StandardBrandService())->getStandardBrandNameListByBrandIds($arr["step_price_extend"]["brand_ids"]);
        }
        $arr["brand_ids"] = $arr["step_price_extend"]["brand_ids"] ?? "";
        $arr["file_url"] = $arr["step_price_extend"]["file_url"] ?? "";
        $arr["eccn"] = $arr["step_price_extend"]["eccn"] ?? "";
        unset($arr["step_price_extend"]);
        $arr["is_default"] = 0;
        if ($arr["order"] == 0) {
            $arr["is_default"] = 1;
        }
        return $arr;
    }

    /**
     * Notes:启用禁用
     * User: sl
     * Date: 2023-03-10 14:08
     * @param $sppe_id
     * @param $update
     * @return mixed
     * @throws InvalidRequestException
     */
    public static function updateGoodsSalePriceGroupStatus($sppe_id, $update)
    {
        $update["update_time"] = time();
        $StepPriceInfo = GoodsSalePriceGroupService::getStepPriceInfoById($sppe_id);
        if (empty($StepPriceInfo)) {
            throw new InvalidRequestException("没找到关联的售价组信息");
        }
        if ($update["status"] == StepPriceNewModel::$STATUS["status_enable"]) {
            $isExistsSameInfo = StepPriceNewModel::isExistsSameInfo(
                $StepPriceInfo["sup_type"],
                $StepPriceInfo["order"],
                $StepPriceInfo["supplier_name"],
                $StepPriceInfo["supplier_id"],
                $StepPriceInfo["supplier_code"],
                $StepPriceInfo["org_id"]
            );
            if (!empty($isExistsSameInfo)) {
                throw new InvalidRequestException("已经存在类似的信息，无法启用");
            }
            //新增缓存信息
            self::addGoodsSalePriceGroupCache($StepPriceInfo["sup_type"], [
                "supplier_id" => $StepPriceInfo["supplier_id"] ?? 0,
                "supplier_code" => $StepPriceInfo["supplier_code"] ?? "",
                "order" => $StepPriceInfo["order"],
                "step_price_data_json" => $StepPriceInfo["step_price_data_json"],
                "mini_profit_ladder" => $StepPriceInfo["mini_profit_ladder"],
                "goods_name" => $StepPriceInfo["step_price_extend"]["goods_name"] ?? "",
                "brand_ids" => $StepPriceInfo["step_price_extend"]["brand_ids"] ?? "",
                "org_id" => $StepPriceInfo['org_id'] ?? 1,
            ]);
        } else {
            //删除对应的售价组缓存
            self::deleteGoodsSalePriceGroupWithOrderCache($StepPriceInfo["sup_type"], [
                "supplier_id" => $StepPriceInfo["supplier_id"] ?? 0,
                "supplier_code" => $StepPriceInfo["supplier_code"] ?? "",
                "order" => $StepPriceInfo["order"],
            ]);
        }

        $bk = StepPriceNewModel::where("sppe_id", intval($sppe_id))->update($update);

        //触发价格预警记录
        PriceWarningService::salePriceGroupTriggerPriceWarning($sppe_id);

        \Log::channel("goodsSalePriceGroup")->info("---------------禁用启用售价组信息------------------");
        \Log::channel("goodsSalePriceGroup")->info(sprintf(
            "禁用/启用售价组 id:%s,修改后状态：%s",
            $sppe_id,
            \Arr::get(StepPriceNewModel::$STATUS_FORMAT, $update["status"], "启用/禁用")
        ));

        ActionLogService::addLog(
            ActionLogService::TYPE_ACTION_SALE_PRICE_GROUP_DISABLE,
            $sppe_id,
            [
                "message" => sprintf("%s售价组", \Arr::get(StepPriceNewModel::$STATUS_FORMAT, $update["status"], "启用/禁用")),
            ]
        );

        return $bk;
    }

    public static function updateOrCreatePriceExtend($sppeId, $data = [])
    {
        $stepPriceExtend = StepPriceExtendModel::getStepPirceExtInfoBySppeId($sppeId, StepPriceExtendModel::PRICE_EXTEND_TYPE_GOODS_PRICE);
        if (!empty($stepPriceExtend)) {
            StepPriceExtendModel::where("sppe_id", $sppeId)->where("type", StepPriceExtendModel::PRICE_EXTEND_TYPE_GOODS_PRICE)->update([
                "goods_name" => $data["goods_name"] ?? "",
                "brand" => $data["brand"] ?? "",
                "brand_ids" => $data["brand_ids"] ?? "",
                "eccn" => $data["eccn"] ?? "",
                "file_url" => $data["file_url"] ?? "",
                "update_time" => time(),
            ]);
        } else {
            StepPriceExtendModel::create([
                "sppe_id" => $sppeId,
                "type" => StepPriceExtendModel::PRICE_EXTEND_TYPE_GOODS_PRICE,
                "goods_name" => $data["goods_name"] ?? "",
                "brand" => $data["brand"] ?? "",
                "brand_ids" => $data["brand_ids"] ?? "",
                "eccn" => $data["eccn"] ?? "",
                "file_url" => $data["file_url"] ?? "",
                "create_time" => time(),
            ]);
        }
    }

    public static function getPriorityLevel($data = [])
    {
        $extOrder = $data["order"];
        $query = StepPriceNewModel::where("sup_type", $data["sup_type"])->where("status", 1);
        if ($data["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU) {
            $query->where("supplier_id", intval($data["supplier_value"]));
        } else {
            $query->where("supplier_code", trim($data["supplier_value"]));
        }
        $order = $query->pluck("order")->toArray();
        $orderList = [];
        if ($extOrder !== "") {
            $orderList[] = intval($extOrder);
        }
        for ($i = 1; $i <= 255; $i++) {
            if (in_array($i, $order)) {
                continue;
            }
            $orderList[] = $i;
        }
        sort($orderList);
        return $orderList;
    }

    /**
     *设置默认售价组 order=0 渠道和供应商
     */
    public static function SetDefaultSalePriceGroup($supType)
    {

        switch ($supType) {
            case StepPriceNewModel::SUPTYPE_ZHUANYING:
                $importDatas = Excel::toArray(new SalePriceGroupImport, public_path() . '/importSalePriceGroup/专营供应商默认售价组利润导入规则.xlsx');
                //读取第一个sheet
                $importData = $importDatas[0];
                $loadData = [];
                foreach ($importData as $row) {
                    $i = 0;
                    $data = [];
                    foreach ($row as $val) {
                        if (!isset(SalePriceGroupImport::$fieldArr[$i])) {
                            break;
                        }

                        if ($val === null) {
                            $val = "";
                        }

                        $data[SalePriceGroupImport::$fieldArr[$i]] = $val;
                        $i++;
                    }
                    $loadData[] = $data;
                }

                if (!empty($loadData[0])) {
                    unset($loadData[0]);
                }
                $loadData = arrayChangeKeyByField($loadData, "supplier_code");
                $supCodes = \Arr::pluck($loadData, "supplier_code");
                $supList = SupplierChannelModel::getSupplierListByCodes($supCodes);
                $supList = arrayChangeKeyByField($supList, "supplier_code");
                $defaultSalePriceGroupLadder = config("config.default_zy_salePriceGroup_ladder");
                $defaultSalePriceGroupLadder = json_decode($defaultSalePriceGroupLadder, true);
                foreach ($loadData as $code => $ratios) {
                    $ratio = $ratios["ratio"] ?? 0;
                    if (!isset($supList[$code])) {
                        continue;
                    }
                    //默认信息是否已经存在 存在则跳过
                    $isExistsSameInfo = StepPriceNewModel::isExistsSameInfo(
                        StepPriceNewModel::SUPTYPE_ZHUANYING,
                        0,
                        $supList[$code]["supplier_name"],
                        0,
                        $supList[$code]["supplier_code"]
                    );
                    if ($isExistsSameInfo) {
                        continue;
                    }

                    $stepPriceData = self::getStepPriceDataByExplode($defaultSalePriceGroupLadder, $ratio);
                    $insertData = [
                        "sppe_sn" => IdSender::getSn(IdSender::TYPE_CUBE_GOODSSALEPRICEGROUP),
                        "supplier_id" => 0,
                        "supplier_code" => $supList[$code]["supplier_code"],
                        "supplier_name" => $supList[$code]["supplier_name"],
                        "step_price_data_json" => json_encode($stepPriceData),
                        "order" => 0,
                        "mini_profit_ladder" => 9,
                        "create_user_id" => 1000,
                        "create_user" => "admin",
                        "create_time" => time(),
                        "sup_type" => StepPriceNewModel::SUPTYPE_ZHUANYING,
                    ];
                    StepPriceNewModel::insertData($insertData);
                }

                break;
            case StepPriceNewModel::SUPTYPE_DAIGOU:
                $json = config("config.default_daigou_salePriceGroup_ladder");
                $supLists = self::getSupList(StepPriceNewModel::SUPTYPE_DAIGOU);
                foreach ($supLists as $item) {
                    $supplierId = $item["supplier_id"] ?? 0;
                    $supplierName = $item["supplier_name"] ?? "";
                    //默认信息是否已经存在 存在则跳过
                    $isExistsSameInfo = StepPriceNewModel::isExistsSameInfo(
                        StepPriceNewModel::SUPTYPE_DAIGOU,
                        0,
                        $supplierName,
                        $supplierId,
                        ""
                    );
                    if ($isExistsSameInfo) {
                        continue;
                    }
                    $insertData = [
                        "sppe_sn" => IdSender::getSn(IdSender::TYPE_CUBE_GOODSSALEPRICEGROUP),
                        "supplier_id" => $supplierId,
                        "supplier_code" => "",
                        "supplier_name" => $supplierName,
                        "step_price_data_json" => $json,
                        "order" => 0,
                        "mini_profit_ladder" => 9,
                        "create_user_id" => 1000,
                        "create_user" => "admin",
                        "create_time" => time(),
                        "sup_type" => StepPriceNewModel::SUPTYPE_DAIGOU,
                    ];

                    StepPriceNewModel::insertData($insertData);
                }

                break;
        }
    }

    /**
     * Notes:
     * User: sl
     * Date: 2023-03-20 10:51
     * @param $defaultSalePriceGroupLadder //默认阶梯数量值
     * @param $minProfit //阶梯9 最低利润值  9-1一次递增1
     */
    public static function getStepPriceDataByExplode($defaultSalePriceGroupLadder, $minProfit)
    {

        foreach ($defaultSalePriceGroupLadder as $k => $v) {
            $defaultSalePriceGroupLadder[$k]["ratio"] = intval($minProfit + count($defaultSalePriceGroupLadder) - intval($k));
            $defaultSalePriceGroupLadder[$k]["ratio_usd"] = intval($minProfit + count($defaultSalePriceGroupLadder) - intval($k));
        }
        return $defaultSalePriceGroupLadder;
    }

    /**
     * Notes:设置代购全局默认售价组
     * User: sl
     * Date: 2023-03-20 14:18
     */
    public static function setDaiGouGlobalDefaultSalePriceGroup()
    {
        $json = config("config.global_default_daigou_salePriceGroup_ladder");
        Redis::connection(config("config.ladder_price_redis_connection_name"))->set(config("config.ladder_price_rediskey_default_daigou"), $json);
    }

    /**
     * Notes:设置专营全局默认售价组
     * User: sl
     * Date: 2023-03-20 14:18
     */
    public static function setZhuanYingGlobalDefaultSalePriceGroup()
    {
        $json = config("config.global_default_zhuanying_salePriceGroup_ladder");
        Redis::connection(config("config.ladder_price_redis_connection_name"))->set(config("config.ladder_price_rediskey_default_zhuanying"), $json);
    }

    /**
     * Notes:同步数据库所有售价组到缓存
     * User: sl
     * Date: 2023-03-20 14:39
     */
    public static function syncSalePriceGroupMysqlDataToRedis($supType)
    {
        switch ($supType) {
            case StepPriceNewModel::SUPTYPE_DAIGOU:
                //同步代购
                $list = StepPriceNewModel::getDefaultStepPriceInfoBySupType(StepPriceNewModel::SUPTYPE_DAIGOU);
                foreach ($list as $item) {
                    if (empty($item["supplier_id"])) {
                        continue;
                    }
                    self::addGoodsSalePriceGroupCache($item["sup_type"], [
                        "supplier_id" => $item["supplier_id"] ?? 0,
                        "supplier_code" => "", //代购编码为空
                        "order" => $item["order"],
                        "mini_profit_ladder" => $item["mini_profit_ladder"] ?? 9,
                        "step_price_data_json" => $item["step_price_data_json"] ?? "",
                        "goods_name" => $item["step_price_extend"]["goods_name"] ?? "",
                        "brand_ids" => $item["step_price_extend"]["brand_ids"] ?? "",
                        "eccn" => $item["step_price_extend"]["eccn"] ?? "",
                    ]);
                }
                break;
            case StepPriceNewModel::SUPTYPE_ZHUANYING:
                //同步专营
                $list = StepPriceNewModel::getDefaultStepPriceInfoBySupType(StepPriceNewModel::SUPTYPE_ZHUANYING);
                foreach ($list as $item) {
                    if (empty($item["supplier_code"])) {
                        continue;
                    }
                    self::addGoodsSalePriceGroupCache($item["sup_type"], [
                        "supplier_id" => 0, //专营id为0
                        "supplier_code" => $item["supplier_code"] ?? "",
                        "order" => $item["order"],
                        "mini_profit_ladder" => $item["mini_profit_ladder"] ?? 9,
                        "step_price_data_json" => $item["step_price_data_json"] ?? "",
                        "goods_name" => $item["step_price_extend"]["goods_name"] ?? "",
                        "brand_ids" => $item["step_price_extend"]["brand_ids"] ?? "",
                        "eccn" => $item["step_price_extend"]["eccn"] ?? "",
                    ]);
                }
                break;
        }
    }
}
