<?php

namespace App\Http\Services;


use App\Exceptions\InvalidRequestException;
use App\Http\Models\ActionLogModel;

use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Utils\NameConvert;
use Carbon\Carbon;

class ActionLogService
{


    const TYPE_ACTION_SALE_PRICE_GROUP = 1000;             // 魔方系统售价组
    const TYPE_ACTION_SALE_PRICE_GROUP_CREATE = 1000001; // 商品销售价组-新增
    const TYPE_ACTION_SALE_PRICE_GROUP_UPDATE = 1000002; // 商品销售价组-修改
    const TYPE_ACTION_SALE_PRICE_GROUP_DISABLE =1000003; // 商品销售价组-启用禁用


    const TYPE_ACTION_CHANNEL_DISCOUNT = 1001;             // 魔方系统渠道折扣
    const TYPE_ACTION_CHANNEL_DISCOUNT_CREATE = 1001001; // 魔方系统渠道折扣-新增
    const TYPE_ACTION_CHANNEL_DISCOUNT_UPDATE = 1001002; // 魔方系统渠道折扣-修改
    const TYPE_ACTION_CHANNEL_DISCOUNT_DISABLE =1001003; // 魔方系统渠道折扣-启用禁用

    const TYPE_ACTION_PRICE_WARNING = 1002;             // 价格预警记录
    const TYPE_ACTION_PRICE_WARNING_SIGN = 1002001; // 标记处理
    const TYPE_ACTION_PRICE_WARNING_VERIFY = 1002002; // 重新验证


    // 日志内容类型map
    const LOG_OBJ_TYPE_MAP = [
        self::TYPE_ACTION_SALE_PRICE_GROUP,
        self::TYPE_ACTION_CHANNEL_DISCOUNT,
        self::TYPE_ACTION_PRICE_WARNING,
    ];

    // 日志行为类型map
    const LOG_ACT_TYPE_MAP = [
        self::TYPE_ACTION_SALE_PRICE_GROUP_CREATE=>"商品销售价组-新增",
        self::TYPE_ACTION_SALE_PRICE_GROUP_UPDATE => "商品销售价组-修改",
        self::TYPE_ACTION_SALE_PRICE_GROUP_DISABLE => "商品销售价组-启用禁用",
        self::TYPE_ACTION_CHANNEL_DISCOUNT_CREATE => "魔方系统渠道折扣-新增",
        self::TYPE_ACTION_CHANNEL_DISCOUNT_UPDATE => "魔方系统渠道折扣-修改",
        self::TYPE_ACTION_CHANNEL_DISCOUNT_DISABLE => "魔方系统渠道折扣-启用禁用",
        self::TYPE_ACTION_PRICE_WARNING_SIGN => "标记处理",
        self::TYPE_ACTION_PRICE_WARNING_VERIFY => "重新验证",
    ];




    public static function addLog($action_type, $obj_id, $log_data)
    {
        $action_log_info = self::getInsertLogInfo($action_type, $obj_id, $log_data);
        return ActionLogModel::addLog($action_log_info);
    }

    // 批量添加日志，只有obj_ids不一样，其他都一样
    public static function addMultiLogsWithObjIds($action_type, $obj_ids, $log_data)
    {
        $action_logs = [];
        if ($obj_ids && is_array($obj_ids)) {
            foreach ($obj_ids as $obj_id) {
                $action_logs[] = self::getInsertLogInfo($action_type, $obj_id, $log_data);
            }
        }

        if ($action_logs) {
            return ActionLogModel::addLogs($action_logs);
        }
        return true;
    }
    // 获取格式化插入信息
    private static function getInsertLogInfo($action_type, $obj_id, $log_data)
    {
        $action_log_info = [
            "act_uid" => (request()->user) ? request()->user->userId : 0,
            "act_type" => $action_type,
            "obj_type" => self::getObjTypeFromActionType($action_type),
            "obj_id" => $obj_id,
            "log_data" => self::dataEncode($log_data),
            "create_uid" => (request()->user) ? request()->user->userId : 0,
            "create_time" => time()
        ];
        return $action_log_info;
    }
    private static function getObjTypeFromActionType($action_type)
    {
        return substr($action_type, 0, 4);
    }

    private static function dataEncode($data)
    {
        return json_encode($data);
    }

    private static function dataDecode($data)
    {
        $message_data = [];
        $log_data = json_decode($data, 1);
        if ($log_data && isset($log_data['message'])) {
            $message_data = $log_data;
        }
        return $message_data;
    }
    public static function getUserLogList($params)
    {
        $data = self::getListByObjType($params,self::LOG_OBJ_TYPE_MAP);
        return $data;
    }
    // 根据日志行为获取列表
    public static function getListByObjType($params, $objType = self::LOG_OBJ_TYPE_MAP)
    {
        $where = [];

        $where[] = [function ($query) use ($objType) {
            $query->whereIn('obj_type', $objType);
        }];

        if (!empty($params['obj_id'])) {
            $where[] = ['obj_id', '=', $params['obj_id']];
        }

        if (!empty($params['operator_id'])) {
            $where[] = ['create_uid', '=', $params['operator_id']];
        }

        if (!empty($params['create_time'])) {
            list($start, $end) = explode('~', $params['create_time']);
            $where[] = ['create_time', '>=', Carbon::parse(trim($start))->startOfDay()->timestamp];
            $where[] = ['create_time', '<=', Carbon::parse(trim($end))->endOfDay()->timestamp];
        }

        $list = ActionLogModel::getListByWhere($where, $params['page'], $params['limit']);

        if (empty($list['data'])) {
            return $list;
        }

        $sale_ids = array_unique(array_column($list['data'], 'create_uid'));

        $sale_names = CmsUserInfoModel::getNameByUserIds($sale_ids);
        foreach ($list['data'] as $k => $v) {
            $log_data = json_decode($v['log_data'], true);

            $list['data'][$k]['content'] = $log_data['message'];
            $list['data'][$k]['act_type_val'] = data_get(self::LOG_ACT_TYPE_MAP, $v['act_type'], '');
            $list['data'][$k]['create_name'] = isset($sale_names[$v['create_uid']]) ? $sale_names[$v['create_uid']] : '';
            $list['data'][$k]['create_time'] = date('Y-m-d H:i:s', $v['create_time']);
            $list['data'][$k]['log_data'] = json_encode($log_data, JSON_UNESCAPED_UNICODE);
        }

        return $list;
    }
}
