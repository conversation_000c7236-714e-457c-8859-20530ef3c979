<?php

namespace App\Http\Import;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\ToArray;

class SalePriceGroupImport  implements ToCollection
{

    public static $titleArr = [
        0 => "供应商编码",
        1 => "利润",
    ];

    public static $fieldArr = [
        0 => "supplier_code",
        1 => "ratio",
    ];


    public function collection(Collection $collection)
    {

    }


}