<?php
/**
 * Created by PhpStorm.
 * User: duwen<PERSON>
 * Date: 2022/3/9
 * Time: 1:35 PM
 */

namespace App\Http\Utils;


class FileUrl
{
    // private static
    private static $baseDomain = null;

    private static function initConfig()
    {
        if (is_null(self::$baseDomain)) {
            self::$baseDomain = Config('website.file_domain');
        }
        return true;
    }

    private static function getBaseDomain()
    {
        return self::$baseDomain;
    }

    public static function getUrl($file_id)
    {
        self::initConfig();
        $file_url = '';
        if ($file_id) {
            $file_url = self::getBaseDomain() . "/download/" . $file_id;
        }
        return $file_url;
    }

}