<?php

namespace App\Http\Utils;

use App\Http\ApiHelper\ApiCode;
use SoapClient;
use App\Exceptions\InvalidRequestException;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class SoapRequester
{

    const SOAP_NAME_ERP = "soap_name_erp";

    public static function request($soap_name, $soap_func, $request_data)
    {
        $request_result = [];
        try {
            ini_set('soap.wsdl_cache_enabled', '0');
            ini_set('default_socket_timeout', 30);//设置socket超时时间
            libxml_disable_entity_loader(false);
            if ($soap_name == self::SOAP_NAME_ERP) {
                $soapClient = new SoapClient(Config('config.erp_domain') . '/ormrpc/services/WSIchuntjKFacade?wsdl');
            }
            $res = $soapClient->$soap_func(json_encode($request_data));
            if ($res) {
                $soap_res_arr = json_decode($res, true);
                if ($soap_res_arr) {
                    $request_result = $soap_res_arr['data'];
                }
                // 如果接口返回错误，记录日志
                if ($soap_res_arr && ($soap_res_arr['code'] !== ApiCode::API_CODE_SUCCESS)) {
                    self::errLog($soap_name, $soap_func, $request_data, $res);
                }
            } else {
                self::errLog($soap_name, $soap_func, $request_data, $res);
            }
        } catch (\Exception $e) {
            $err_json = json_encode([
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'code' => $e->getCode()
            ]);
            (new Logger('soap'))->pushHandler(new StreamHandler(storage_path('logs/soap.log')))->error($err_json);
            self::errLog($soap_name, $soap_func, $request_data, null);
            throw new InvalidRequestException('soap接口请求异常，请联系技术查看日志，错误信息：' . $e->getMessage());
        }
        return $request_result;
    }

    // 请求错误日志
    private static function errLog($soap_name, $soap_func, $request_data, $res)
    {
        $err_json = json_encode([
            'soap_name' => $soap_name,
            'soap_func' => $soap_func,
            'request_data' => $request_data,
            'res' => $res,
        ]);
        (new Logger('soap'))->pushHandler(new StreamHandler(storage_path('logs/soap.log')))->error($err_json);
    }

}