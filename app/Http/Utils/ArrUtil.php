<?php
/**
 * Created by PhpStorm.
 * User: du<PERSON><PERSON>
 * Date: 2021/9/1
 * Time: 9:40 AM
 */

namespace App\Http\Utils;


class ArrUtil
{
    public static function notContainsOnlyNull($arr)
    {
        return !empty(array_filter($arr, function ($a) {
            return $a !== null;
        }));
    }

    // 判断数组值是否相同，如果都相同，那么最终array_unique只有一个值
    public static function isSame($arr)
    {
        return count(array_unique($arr)) == 1;
    }

}