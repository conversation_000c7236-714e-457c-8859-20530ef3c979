<?php
/**
 * Created by PhpStorm.
 * User: duwen<PERSON>
 * Date: 2021/9/1
 * Time: 9:40 AM
 */

namespace App\Http\Utils;


use Illuminate\Support\Facades\Validator;

class ValidatorMsg
{

    // 只返回错误的第一条错误
    public static function getMsg($error_list)
    {
        $err_msg = '';
        foreach ($error_list as $err_key => $err_msgs) {
            foreach ($err_msgs as $msg) {
                $err_msg .= "{$err_key}|{$msg}";
                break;
            }
            break;
        }
        return $err_msg;
    }

    public static function checkValidateParamThrowException($validateRule,$params)
    {
        $validator = Validator::make($params, $validateRule);

        if ($validator->fails()) {
            $errors = $validator->errors()->toJson(JSON_UNESCAPED_UNICODE);
            throw  new \InvalidArgumentException($errors);
        }
        return true;
    }
}
