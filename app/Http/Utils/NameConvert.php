<?php
/**
 * Created by PhpStorm.
 * User: duwenjun
 * Date: 2021/8/13
 * Time: 4:44 PM
 */

namespace App\Http\Utils;


use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\Order\OrderServiceItemsModel;
use App\Http\Models\PayableModel;
use App\Http\Models\PaymentItemsModel;
use App\Http\Models\PaymentModel;
use App\Http\Models\PurchaseApproveModel;
use App\Http\Models\PurchaseSeviceApproveModel;
use App\Http\Services\PriceService;

class NameConvert
{
    // 获取性别
    public static function getGenderName($gender)
    {
        $gender = "未设置";
        if ($gender == CmsUserInfoModel::GENDER_TYPE_MAN) {
            $gender = "男性";
        }
        if ($gender == CmsUserInfoModel::GENDER_TYPE_WOMAN) {
            $gender = "女性";
        }
        return $gender;
    }

    // 获取间隔天数；用于计算有效期，因为有效期为有效天数+创建时间，数据库存储的有效期为时间戳，需要反向计算有效期
    public static function getIntervalDay($expire_time, $create_time)
    {
        $interval_days = '';
        if ($expire_time) {
            $interval_days = ($expire_time - $create_time) / 86400;
            $interval_days = ceil($interval_days);
        }
        return $interval_days;
    }

    // 获取创建时间
    public static function getDateTime($create_time)
    {
        $date_time = '';
        if ($create_time && is_numeric($create_time)) {
            $date_time = date("Y-m-d H:i:s", $create_time);
        }
        return $date_time;
    }

    public static function getDate($create_time)
    {
        $time = '';
        if ($create_time) {
            $time = date("Y-m-d", $create_time);
        }
        return $time;
    }

    // 获取审核状态
    public static function getApproveStatusName($a_status)
    {
        $status_name = '';
        if ($a_status == PurchaseApproveModel::APPROVE_STATUS_PROCESS) {
            $status_name = "审核中";
        }
        if ($a_status == PurchaseApproveModel::APPROVE_STATUS_PASS) {
            $status_name = "已通过";
        }
        if ($a_status == PurchaseApproveModel::APPROVE_STATUS_FAIL) {
            $status_name = "未通过";
        }
        if ($a_status == PurchaseSeviceApproveModel::APPROVE_STATUS_CANCEL) {
            $status_name = "取消审核";
        }
        return $status_name;
    }

    // 获取付款申请单状态
    public static function getPaymentStatusName($status)
    {
        $status_name = '';
        $status_name_map = PaymentModel::$STATUS_NAME_MAP;
        if (isset($status_name_map[$status])) {
            $status_name = $status_name_map[$status];
        }
        return $status_name;
    }

    // 获取付款申请单来源类型
    public static function getPaymentTypeName($payment_type)
    {
        $type_name = '';
        if ($payment_type == PaymentModel::PAYMENT_TYPE_PUR) {
            $type_name = "采购单";
        }
        if ($payment_type == PaymentModel::PAYMENT_TYPE_PAYABLE) {
            $type_name = "应付单";
        }
        return $type_name;
    }

    // 获取付款申请单付款类型 1预付款2退预付款3采购付款4退采购付款
    public static function getPaymentPayTypeName($pay_type)
    {
        $type_name = '';
        if (isset(PaymentItemsModel::$PAY_TYPE_NAME_MAP[$pay_type])) {
            $type_name = PaymentItemsModel::$PAY_TYPE_NAME_MAP[$pay_type];
        }
        return $type_name;
    }

    // 获取应付单状态
    public static function getPayableStatusName($status)
    {
        $status_name = '';
        $status_name_map = PayableModel::$STATUS_NAME_MAP;
        if (isset($status_name_map[$status])) {
            $status_name = $status_name_map[$status];
        }
        return $status_name;
    }

    // 获取应付单付款状态
    public static function getPayablePayStatusName($status)
    {
        $pay_status_name = '';
        $status_name_map = PayableModel::$PAY_STATUS_NAME_MAP;
        if (isset($status_name_map[$status])) {
            $pay_status_name = $status_name_map[$status];
        }
        return $pay_status_name;
    }

    // 获取应付单开票状态
    public static function getPayableInvoiceStatusName($status)
    {
        $invoice_status_name = '';
        $status_name_map = PayableModel::$INVOICE_STATUS_NAME_MAP;
        if (isset($status_name_map[$status])) {
            $invoice_status_name = $status_name_map[$status];
        }
        return $invoice_status_name;
    }

    // 获取应付单入库类型
    public static function getPayableStockInTypeName($stock_in_type)
    {
        $stock_in_type_name = '';
        $stock_in_type_name_map = PayableModel::$STOCK_IN_NAME_MAP;
        if (isset($stock_in_type_name_map[$stock_in_type])) {
            $stock_in_type_name = $stock_in_type_name_map[$stock_in_type];
        }
        return $stock_in_type_name;
    }

    // 获取报价货币名称，根据类型转换名称，如1=人民币
    public static function getCurrencyName($currency_type)
    {
        $name = '';
        if ($currency_type) {
            $currency_map = config('field.Currency');
            if ($currency_map && isset($currency_map[$currency_type])) {
                $name = $currency_map[$currency_type];
            }
        }
        return $name;
    }

    // 获取币种code，如 1=RMB
    public static function getCurrencyCode($currency_type)
    {
        $name = '';
        if ($currency_type) {
            $currency_map = config('field.currency_code');
            if ($currency_map && isset($currency_map[$currency_type])) {
                $name = $currency_map[$currency_type];
            }
        }
        return $name;
    }

    // 获取报价货币类型，根据名称获取类型，如 人民币=1
    public static function getCurrencyType($currency_name)
    {
        $type = 0;
        if ($currency_name) {
            $currency_map = config('field.Currency');
            $type = array_search($currency_name, $currency_map);
        }
        return $type;
    }

    // 售后单-申请原因
    public static function getServiceApplyReasonVal($apply_reason_type)
    {
        $apply_reason_val = '';
        if ($apply_reason_type) {
            $apply_reason_map = OrderServiceItemsModel::$APPLY_REASON_MAP;
            if (isset($apply_reason_map[$apply_reason_type])) {
                $apply_reason_val = $apply_reason_map[$apply_reason_type];
            }
        }
        return $apply_reason_val;
    }

    // 售后单-货物处理方案
    public static function getServiceRefundPlanVal($refund_plan_type)
    {
        $refund_plan_val = '';
        if ($refund_plan_type == OrderServiceItemsModel::FEFUND_PLAN_SUP) {
            $refund_plan_val = "退供应商";
        }
        if ($refund_plan_type == OrderServiceItemsModel::FEFUND_PLAN_SELF) {
            $refund_plan_val = "转自营";
        }
        return $refund_plan_val;
    }

    //这个currency可以传currency或者company_id,因为这两个的值都是类型相等的,前者为1,后者肯定也是1
    public static function getFmtPrice($price, $currency, $thousands_sep = ',')
    {
        $price = (float)$price;
        $prefix_sign = '';
        if ($price < 0) {
            $prefix_sign = "-";
            $price = abs($price);
        }
        $currency_sign = ($currency == PriceService::CURRENCY_TYPE_RMB ? '¥' : '$');
        return $prefix_sign . $currency_sign . (string)number_format($price, 2, '.', $thousands_sep);
    }

    // 获取格式化金额，不带币种符合
    public static function getNoSignFmtPrice($price)
    {
        return (string)number_format($price, 2);
    }

    //这个currency可以传currency或者company_id,因为这两个的值都是类型相等的,前者为1,后者肯定也是1
    public static function getSignPrice($price, $currency)
    {
        $price = (float)$price;
        $prefix_sign = '';
        if ($price < 0) {
            $prefix_sign = "-";
            $price = abs($price);
        }
        $currency_sign = ($currency == PriceService::CURRENCY_TYPE_RMB ? '¥' : '$');
        return $prefix_sign . $currency_sign . (string)number_format($price, 2);
    }

    // 固定符号为人民币
    public static function getRmbSignPrice($price)
    {
        $price = (float)$price;
        $prefix_sign = '';
        if ($price < 0) {
            $prefix_sign = "-";
            $price = abs($price);
        }
        $currency_sign = '¥';
        return $prefix_sign . $currency_sign . (string)$price;
    }

    /**
     * 将单个数字转换成中文大写
     * 零、壹、贰、叁、肆、伍、陆、柒、捌、玖
     * @param $num
     * <AUTHOR>
     * @date 2022/3/29
     */
    public static function numToUpperChar($num)
    {
        switch ($num) {
            case 0:
                return '零';
            case 1:
                return '壹';
            case 2:
                return '贰';
            case 3:
                return '叄';
            case 4:
                return '肆';
            case 5:
                return '伍';
            case 6:
                return '陆';
            case 7:
                return '柒';
            case 8:
                return '捌';
            case 9:
                return '玖';
            default:
                return '';
        }

    }

}
