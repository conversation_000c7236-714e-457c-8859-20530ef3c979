<?php

namespace App\Http\Middleware;

use Closure;

class XhProf
{
    public function handle($request, Closure $next)
    {
        $app_config = get_resource_config_section('app', 'crm');
        $open_xhprof = isset($app_config['open_xhprof']) ? $app_config['open_xhprof'] : 0;
        if ($open_xhprof && function_exists('xhprof_enable')) {
            list($usec, $sec) = explode(" ", microtime());
            $startTime = (float)$sec + (float)$usec; // 程序开始时间
            xhprof_enable(XHPROF_FLAGS_NO_BUILTINS | XHPROF_FLAGS_CPU | XHPROF_FLAGS_MEMORY, []);

            $response = $next($request);

            if (function_exists('xhprof_disable')) {
                list($usec, $sec) = explode(" ", microtime());
                $endTime = (float)$sec + (float)$usec;
                $data = xhprof_disable();

                $excl_wall_time = isset($app_config['xhprof_excl_wall_time']) ? $app_config['xhprof_excl_wall_time'] : 0.5;
                if (($endTime - $startTime) > $excl_wall_time) {
                    file_put_contents(sys_get_temp_dir() . DIRECTORY_SEPARATOR . uniqid() . ".crm" . str_replace('/',
                            "-", $request->path()) . '.xhprof', serialize($data));
                }
            }
            return $response;
        } else {
            return $next($request);
        }
    }
}
