<?php

namespace App\Http\Queue;

use Monolog\Formatter\LineFormatter;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
use Monolog\Handler\StreamHandler;
use Monolog\Logger;

class DGKRabbitQueueModel
{

    private $connection;

    const DEFAULT_QUEUE = 'lie_queue_pur';

    const QUEUE_DGK_SYNC_ORDER = 'lie_queue_dgk_sync_order';

    const QUEUE_ORDER = 'lie_queue_order';

    const QUEUE_ERP = 'lie_queue_erp';

    const FORWARD_TYPE_HTTP = "http";

    const FORWARD_TYPE_SOAP = "soap";

    public function __construct(string $connect_name = '')
    {
        if ($connect_name) {
            $config = Config('rabbitmq.connections.' . $connect_name);
        } else {
            $default_connect_name = Config('rabbitmq.default');
            $config = Config('rabbitmq.connections.' . $default_connect_name);
        }

        try {
            // 创建rabbitmq链接
            $this->connection = new AMQPStreamConnection(
                $config['host'], $config['port'], $config['login'], $config['password'], $config['vhost']
            );
        } catch (\Exception $e) {
            $this->connection = null;
            $err_json = json_encode([
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'code' => $e->getCode()
            ]);
            (new Logger('queue'))->pushHandler(new StreamHandler(storage_path('logs/queue.log')))->info($err_json);
        }

    }

    // 队列推送
    public function insertQueue(
        $queue_data,
        $queue_name = self::DEFAULT_QUEUE
    ) {
        try {
            if ($this->connection) {
                $message = new AMQPMessage(json_encode($queue_data, JSON_UNESCAPED_UNICODE));
                $channel = $this->connection->channel();
                $channel->queue_declare($queue_name, false, true, false, false);
                $channel->basic_publish($message, '', $queue_name); // 推送消息
                $channel->close();
                $this->log("queue_insert", $queue_data, $queue_name);
            } else {
                throw new \Exception("rabbit connection is faild");
            }
        } catch (\Exception $e) {
            $this->log("queue_insert_error", $queue_data, $queue_name);
            return false;
        }
        return true;
    }

    public function getConnection()
    {
        return $this->connection;
    }

    private function log($sign, $queue_message, $queue_name = '')
    {
        $user_agent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        $log_message = $sign . "|##data:" . json_encode(['queue_name' => $queue_name, 'mq_data' => $queue_message]) . ",##" . $user_agent;
        $dateFormat = "Y-m-d H:i:s";
        // 最后创建一个格式化器
        $formatter = new LineFormatter(null, $dateFormat);
        $stream = new StreamHandler(storage_path('logs/queue.log'));
        $stream->setFormatter($formatter);
        (new Logger('queue'))->pushHandler($stream)->info($log_message);
    }

    public function __destruct()
    {
        if ($this->connection) {
            $this->connection->close();
        }

    }

}
