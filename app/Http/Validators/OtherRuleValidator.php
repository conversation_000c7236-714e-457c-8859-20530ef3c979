<?php


namespace App\Http\Validators;

use App\Http\Services\SupplierService;
use App\Http\Models\Cube\OtherFeeRuleModel;
use App\Http\Models\Self\SelfClassifyModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class OtherRuleValidator
{
    public function checkSave($data)
    {
        // 验证数据
        if (empty($data['supplier_id'])) {
            return '请选择渠道';
        }
        // 验证打卷费规则
        if (!empty($data['rolling_rule']['is_charge'])) {
            //如果是按包装方式匹配
            if ($data['rolling_rule']['config_type'] == OtherFeeRuleModel::CONFIG_TYPE_PACKAGE) {
                if (empty($data['rolling_rule']['package_keyword'])) {
                    return '请添加包装方式关键词';
                }
            }
            // if (empty($data['rolling_rule']['cny_fee']) && empty($data['rolling_rule']['usd_fee'])) {
            //     return '请至少输入一种币种的打卷费金额';
            // }

            if (!empty($data['rolling_rule']['cny_fee'])) {
                if (!is_numeric($data['rolling_rule']['cny_fee'])) {
                    return '人民币打卷费必须为数字';
                }
                if (floatval($data['rolling_rule']['cny_fee']) < 0) {
                    return '人民币打卷费不能小于0';
                }
                // 检查小数点后位数
                if (strpos($data['rolling_rule']['cny_fee'], '.') !== false) {
                    $decimal = explode('.', $data['rolling_rule']['cny_fee'])[1];
                    if (strlen($decimal) > 4) {
                        return '人民币打卷费最多支持4位小数';
                    }
                }
            }

            if (!empty($data['rolling_rule']['usd_fee'])) {
                if (!is_numeric($data['rolling_rule']['usd_fee'])) {
                    return '美金打卷费必须为数字';
                }
                if (floatval($data['rolling_rule']['usd_fee']) < 0) {
                    return '美金打卷费不能小于0';
                }
                // 检查小数点后位数
                if (strpos($data['rolling_rule']['usd_fee'], '.') !== false) {
                    $decimal = explode('.', $data['rolling_rule']['usd_fee'])[1];
                    if (strlen($decimal) > 4) {
                        return '美金打卷费最多支持4位小数';
                    }
                }
            }
        }

        // 验证操作费规则
        if (!empty($data['operation_rule']['is_charge'])) {
            if ($data['operation_rule']['config_type'] == OtherFeeRuleModel::CONFIG_TYPE_PACKAGE) {
                if (empty($data['operation_rule']['package_keyword'])) {
                    return '请添加包装方式关键词';
                }
            }
            // if (empty($data['operation_rule']['cny_fee']) && empty($data['operation_rule']['usd_fee'])) {
            //     return '请至少输入一种币种的操作费金额';
            // }

            if (!empty($data['operation_rule']['cny_fee'])) {
                if (!is_numeric($data['operation_rule']['cny_fee'])) {
                    return '人民币操作费必须为数字';
                }
                if (floatval($data['operation_rule']['cny_fee']) < 0) {
                    return '人民币操作费不能小于0';
                }
                // 检查小数点后位数
                if (strpos($data['operation_rule']['cny_fee'], '.') !== false) {
                    $decimal = explode('.', $data['operation_rule']['cny_fee'])[1];
                    if (strlen($decimal) > 4) {
                        return '人民币操作费最多支持4位小数';
                    }
                }
            }

            if (!empty($data['operation_rule']['usd_fee'])) {
                if (!is_numeric($data['operation_rule']['usd_fee'])) {
                    return '美金操作费必须为数字';
                }
                if (floatval($data['operation_rule']['usd_fee']) < 0) {
                    return '美金操作费不能小于0';
                }
                // 检查小数点后位数
                if (!empty($data['operation_rule']['usd_fee'])) {
                    if (!is_numeric($data['operation_rule']['usd_fee'])) {
                        return '美金操作费必须为数字';
                    }
                    if (floatval($data['operation_rule']['usd_fee']) < 0) {
                        return '美金操作费不能小于0';
                    }
                    // 检查小数点后位数
                    if (strpos($data['operation_rule']['usd_fee'], '.') !== false) {
                        $decimal = explode('.', $data['operation_rule']['usd_fee'])[1];
                        if (strlen($decimal) > 4) {
                            return '美金操作费最多支持4位小数';
                        }
                    }
                }
                return true;
            }
        }

        return true;
    }
}
