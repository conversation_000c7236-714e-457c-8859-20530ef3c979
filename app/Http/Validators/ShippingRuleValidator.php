<?php


namespace App\Http\Validators;

use App\Http\Models\Self\SelfClassifyModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\SupplierService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Validator;

class ShippingRuleValidator
{
    public function checkSave($data)
    {

        $validator = Validator::make($data, [], [], $this->attributes());
        if ($validator->fails()) {
            return $validator->errors()->first();
        }
        if (empty($data['supplier_id'])) {
            return '请选择一个渠道';
        }

        if (!empty($data['supplier_id']) && $data['supplier_id'] == 17) {
            if (empty($data['supplier_code'])) {
                return '请输入供应商编码';
            }
        }

        if (empty($data['type'])) {
            return '请选择运费规则类型';
        }

        switch ($data['type']) {
            case 1: //按订单金额配置
                //1、人民币、美金规则可只填其中一个，但必须至少有一个币种的规则要有值
                //2、并且，当填写人民币或美金规则时，【订单包邮最小金额】以及【不满足金额收取费用】这两个字段必须都有值，不能只填其中一个

                // 检查是否至少有一个币种的规则
                $hasCnyRule = ($data['free_shipping_cny_amount'] !== '' || $data['shipping_cny_amount'] !== '');
                $hasUsdRule = ($data['free_shipping_usd_amount'] !== '' || $data['shipping_usd_amount'] !== '');

                if (!$hasCnyRule && !$hasUsdRule) {
                    return '人民币、美金规则至少填写一种';
                }

                // 检查人民币规则的完整性
                if ($hasCnyRule) {
                    if ((empty($data['free_shipping_cny_amount']) && $data['free_shipping_cny_amount'] !== '0') || (empty($data['shipping_cny_amount']) && $data['shipping_cny_amount'] !== '0')) {
                        return '人民币规则的【订单包邮最小金额】和【不满足金额收取费用】必须同时填写';
                    }
                    // 验证数字格式
                    if (!is_numeric($data['free_shipping_cny_amount']) || floatval($data['free_shipping_cny_amount']) < 0) {
                        return '人民币【订单包邮最小金额】必须为非负数字';
                    }
                    if (!is_numeric($data['shipping_cny_amount']) || floatval($data['shipping_cny_amount']) < 0) {
                        return '人民币【不满足金额收取费用】必须为非负数字';
                    }
                }

                // 检查美金规则的完整性
                if ($hasUsdRule) {
                    if ((empty($data['free_shipping_usd_amount']) && $data['free_shipping_usd_amount'] !== '0') || (empty($data['shipping_usd_amount']) && $data['shipping_usd_amount'] !== '0')) {
                        return '美金规则的【订单包邮最小金额】和【不满足金额收取费用】必须同时填写';
                    }
                    // 验证数字格式
                    if (!is_numeric($data['free_shipping_usd_amount']) || floatval($data['free_shipping_usd_amount']) < 0) {
                        return '美金【订单包邮最小金额】必须为非负数字';
                    }
                    if (!is_numeric($data['shipping_usd_amount']) || floatval($data['shipping_usd_amount']) < 0) {
                        return '美金【不满足金额收取费用】必须为非负数字';
                    }
                }
                // // 判断是否开启了按汇率转换,如果不是的话,对应的价格不能为空
                // if ($data['is_change_rate_cny'] == -1 && (empty($data['free_shipping_cny_amount']&&$data['free_shipping_cny_amount'] !== '0') && empty($data['shipping_cny_amount']&&$data['shipping_cny_amount'] !== '0'))) {
                //     return '没有开启按汇率转换,人民币规则的【订单包邮最小金额】和【不满足金额收取费用】必须同时填写';
                // }
                // if ($data['is_change_rate_usd'] == -1 && (empty($data['free_shipping_usd_amount']&&$data['free_shipping_usd_amount'] !== '0') && empty($data['shipping_usd_amount']&&$data['shipping_usd_amount'] !== '0'))) {
                //     return '没有开启按汇率转换,美金规则的【订单包邮最小金额】和【不满足金额收取费用】必须同时填写';
                // }

                break;

            case 2: //按订单金额阶梯配置
                // 1. 字段说明：
                // - base_cny_amount: 人民币基础运费
                // - base_usd_amount: 美金基础运费
                // - ladder_cny_amount: 人民币阶梯运费金额
                // - ladder_additional_cny_amount: 人民币阶梯额外运费
                // - ladder_usd_amount: 美金阶梯运费金额
                // - ladder_additional_usd_amount: 美金阶梯额外运费

                // 2. 验证规则：
                // 2.1 所有金额字段必须：
                //   - 大于等于0
                //   - 支持4位小数
                //   - 必须是有效数字

                // 2.2 币种规则：
                //   - 人民币、美金规则可以只填其中一个
                //   - 但必须至少有一个币种的规则要有值

                // 2.3 完整性规则：
                //   当填写某个币种规则时：
                //   - 【基础运费】必须填写
                //   - 【阶梯运费】的两个字段(金额和额外运费)必须同时填写，不能只填其中一个

                // 验证代码实现：
                if ($data['base_cny_amount'] === '' && $data['base_usd_amount'] === '') {
                    return '人民币、美金基础运费至少填写一种';
                }

                // 验证人民币规则
                if (!empty($data['base_cny_amount'])) {
                    // 验证数字格式和范围
                    if (!is_numeric($data['base_cny_amount']) || floatval($data['base_cny_amount']) < 0) {
                        return '人民币基础运费必须为非负数字';
                    }
                    // 验证小数位数
                    if (strpos($data['base_cny_amount'], '.') !== false) {
                        $decimal = explode('.', $data['base_cny_amount'])[1];
                        if (strlen($decimal) > 4) {
                            return '人民币基础运费最多支持4位小数';
                        }
                    }
                    // 验证阶梯运费完整性
                    if ((empty($data['ladder_cny_amount']) && $data['ladder_cny_amount'] !== '0') || (empty($data['ladder_additional_cny_amount']) && $data['ladder_additional_cny_amount'] !== '0')) {
                        return '人民币阶梯运费的金额和额外运费必须同时填写';
                    }
                }

                // 验证美金规则
                if (!empty($data['base_usd_amount'])) {
                    // 验证数字格式和范围
                    if (!is_numeric($data['base_usd_amount']) || floatval($data['base_usd_amount']) < 0) {
                        return '美金基础运费必须为非负数字';
                    }
                    // 验证小数位数
                    if (strpos($data['base_usd_amount'], '.') !== false) {
                        $decimal = explode('.', $data['base_usd_amount'])[1];
                        if (strlen($decimal) > 4) {
                            return '美金基础运费最多支持4位小数';
                        }
                    }
                    // 验证阶梯运费完整性
                    if ((empty($data['ladder_usd_amount']) && $data['ladder_usd_amount'] !== '0') || (empty($data['ladder_additional_usd_amount']) && $data['ladder_additional_usd_amount'] !== '0')) {
                        return '美金阶梯运费的金额和额外运费必须同时填写';
                    }
                }

                // 判断是否开启了按汇率转换,如果不是的话,对应的价格不能为空
                if ($data['ladder_is_change_rate_cny'] == -1 && empty($data['ladder_cny_amount']) && empty($data['ladder_additional_cny_amount'])) {
                    return '没有开启按汇率转换,人民币阶梯运费的金额和额外运费必须同时填写';
                }
                if ($data['ladder_is_change_rate_usd'] == -1 && empty($data['ladder_usd_amount']) && empty($data['ladder_additional_usd_amount'])) {
                    return '没有开启按汇率转换,美金阶梯运费的金额和额外运费必须同时填写';
                }
                break;


            case 3: //按订单型号数量配置
                // 1、如果有一行的人民币运费有值，则每一行的人民币运费都要有值；如果有一行的美金运费有值，则每一行的美金运费都必须有值
                // 2、人民币、美金规则可只填其中一个，但必须至少有一个币种的规则有值
                // 3、除了最后一行外，其余行的最大型号数量都必填

                $hasCnyRule = false;
                $hasUsdRule = false;
                $rowCount = 0;

                // 获取所有行数据
                while (isset($data['sku_count'][$rowCount])) {
                    $rowCount++;
                }

                if ($rowCount == 0) {
                    return '请至少添加一行型号数量配置';
                }

                for ($i = 0; $i < $rowCount; $i++) {
                    $row = $data['sku_count'][$i];

                    // 验证最小值
                    if (!isset($row['min_count']) || !is_numeric($row['min_count']) || floatval($row['min_count']) < 0) {
                        return '最小型号数量必须为非负数字';
                    }

                    // 验证最大值（除最后一行外必填）
                    if ($i < $rowCount - 1) {
                        if (empty($row['max_count']) || !is_numeric($row['max_count']) || floatval($row['max_count']) < 0) {
                            return '除最后一行外，其他行的最大型号数量必须填写且为非负数字';
                        }
                        // 验证范围：最大值必须大于最小值
                        if (floatval($row['max_count']) <= floatval($row['min_count'])) {
                            return '每行的最大型号数量必须大于最小型号数量';
                        }
                    }

                    // 检查人民币运费
                    if (!empty($row['cny_fee'])) {
                        $hasCnyRule = true;
                        if (!is_numeric($row['cny_fee']) || floatval($row['cny_fee']) < 0) {
                            return '人民币运费必须为非负数字';
                        }
                    }

                    // 检查美金运费
                    if (!empty($row['usd_fee'])) {
                        $hasUsdRule = true;
                        if (!is_numeric($row['usd_fee']) || floatval($row['usd_fee']) < 0) {
                            return '美金运费必须为非负数字';
                        }
                    }

                    // 验证区间连续性
                    if ($i > 0) {
                        $prevRow = $data['sku_count'][$i - 1];
                        if (floatval($row['min_count']) != floatval($prevRow['max_count']) + 1) {
                            return '型号数量区间必须连续,比如上一行是1000最大,那么下一行就要1001开头';
                        }
                    }
                }

                // // 验证至少有一个币种的规则
                // if (!$hasCnyRule && !$hasUsdRule) {
                //     return '人民币、美金运费规则至少填写一种';
                // }

                // 验证人民币运费的完整性
                if ($hasCnyRule) {
                    for ($i = 0; $i < $rowCount; $i++) {
                        if (empty($data['sku_count'][$i]['cny_fee'])) {
                            return '如果填写人民币运费，则所有行都必须填写';
                        }
                    }
                }

                // 验证美金运费的完整性
                if ($hasUsdRule) {
                    for ($i = 0; $i < $rowCount; $i++) {
                        if (empty($data['sku_count'][$i]['usd_fee'])) {
                            return '如果填写美金运费，则所有行都必须填写';
                        }
                    }
                }

                // 判断是否开启了按汇率转换
                // if ($data['count_is_change_rate_cny'] == -1 && !$hasCnyRule) {
                //     return '没有开启按汇率转换，人民币运费必须填写';
                // }
                // if ($data['count_is_change_rate_usd'] == -1 && !$hasUsdRule) {
                //     return '没有开启按汇率转换，美金运费必须填写';
                // }

                break;

            case 4: //按订单型号重量配置
                $hasCnyRule = false;
                $hasUsdRule = false;
                $rowCount = 0;

                // 获取所有行数据
                while (isset($data['sku_weight'][$rowCount])) {
                    $rowCount++;
                }

                if ($rowCount == 0) {
                    return '请至少添加一行型号重量配置';
                }

                for ($i = 0; $i < $rowCount; $i++) {
                    $row = $data['sku_weight'][$i];

                    // 验证最小值
                    if (!isset($row['min_weight']) || !is_numeric($row['min_weight']) || floatval($row['min_weight']) < 0) {
                        return '最小重量必须为非负数字';
                    }

                    // 验证小数位数
                    if (strpos($row['min_weight'], '.') !== false) {
                        $decimal = explode('.', $row['min_weight'])[1];
                        if (strlen($decimal) > 4) {
                            return '重量最多支持4位小数';
                        }
                    }

                    // 验证最大值（除最后一行外必填）
                    if ($i < $rowCount - 1) {
                        if (empty($row['max_weight']) || !is_numeric($row['max_weight']) || floatval($row['max_weight']) < 0) {
                            return '除最后一行外，其他行的最大重量必须填写且为非负数字';
                        }
                        // 验证范围：最大值必须大于最小值
                        if (floatval($row['max_weight']) <= floatval($row['min_weight'])) {
                            return '每行的最大重量必须大于最小重量';
                        }
                        // 验证最大值小数位数
                        if (strpos($row['max_weight'], '.') !== false) {
                            $decimal = explode('.', $row['max_weight'])[1];
                            if (strlen($decimal) > 4) {
                                return '重量最多支持4位小数';
                            }
                        }
                    }

                    // 检查人民币运费
                    if (!empty($row['cny_fee'])) {
                        $hasCnyRule = true;
                        if (!is_numeric($row['cny_fee']) || floatval($row['cny_fee']) < 0) {
                            return '人民币运费必须为非负数字';
                        }
                    }

                    // 检查美金运费
                    if (!empty($row['usd_fee'])) {
                        $hasUsdRule = true;
                        if (!is_numeric($row['usd_fee']) || floatval($row['usd_fee']) < 0) {
                            return '美金运费必须为非负数字';
                        }
                    }

                    // 验证区间连续性
                    if ($i > 0) {
                        $prevRow = $data['sku_weight'][$i - 1];
                        if (abs(floatval($row['min_weight']) - floatval($prevRow['max_weight'])) > 0.0001) {
                            return '重量区间必须连续';
                        }
                    }
                }


                // 验证人民币运费的完整性
                if ($hasCnyRule) {
                    for ($i = 0; $i < $rowCount; $i++) {
                        if (empty($data['sku_weight'][$i]['cny_fee'])) {
                            // return '如果填写人民币运费，则所有行都必须填写';
                        }
                    }
                }

                // 验证美金运费的完整性
                if ($hasUsdRule) {
                    for ($i = 0; $i < $rowCount; $i++) {
                        if (empty($data['sku_weight'][$i]['usd_fee'])) {
                            // return '如果填写美金运费，则所有行都必须填写';
                        }
                    }
                }

                // 判断是否开启了按汇率转换
                // if ($data['weight_is_change_rate_cny'] == -1 && !$hasCnyRule) {
                //     return '没有开启按汇率转换，人民币运费必须填写';
                // }
                // if ($data['weight_is_change_rate_usd'] == -1 && !$hasUsdRule) {
                //     return '没有开启按汇率转换，美金运费必须填写';
                // }

                break;

            default:
                return '不支持的运费规则类型';
                break;
        }
        return true;
    }

    private function attributes()
    {
        return [];
    }
}
