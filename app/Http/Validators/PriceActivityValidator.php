<?php


namespace App\Http\Validators;

use Validator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use App\Http\Services\SupplierService;
use App\Http\Models\Self\SelfClassifyModel;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class PriceActivityValidator
{

    const SUPPLIER_ID_ZIYING = 10000;
    const SUPPLIER_ID_ZHUANMAI = 17;
    //通用逻辑检查
    //type=1 价格活动, type=2 赠品活动
    public function commonLogicCheck($request, $type = 1)
    {
        $data = $request->all();
        $activityCheckResult = $this->checkCanAddActivity($data, $type);
        if ($activityCheckResult !== true) {
            return $activityCheckResult;
        }
        if ($data['exclude_standard_brand_ids']) {
            $count = count(explode(',', $data['exclude_standard_brand_ids']));
            if ($count > 30) {
                return "排除的品牌最多30个";
            }
        }
        return true;
    }

    //判断供应商是否已经在搞活动
    private function checkCanAddActivity($data, $type)
    {
        if (empty(Arr::get($data, 'supplier_ids'))) {
            return "供应商不能为空";
        }

        //先去找出所有活动的key,即是供应商id
        $redisKey = $type == 1 ? 'lie_price_activity' : 'lie_gift_activity';
        $redis = Redis::connection('sku');
        $supplierIds = explode(',', $data['supplier_ids']);
        //获取所有自营分类
        $selfClassModel = new SelfClassifyModel();
        $classList = $selfClassModel->where('parent_id', '!=', 0)->pluck('class_name', 'class_id');
        //获取所有渠道编码
        $supplierCodeModel = new SupplierChannelModel();
        $supplierCodeList = $supplierCodeModel->getSupplierCodeList();
        //获取所有供应商
        $supplierService = new SupplierService();
        $suppliers = $supplierService->getSupplierIdSupplierNameMap();
        foreach ($supplierIds as $supplierId) {
            //先去判断基础数据是否满足要求
            //自营 : 当有品牌选择的时候,分类一定不为空
            if ($supplierId == self::SUPPLIER_ID_ZIYING && $data['standard_brand_ids']) {
                if (empty($data['class_ids'])) {
                    return "当有选择品牌时,自营供应商活动的分类不能为空";
                }
            } elseif ($supplierId == self::SUPPLIER_ID_ZHUANMAI && $data['standard_brand_ids']) {
                if (empty($data['canals'])) {
                    return "当有选择品牌时,专卖供应商活动的渠道标签不能为空";
                }
            }
            //然后去挨个遍历去判断是否能搞活动
            $supplierName = "[" . \Arr::get($suppliers, $supplierId, '不明') . "]";
            $supplierActivities = $redis->hget($redisKey, $supplierId);
            $supplierActivities = json_decode($supplierActivities, true);
            $sameClassNames = $sameCanals = $sameBrands = [];

            //redis没有活动的时候,肯定可以直接建立活动
            if (empty($supplierActivities)) {
                continue;
            }
            foreach ($supplierActivities as $activity) {
                //已经删除的或者过期的活动不用去判断冲突
                if (\Arr::get($activity, 'status') == -1 || $activity['end_time'] < time()) {
                    continue;
                }
                //更新的时候自己不用去判断
                if ($activity['activity_id'] == \Arr::get($data, 'activity_id', 0)) {
                    continue;
                }
                //                //判断当前想要保存的活动,是否是要设置为全局活动,如果是,则要去判断该供应商是否有活动,否则一律不让建立全局活动
                //                if (empty($data['standard_brand_ids']) && empty($data['class_ids']) && empty($data['canals'])) {
                //                    return "供应商" . $supplierName . "已经有活动在生效,无法添加全局活动";
                //                }
                //                //先去判断是否是针对该供应商全局活动,如果是的话,整个供应商都只能搞一个,不需要去判断品牌,分类等
                //                if ($activity['entire_supplier_activity'] == true) {
                //                    return "供应商" . $supplierName . "已经有针对该供应商全局活动,不能再设置";
                //                }
                //判断自营
                if ($supplierId == self::SUPPLIER_ID_ZIYING) {
                    //自营只需要去判断分类是否已经有活动在搞
                    if ($activity['class_ids']) {
                        $classIds = $activity['class_ids'];
                        $classIdsList = explode(',', $classIds);
                        $saveClassIdsList = explode(',', $data['class_ids']);
                        $sameClassIds = array_intersect($classIdsList, $saveClassIdsList);
                        foreach ($sameClassIds as $classId) {
                            $sameClassNames[] = \Arr::get($classList, $classId);
                        }
                    }
                }

                //3.专卖只需要判断渠道是否已经有活动在搞
                if ($supplierId == self::SUPPLIER_ID_ZHUANMAI && $activity['canals']) {
                    $canals = $activity['canals'];
                    $canalList = explode(',', $canals);
                    $saveCanalList = explode(',', $data['canals']);
                    $sameCanals = array_intersect($canalList, $saveCanalList);
                    foreach ($sameCanals as $canal) {
                        $sameCanals[] = \Arr::get($supplierCodeList, $canal);
                    }
                }

                //4.专营非专卖的,只需要判断是否已经有品牌在搞
                if ($supplierId != self::SUPPLIER_ID_ZHUANMAI && $supplierId != self::SUPPLIER_ID_ZIYING && $activity['standard_brand_ids']) {
                    $brandIds = $activity['standard_brand_ids'];
                    $brandIdsList = explode(',', $brandIds);
                    $saveCanalList = explode(',', $data['standard_brand_ids']);
                    $sameBrands = array_intersect($brandIdsList, $saveCanalList);
                    foreach ($sameBrands as $brandId) {
                        $sameBrands[] = $redis->hget('brand', $brandId);
                    }
                }
            }
        }
        return true;
    }

    public function checkSave($request)
    {
        $data = $request->all();
        $validator = Validator::make($data, [
            'activity_name' => 'required|max:50',
            'start_time' => 'required',
            'end_time' => 'required',
            'goods_scope' => 'in:1,2',
            'show_name' => 'required|max:2|string',
            'sign' => 'required|max:4',
            'sign_text' => 'required|max:16',
            'ratio' => 'numeric|max:100',
            'ratio_us' => 'numeric|max:100',
            'allow_coupon' => 'required',
            'supplier_ids' => 'required',
        ], [], $this->attributes());
        if ($validator->fails()) {
            return $validator->errors()->first();
        }

        if ($data['supplier_ids']) {
            $supplierIds = explode(',', trim($data['supplier_ids'], ','));
            if (count($supplierIds) > 1 && in_array(17, $supplierIds)) {
                return "不允许同时设置专营和代购在一个折扣活动";
            }
        }

        $oneDaySeconds = 3600 * 24;
        if (strtotime($request->get('end_time')) - strtotime($request->get('start_time')) < $oneDaySeconds) {
            return '结束时间必须大于开始时间一天或以上';
        }
        //判断参与币种
        if (empty($data['currency_rmb']) && empty($data['currency_us'])) {
            return '必须选择一个币种';
        }

        if (!empty($data['currency_rmb']) && empty($data['ratio'])) {
            return '参与币种为人民币时,人民币折扣系数不能为空';
        }

        if (!empty($data['currency_us']) && empty($data['ratio_us'])) {
            return '参与币种为美金时,美金折扣系数不能为空';
        }

        if (!empty($data['org_id']) && $data['org_id'] == PriceActivityModel::ORG_IEDGE) {
            if ($data['supplier_ids'] != 17) {
                return "爱智活动只能设置专营供应商";
            }
        }

        //通用逻辑判断
        $result = $this->commonLogicCheck($request, 1);
        if ($result !== true) {
            return $result;
        }

        return null;
    }

    private function attributes()
    {
        return [
            'show_name' => '展示名称',
            'sign' => '营销标签',
            'sign_text' => '标签说明',
            'ratio' => '折扣系数',
            'ratio_us' => '美金折扣系数',
            'activity_name' => '活动名称',
            'start_time' => '活动开始时间',
            'end_time' => '活动结束时间',
            'allow_coupon' => '是否能使用优惠券',
            'supplier_ids' => '供应商',
            'goods_scope' => '商品范围',
            'user_scope' => '用户范围',
        ];
    }
}
