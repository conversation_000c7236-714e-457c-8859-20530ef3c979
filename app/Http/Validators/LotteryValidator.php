<?php


namespace App\Http\Validators;

use App\Http\Models\Self\SelfClassifyModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\SupplierService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Validator;

class LotteryValidator
{
    public function checkSave($data)
    {
        $validator = Validator::make($data, [

        ], [], $this->attributes());
        if ($validator->fails()) {
            return $validator->errors()->first();
        }
        if (empty($data['lottery_name'])) {
            return '请输入抽奖活动名称';
        }
        if (empty($data['start_time'])) {
            return '请输入活动开始时间';
        }
        if (empty($data['end_time'])) {
            return '请输入活动结束时间';
        }
        if (empty($data['win_limit_per_day']) && empty($data['win_limit_in_all'])) {
            return '至少要有一个限制条件';
        }
        if (!empty($data['qualify_get_rule_register']) && empty($data['qualify_register'])) {
            return '请输入注册最多赠送抽奖资格次数';
        }
        if (!empty($data['qualify_get_rule_login'])) {
            if (empty($data['qualify_login']) && empty($data['qualify_login_day'])) {
                return '请输入登陆最多赠送抽奖资格次数 或 登陆每天赠送抽奖次数';
            }
        }
        if (!empty($data['qualify_get_rule_share']) && empty($data['qualify_share'])) {
            return '请输入分享最多赠送抽奖资格次数';
        }
        if (!empty($data['qualify_get_rule_order'])) {
            if (empty($data['qualify_order'])) {
                return '请输入下单赠送抽奖资格次数';
            }
            if (empty($data['activity_url'])) {
                return '请输入支付成功页要跳转的抽奖地址';
            }
            if (empty($data['paysucimg'])) {
                return '请上传支付成功活动banner图';
            }
        }

        if ((!empty($data['show_success_banner']) && $data['show_success_banner'] == 'on') && !$data['success_banner_image']) {
            if ($data['org_id'] == 1) {
                return '请上传抽奖成功展示banner图';
            }
        }

        if ($data['is_first_login']) {
            if (empty($data['animation_img'])) {
                return '抽奖动效图不能为空';
            }

            if (empty($data['lottery_img'])) {
                return '抽奖活动图不能为空';
            }
        }

        return null;
    }

    private function attributes()
    {
        return [

        ];
    }
}
