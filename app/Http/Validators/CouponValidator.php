<?php


namespace App\Http\Validators;

use App\Http\Models\Self\SelfClassifyModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\SupplierService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Redis;
use Validator;

class CouponValidator
{
    public function checkSave($data)
    {
        $validator = Validator::make($data, [
            'coupon_name' => 'required|max:32',
            'coupon_desc' => 'required|max:16',
            'require_amount' => 'required|numeric',
            'sale_amount' => 'required|numeric',
            'coupon_type' => 'required',
            'coupon_num' => 'required|numeric',
            'total_receive_num' => 'numeric',
            'day_receive_num' => 'numeric',
            'order_num' => 'numeric',
        ], [], $this->attributes());
        if ($validator->fails()) {
            return $validator->errors()->first();
        }
        if ($data['coupon_type'] == 2) {
            if ($data['sale_amount'] >= 1 || $data['sale_amount'] < 0) {
                return '折扣不能大于等于1或小于0';
            }
        }
        if ($data['coupon_num'] > 10000) {
            return '超过发行量最大限制,最大为10000';
        }

        if ($data['time_type'] == 2) {
            if (!is_numeric($data['usable_time'])) {
                return '选择有效期类型为相对时间的时候,有效期日期天数必须是0或者数字';
            }
        }

        if (empty($data['total_receive_num']) && empty($data['day_receive_num'])) {
            return '限领规则必须设置一项且不为空';
        }

        if (!empty($data['coupon_goods_range'])){
            if ($data['coupon_goods_range'] == 2) {
                if (empty($data['selected_supplier_id'])) {
                    return '请选择一个供应商';
                }
            } elseif ($data['coupon_goods_range'] == 3 && $data['coupon_mall_type'] == 3) {
//            if (empty($data['brand_ids']) && empty($data['self_brand_ids'])) {
//                return '请选择一个品牌';
//            }
                if (empty($data['supplier_ids']) && $data['coupon_goods_range'] != 2) {
                    return '请选择一个供应商';
                }
            } else {
                if ($data['coupon_goods_range'] == 4) {
                    if (empty($data['class_ids'])) {
                        return '请填写分类ID';
                    }
                }
            }
        }

        if ($data['time_type'] == 1) {
            if (empty($data['start_time']) || empty($data['end_time'])) {
                return '请选择开始和结束的有效日期';
            }
        }
        if ($data['time_type'] == 2) {
            if (empty($data['usable_time'])) {
                return '请填写相对有效时间';
            }
        }
        if (empty($data['total_receive_num']) && empty($data['day_receive_num'])) {
            return '请输入每人可领取数量或每人每天可领取数量';
        }

        //0.01代表无门槛,所以要特殊判断
        if ($data['require_amount'] != 0.01) {
            if (intval($data['require_amount']) < floatval($data['sale_amount'])) {
                return '金额下限不能小于优惠金额';
            }
        }

        if ($data['coupon_get_rule'] == 1 && empty($data['reg_start_time'])) {
            return '请填写注册开始时间';
        }

        if ($data['coupon_get_rule'] == 3 && empty($data['order_num'])) {
            return '选择付款可领取时,需要填写每笔订单可领取张数';
        }

        if ($data['issue_type'] == 2) {
            if (!empty($data) && $data['coupon_get_rule'] == 3) {
                if (empty($data['effect_start_time']) || empty($data['effect_end_time'])) {
                    return '请选择开始和结束的赠送日期';
                }
            }
            //如果是系统发放 必须选择一个发放方式
            if (empty($data['coupon_get_rule'])) {
                return '系统发放的优惠券必须选择一个领取规则';
            }
        }

        if (!empty($data['limit_time']) && ($data['limit_time'] < 0 || $data['limit_time'] > 23)) {
            return '请填写有效的时间 0-23';
        }
        return null;
    }

    private function attributes()
    {
        return [
            'coupon_name' => '优惠券名称',
            'coupon_desc' => '优惠券描述',
            'require_amount' => '可使用券金额下限',
            'sale_amount' => '优惠金额',
            'coupon_num' => '发行量',
            'coupon_get_rule' => '领取规则'
        ];
    }
}
