<?php

namespace App\Http\Controllers\Api;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\ChannelDiscountModel;
use App\Http\Models\Cube\StepPriceNewModel;
use App\Http\Services\ChannelDiscountService;
use App\Http\Services\GoodsSalePriceGroupService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ChannelDiscountController extends  BaseApiController
{

    /**
     * Notes:列表
     * User: sl
     * Date: 2023-03-04 11:10
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request){
        $requestParams = $request->all();
//        $data["supplier_keyword"] =  arrayGet($requestParams, "supplier_keyword", "", "trim");
//        $data["supplier_code"] =  arrayGet($requestParams, "supplier_code", "", "trim");
        $data["supplier_id"] =  arrayGet($requestParams, "supplier_id", "", "trim");
        $data["brand_name"] =  arrayGet($requestParams, "brand_name", "", "trim");
        $data["status"] =  arrayGet($requestParams, "status", "");
        $data["is_default"] =  arrayGet($requestParams, "is_default", "");
        $data["page"] = arrayGet($requestParams, "page", 1, "intval");
        $data["limit"] = arrayGet($requestParams, "limit", 15, "intval");
        list($list,$count) = ChannelDiscountService::getChannelDiscountList($data);
        return $this->setSuccessData(["list"=>$list,"total"=>$count]);
    }


    /**
     * Notes:启用禁用
     * User: sl
     * Date: 2023-03-04 11:10
     * @param Request $request
     */
    public function disable(Request $request){
        $requestParams = $request->all();
        $channelDisctId =  arrayGet($requestParams, "channel_disct_id", 0, "intval");
        $channelDisctInfo = ChannelDiscountService::getChannelDisctById($channelDisctId);
        if(empty($channelDisctInfo)){
            throw new InvalidRequestException("没找到关联的渠道折扣信息");
        }
        if($channelDisctInfo["status"] == ChannelDiscountModel::$STATUS["status_enable"]){
            $update["status"] = ChannelDiscountModel::$STATUS["status_disable"];
        }else{
            $update["status"] = ChannelDiscountModel::$STATUS["status_enable"];
        }

        ChannelDiscountService::updatechannelDisctStatus($channelDisctId,$update);
        return $this->setSuccess();
    }

    public function add(Request $request){
        $requestParams = $request->all();
        $validator = Validator::make($requestParams, [
            'sup_type' => 'required',
            'supplier_value' => 'required',
            'is_default' => 'required',
            'order' => 'numeric|between:0,255',
            'ration' => 'required|gt:0|max:100',
            'ration_usd' => 'required|gt:0|max:100',

        ])->setAttributeNames(
            [
                'sup_type' => '渠道/供应商类型',
                'supplier_value' => '渠道/供应商',
                'is_default' => '是否默认',
                'ration' => '人民币渠道折扣',
                'ration_usd' => '美金渠道折扣',
                'order' => '优先级',
            ]
        );

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new InvalidRequestException($errors[0]??"未知错误，请联系技术");
        }
        $data["sup_type"] =  arrayGet($requestParams, "sup_type", 0, "intval");
        $data["supplier_value"] =  arrayGet($requestParams, "supplier_value", "", "trim");
        $data["is_default"]  =  arrayGet($requestParams, "is_default", 0, "intval");
        $data["supplier_name"] =  arrayGet($requestParams, "supplier_name", "", "trim");
        $data["order"]  =  arrayGet($requestParams, "order", 0, "intval");
        $data["goods_name"]  =  arrayGet($requestParams, "goods_name", "", "trim");
        $data["file_url"]  =  arrayGet($requestParams, "file_url", "", "trim");
        $data["brand"]  =  arrayGet($requestParams, "brand", "", "trim");
        $data["brand_ids"]  =  arrayGet($requestParams, "brand_ids", "", "trim");
        $data["remark"]  =  arrayGet($requestParams, "remark", "", "trim");
        //20230516渠道折扣值改为了百分比浮点数0-100之间  以前是0-1的小数
        $data["ration"]  =  arrayGet($requestParams, "ration", "0", "floatval");
        $data["ration_usd"]  =  arrayGet($requestParams, "ration_usd", "0", "floatval");
        //接收到的0-100浮点数  转成小数存储
        $data["ration"] = decimal_number_format($data["ration"]/100,DIGITS_FOUR);
        $data["ration_usd"] = decimal_number_format($data["ration_usd"]/100,DIGITS_FOUR);
        if(floatval($data["ration"]) > 1){
            $data["ration"] = 1;
        }

        if(floatval($data["ration_usd"]) > 1){
            $data["ration_usd"] = 1;
        }

        if(floatval($data["ration_usd"]) < 0.0001 || floatval($data["ration"]) < 0.0001){
            throw new InvalidRequestException("渠道折扣率必须大于0");
        }

        $data["supplier_code"] =  "";
        $data["supplier_id"] =  0;
        if($data["sup_type"] == ChannelDiscountModel::SUPTYPE_ZHUANYING){
            $data["supplier_code"] =  (string)$data["supplier_value"];
        }
        if($data["sup_type"] == ChannelDiscountModel::SUPTYPE_DAIGOU){
            $data["supplier_id"] =  (int)$data["supplier_value"];
        }

        if($data["is_default"] == ChannelDiscountModel::DEFAULT_GOODS_PRICE_YES){
            $data["order"] = 0;
        }
        ChannelDiscountService::addCnannelDiscount($data);
        return $this->setSuccess();

    }

    public function update(Request $request){
        $requestParams = $request->all();
        $validator = Validator::make($requestParams, [
            'channel_disct_id' => 'required',
            'is_default' => 'required',
            'order' => 'numeric|between:0,255',
            'ration' => 'required|gt:0|max:100',
            'ration_usd' => 'required|gt:0|max:100',

        ])->setAttributeNames(
            [
                'channel_disct_id' => '渠道折扣id',
                'is_default' => '是否默认',
                'ration' => '人民币渠道折扣',
                'ration_usd' => '美金渠道折扣',
                'order' => '优先级',
            ]
        );

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new InvalidRequestException($errors[0]??"未知错误，请联系技术");
        }
        $data["channel_disct_id"] =  arrayGet($requestParams, "channel_disct_id", 0, "intval");
        $data["is_default"]  =  arrayGet($requestParams, "is_default", 0, "intval");
        $data["order"]  =  arrayGet($requestParams, "order", 0, "intval");
        $data["goods_name"]  =  arrayGet($requestParams, "goods_name", "", "trim");
        $data["file_url"]  =  arrayGet($requestParams, "file_url", "", "trim");
        $data["brand"]  =  arrayGet($requestParams, "brand", "", "trim");
        $data["brand_ids"]  =  arrayGet($requestParams, "brand_ids", "", "trim");
        $data["remark"]  =  arrayGet($requestParams, "remark", "", "trim");
//        $data["ration"]  =  arrayGet($requestParams, "ration", "10", "channelDiscountFormatStr");
//        $data["ration_usd"]  =  arrayGet($requestParams, "ration_usd", "10", "channelDiscountFormatStr");

        //20230516渠道折扣值改为了百分比浮点数0-100之间  以前是0-1的小数
        $data["ration"]  =  arrayGet($requestParams, "ration", "0", "floatval");
        $data["ration_usd"]  =  arrayGet($requestParams, "ration_usd", "0", "floatval");
        //接收到的0-100浮点数  转成小数存储
        $data["ration"] = decimal_number_format($data["ration"]/100,DIGITS_FOUR);
        $data["ration_usd"] = decimal_number_format($data["ration_usd"]/100,DIGITS_FOUR);
        if(floatval($data["ration"]) > 1){
            $data["ration"] = 1;
        }

        if(floatval($data["ration_usd"]) > 1){
            $data["ration_usd"] = 1;
        }

        if(floatval($data["ration_usd"]) < 0.0001 || floatval($data["ration"]) < 0.0001){
            throw new InvalidRequestException("渠道折扣率必须大于0");
        }

        $oldinfo = ChannelDiscountModel::getChannelDidInfo($data["channel_disct_id"]);
        if(empty($oldinfo)){
            throw new InvalidRequestException("没找到相关联数据");
        }
        if($oldinfo["status"] == ChannelDiscountModel::STATUS_DISABLE){
            throw new InvalidRequestException("该记录已经禁用，无法编辑");
        }
        $data["sup_type"] =  $oldinfo["sup_type"];
        $data["supplier_code"] =  $oldinfo["supplier_code"];
        $data["supplier_id"] =  $oldinfo["supplier_id"];
        $data["supplier_name"] =  $oldinfo["supplier_name"];
        if($data["is_default"] == ChannelDiscountModel::DEFAULT_GOODS_PRICE_YES){
            $data["order"] = 0;
        }
        ChannelDiscountService::updateCnannelDiscount($data,$oldinfo);
        return $this->setSuccess();
    }

    /**
     * Notes:获取渠道折扣优先级
     * User: sl
     * Date: 2023-03-06 17:18
     * @param Request $request
     */
    public function getPriorityLevel(Request $request){
        $requestParams = $request->all();
        $data["sup_type"] =  arrayGet($requestParams, "sup_type", 0, "intval");
        $data["supplier_value"] =  arrayGet($requestParams, "supplier_value", "", "trim");
        $data["order"] =  arrayGet($requestParams, "order", "", "trim");
        $validator = Validator::make($data, [
            'sup_type' => 'required',
            'supplier_value' => 'required',
        ])->setAttributeNames(
            [
                'sup_type' => '渠道或代购类型',
                'supplier_value' => '渠道id或者供应商编码',
            ]
        );

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new InvalidRequestException($errors[0]??"未知错误，请联系技术");
        }
        $priorityLevel = ChannelDiscountService::getPriorityLevel($data);
        return $this->setSuccessData($priorityLevel);
    }

    /**
     * Notes:getRelationChanDis
     * User: sl
     * Date: 2023-03-07 14:55
     * @param Request $request
     */
    public function getRelationChanDis(Request $request){
        $requestParams = $request->all();
        $data["sup_type"] =  arrayGet($requestParams, "sup_type", 0, "intval");
        $data["supplier_value"] =  arrayGet($requestParams, "supplier_value", "", "trim");
        $validator = Validator::make($data, [
            'sup_type' => 'required',
            'supplier_value' => 'required',
        ])->setAttributeNames(
            [
                'sup_type' => '渠道或代购类型',
                'supplier_value' => '渠道id或者供应商编码',
            ]
        );

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new InvalidRequestException($errors[0]??"未知错误，请联系技术");
        }
        list($list,$count)  = ChannelDiscountService::getRelationChanDis($data);
        return $this->setSuccessData(["list"=>$list,"total"=>$count]);
    }

    public function getChannelDidInfo(Request $request){
        $requestParams = $request->all();
        $channelDisctId =  arrayGet($requestParams, "channel_disct_id", 0, "intval");
        $data = ChannelDiscountService::getChannelDidInfo($channelDisctId);
        return $this->setSuccessData($data);
    }
}