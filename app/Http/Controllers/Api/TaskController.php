<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\ActionLogModel;
use App\Http\Services\ActionLogService;
use App\Http\Services\CmsUserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class TaskController extends Controller
{
    public function list(Request $request)
    {
        $params = [
            'page'        => $request->input("page",1),
            'limit'       => $request->input("limit",10),
            'obj_type'    => $request->input("obj_type"),
            'act_type'    => $request->input("act_type"),
            'create_uid'  => $request->input("create_uid"),
            'obj_id'      => $request->input("obj_id"),
            'create_time' => $request->input("create_time"),
            'keyword'     => $request->input("keyword"),
        ];

        $validator = Validator::make($params, [
            'page'  => 'required|numeric',
            'limit' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        $where = self::buildWhere($params);
        $sync_log_list = \App\Http\Models\Crm\ActionLogModel::getListByWhere($where, $params['page'], $params['limit']);
        $userMap = CmsUserService::getCmsUserNameMap(array_column($sync_log_list['data'], 'create_uid'));
        foreach ($sync_log_list['data'] as &$sync_log) {
            $sync_log['create_name'] = $userMap[$sync_log['create_uid']] ?? "";
            $logData = json_decode($sync_log['log_data'], true);
            $sync_log['log_data'] = $logData['message'] ?? "";
            $sync_log['create_time'] = date('Y-m-d H:i', $sync_log['create_time']);
            unset($sync_log);
        }

        $data = [
            'total' => $sync_log_list['total'],
            'list'  => $sync_log_list['data'],
        ];
        return $this->setSuccessData($data);
    }

    public static function buildWhere($params)
    {
        $where = [];
        if (isset($params['obj_type']) && $params['obj_type']) {
            $where[] = [
                'obj_type', '=', $params['obj_type']
            ];
        }
        if (isset($params['act_type']) && $params['act_type']) {
            $where[] = [
                'act_type', '=', $params['act_type']
            ];
        }
        if (isset($params['create_uid']) && $params['create_uid']) {
            $where[] = [
                'create_uid', '=', $params['create_uid']
            ];
        }
        if (isset($params['obj_id']) && $params['obj_id']) {
            $where[] = [
                'obj_id', '=', $params['obj_id']
            ];
        }
        if (isset($params['keyword']) && $params['keyword']) {
            $where[] = [
                'log_data', 'like', "%{$params['keyword']}%"
            ];
        }
        if (isset($params['create_time']) && $params['create_time']) {
            list($start, $end) = explode('~', $params['create_time']);
            $where[] = ['create_time', '>=', Carbon::parse(trim($start))->startOfDay()->timestamp];
            $where[] = ['create_time', '<=', Carbon::parse(trim($end))->endOfDay()->timestamp];
        }
        return $where;
    }

}
