<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Services\OtherFeeRuleService;
use App\Http\Models\Cube\OtherFeeRuleModel;
use App\Http\Models\Cube\OperationLogModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\ShippingRuleService;
use App\Http\Validators\OtherRuleValidator;

class OtherFeeRuleController extends Controller
{
    /**
     * 获取其他费用规则列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOtherFeeRuleList(Request $request)
    {
        $map = $request->all();
        $result = (new OtherFeeRuleService())->getOtherFeeRuleList($map);
        return $this->setSuccessData($result['data'], $result['total']);
    }

    /**
     * 保存其他费用规则
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveOtherFeeRule(Request $request)
    {
        $data = $request->all();
        $validator = (new OtherRuleValidator())->checkSave($data);
        if ($validator !== true) {
            return $this->setError($validator);
        }

        try {
            $result = (new OtherFeeRuleService())->saveOtherFeeRule($data);
            return $this->setSuccess('保存成功');
        } catch (\Exception $e) {
            return $this->setError($e->getMessage());
        }
    }

    /**
     * 删除其他费用规则
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteOtherFeeRule(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('参数错误');
        }

        try {
            $result = (new OtherFeeRuleService())->deleteOtherFeeRule($id);
            return $this->setSuccess('删除成功');
        } catch (\Exception $e) {
            return $this->setError($e->getMessage());
        }
    }

    /**
     * 修改其他费用规则状态
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');
        if (empty($id) || empty($status)) {
            return $this->setError('参数错误');
        }

        try {
            $result = (new OtherFeeRuleService())->changeStatus($id, $status);
            return $this->setSuccess('操作成功');
        } catch (\Exception $e) {
            return $this->setError($e->getMessage());
        }
    }

    /**
     * 获取操作日志
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOperationLogs(Request $request)
    {
        $ruleId = $request->input('id');

        $logs = OperationLogModel::where('rule_id', $ruleId)
            ->where('type', OperationLogModel::TYPE_OTHER_FEE_RULE)
            ->orderBy('operation_time', 'desc')
            ->with('operator')
            ->get()
            ->toArray();

        foreach ($logs as &$log) {
            $log['operation_time'] = date('Y-m-d H:i:s', $log['operation_time']);
            $log['operator_name'] = $log['operator']['name'] ?? '';
        }

        return $this->setSuccessData($logs);
    }
}
