<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\ActionLogService;
use App\Http\Services\CmsUserService;
use App\Http\Services\UserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserController extends Controller
{
    public function list(Request $request)
    {
        $params = [
            'page'              => $request->input("page", 1),
            'limit'             => $request->input("limit", 10),
            'user_sn'           => $request->input("user_sn"),
            'user_name'         => $request->input("user_name"),
            'sale_id'           => $request->input("sale_id"),
            'reg_source_params' => $request->input("reg_source_params"),
            'user_nature'       => $request->input("user_nature"),
            'is_have_order'     => $request->input("is_have_order"),
            'account_type'      => $request->input("account_type"),
            'status'            => $request->input("status"),
            'user_tag'          => $request->input("user_tag"),
            'search_params'     => $request->input("search_params"),
            'ptag_params'       => $request->input("ptag_params"),
            'adtag_params'      => $request->input("adtag_params"),
            'is_auto_free'      => $request->input("is_auto_free"),
            'reg_time'          => $request->input("reg_time"),
        ];

        $validator = Validator::make($params, [
            'page'  => 'required|numeric',
            'limit' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        $data = UserService::getList($params);
        return $this->setSuccessData($data);
    }

    public function checkAccount(Request $request)
    {
        $params = [
            'account' => $request->input("account"),
        ];

        $validator = Validator::make($params, [
            'account' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        $data = UserService::checkAccount($params);
        return $this->setSuccessData($data);
    }

    public function add(Request $request)
    {

        $params = [
            //账号
            'account'             => $request->input("account"),
            //客户基础信息
            'contacts'            => $request->input("contacts"),//联系人
            'account_type'        => $request->input("account_type"),//账号分类 1客户账号 2内部测试 3内部尽调
            'is_send_marking_sms' => $request->input("is_send_marking_sms"),//是否发送短信
            'reg_source'          => $request->input("reg_source"),//客户来源
            'qq'                  => $request->input("qq"),
            'wechat'              => $request->input("wechat"),
            'user_position'       => $request->input("user_position"),//客户职务
            'user_nature'         => $request->input("user_nature"),

            //以下是发票/合同信息
            'is_tax_no'           => $request->input("is_tax_no"),//是否填写发票信息
            'tax_title'           => $request->input("tax_title"),//初鉴性质
            'first_nature'        => $request->input("first_nature"),//发票主体
            'business_license'    => $request->input("business_license"),//开票公司名称
            'tax_no'              => $request->input("tax_no"),//税务登记号
            'company_phone'       => $request->input("company_phone"),//公司电话
            'bank_name'           => $request->input("bank_name"),//开户银行
            'bank_account'        => $request->input("bank_account"),//银行账号
            'business_license'    => $request->input("business_license"),//营业执照
            'company_address'     => $request->input("company_address"),//公司地址
            'company_desc'        => $request->input("company_desc"),//公司简介

            //以下是收货信息
            'address_list'        => $request->input("address_list"),//收货信息

            //以下是标签信息
            'user_tag'            => $request->input("user_tag"),//标签信息 逗号分隔
            //以下是客户备注
            "user_remark"         => $request->input("user_remark"),//客户备注
        ];
//        $address_list=[
//            [
//                'province',
//                'city',
//                'area',
//                'address',
//                'username',
//                'phone',
//                "is_default"
//            ]
//        ];


        $validator = Validator::make($params, [
            'page'  => 'required|numeric',
            'limit' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }


    }

    public function getUserInfo(Request $request)
    {

        $params = [
            'user_id' => $request->input("user_id"),
        ];

        $validator = Validator::make($params, [
            'user_id' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        $data = UserService::getUserInfo($params['user_id']);
        return $this->setSuccessData($data);
    }

    public function updateUserBaseInfo(Request $request)
    {

        $params = [
            'user_id'             => $request->input("user_id"),
            'contacts'            => $request->input("contacts"),//联系人
            'account_type'        => $request->input("account_type"),//账号分类 1客户账号 2内部测试 3内部尽调
            'is_send_marking_sms' => $request->input("is_send_marking_sms"),//是否发送短信
            'reg_source'          => $request->input("reg_source"),//客户来源
            'qq'                  => $request->input("qq"),
            'wechat'              => $request->input("wechat"),
            'user_position'       => $request->input("user_position"),//客户职务
            'user_nature'         => $request->input("user_nature"),//客户性质
        ];

        $validator = Validator::make($params, [
            'user_id' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        $data = UserService::getUserInfo($params['user_id']);
        return $this->setSuccessData($data);
    }

    public function updateUserRemark(Request $request)
    {

        $params = [
            'user_id'     => $request->input("user_id"),
            'user_remark' => $request->input("user_remark"),//客户备注
        ];

        $validator = Validator::make($params, [
            'user_id' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        $data = UserService::getUserInfo($params['user_id']);
        return $this->setSuccessData($data);

    }

    public function deleteTag(Request $request)
    {

    }

    public function updateTag(Request $request)
    {

    }

    public function addTag(Request $request)
    {

    }

    public function transferUser(Request $request)
    {

    }

    public function freeUser(Request $request)
    {

    }

    public function updateUserStatus(Request $request)
    {

    }

    public function updateIsAutoFreeUser(Request $request)
    {

    }
}
