<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\StepPriceNewModel;
use App\Http\Services\GoodsSalePriceGroupService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class GoodsSalePriceGroupController extends BaseApiController
{

    /**
     * Notes:新增售价组
     * User: sl
     * Date: 2023-03-03 14:35
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws InvalidRequestException
     */
    public function add(Request $request)
    {
        $requestParams = $request->all();
        $validator = Validator::make($requestParams, [
            'sup_type' => 'required',
            //'supplier_name' => 'required',
            'supplier_value' => 'required',
            'org_id' => 'required',
//            'order' => 'required',

        ])->setAttributeNames(
            [
                'sup_type' => '1代购(渠道) 2专营 (供应商)',
                'supplier_name' => '渠道/供应商名称',
                'is_default' => '是否默认',
                'order' => '优先级',
                'supplier_value' => '渠道/供应商',
                'supplier_code' => '供应商编码',
                'supplier_id' => '供应商id',
                'org_id' => '组织id',
            ]
        );
        $data["sup_type"] = arrayGet($requestParams, "sup_type", 0, "intval");
        $data["supplier_value"] = arrayGet($requestParams, "supplier_value", "", "trim");
        $data["is_default"] = arrayGet($requestParams, "is_default", 0, "intval");
        //新增组织接收
        $data["org_id"] = arrayGet($requestParams, "org_id", 1, "intval");
        $data["supplier_code"] = "";
        $data["supplier_id"] = 0;
        if ($data["sup_type"] == StepPriceNewModel::SUPTYPE_ZHUANYING) {
            $data["supplier_code"] = $data["supplier_value"];

        }
        if ($data["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU) {
            $data["supplier_id"] = $data["supplier_value"];
        }
        $supplierName = GoodsSalePriceGroupService::getSupplierName($data['supplier_value'], $data['sup_type']);
        $data["supplier_name"] = $supplierName;
        $data["order"] = arrayGet($requestParams, "order", "");
        $data["goods_name"] = arrayGet($requestParams, "goods_name", "", "trim");
        $data["brand"] = arrayGet($requestParams, "brand", "", "trim");
        $data["brand_ids"] = arrayGet($requestParams, "brand_ids", "", "trim");
        $data["file_url"] = arrayGet($requestParams, "file_url", "", "trim");
        $data["eccn"] = arrayGet($requestParams, "eccn", "", "trim");
        $data["step_price_data_json"] = arrayGet($requestParams, "step_price_data_json", "", "trim");
        $data["mini_profit_ladder"] = arrayGet($requestParams, "mini_profit_ladder", 0, "intval");
        if ($data["is_default"] == StepPriceNewModel::DEFAULT_GOODS_PRICE_YES) {
            $data["order"] = 0;
            $data["goods_name"] = "";
            $data["brand"] = "";
            $data["brand_ids"] = "";
            $data["file_url"] = "";
            $data["eccn"] = "";
        }

        if (!self::checkStepPriceDataJsonByProfit($data["step_price_data_json"],$data["org_id"])) {
            throw new InvalidRequestException("阶梯利润值只允许递减并且大于等于0值");
        }
        $tmpStepPriceDataJson = json_decode($data["step_price_data_json"], true);

        array_walk($tmpStepPriceDataJson, function (&$tmpStepPriceDataJsonVal, $tmpStepPriceDataJsonkey) {
            $tmpStepPriceDataJsonVal["ratio"] = channelDiscountRound($tmpStepPriceDataJsonVal["ratio"]);
            $tmpStepPriceDataJsonVal["ratio_usd"] = channelDiscountRound($tmpStepPriceDataJsonVal["ratio_usd"]);
            if (isset($tmpStepPriceDataJsonVal["ladder_price_egt50_lt200"])) {
                $tmpStepPriceDataJsonVal["ladder_price_egt50_lt200"] = intval($tmpStepPriceDataJsonVal["ladder_price_egt50_lt200"]);
            }
            if (isset($tmpStepPriceDataJsonVal["ladder_price_egt200"])) {
                $tmpStepPriceDataJsonVal["ladder_price_egt200"] = intval($tmpStepPriceDataJsonVal["ladder_price_egt200"]);
            }
        });

        if ($data["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU) {
            $data["mini_profit_ladder"] = 9;
            foreach ($tmpStepPriceDataJson as $k => $v) {
                if (isset($tmpStepPriceDataJson[$k]["ladder_price_egt50_lt200"])) {
                    unset($tmpStepPriceDataJson[$k]["ladder_price_egt50_lt200"]);
                }
                if (isset($tmpStepPriceDataJson[$k]["ladder_price_egt200"])) {
                    unset($tmpStepPriceDataJson[$k]["ladder_price_egt200"]);
                }
            }
        }

        $data["step_price_data_json"] = is_array($tmpStepPriceDataJson) ? json_encode($tmpStepPriceDataJson) : $tmpStepPriceDataJson;
        $validator->after(function ($validator) use ($data) {
            if ($data["order"] === "") {
                $validator->errors()->add("order", "优先级不能为空");
            }
            if ($data["sup_type"] == StepPriceNewModel::SUPTYPE_ZHUANYING && empty($data["mini_profit_ladder"])) {
                $validator->errors()->add("mini_profit_ladder", "供应商(专营)必须有最低利润点阶梯参数");
            }
        });
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new InvalidRequestException($errors[0] ?? "未知错误，请联系技术");
        }

        $step_price_data_json = json_decode($data["step_price_data_json"], true);

        foreach ($step_price_data_json as $val) {
            if (channelDiscountRound($val["ratio"]) < 0 || channelDiscountRound($val["ratio_usd"]) < 0) {
                throw new InvalidRequestException("销售利润必须大于等于0");
            }

            if ($data["sup_type"] == StepPriceNewModel::SUPTYPE_ZHUANYING && (floatval($val["ladder_price_egt50_lt200"]) <= 0 || floatval($val["ladder_price_egt200"]) <= 0)) {
                throw new InvalidRequestException("供应商(专营)阶梯数量基数必须大于0");
            }
        }
        GoodsSalePriceGroupService::addGoodsSalePriceGroup($data);
        return $this->setSuccess();
    }

    /**
     * Notes:导入参与的型号
     * User: sl
     * Date: 2023-03-03 14:35
     * @param Request $request
     */
    public function importGoodsNames(Request $request)
    {
        $file = $request->file("file");
        if (!$file) {
            throw new InvalidRequestException("文件错误");
        }
        $data = "AD8367-EVALZ@€@LT1134ACN#PBF@€@DC1495A@€@DC1814A-C@€@LTC1555LEGN-1.8#PBF@€@O 19.2-JSO22D1AC-D-2V3-T3-N-D@€@INSERT PER EN4644 14X PLG B CODE@€@";
        return $this->setSuccessData($data);
    }


    public function update(Request $request)
    {
        $requestParams = $request->all();
        $validator = Validator::make($requestParams, [
            'sppe_id' => 'required',
            'supplier_name' => 'required',

        ])->setAttributeNames(
            [
                'sppe_id' => '售价组id',
                'sup_type' => '1代购(渠道) 2专营 (供应商)',
                'supplier_name' => '渠道/供应商名称',
                'is_default' => '是否默认',
                'order' => '优先级',
                'supplier_code' => '供应商编码',
                'supplier_id' => '供应商id',
            ]
        );
        $data["sppe_id"] = arrayGet($requestParams, "sppe_id", 0, "intval");
        $data["order"] = arrayGet($requestParams, "order", 0, "intval");
        $data["goods_name"] = arrayGet($requestParams, "goods_name", "", "trim");
        $data["brand"] = arrayGet($requestParams, "brand", "", "trim");
        $data["brand_ids"] = arrayGet($requestParams, "brand_ids", "", "trim");
        $data["file_url"] = arrayGet($requestParams, "file_url", "", "trim");
        $data["eccn"] = arrayGet($requestParams, "eccn", "", "trim");
        $data["step_price_data_json"] = arrayGet($requestParams, "step_price_data_json", "", "trim");
        $data["mini_profit_ladder"] = arrayGet($requestParams, "mini_profit_ladder", 0, "intval");
        $data["org_id"] = arrayGet($requestParams, "org_id", 1, "intval");

        //修改的时候渠道和 是否默认不让修改
        $strePriceInfo = StepPriceNewModel::getStepPriceInfoById(intval($data["sppe_id"]));
        if (empty($strePriceInfo)) {
            throw new InvalidRequestException("没有找到编辑的数据详情~_~");
        }
        if ($strePriceInfo["status"] == StepPriceNewModel::$STATUS["status_disable"]) {
            throw new InvalidRequestException("该记录已经禁用了，无法编辑~_~");
        }

        $isDefault = $strePriceInfo["order"] == 0 ? StepPriceNewModel::DEFAULT_GOODS_PRICE_YES : StepPriceNewModel::DEFAULT_GOODS_PRICE_NO;
        $supType = $strePriceInfo["sup_type"];

        if ($isDefault == StepPriceNewModel::DEFAULT_GOODS_PRICE_YES) {
            $data["order"] = 0;
            $data["goods_name"] = "";
            $data["brand"] = "";
            $data["brand_ids"] = "";
            $data["file_url"] = "";
            $data["eccn"] = "";
        }

        if ($isDefault == StepPriceNewModel::DEFAULT_GOODS_PRICE_NO && $data["order"] == 0) {
            throw new InvalidRequestException("默认折扣优先级必须为0");
        }

//        if($supType == StepPriceNewModel::SUPTYPE_ZHUANYING){
//            $data["goods_name"] = "";
//            $data["brand"] = "";
//            $data["brand_ids"] = "";
//            $data["file_url"] = "";
//            $data["eccn"] = "";
//        }
        if (!self::checkStepPriceDataJsonByProfit($data["step_price_data_json"],$data["org_id"])) {
            throw new InvalidRequestException("阶梯利润值只允许递减并且大于等于0值");
        }
        $tmpStepPriceDataJson = json_decode($data["step_price_data_json"], true);
        array_walk($tmpStepPriceDataJson, function (&$tmpStepPriceDataJsonVal, $tmpStepPriceDataJsonkey) {
            $tmpStepPriceDataJsonVal["ratio"] = channelDiscountRound($tmpStepPriceDataJsonVal["ratio"]);
            $tmpStepPriceDataJsonVal["ratio_usd"] = channelDiscountRound($tmpStepPriceDataJsonVal["ratio_usd"]);
            if (isset($tmpStepPriceDataJsonVal["ladder_price_egt50_lt200"])) {
                $tmpStepPriceDataJsonVal["ladder_price_egt50_lt200"] = intval($tmpStepPriceDataJsonVal["ladder_price_egt50_lt200"]);
            }
            if (isset($tmpStepPriceDataJsonVal["ladder_price_egt200"])) {
                $tmpStepPriceDataJsonVal["ladder_price_egt200"] = intval($tmpStepPriceDataJsonVal["ladder_price_egt200"]);
            }
        });
        if ($supType == StepPriceNewModel::SUPTYPE_DAIGOU) {
            $data["mini_profit_ladder"] = 9;
            foreach ($tmpStepPriceDataJson as $k => $v) {
                if (isset($tmpStepPriceDataJson[$k]["ladder_price_egt50_lt200"])) {
                    unset($tmpStepPriceDataJson[$k]["ladder_price_egt50_lt200"]);
                }
                if (isset($tmpStepPriceDataJson[$k]["ladder_price_egt200"])) {
                    unset($tmpStepPriceDataJson[$k]["ladder_price_egt200"]);
                }
            }
        }
        $data["step_price_data_json"] = is_array($tmpStepPriceDataJson) ? json_encode($tmpStepPriceDataJson) : $tmpStepPriceDataJson;

        $validator->after(function ($validator) use ($data, $supType) {
            if ($supType == StepPriceNewModel::SUPTYPE_ZHUANYING && empty($data["mini_profit_ladder"])) {
                $validator->errors()->add("mini_profit_ladder", "供应商(专营)必须有最低利润点阶梯参数");
            }
        });
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new InvalidRequestException($errors[0] ?? "未知错误，请联系技术");
        }

        $step_price_data_json = json_decode($data["step_price_data_json"], true);
        foreach ($step_price_data_json as $val) {
            if (floatval($val["ratio"]) < 0 || floatval($val["ratio_usd"]) < 0) {
                throw new InvalidRequestException("销售利润必须大于等于0");
            }

            if ($supType == StepPriceNewModel::SUPTYPE_ZHUANYING && (floatval($val["ladder_price_egt50_lt200"]) <= 0 || floatval($val["ladder_price_egt200"]) <= 0)) {
                throw new InvalidRequestException("供应商(专营)阶梯数量基数必须大于0");
            }
        }
        GoodsSalePriceGroupService::updateGoodsSalePriceGroup($data, $strePriceInfo);
        return $this->setSuccess();

    }

    /**
     * Notes:列表
     * User: sl
     * Date: 2023-03-04 11:10
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request)
    {
        $requestParams = $request->all();
        $data["sppe_sn"] = arrayGet($requestParams, "sppe_sn", 0, "trim");
        $data["status"] = arrayGet($requestParams, "status");
        $data["org_id"] = arrayGet($requestParams, "org_id", 0, "intval");
        $data["is_default"] = arrayGet($requestParams, "is_default");
        $data["supplier_id"] = arrayGet($requestParams, "supplier_id", "", "trim");
//        $data["supplier_keyword"] =  arrayGet($requestParams, "supplier_keyword", "", "trim");
        $data["page"] = arrayGet($requestParams, "page", "", "trim");
        $data["limit"] = arrayGet($requestParams, "limit", "", "trim");

        list($list, $count) = GoodsSalePriceGroupService::getGoodsSalePriceGroupList($data);
        return $this->setSuccessData(["list" => $list, "total" => $count]);
    }


    /**
     * Notes:启用禁用
     * User: sl
     * Date: 2023-03-04 11:10
     * @param Request $request
     */
    public function disable(Request $request)
    {
        $requestParams = $request->all();
        $sppe_id = arrayGet($requestParams, "sppe_id", 0, "intval");
        $StepPriceInfo = GoodsSalePriceGroupService::getStepPriceInfoById($sppe_id);
        if (empty($StepPriceInfo)) {
            throw new InvalidRequestException("没找到关联的售价组信息");
        }
        if ($StepPriceInfo["status"] == StepPriceNewModel::$STATUS["status_enable"]) {
            $update["status"] = StepPriceNewModel::$STATUS["status_disable"];
        } else {
            $update["status"] = StepPriceNewModel::$STATUS["status_enable"];
        }
        GoodsSalePriceGroupService::updateGoodsSalePriceGroupStatus($sppe_id, $update);
        return $this->setSuccess();
    }

    /**
     * Notes:获取商品售价组详情
     * User: sl
     * Date: 2023-03-04 12:00
     * @param Request $request
     */
    public function getGoodsSalePriceGroupInfo(Request $request)
    {
        $requestParams = $request->all();
        $sppe_id = arrayGet($requestParams, "sppe_id", 0, "intval");
        $stepPriceInfo = GoodsSalePriceGroupService::getGoodsSalePriceGroupInfo($sppe_id);
        if (empty($stepPriceInfo)) {
            return $this->setError("没找到相关数据");
        }
        return $this->setSuccessData($stepPriceInfo);
    }

    /**
     * Notes:获取商品售价组优先级
     * User: sl
     * Date: 2023-03-06 17:18
     * @param Request $request
     */
    public function getPriorityLevel(Request $request)
    {
        $requestParams = $request->all();
        $data["sup_type"] = arrayGet($requestParams, "sup_type", 0, "intval");
        $data["supplier_value"] = arrayGet($requestParams, "supplier_value", "", "trim");
        $data["order"] = arrayGet($requestParams, "order", "", "trim");
        $validator = Validator::make($data, [
            'sup_type' => 'required',
            'supplier_value' => 'required',
        ])->setAttributeNames(
            [
                'sup_type' => '渠道或代购类型',
                'supplier_value' => '渠道id或者供应商id',
            ]
        );

        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new InvalidRequestException($errors[0] ?? "未知错误，请联系技术");
        }
        $priorityLevel = GoodsSalePriceGroupService::getPriorityLevel($data);
        return $this->setSuccessData($priorityLevel);
    }

    /**
     * Notes:
     * User: sl
     * Date: 2023-03-15 14:01
     */
    public static function checkStepPriceDataJsonByProfit($stepPriceDataJson,$orgId=1)
    {
        $isOk = true;
        if ($orgId!=1) {
            return true;
        }
        if (is_array($stepPriceDataJson)) {
            $data = $stepPriceDataJson;
        } else {
            $data = json_decode($stepPriceDataJson, true);
        }
        foreach ($data as $k => $item) {
            if (channelDiscountRound($item["ratio"]) < 0 || channelDiscountRound($item["ratio_usd"]) < 0) {
                $isOk = false;
                break;
            }
        }

        foreach ($data as $k => $item) {
            if (isset($data[$k + 1]["ratio"]) && $data[$k + 1]["ratio"] > floatval($item["ratio"])) {
                $isOk = false;
                break;
            }
            if (isset($data[$k + 1]["ratio_usd"]) && $data[$k + 1]["ratio_usd"] > floatval($item["ratio_usd"])) {
                $isOk = false;
                break;
            }
        }
        return $isOk;
    }

    /**
     * 获取售价组操作日志
     */
    public function getOperationLogs(Request $request)
    {
        $requestParams = $request->all();
        $sppeId = arrayGet($requestParams, "sppe_id");

        if (empty($sppeId)) {
            throw new InvalidRequestException("售价组ID不能为空");
        }

        $logs = GoodsSalePriceGroupService::getOperationLogs($sppeId);

        return $this->setSuccess($logs);
    }

}
