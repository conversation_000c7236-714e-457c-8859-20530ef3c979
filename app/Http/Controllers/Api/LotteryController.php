<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\InvalidRequestException;
use App\Http\Controllers\Controller;
use App\Http\Models\Cube\CouponModel;
use App\Http\Models\Cube\LotteryModel;
use App\Http\Models\Cube\PrizeModel;
use App\Http\Models\Cube\PrizeWinnerModel;
use App\Http\Models\Liexin\UserMainModel;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Transformers\LotteryTransformer;
use App\Http\Services\LotteryService;
use App\Http\Validators\LotteryValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class LotteryController extends Controller
{
    //抽奖列表
    public function getLotteryList(Request $request)
    {
        $map = $request->all();
        $result = (new LotteryService())->getLotteryList($map);
        if (!empty($result['data'])) {
            $transformer = new LotteryTransformer();
            $result['data'] = $transformer->listTransform($result['data']);
            return $this->setSuccessData($result['data'], $result['total']);
        }
        return $this->setSuccessData([]);
    }

    //保存抽奖
    public function saveLottery(Request $request)
    {
        $inData = $request->input();
        $isCopy = $request->input('form_data.is_copy');
        if ($isCopy) {
            $inData['lottery_id'] = 0;
        }
        $validator = new LotteryValidator();
        //将非奖品数据 规整到data中
        $data = $inData['form_data'];
        $data['is_first_login'] = isset($data['is_first_login']) ? 1 : 0;
        if ($data['qualify_login'] != 1) {
            $data['lottery_img'] = '';
            $data['animation_img'] = '';
            $data['is_first_login'] = 0;
        }
        //先去表单验证
        $validateResult = $validator->checkSave($data);
        if ($validateResult) {
            return $this->setError($validateResult);
        }
        //解析奖品
        $prizeArr = json_decode($inData['prize_json'], true);
        $data['lottery_id'] = $inData['lottery_id'];
        //数据校验
        $currentUid = $request->user->userId;
        //初始化model
        $lotteryModel = [];
        //获取总奖品数 以及 判断概率和是否正确
        $chanceSum = 0;
        $allPrizeNum = 0;
        foreach ($prizeArr as $k => $prize) {
            if ($prize['prize_num'] > 10000 && empty($inData['lottery_id'])) {
                throw new InvalidRequestException('奖品数量最多为10000');
            }
            //计算所有奖品数
            if ($prize['prize_type'] != 4) {
                $allPrizeNum += $prize['prize_num'];
            }
            //计算总概率
            $chanceSum += $prize['chance'];
            //验证奖品数量与该奖品抽中/概率的合理性
            if ($prize['prize_num'] == 0) {
                if ($prize['prize_send_num_day'] != 0 || $prize['chance'] != 0) {
                    return $this->setError('如果第' . ($k + 1) . '个奖品数量为0，那么每天抽中的数量和中奖概率也必须为0');
                }
            }
        }
        if ($chanceSum != 100) {
            return $this->setError('奖品的中奖概率之和要等于100');
        }


        //获取 送资格渠道s
        $qualifyGetRules = (new LotteryService())->qualifyGetRules($data);
        $lotteryModel['lottery_name'] = $data['lottery_name'];
        $lotteryModel['start_time'] = strtotime($data['start_time']);
        $lotteryModel['end_time'] = strtotime($data['end_time']);
        $lotteryModel['all_prize_num'] = $allPrizeNum;
        $lotteryModel['status'] = 1;
        $lotteryModel['qualify_get_rule'] = $qualifyGetRules;
        $lotteryModel['qualify_login'] = $data['qualify_login'];
        $lotteryModel['qualify_share'] = $data['qualify_share'];
        $lotteryModel['qualify_follow'] = $data['qualify_follow'];//关注送抽奖次数
        $lotteryModel['qualify_login_day'] = $data['qualify_login_day'];
        $lotteryModel['qualify_order'] = $data['qualify_order'];
        $lotteryModel['org_id'] = !empty($data['iedge_org_id']) ? $data['iedge_org_id'] : ($data['org_id'] ?? 1);
        $lotteryModel['activity_url'] = $data['activity_url'];
        $lotteryModel['qualify_register'] = $data['qualify_register'];
        $lotteryModel['win_limit_per_day'] = $data['win_limit_per_day'];
        $lotteryModel['win_limit_in_all'] = $data['win_limit_in_all'];
        $lotteryModel['limit_get_amount'] = $data['limit_get_amount'];
        $lotteryModel['prize_type_num'] = 8;
        $lotteryModel['pay_suc_img_url'] = $data['paysucimg'];
        $lotteryModel['success_banner_image'] = $data['success_banner_image'];
        $lotteryModel['lottery_img'] = $data['lottery_img'];
        $lotteryModel['animation_img'] = $data['animation_img'];
        $lotteryModel['is_first_login'] = $data['is_first_login'];
        $lotteryModel['show_success_banner'] = (!empty($data['show_success_banner']) && $data['show_success_banner'] == 'on') ? 1 : -1;
        if ($lotteryModel['show_success_banner'] == -1) {
            $lotteryModel['success_banner_image'] = '';
        }
        //去掉钱包校验,写死类型2
        $lotteryModel['money_get_type'] = 2;

        //统计这些抽奖需要多少钱
        $sumMoney = 0;
        //统计奖品发放钱包的总金额
        foreach (json_decode($inData['prize_json']) as $key => $value) {
            //如果是其他(发放到钱包的) 累加金额
            if ($value->prize_type == 3) {
                $sumMoney += ($value->prize_value * $value->prize_num);
            }
        }
        //去掉钱包校验
//        $isValid = (new LotteryService())->checkBalance($sumMoney);
//        if ($isValid) {
//            $lotteryModel['apply_amount'] = $data['apply_amount'];
//        } else {
//            return $this->setError('系统可用余额不足,操作失败');
//        }

        if (empty($data['lottery_id'])) {
            //不能创建同名活动
            $exist_id = LotteryModel::select('lottery_id')->where('lottery_name',
                $data['lottery_name'])->first();
            if (!empty($exist_id)) {
                return $this->setError('已存在同名活动，请更改活动名称');
            }
        }

        //事务
        DB::connection('web')->transaction(function () use ($prizeArr, $data, $lotteryModel, $currentUid) {
            $redis = Redis::connection('user');
            if (empty($data['lottery_id'])) {
                //新增
                //抽奖活动
                $lotteryModel['publisher_id'] = $currentUid;
                $lotteryModel['updater_id'] = $currentUid;
                $lotteryModel['create_time'] = time();
                $lotteryModel['update_time'] = time();
                $insertLotteryId = LotteryModel::insertGetId($lotteryModel);
                if (!$insertLotteryId) {
                    throw new InvalidRequestException('新增抽奖活动失败');
                }
                $lotteryModel['lottery_id'] = $insertLotteryId;
                $redis->hset('lie_lottery_activity', $insertLotteryId, json_encode($lotteryModel));
                $prizes = [];
                //奖品
                foreach ($prizeArr as $k => $prize) {
                    unset($prize['prize_id']);
                    $prize['lottery_id'] = $insertLotteryId;
                    $prize['is_need_deliver'] = ($prize['prize_type'] == 1) ? 1 : 2;
                    $prize['create_time'] = time();
                    $prize['update_time'] = time();
                    //优惠券则coupon_id存在ex_str中,name存名字
                    if ($prize['prize_type'] == 2) {
                        //id 为新增或更改的优惠券标志
                        //新增时 ex_str 为空
                        //直接 将coupon_id从prize_name中截取出 赋值给ex_str即可
                        //prize_name为优惠券名字
                        if (0 !== strpos($prize['prize_name'], 'id')) {
                            throw new InvalidRequestException('第' . ($k + 1) . '个奖品名称请填写id+优惠券id');
                        }
                        $prize['ex_str'] = trim(substr($prize['prize_name'], 2));
                        $couponName = CouponModel::select('coupon_name')->where('coupon_id',
                            trim(substr($prize['prize_name'], 2)))->first();
                        if (!empty($couponName)) {
                            $prize['prize_name'] = $couponName->coupon_name;
                        } else {
                            return $this->setError('第' . ($k + 1) . '个奖品的优惠券ID输入有误');
                        }
                    } else {
                        $prize['ex_str'] = '';
                    }
                    $insertPrizeId = PrizeModel::insertGetId($prize);
                    if (!$insertPrizeId) {
                        throw new InvalidRequestException('新增抽奖活动失败');
                    }
                    $prize['prize_id'] = $insertPrizeId;
                    $prizes[] = $prize;
                    //未中奖的奖品单独存redis
                    if ($prize['prize_type'] == 4) {
                        $redis->hset('lie_prize_dont_win', $insertPrizeId . 'dw', json_encode($prize));
                    }
                }
                $redis->hset('lie_prize', $insertLotteryId, json_encode($prizes));
                //创建奖品redis缓存，防止超抽
                (new LotteryService())->saveDrawInfo($lotteryModel, $insertLotteryId, $prizes);
            } else {
                //编辑
                //抽奖活动
                $lotteryModel['updater_id'] = $currentUid;
                $lotteryModel['update_time'] = time();
                $result = LotteryModel::where('lottery_id',
                    $data['lottery_id'])->update($lotteryModel);
                if (!$result) {
                    throw new InvalidRequestException('编辑抽奖活动失败');
                }
                $lotteryModel['lottery_id'] = $data['lottery_id'];
                $redis->hset('lie_lottery_activity', $data['lottery_id'], json_encode($lotteryModel));

                //奖品
                $prizes = [];
                foreach ($prizeArr as $k => $prize) {
                    $prize['update_time'] = time();
                    //优惠券则coupon_id存在ex_str中,name存名字
                    if ($prize['prize_type'] == 2) {
                        //编辑时 有两种情况
                        //1.从 非优惠券  -->  优惠券 类型
                        if (empty($prize['ex_str'])) {
                            //此时 必须 加表示 'id' 说明这是要 搞优惠券咯
                            if (0 === strpos($prize['prize_name'], 'id')) {
                                $couponName = CouponModel::select('coupon_name')->where('coupon_id',
                                    trim(substr($prize['prize_name'], 2)))->first();
                                if (empty($couponName)) {
                                    throw new InvalidRequestException('第' . ($k + 1) . '个奖品的优惠券ID输入有误');
                                } else {
                                    $prize['ex_str'] = trim(substr($prize['prize_name'], 2));
                                    $prize['prize_name'] = $couponName->coupon_name;
                                }
                            } else {
                                throw new InvalidRequestException('第' . ($k + 1) . '个奖品的优惠券ID输入有误');
                            }
                        } else//2.优惠券 --> 优惠券 类型  只是改了名字 或者 换其他的优惠券
                        {
                            //只需判断 换成其他的优惠券的情况
                            if (0 === strpos($prize['prize_name'], 'id')) {
                                if (0 === strpos($prize['prize_name'], 'id')) {
                                    $couponName = CouponModel::select('coupon_name')->where('coupon_id',
                                        trim(substr($prize['prize_name'], 2)))->first();
                                    if (empty($couponName)) {
                                        throw new InvalidRequestException('第' . ($k + 1) . '个奖品的优惠券ID输入有误');
                                    } else {
                                        $prize['ex_str'] = trim(substr($prize['prize_name'], 2));
                                        $prize['prize_name'] = $couponName->coupon_name;
                                    }
                                }
                            }
                        }
                    } else {
                        $prize['ex_str'] = '';
                    }
                    $result = PrizeModel::where('prize_id',
                        $prize['prize_id'])->where('lottery_id', $data['lottery_id'])->update($prize);
                    if (!$result) {
                        throw new InvalidRequestException('编辑抽奖活动失败');
                    }
                    $prizes[] = $prize;
                }
                $redis->hset('lie_prize', $data['lottery_id'], json_encode($prizes));

                //创建奖品redis缓存，防止超抽
                (new LotteryService())->saveDrawInfo($lotteryModel, $data['lottery_id'], $prizes);
            }
            return true;
        });
        //删除特定的redis key
        $redis = Redis::connection('user');
        $redis->del('lie_user_draw_count');
        return $this->setSuccess('保存抽奖成功');
    }

    //删除抽奖活动
    public function deleteLottery(Request $request)
    {
        $lotteryId = $request->input('lottery_id');
        $result = LotteryModel::where('lottery_id', $lotteryId)
            ->update([
                'status' => LotteryModel::STATUS_DELETED,
                'updater_id' => request()->user->userId,
                'update_time' => time(),
            ]);
        if (!$result) {
            return $this->setError('状态变更失败');
        }
        return $this->setSuccess('删除成功');
    }
}
