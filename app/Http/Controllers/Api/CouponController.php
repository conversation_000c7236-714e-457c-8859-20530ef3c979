<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\Cube\CouponClassModel;
use App\Http\Models\Liexin\UserMainModel;
use App\Http\Models\Cube\CouponModel;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Transformers\CouponTransformer;
use App\Http\Services\CouponService;
use App\Http\Validators\CouponValidator;
use App\Imports\IssueCouponImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;

class CouponController extends Controller
{
    //优惠券列表
    public function getCouponList(Request $request)
    {
        $map = $request->all();
        $result = (new CouponService())->getCouponList($map);
        if (!empty($result['data'])) {
            $transformer = new CouponTransformer();
            $result['data'] = $transformer->listTransform($result['data']);
            return $this->setSuccessData($result['data'], $result['total']);
        }
        return $this->setSuccessData([]);
    }

    //保存优惠券
    public function saveCoupon(Request $request)
    {
        $data = $request->input();
        $validator = new CouponValidator();
        //先去表单验证
        $validateResult = $validator->checkSave($data);
        if ($validateResult) {
            return $this->setError($validateResult);
        }
        $couponService = new CouponService();
        $result = $couponService->saveCoupon($data);
        return $this->setSuccess('保存优惠券成功');
    }

    //生成请求参数
    private function generateParamsWithCouponId($couponId)
    {
        $params = [];
        $params['k1'] = time();
        $params['k2'] = pwdhash($params['k1'], 'fh6y5t4rr351d2c3bryi');
        $params['id'] = $couponId;
        return $params;
    }


    public function deleteCoupon(Request $request)
    {
        $couponId = $request->input('coupon_id');
        $params = $this->generateParamsWithCouponId($couponId);
        $apiDomain = config('website.api_domain');
        $destroyRedisResult = curl("$apiDomain/coupon/destroy", $params);
        $destroyRedisResult = json_decode($destroyRedisResult, true);
        if (!empty($destroyRedisResult['err_code'])) {
            $msg = $destroyRedisResult['err_msg'];
        } else {
            $msg = '';
        }
        $result = CouponModel::where('coupon_id', $couponId)->update(['status' => -3]);
        return $this->setSuccess('删除成功 ' . $msg);
    }

    //发券上传excel
    public function analysisIssueCouponFile(Request $request)
    {
        ini_set('memory_limit', '512M');
        $file = $request->file('file');
        $sendList = $notExistList = [];
        $accountList = Excel::toArray(new IssueCouponImport(), $file)[0];
        $orgId = $request->user->org_id;
        foreach ($accountList as $k => $v) {
            if ($k == 0) {
                continue;
            }
            if (empty($v)) {
                continue;
            }
            $account = $v[0];
            //电话号码/邮箱
            $account = is_numeric($account) ? (int)$account : $account;
            if ($orgId == 1) {
                $userId = (new UserMainModel)->getUserIdWithMobileOrEmail($account);
            } else {
                $userId = (new UserMainModel())->getUserIdWithMobileOrEmailFromUcenter($account, $orgId);
            }
            if ($userId) {
                if (strpos($account, ',') === false) {
                    $sendList[] = $account;
                } else {
                    $notExistList[] = $account;
                }

            } else {
                if (!empty($account)) {
                    $notExistList[] = $account;
                }
            }
        }
        $sendList = array_values(array_unique($sendList));
        $notExistList = array_values(array_unique($notExistList));
        $data['valid_account_list'] = $sendList;
        $data['invalid_account_list'] = $notExistList;
        return $this->setSuccessData($data);
    }

    //发放优惠券
    public function issueCoupon(Request $request)
    {
        set_time_limit(0);
        $data = $request->input();
        if (empty($data['coupon_id'])) {
            return $this->setError('请选择优惠券');
        }
        if (empty($data['issue_num'])) {
            return $this->setError('请输入限领规则');
        }
        if (empty($data['send_list'])) {
            return $this->setError('请填写发放名单');
        }

        $orgId = CouponModel::where('coupon_id', $data['coupon_id'])->value('org_id');
        //调用api
        $params = $this->generateParamsWithCouponId($data['coupon_id']);
        $sendUserListStr = $data['send_list'];
        $sendUserList = array_filter(explode(',', $sendUserListStr));
        $notExistList = [];
        if ($orgId == 1) {
            foreach ($sendUserList as $userAccount) {
                $userAccount = trim($userAccount);
                //这里要兼容华云
                $userId = (new UserMainModel())->getUserIdWithMobileOrEmail($userAccount);
                if (empty($userId)) {
                    $notExistList[] = $userAccount;
                }
            }
        } else {
            foreach ($sendUserList as $userAccount) {
                $userAccount = trim($userAccount);
                //这里要兼容华云
                $userId = (new UserMainModel())->getUserIdWithMobileOrEmailFromUcenter($userAccount, $orgId);
                if (empty($userId)) {
                    $notExistList[] = $userAccount;
                }
            }
        }

        if (count($notExistList) > 0) {
            return $this->setError('列表中有账号不存在:' . implode(',', $notExistList));
        }
        for ($j = 0; $j < count($sendUserList); $j++) {
            for ($i = 0; $i < $data['issue_num']; $i++) {
                $tmpTime = $this->generateParamsWithCouponId($data['coupon_id']);
                $params['k1'] = $tmpTime['k1'];
                $params['k2'] = $tmpTime['k2'];
                /*
                    特别提示:这里不要用网上的正则去验证手机号 因为总是在增加新手机号段
                    反正不是邮箱 就是手机号
                */

                if ($orgId == 1) {
                    //请求API
                    $isEmail = filter_var($sendUserList[$j], FILTER_VALIDATE_EMAIL);
                    if ($isEmail) {
                        $params['uid'] = (new UserMainModel())->getUserIdByEmail(trim($sendUserList[$j]));
                    } else {
                        $params['uid'] = (new UserMainModel())->getUserIdByMobile(trim($sendUserList[$j]));
                    }
                } else {
                    $params['uid'] = (new UserMainModel())->getUserIdWithMobileOrEmailFromUcenter(trim($sendUserList[$j]), $orgId);
                }

                $params['adtag'] = 'inner_issue';
                $params['pf'] = 5;
                $params['coupon_source'] = 1;
                $params['order_id'] = 0;
                $params['org_id'] = $orgId ?? 1;
                $apiDomain = config('website.api_domain');
                $issueResult = curl("$apiDomain/coupon/issue", $params);
                $issueResult = json_decode($issueResult, true);
                if ($issueResult == null) {
                    return $this->setError('发券请求API失败,请检查防火墙');
                } elseif (!empty($issueResult['err_code'])) {
                    return $this->setError($issueResult['err_msg']);
                }
            }
        }
        return $this->setSuccess('发放惠券成功');
    }

    public function reviewCoupons(Request $request)
    {
        $couponIds = $request->input('coupon_ids');
        $couponIds = explode(',', trim($couponIds, ','));
        $reviewRemark = $request->input('review_remark');
        $status = $request->input('status', -2);
        if (empty($couponIds)) {
            return $this->setError('需要审核的优惠券id不能为空');
        }
        if (empty($reviewRemark)) {
            return $this->setError('审核备注不能为空');
        }
        $result = (new CouponService())->reviewCoupons($couponIds, $status, $reviewRemark);
        if (!$result) {
            return $this->setError('审核优惠券失败');
        }
        return $this->setSuccess('审核优惠券成功');
    }

}
