<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\ActionLogModel;
use App\Http\Models\Cube\ActivityModel;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Services\ActionLogService;
use App\Http\Services\ActivityElementService;
use App\Http\Services\ActivityService;
use App\Http\Services\CmsUserService;
use App\Http\Services\GoodsSalePriceGroupService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;


class ActivityElementController extends Controller
{
    public function list(Request $request)
    {
        //活动编号,活动地址,活动分类,活动状态,活动时间(1个字段),创建时间(1个字段)
        $params = [
            'page'           => $request->input("page", 1),//页数
            'limit'          => $request->input("limit", 10),//每页数量
            'element_name'   => $request->input("element_name"),//元件名
            'element_code'   => $request->input("element_code"),//元件code
            'element_status' => $request->input("element_status"),//元件状态 1启用,-1禁用
        ];
        $validator = Validator::make($params, [
            'page'  => 'required|numeric',
            'limit' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityElementService::getList($params);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function add(Request $request)
    {
        $params = [
            'element_name' => $request->input("element_name"),
            'element_code' => $request->input("element_code"),
        ];
        $validator = Validator::make($params, [
            'element_name' => 'required|string',
            'element_code' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityElementService::add($params);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function update(Request $request)
    {
        //活动id
        $params = [
            'id'           => $request->input("id"),
            'element_name' => $request->input("element_name"),
            'element_code' => $request->input("element_code"),
        ];
        $validator = Validator::make($params, [
            'id'           => 'required|numeric',
            'element_name' => 'required|string',
            'element_code' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityElementService::update($params['id'], $params);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function getInfo(Request $request)
    {
        //活动id
        $params = [
            'id' => $request->input("id"),
        ];
        $validator = Validator::make($params, [
            'id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityElementService::getInfo($params['id']);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function updateStatus(Request $request)
    {
        //活动id,活动状态(0-关闭,1-开启)
        $params = [
            'id'     => $request->input("id"),
            'status' => $request->input("status"),
        ];
        $validator = Validator::make($params, [
            'id'     => 'required|numeric',
            'status' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityElementService::updateStatus($params['id'], $params['status']);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    /**
     * 验证抽奖活动是否存在
     * checkLottery
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * <AUTHOR>
     * Time: 12:01 PM
     */
    public function checkLottery(Request $request)
    {
        //活动地址
        $params = [
            'lottery_ids' => $request->input("lottery_ids"),
        ];
        $validator = Validator::make($params, [
            'lottery_ids' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }

        try {
            $data = ActivityElementService::checkMarketingData(['lottery_ids' => $params['lottery_ids']]);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    /**
     * 验证优惠券活动是否存在
     * checkLottery
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * <AUTHOR>
     * Time: 12:01 PM
     */
    public function checkCoupon(Request $request)
    {
        //优惠券编码
        $params = [
            'coupon_sns' => $request->input("coupon_sns"),
        ];
        $validator = Validator::make($params, [
            'coupon_sns' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }

        try {
            $data = ActivityElementService::checkMarketingData(['coupon_sns' => $params['coupon_sns']]);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function checkActivityPriceId(Request $request)
    {
        //活动id
        $params = [
            'activity_price_id' => $request->input("activity_price_id"),
        ];
        $validator = Validator::make($params, [
            'activity_price_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }

        try {
            $data = PriceActivityModel::getOneById($params['activity_price_id'] ?? 0);
            if (!$data) {
                throw new \Exception('活动价不存在');
            }
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function checkSupplierIds(Request $request)
    {
        //活动id
        $params = [
            'supplier_ids' => $request->input("supplier_ids"),
        ];
        $validator = Validator::make($params, [
            'supplier_ids' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityElementService::getSupplierListByIds($params['supplier_ids']);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function checkSupplierCodes(Request $request)
    {
        //活动id
        $params = [
            'supplier_codes' => $request->input("supplier_codes"),
        ];
        $validator = Validator::make($params, [
            'supplier_codes' => 'required|string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityElementService::getSupplierListByCodes($params['supplier_codes']);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    //根据一个文件地址,返回一个id,逗号分隔的字符串
    public function getSkuIdsByFile(Request $request)
    {
        $params = [
            'file_url'    => $request->input('file_url'),
            'activity_id' => $request->input('activity_id') ?? 0,
        ];
        $validator = Validator::make($params, [
            'file_url'    => 'required',
            'activity_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityElementService::getIdsByCsvFile($params['file_url']);
            //去掉前面的SKU字符串
            foreach ($data as $key => &$datum) {
                $datum = str_replace('SKU', '', $datum);
            }
            //插入到数据库中,让搜索那边处理
            ActivityElementService::updateActivitySkuName($params['activity_id'], $data);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData(implode(',', $data));
    }

    public function deleteSkuNameByActivityId(Request $request)
    {
        $params = [
            'activity_id' => $request->input('activity_id'),
        ];
        $validator = Validator::make($params, [
            'activity_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            ActivityElementService::deleteSkuNameByActivityId($params['activity_id']);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
    }

    public function getZhuanYingSupList(Request $request)
    {
        $zhuanYingSupList = GoodsSalePriceGroupService::getSupList(2);
        return $this->setSuccessData(["list" => $zhuanYingSupList]);
    }


}
