<?php

namespace App\Http\Controllers\Api;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\ChannelDiscountModel;
use App\Http\Models\Cube\StepPriceNewModel;
use App\Http\Services\ChannelDiscountService;
use App\Http\Services\CurrencyConfigService;
use App\Http\Services\GoodsPriceSystemService;
use App\Http\Services\GoodsSalePriceGroupService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class GoodsPriceSystemController extends  BaseApiController
{

    /**
     * Notes:商品价格体系列表
     * User: sl
     * Date: 2023-03-04 11:10
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request){
        $requestParams = $request->all();
        $data["skuids"] =  arrayGet($requestParams, "skuids", "", "trim");
        $data["goods_name"] =  arrayGet($requestParams, "goods_name", "", "trim");
        $data["supplier_id"] =  arrayGet($requestParams, "supplier_id", "","trim");
        $data["brand_id"] =  arrayGet($requestParams, "brand_id", "","trim");
        $data["page"] =  arrayGet($requestParams, "page", "1","intval");
        $data["limit"] =  arrayGet($requestParams, "limit", "15","intval");
        $data["canal"] = "";
        if($data["supplier_id"] && strtoupper(substr($data["supplier_id"],0,1)) == "L"){
            $data["canal"] = $data["supplier_id"];
            $data["supplier_id"] = 0;
        }
        if($data["skuids"]){
            $list = GoodsPriceSystemService::getSkusInfo($data["skuids"]);
            $count = count($list);
        }else{
            list($list,$count) = GoodsPriceSystemService::getSkuList($data);
        }
        return $this->setSuccessData(["list"=>$list,"total"=>$count]);
    }

    /**
     * Notes:新增供应商特殊币种配置
     * User: sl
     * Date: 2023-08-16 14:47
     * @param Request $request
     */
    public function addCurrencyConfig(Request $request){
        $requestParams = $request->all();
        $validator = Validator::make($requestParams, [
            'sup_type' => 'required',
            'supplier_name' => 'required',
            'supplier_value' => 'required',
            'currency' => 'required',

        ])->setAttributeNames(
            [
                'sup_type' => '1代购(渠道) 2专营 (供应商)',
                'supplier_name' => '渠道/供应商名称',
                'supplier_value' => '渠道/供应商',
                'currency' => '原币钟',
            ]
        );
        $data["sup_type"] =  arrayGet($requestParams, "sup_type", 0, "intval");
        $data["supplier_value"] =  arrayGet($requestParams, "supplier_value", "", "trim");
        $data["supplier_name"] =  arrayGet($requestParams, "supplier_name", "", "trim");
        $data["currency"] =  arrayGet($requestParams, "currency", "", "trim");
        $data["us_to_cn"] =  arrayGet($requestParams, "us_to_cn",0,"intval");
        $data["is_tax"] =  arrayGet($requestParams, "is_tax",0,"intval");
        $data["customize_rate_rmb"] =  arrayGet($requestParams, "customize_rate_rmb",0);
        $data["customize_rate_usd"] =  arrayGet($requestParams, "customize_rate_usd",0);
        $data["customize_rate_rmb"] = round($data["customize_rate_rmb"],4);
        $data["customize_rate_usd"] = round($data["customize_rate_usd"],4);
        $data["supplier_code"] =  "";
        $data["supplier_id"] =  0;
        if($data["sup_type"] == StepPriceNewModel::SUPTYPE_ZHUANYING){
            $data["supplier_code"] =  $data["supplier_value"];
        }
        if($data["sup_type"] == StepPriceNewModel::SUPTYPE_DAIGOU){
            $data["supplier_id"] =  $data["supplier_value"];
        }
        if ($validator->fails()) {
            $errors = $validator->errors()->all();
            throw new InvalidRequestException($errors[0]??"未知错误，请联系技术");
        }
        if($data["currency"] != 1){
            $data["is_tax"] = 0;
        }
        CurrencyConfigService::addCurrencyConfig($data);
        return $this->setSuccess();
    }

    //获取供应商特殊币种配置列表
    public function getCurrencyConfigList(Request $request){
        $requestParams = $request->all();

        list($list,$count) = CurrencyConfigService::getCurrencyConfigList($requestParams);
        return $this->setSuccessData(["list"=>$list,"total"=>$count]);
    }
    //启用禁用供应商特殊币种配置
    public function disableCurrencyConfig(Request $request){
        $requestParams = $request->all();
        CurrencyConfigService::disableCurrencyConfig($requestParams);
        return $this->setSuccess();
    }

    //更新美金转人民币状态
    public function updateUsToCnStatus(Request $request){
        $requestParams = $request->all();
        CurrencyConfigService::updateUsToCnStatus($requestParams);
        return $this->setSuccess();
    }



}
