<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Transformers\UserCouponTransformer;
use App\Http\Services\UserCouponService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

//用户优惠券
class UserCouponController extends Controller
{
    //价格活动列表
    public function getUserCouponList(Request $request)
    {
        $map = $request->all();
        $result = (new UserCouponService())->getUserCouponList($map);
        if (!empty($result['data'])) {
            $transformer = new UserCouponTransformer();
            $result['data'] = $transformer->listTransform($result['data']);
            return $this->setSuccessData($result['data'], $result['total']);
        }
        return $this->setSuccessData([]);
    }

    public function exportUserCouponList(Request $request)
    {
        $params = $request->input('params');
        $params = json_decode($params, true);
        $params['is_export'] = 1;
        $list = (new UserCouponService())->getUserCouponList($params);
        $csvData = [];
        $transformer = new UserCouponTransformer();
        if (empty($list['data'])) {
            return '导出的数据为空';
        }
        $list['data'] = $transformer->listTransform($list['data']);
        foreach ($list['data'] as $item) {
            $item = [
                $item['user_coupon_id'],
                $item['coupon_name'],
                $item['coupon_desc'],
                $item['user_account']. "\t",
                $item['adtag'],
                $item['coupon_sn'],
                $item['coupon_type_name'],
                $item['status_name'],
                $item['order_sn'],
                $item['create_time'] . "\t",
                $item['end_time'] . "\t",
                $item['use_time'] . "\t",
            ];
            $csvData[] = $item;
        }
        $header = [
            '优惠券领取ID',
            '优惠券名称',
            '优惠券描述',
            '客户账号',
            '渠道来源',
            '优惠券批次',
            '优惠券类型',
            '使用状态',
            '订单编号',
            '领取时间',
            '到期时间',
            '使用时间',
        ];
        exportCsv($csvData, '导出优惠券用户领取列表', $header);
        exit;
    }
}
