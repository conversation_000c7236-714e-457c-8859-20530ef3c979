<?php

namespace App\Http\Controllers\Api;
use App\Exceptions\InvalidRequestException;
use App\Http\Models\Cube\ChannelDiscountModel;
use App\Http\Models\Cube\StepPriceNewModel;
use App\Http\Services\ChannelDiscountService;
use App\Http\Services\GoodsPriceSystemService;
use App\Http\Services\GoodsSalePriceGroupService;
use App\Http\Services\PriceWarningService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PriceWarningController extends  BaseApiController
{

    /**
     * Notes:价格预警记录列表
     * User: sl
     * Date: 2023-03-04 11:10
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request){
        $requestParams = $request->all();
        $data["price_activity_id"] =  arrayGet($requestParams, "price_activity_id", "", "trim");
        $data["activity_name"] =  arrayGet($requestParams, "activity_name", "", "trim");
        $data["sppe_sn"] =  arrayGet($requestParams, "sppe_sn", "", "trim");
        $data["status"] =  arrayGet($requestParams, "status","");
        $data["warn_time"] =  arrayGet($requestParams, "warn_time","");
        list($list,$count) = PriceWarningService::getPriceWarningList($data);
        return $this->setSuccessData(["list"=>$list,"total"=>$count]);
    }

    public function dealwith(Request $request){
        $requestParams = $request->all();
        $priceWarningIds =  arrayGet($requestParams, "price_warning_id", "", "trim");
        $data["status"] =  arrayGet($requestParams, "status", "", "trim");
        $data["remark"] =  arrayGet($requestParams, "remark", "", "trim");
        $priceWarningIds = explode(",",$priceWarningIds);
        $priceWarningIds = array_filter_unique($priceWarningIds);
        foreach($priceWarningIds as $priceWarningId){
            $data["price_warning_id"] = $priceWarningId;
            PriceWarningService::dealWithPriceWarning($data);
        }

        return $this->setSuccess();
    }

    public function recertify(Request $request){
        $requestParams = $request->all();
        $priceWarningId =  arrayGet($requestParams, "price_warning_id", 0, "intval");
        PriceWarningService::recertifyPriceWarning($priceWarningId);
        return $this->setSuccess();
    }



}