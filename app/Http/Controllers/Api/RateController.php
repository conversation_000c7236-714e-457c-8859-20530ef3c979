<?php

namespace App\Http\Controllers\Api;


use App\Http\Caches\RateCache;
use App\Http\Services\ThirdErpService;
use Illuminate\Http\Request;
use \Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Http;

class RateController extends BaseApiController
{



    public function getRateList(Request $request)
    {
        $search_currency_name = $request->input("currency_name");
        $RateCache = new RateCache();
        $rate_info = $RateCache->getRateInfo();
        $currency_map = Config('namemap.currency');
        $rate_list = [];

        foreach ($currency_map as $currency_name) {
            // 如果是查询指定币种，那么非币种的就跳过
            if ($search_currency_name && ($currency_name != $search_currency_name)) {
                continue;
            }
            $rate_list[] = [
                "currency_name" => $currency_name,
                "target_currency_name" => "人民币",
                "rate" => isset($rate_info[$currency_name]) ? $rate_info[$currency_name] : '',
                "update_time" => isset($rate_info["update_time"]) ? $rate_info["update_time"] : ''
            ];
        }

        $data = [
            "list" => $rate_list,
            "total" => count($rate_list)
        ];
        return $this->setSuccessData($data);
    }

    /*
 * 缓存erp汇率存到redis
 */
    public function getErpRate(Request $request)
    {
        $response = Http::asJson()->get(trim(config("website.order_domain"),"/")."/open/erp_rate");
        $res = $response->body();

        //这是定时任务系统的同步汇率
        $url = 'http://crons.ichunt.net/ajax/quickExecTask';
        $data = [
            'id' => '62a94bc6be29d122655f4782',
            'job_name' => '基石同步ERP美金汇率',
            'job_list[172.18.137.41][job_etcd_name]' => '83a44badeda8b393d0cceddf436d9206',
            'job_list[172.18.137.41][etcd_key]' => '/cron/jobs/172.18.137.41/基石同步ERP美金汇率',
            'command' => '/data2/ichunt-cron/crons/footstone_sync_rate.sh',
        ];
        $userId = $request->cookie('oa_user_id') ?: $request->header('oa_user_id');
        $skey = $request->cookie('oa_skey') ?: $request->header('oa_skey');
        $cookie = 'oa_user_id=' . $userId . '; oa_skey=' . $skey;
        $result = curlWithCookie($url, $data, true, 1, $cookie);
        $result = json_decode($result, true);

        return $this->setSuccessData($res);
    }


}
