<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\Cube\LotteryModel;
use App\Http\Models\Cube\PrizeConsigneeInfoModel;
use App\Http\Models\Cube\PrizeLogModel;
use App\Http\Models\Cube\PrizeModel;
use App\Http\Models\Cube\PrizeWinnerModel;
use App\Http\Models\Liexin\UserMainModel;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Services\UserLotteryService;
use App\Http\Transformers\LotteryTransformer;
use App\Http\Services\LotteryService;
use App\Http\Transformers\UserLotteryTransformer;
use App\Http\Validators\LotteryValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class UserLotteryController extends Controller
{
    //抽奖列表
    public function getUserLotteryList(Request $request)
    {
        $map = $request->all();
        $result = (new UserLotteryService())->getUserLotteryList($map);
        if (!empty($result['data'])) {
            $transformer = new UserLotteryTransformer();
            $result['data'] = $transformer->listTransform($result['data']);
            return $this->setSuccessData($result['data'], $result['total']);
        }
        return $this->setSuccessData([]);
    }

    //保存抽奖
    public function addUserLottery(Request $request)
    {
        $data = $request->input();
        //数据校验
        if (empty($data['user_info'])) {
            return $this->setError('请输入要发奖的目标用户');
        }
        if (empty($data['lottery_id'])) {
            return $this->setError('请选择抽奖活动');
        }
        if (empty($data['prize_id'])) {
            return $this->setError('请选择奖品');
        }

        $userId = (new UserMainModel())->getUserIdWithMobileOrEmail($data['user_info']);
        if (empty($userId)) {
            return $this->setError('没有查到此用户');
        }

        //奖品数量检测
        $redis = Redis::connection('user');
        $prizes = $redis->hget('lie_prize', $data['lottery_id']);
        $prizes = json_decode($prizes, true);
        foreach ($prizes as $v) {
            if ($v['prize_id'] == $data['prize_id'] && $v['prize_num'] <= 0) {
                return $this->setError('该奖品剩余数量为0');
            }
        }

        //中奖纪录
        $winnerModel['lottery_id'] = $data['lottery_id'];
        $winnerModel['lottery_name'] = $data['lottery_name'];
        $winnerModel['user_id'] = $userId;
        $winnerModel['prize_id'] = $data['prize_id'];
        $winnerModel['prize_get_time'] = time();
        $winnerModel['prize_name'] = $data['prize_name'];
        $winnerModel['platform'] = $data['platform'];
        $winnerModel['test_type'] = 2;//1否 2是
        $winnerModel['draw_type'] = $data['draw_type'];
        $winnerModel['create_time'] = time();
        $winnerModel['update_time'] = time();
        $winnerModel['operator_id'] = $request->user->userId;
        $winnerModel['is_sent'] = $data['is_sent'];
        $prizeType = PrizeModel::select('prize_type')->where('prize_id',
            $data['prize_id'])->where('lottery_id', $data['lottery_id'])->first();
        $winnerModel['prize_type'] = $prizeType->prize_type;

        //抽奖日志
        $drawLogModel['lottery_id'] = $data['lottery_id'];
        $drawLogModel['user_id'] = $userId;
        $drawLogModel['prize_id'] = $data['prize_id'];
        $drawLogModel['prize_get_time'] = time();
        $drawLogModel['draw_type'] = $data['draw_type'];
        $drawLogModel['platform'] = $data['platform'];
        $drawLogModel['is_win'] = ($prizeType->prize_type == 4) ? 2 : 1;
        $drawLogModel['create_time'] = time();

        //事务
        DB::connection('web')->transaction(function () use ($winnerModel, $data, $drawLogModel, $redis) {
            //如果未中奖则不写到中奖表里
            if ($winnerModel['prize_type'] != 4) {
                $userPrizeId = PrizeWinnerModel::insertGetId($winnerModel);
                if (!$userPrizeId) {
                    return $this->setError('添加中奖纪录失败');
                } else {
                    $winnerModel['user_prize_id'] = $userPrizeId;
                }
                //Redis添加中奖名单
                $rdGet = $redis->hget('lie_prize_winner', $data['lottery_id']);
                $rdGet = json_decode($rdGet, true);
                $rdGet[] = $winnerModel;
                $redis->hset('lie_prize_winner', $data['lottery_id'], json_encode($rdGet));

                //减奖品数
                $result1 = PrizeModel::where('prize_id', $data['prize_id'])->decrement('prize_num');
                if (!$result1) {
                    return $this->setError('奖品数量减1失败');
                } else {
                    //Redis减奖品数
                    $rdGet = $redis->hget('lie_prize', $data['lottery_id']);
                    $rdGet = json_decode($rdGet, true);
                    foreach ($rdGet as $k => &$v) {
                        if ($v['prize_id'] == $data['prize_id']) {
                            $v['prize_num'] = --$v['prize_num'];
                            break;
                        }
                    }
                    unset($v);
                    $redis->hset('lie_prize', $data['lottery_id'], json_encode($rdGet));
                }
            }
            //写中奖日志
            $result3 = PrizeLogModel::insert($drawLogModel);
            if (!$result3) {
                return $this->setError('写抽奖日志失败');
            }
        });

        return $this->setSuccess('添加中奖成功');
    }


    //发放抽奖
    public function issueUserLottery(Request $request)
    {
        $data = $request->input('data');
        $info = json_decode($data, true);

        //用户信息模型
        $csnModel['user_id'] = $info['user_id'];
        $csnModel['consignee'] = !empty($info['consignee']) ? $info['consignee'] : '';
        $csnModel['mobile'] = !empty($info['mobile']) ? $info['mobile'] : '';
        $csnModel['province'] = !empty($info['province']) ? $info['province'] : 0;
        $csnModel['city'] = !empty($info['city']) ? $info['city'] : 0;
        $csnModel['district'] = !empty($info['district']) ? $info['district'] : 0;
        $csnModel['detail_address'] = !empty($info['detail_address']) ? $info['detail_address'] : '';

        //日志模型
        $drawLogModel['lottery_id'] = $info['lottery_id'];
        $drawLogModel['draw_type'] = $info['draw_type'];
        $drawLogModel['platform'] = $info['platform'];
        $drawLogModel['prize_id'] = $info['prize_id'];
        $drawLogModel['user_id'] = $info['user_id'];
        $drawLogModel['prize_get_time'] = time();
        $drawLogModel['is_win'] = 1;

        $exist = PrizeConsigneeInfoModel::where('user_id', $info['user_id'])->first();
        //事务
        DB::connection('web')->transaction(function () use ($csnModel, $info, $drawLogModel, $exist) {
            //用户地址信息变更
            if (empty($exist)) {
                //添加用户地址信息
                $csnModel['create_time'] = time();
                $result = PrizeConsigneeInfoModel::insert($csnModel);
                if (!$result) {
                    return $this->setError('发奖失败');
                }
            } else {
                //更新用户地址信息
                $csnModel['update_time'] = time();
                $result = PrizeConsigneeInfoModel::where('user_id',
                    $info['user_id'])->update($csnModel);
                if (!$result) {
                    return $this->setError('发奖失败');
                }
            }
            //更改发奖状态
            $result = PrizeWinnerModel::where('user_prize_id',
                $info['user_prize_id'])->update([
                'is_sent' => 1,
                'awb_no' => !empty($info['awb_no']) ? $info['awb_no'] : ''
            ]);
            if (!$result && 0 !== $result) {
                return $this->setError('发奖失败');
            }
        });
        //redis更改发奖状态
        $redis = Redis::connection('user');
        $rdGet = $redis->hget('lie_prize_winner', $info['lottery_id']);
        $rdGet = json_decode($rdGet, true);
        if ($rdGet) {
            foreach ($rdGet as &$v) {
                if ($v['user_prize_id'] == $info['user_prize_id']) {
                    $v['is_sent'] = 1;
                    break;
                }
            }
            unset($v);
            $redis->hset('lie_prize_winner', $info['lottery_id'], json_encode($rdGet));
        }
        return $this->setSuccess('发奖成功');
    }

    //平台发放现金券
    public function issuePrizeOther(Request $request)
    {
        $info = $request->input();
        //更改发奖状态
        $result = PrizeWinnerModel::where('user_prize_id',
            $info['user_prize_id'])->update(['is_sent' => 1]);
        if (!$result) {
            return $this->setError('此现金券已发放过了');
        }
        //更新Redis数据
        $redis = Redis::connection('user');
        $rdGet = $redis->hget('lie_prize_winner', $info['lottery_id']);
        $rdGet = json_decode($rdGet, true);
        foreach ($rdGet as &$v) {
            if ($v['user_prize_id'] == $info['user_prize_id']) {
                $v['is_sent'] = 1;
                break;
            }
        }
        unset($v);
        $redis->hset('lie_prize_winner', $info['lottery_id'], json_encode($rdGet));
        return $this->setSuccess('发现金券成功');
    }
}
