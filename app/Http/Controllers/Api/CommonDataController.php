<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\Self\SelfClassifyModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\BrandService;
use App\Http\Services\DataService;
use App\Http\Services\StandardBrandService;
use App\Http\Services\SupplierService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;

//通用API,比如获取品牌列表,分类列表等
class CommonDataController extends Controller
{
    //获取供应商列表,比如arrow,digikey,bisco等
    public function getSupplierList(Request $request)
    {
        $service = new SupplierService();
        $suppliers = $service->getSuppliersData();
        return $this->setSuccessData($suppliers);
    }

    //获取自营分类
    public function getSelfClassList(Request $request)
    {
        $model = new SelfClassifyModel();
        $result = $model->getClassList();
        return $this->setSuccessData($result);
    }

    //获取供应商编码列表
    public function getCanalList(Request $request)
    {
        $redis = Redis::connection('sku');
        $model = new SupplierChannelModel();
        $data = [];
        $map = $request->all();
        $query = SupplierChannelModel::where('is_type', SupplierChannelModel::TYPE_official)
            ->whereIn('status', [0, -1, 1, 2]);
        if (!empty($map['supplier_name'])) {
            $query->where('supplier_name', 'like', "%{$map['supplier_name']}%")
            ->orWhere('supplier_code', 'like', "%{$map['supplier_name']}%");
        }
        $codeList = $query->select(['supplier_name', 'supplier_code'])->paginate(30)->toArray();
        foreach ($codeList['data'] as $key => $code) {
            $data[] = [
                'supplier_code' => $code['supplier_code'],
                'supplier_name' => $code['supplier_name'] . '(' . $code['supplier_code'] . ')',
            ];
        }
        $lastPage = \Arr::get($codeList, 'last_page');
        $total = \Arr::get($codeList, 'total');
        echo json_encode([
            'err_code' => 0,
            'err_msg' => 'ok',
            'total' => $total,
            'count' => $total,
            'data' => $data,
            'last_page' => $lastPage
        ]);
        exit();
    }

    //批量校验普通品牌,并且返回具体的id和错误信息
    public function checkBrandNameList(Request $request)
    {
        //接收的是标准品牌名称
        $brandNameList = $request->input('brand_name_list');
        $brandType = $request->input('brand_type');
        if (empty($brandNameList)) {
            return $this->setSuccessData([
                'invalid_brand_name_list' => [],
                'valid_brand_name_list' => [],
                'valid_brand_ids' => [],
            ]);
        }
        $brandNameList = explode(',', trim($brandNameList, ','));
        $result = (new BrandService())->checkBrandNameList($brandNameList, $brandType);
        return $this->setSuccessData($result);
    }

    //批量校验标准品牌,并且返回具体的id和错误信息
    public function checkStandardBrandNameList(Request $request)
    {
        //接收的是标准品牌名称
        $standardBrandNameList = $request->input('standard_brand_name_list');
        if (empty($standardBrandNameList)) {
            return $this->setSuccessData([
                'invalid_brand_name_list' => [],
                'valid_brand_name_list' => [],
                'valid_brand_ids' => [],
            ]);
        }
        $standardBrandNameList = explode(',', trim($standardBrandNameList, ','));
        $result = (new StandardBrandService())->checkStandardBrandNameList($standardBrandNameList);
        return $this->setSuccessData($result);
    }

    //处理数据接口
    public function handleData(Request $request)
    {
        DataService::updatePriceActivityCurrencyType();
    }

    //搜索标准品牌
    public function searchStandardBrand(Request $request)
    {
        $brandName = $request->input('brand_name');
        $result = (new StandardBrandService())->searchStandardBrandByOldBrand($brandName);
//        $result = (new StandardBrandService())->searchStandardBrand($brandName);
        if(empty($result["data"])){
            $result = (new StandardBrandService())->searchStandardBrandByOldBrand($brandName);
        }
//dump($result);
        $oneBrand = (new StandardBrandService())->searchStandardBrandEq($brandName);
        $page = $request->input('page',"");
        if(!empty($result["data"]) && $oneBrand && is_array($result["data"]) && intval($page) == 1){
            $isExists = false;
            foreach ($result["data"] as $item){
                if($item->brand_id == $oneBrand->brand_id){
                    $isExists = true;
                }
            }
            if(!$isExists){
                array_unshift($result["data"],$oneBrand);
            }

        }

        echo json_encode($result);
        exit();
    }

    public function uploadFile(Request $request)
    {
        $file = $request->file('file');
    }
}
