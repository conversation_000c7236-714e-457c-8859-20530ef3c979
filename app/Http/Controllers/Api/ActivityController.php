<?php

namespace App\Http\Controllers\Api;

use App\Enum\CompanyEnum;
use App\Http\Controllers\Controller;
use App\Http\Models\ActionLogModel;
use App\Http\Models\Cube\ActivityModel;
use App\Http\Services\ActionLogService;
use App\Http\Services\ActivityService;
use App\Http\Services\CmsUserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;


/**
 *
 * @property int    $id
 * @property string $activity_no 活动编号
 * @property string $activity_name 活动名称
 * @property int    $activity_type 活动分类(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动)
 * @property string $activity_url 活动地址
 * @property int    $activity_start_time 活动开始时间
 * @property int    $activity_end_time 活动结束时间
 * @property int    $web_type 网页类型(1-网页,2-h5,3-全部都有)
 * @property int    $activity_status 活动状态(1待配置,2已配置未上线,3已上线,-1已过期)
 * @property int    $create_time 创建时间
 * @property int    $create_user_id 创建人id
 * @property string $create_user_name 创建人名称
 * @property int    $activity_enable 活动启用状态(1启用,-1未启用)
 * @property string $web_title 网页标题
 * @property string $web_keywords 网页关键词
 * @property string $web_description 网页描述
 * @property string $web_html web网页html
 * @property string $web_html_config web网页html配置项
 * @property string $h5_html h5网页html
 * @property string $h5_html_config
 * @property int    $update_time 更新时间
 *
 */
class ActivityController extends Controller
{
    public function list(Request $request)
    {
        //活动编号,活动地址,活动分类,活动状态,活动时间(1个字段),创建时间(1个字段)
        $params = [
            'page'            => $request->input("page", 1),//页数
            'limit'           => $request->input("limit", 10),//每页数量
            'activity_no'     => $request->input("activity_no"),//活动编号
            'activity_url'    => $request->input("activity_url"),//活动地址
            'activity_type'   => $request->input("activity_type"),//活动分类(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动)
            'activity_status' => $request->input("activity_status"),//活动状态(1待配置,2已配置未上线,3已上线,-1已过期)
            'activity_enable' => $request->input("activity_enable"),//活动启用状态(1启用,-1未启用)
            'org_id'          => $request->input("org_id"),//组织id
            'activity_time'   => $request->input("activity_time"),//活动时间
            'create_time'     => $request->input("create_time"),//创建时间
        ];
        $validator = Validator::make($params, [
            'page'  => 'required|numeric',
            'limit' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityService::getList($params);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function add(Request $request)
    {
        //活动名称,网页标题,活动地址,活动分类,活动开始时间,活动结束时间,网页类型(1-网页,2-h5,3-全部都有),网页关键词,网页描述
        $params = [
            'org_id'                  => $request->input("org_id", CompanyEnum::LiexXinCompanyId),
            'activity_name'           => $request->input("activity_name"),
            //活动名称
            'activity_url'            => $request->input("activity_url"),
            //活动地址
            'activity_type'           => $request->input("activity_type"),
            //活动分类(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动)
            'activity_time'           => $request->input("activity_time"),
            //活动开始,结束时间
            'web_type'                => $request->input("web_type"),
            //网页类型(1-网页,2-h5,3-全部都有)
            'web_title'               => $request->input("web_title"),
            //网页标题
            'web_keywords'            => $request->input("web_keywords"),
            //网页关键词
            'web_description'         => $request->input("web_description"),
            //网页描述
            'is_activity_center_show' => $request->input('is_activity_center_show', -1),
            //是否展示在活动中心
            'image_pc'                => $request->input('image_pc'),
            //pc图片
            'image_h5'                => $request->input('image_h5'),
            //h5图片
        ];
        $validator = Validator::make($params, [
            'activity_name'   => 'required|string',
            'activity_url'    => 'required|string',
            'activity_type'   => 'required|numeric',
            'activity_time'   => 'required|string',
            'web_type'        => 'required|numeric',
            'web_title'       => 'required|string',
            'web_keywords'    => 'string',
            'web_description' => 'string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityService::add($params);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function update(Request $request)
    {
        //活动编号,活动名称,网页标题,活动地址,活动分类,活动开始时间,活动结束时间,网页类型(1-网页,2-h5,3-全部都有),网页关键词,网页描述
        $params = [
            'id'                      => $request->input("id"),
            //活动编号
            'activity_name'           => $request->input("activity_name"),
            //活动地址
            'activity_url'            => $request->input("activity_url"),
            //活动名称
            'activity_type'           => $request->input("activity_type"),
            //活动分类(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动)
            'activity_time'           => $request->input("activity_time"),
            //活动结束时间
            'web_type'                => $request->input("web_type"),
            //网页类型(1-网页,2-h5,3-全部都有)
            'web_title'               => $request->input("web_title"),
            //网页标题
            'web_keywords'            => $request->input("web_keywords"),
            //网页关键词
            'web_description'         => $request->input("web_description"),
            //网页描述
            'is_activity_center_show' => $request->input('is_activity_center_show', -1),
            //是否展示在活动中心
            'image_pc'                => $request->input('image_pc'),
            //pc图片
            'image_h5'                => $request->input('image_h5'),
            //h5图片
        ];
        $validator = Validator::make($params, [
            'id'              => 'required|string',
            'activity_name'   => 'required|string',
            'activity_type'   => 'required|numeric',
            'activity_time'   => 'required|string',
            'web_type'        => 'required|numeric',
            'web_title'       => 'required|string',
            'web_keywords'    => 'string',
            'web_description' => 'string',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityService::update($params['id'], $params);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function delete(Request $request)
    {
        //活动id
        $params = [
            'id' => $request->input("id"),
        ];
        $validator = Validator::make($params, [
            'id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityService::delete($params);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function getInfo(Request $request)
    {
        //活动id
        $params = [
            'id' => $request->input("id"),
        ];
        $validator = Validator::make($params, [
            'id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityService::getInfo($params);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function copy(Request $request)
    {
        //活动id
        $params = [
            'id' => $request->input("id"),
        ];
        $validator = Validator::make($params, [
            'id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityService::copy($params);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function activityEnable(Request $request)
    {
        //活动id,活动状态(-1-关闭,1-开启)
        $params = [
            'id'              => $request->input("id"),
            'activity_enable' => $request->input("activity_enable"),
        ];
        $validator = Validator::make($params, [
            'id'              => 'required|numeric',
            'activity_enable' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
//        try {
        $data = ActivityService::activityEnable($params['id'], $params['activity_enable']);
//        } catch (\Throwable $throwable) {
//            return $this->setError($throwable->getMessage());
//        }
        return $this->setSuccessData($data);
    }

    public function updatePcData(Request $request)
    {
        //活动id,pc端数据
        $params = [
            'id'     => $request->input("id"),
            'html'   => $request->input("html"),
            'config' => $request->input("config"),
            'order'  => $request->input("order"),
        ];
        $validator = Validator::make($params, [
            'id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityService::updatePcData($params['id'], $params['html'], $params['config']);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }

    public function updateH5Data(Request $request)
    {
        //活动id,h5端数据
        $params = [
            'id'     => $request->input("id"),
            'html'   => $request->input("html"),
            'config' => $request->input("config"),
            'order'  => $request->input("order"),
        ];
        $validator = Validator::make($params, [
            'id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        try {
            $data = ActivityService::updateH5Data($params['id'], $params['html'], $params['config']);
        } catch (\Throwable $throwable) {
            return $this->setError($throwable->getMessage());
        }
        return $this->setSuccessData($data);
    }


}
