<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Services\ShippingRuleService;
use App\Http\Models\Cube\OperationLogModel;
use App\Http\Models\Cube\ShippingRuleModel;
use App\Http\Validators\ShippingRuleValidator;
use App\Http\Models\Supplier\SupplierChannelModel;

class ShippingRuleController extends Controller
{
    /**
     * 获取运费规则列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getShippingRuleList(Request $request)
    {
        $map = $request->all();
        $result = (new ShippingRuleService())->getShippingRuleList($map);

        // 处理返回结果，将JSON规则展开为字段
        if (!empty($result['data'])) {
            foreach ($result['data'] as &$item) {
                if (!empty($item['rule']) && is_array($item['rule'])) {
                    foreach ($item['rule'] as $key => $value) {
                        $item[$key] = $value;
                    }
                }
            }
        }

        return $this->setSuccessData($result['data'], $result['total']);
    }

    /**
     * 保存运费规则
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveShippingRule(Request $request)
    {
        $data = $request->all();
        //layui的switch要开启才会传值过来
        $data['is_change_rate_cny'] = $data['is_change_rate_cny'] ?? -1;
        $data['is_change_rate_usd'] = $data['is_change_rate_usd'] ?? -1;
        $data['ladder_is_change_rate_cny'] = $data['ladder_is_change_rate_cny'] ?? -1;
        $data['ladder_is_change_rate_usd'] = $data['ladder_is_change_rate_usd'] ?? -1;
        $data['count_is_change_rate_cny'] = $data['count_is_change_rate_cny'] ?? -1;
        $data['count_is_change_rate_usd'] = $data['count_is_change_rate_usd'] ?? -1;
        $data['weight_is_change_rate_cny'] = $data['weight_is_change_rate_cny'] ?? -1;
        $data['weight_is_change_rate_usd'] = $data['weight_is_change_rate_usd'] ?? -1;
        $validator = (new ShippingRuleValidator())->checkSave($data);
        if ($validator !== true) {
            return $this->setError($validator);
        }
        $result = (new ShippingRuleService())->saveShippingRule($data);
        return $this->setSuccess('保存成功');
    }

    /**
     * 删除运费规则
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteShippingRule(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError('参数错误');
        }

        try {
            $result = (new ShippingRuleService())->deleteShippingRule($id);
            return $this->setSuccess('删除成功');
        } catch (\Exception $e) {
            return $this->setError($e->getMessage());
        }
    }

    /**
     * 修改运费规则状态
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeStatus(Request $request)
    {
        $id = $request->input('id');
        $status = $request->input('status');
        if (empty($id) || empty($status)) {
            return $this->setError('参数错误');
        }

        try {
            $result = (new ShippingRuleService())->changeStatus($id, $status);
            return $this->setSuccess('操作成功');
        } catch (\Exception $e) {
            return $this->setError($e->getMessage());
        }
    }

    /**
     * 获取操作日志
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOperationLogs(Request $request)
    {
        $ruleId = $request->input('id');

        $logs = OperationLogModel::where('rule_id', $ruleId)
            ->where('type', OperationLogModel::TYPE_SHIPPING_RULE)
            ->orderBy('operation_time', 'desc')
            ->with('operator')
            ->get()
            ->toArray();

        foreach ($logs as &$log) {
            $log['operation_time'] = date('Y-m-d H:i:s', $log['operation_time']);
            $log['operator_name'] = $log['operator']['name'] ?? '';
        }

        return $this->setSuccessData($logs);
    }
}
