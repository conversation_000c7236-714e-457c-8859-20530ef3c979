<?php

namespace App\Http\Controllers\Api;

use App\Exports\PriceActivityStatisticsExport;
use App\Http\Controllers\Controller;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Transformers\PriceActivityTransformer;
use App\Http\Services\PriceActivityService;
use App\Http\Validators\PriceActivityValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Excel;
use Illuminate\Support\Facades\Redis;

class PriceActivityController extends Controller
{
    //价格活动列表
    public function getPriceActivityList(Request $request)
    {
        $map = $request->all();
        $result = (new PriceActivityService())->getActivityList($map);
        if (!empty($result['data'])) {
            $transformer = new PriceActivityTransformer();
            $result['data'] = $transformer->listTransform($result['data']);
            return $this->setSuccessData($result['data'], $result['total']);
        }
        return $this->setSuccessData([]);
    }

    //保存活动
    public function savePriceActivity(Request $request)
    {
        $validator = new PriceActivityValidator();
        //先去表单验证
        $validateResult = $validator->checkSave($request);
        if ($validateResult) {
            return $this->setError($validateResult);
        }
        //得到精简转化后的请求数据
        $priceActivityService = new PriceActivityService();
        $requestData = (new PriceActivityService())->transformRequestData($request);
        $model = new PriceActivityModel();
        $activityId = $request->get('activity_id');
        $originalSkuFileUrl = '';
        if ($activityId) {
            $originalSkuFileUrl = PriceActivityModel::where('id', $activityId)->value('sku_file_url');
        }
        if (!empty($activityId)) {
            //还要去判断商品是否跑完映射es的程序
            $redis = Redis::connection('sku');
            if ($redis->hget('activity_to_es', $activityId)) {
//                return $this->setError("该活动的商品映射还在执行,请等待完成后再提交");
            }
        }
        if ($activityId) {
            $requestData['update_time'] = time();
            $result = $model->where('id', $activityId)->update($requestData);
            $priceActivityService->saveActivityToRedis($activityId, $requestData, 'price');
        } else {
            unset($requestData['activity_id']);
            $activityId = $model->insertGetId($requestData);
            $result = $activityId;
            $priceActivityService->saveActivityToRedis($activityId, $requestData, 'price');
        }
        //插入到数据库
        if ($request->input('upload_change')) {
            $priceActivityService->dealWithSkuNameCsv($requestData, $activityId);
        }
        if ($result) {
            return $this->setSuccess("保存活动成功");
        } else {
            return $this->setError("保存活动失败");
        }

    }

    //发布活动
    public function publishPriceActivity(Request $request)
    {
        $id = $request->get('activity_id');
        $model = new PriceActivityModel();
        $result = $model->where('id', $id)->update(['status' => 1, 'update_time' => time()]);
        //然后去redis修改状态
        if ($result) {
            $redis = Redis::connection('sku');
            //遍历所有供应商数据,删除里面对应的活动
            $supplierActivities = $redis->hgetall('lie_price_activity');
            foreach ($supplierActivities as $supplierId => $supplierActivity) {
                $supplierActivity = json_decode($supplierActivity, true);
                foreach ($supplierActivity as $k => $activity) {
                    if ($activity['activity_id'] == $id) {
                        $supplierActivity[$k]['status'] = 1;
                    }
                }
                $supplierActivity = array_values($supplierActivity);
                $redis->hset('lie_price_activity', $supplierId, json_encode($supplierActivity));
            }
            return $this->setSuccess("发布活动成功");
        }
        return $this->setError("发布活动失败");
    }

    //导出统计
    public function exportPriceActivityStatistics(Request $request)
    {
        $activityId = $request->get('activity_id');
        if (empty($activityId)) {
            return "导出活动的id不能为空";
        }
        return Excel::download(new PriceActivityStatisticsExport($activityId), '促销活动统计_' . $activityId . '.xlsx');
    }

    public function deletePriceActivity(Request $request)
    {
        $id = $request->get('activity_id');
        //先去数据库删除,然后去redis删除
        $model = new PriceActivityModel();
        $res = $model->where('id', $id)->update(['status' => -1, 'update_time' => time()]);
        if ($res) {
            $redis = Redis::connection('sku');
            //遍历所有供应商数据,删除里面对应的活动
            $supplierActivities = $redis->hgetall('lie_price_activity');
            foreach ($supplierActivities as $supplierId => $supplierActivity) {
                $supplierActivity = json_decode($supplierActivity, true);
                foreach ($supplierActivity as $k => $activity) {
                    if ($activity['activity_id'] == $id) {
                        unset($supplierActivity[$k]);
                    }
                }
                $supplierActivity = array_values($supplierActivity);
                $redis->hset('lie_price_activity', $supplierId, json_encode($supplierActivity));
            }
            return $this->setSuccess('删除成功');
        }
        return $this->setError('删除失败');
    }
}
