<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Models\ActionLogModel;
use App\Http\Services\ActionLogService;
use App\Http\Services\CmsUserService;
use App\Http\Services\UserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AddressController extends Controller
{
    public function add(Request $request)
    {

        $params = [
            'province'   => $request->input("province"),//省
            'city'       => $request->input("city"),//市
            'area'       => $request->input("area"),//地区
            'address'    => $request->input("address"),//详细地址
            'username'   => $request->input("username"),//收货人
            'phone'      => $request->input("phone"),//联系电话
            'is_default' => $request->input("is_default"),//是否为默认地址
        ];

        $validator = Validator::make($params, [
            'province'   => 'required',
            'city'       => 'required',
            'area'       => 'required',
            'address'    => 'required',
            'username'   => 'required',
            'phone'      => 'required|numeric',
            'is_default' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        $data = UserService::addAddress($params);
        return $this->setSuccessData($data);
    }

    public function setDefault(Request $request)
    {
        $addressId = $request->input("address_id");
        $validator = Validator::make([
            'address_id' => $addressId,
        ], [
            'address_id' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }
        $data = UserService::setDefault($addressId);
    }

    public function update(Request $request)
    {

        $params = [
            'address_id' => $request->input("address_id"),//省
            'province'   => $request->input("province"),//省
            'city'       => $request->input("city"),//市
            'area'       => $request->input("area"),//地区
            'address'    => $request->input("address"),//详细地址
            'username'   => $request->input("username"),//收货人
            'phone'      => $request->input("phone"),//联系电话
            'is_default' => $request->input("is_default"),//是否为默认地址
        ];

        $validator = Validator::make($params, [
            'address_id'   => 'required',
            'province'   => 'required',
            'city'       => 'required',
            'area'       => 'required',
            'address'    => 'required',
            'username'   => 'required',
            'phone'      => 'required|numeric',
            'is_default' => 'required|numeric',
        ]);
        if ($validator->fails()) {
            $error = $validator->errors()->first();
            return $this->setError($error);
        }

    }

    public function delete(Request $request){
        $addressId = $request->input("address_id");
        $validator = Validator::make([
            'address_id' => $addressId,
        ], [
            'address_id' => 'required|numeric',
        ]);


    }


}
