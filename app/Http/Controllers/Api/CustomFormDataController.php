<?php

namespace App\Http\Controllers\Api;

use App\Exports\CustomFormDataExport;
use App\Exports\CustomFormDataStatisticsExport;
use App\Http\Controllers\Controller;
use App\Http\Models\Cube\CustomFormDataModel;
use App\Http\Transformers\CustomFormDataTransformer;
use App\Http\Services\CustomFormDataService;
use App\Http\Validators\CustomFormDataValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Excel;
use Illuminate\Support\Facades\Redis;

class CustomFormDataController extends Controller
{
    //价格活动列表
    public function getCustomFormDataList(Request $request)
    {
        $map = $request->all();
        $result = (new CustomFormDataService())->getCustomFormDataList($map);
        if (!empty($result['data'])) {
            $transformer = new CustomFormDataTransformer();
            $result['data'] = $transformer->listTransform($result['data']);
            return $this->setSuccessData($result['data'], $result['total']);
        }
        return $this->setSuccessData([]);
    }

    //查看form_data的敏感信息
    public function getCustomFormData(Request $request)
    {
        $data = CustomFormDataModel::where('id', $request->id)->first()->toArray();
        return $this->setSuccessData($data);
    }

    //导出统计
    public function exportCustomFormData(Request $request)
    {
        $params = $request->get('params');
        return Excel::download(new CustomFormDataExport($params), '表单提交数据导出.xlsx');
    }
}
