<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Services\ActionLogService;
use App\Http\Services\CmsUserService;
use Illuminate\Http\Request;

class LogController extends Controller
{
    public function getLogList(Request $request)
    {
        $params = [
            'page'        => $request->input("page"),
            'limit'       => $request->input("limit"),
            'obj_id'      => $request->input("obj_id"),
            'operator_id' => $request->input("operator_id"),
            'create_time' => $request->input("create_time"),
        ];

        $list = ActionLogService::getListByObjType($params);

        return $this->setSuccessData($list['data'], $list['total']);
    }

    public function getUserLogList(Request $request){
        $params = [
            'page'        => $request->input("page"),
            'limit'       => $request->input("limit"),
            'obj_id'      => $request->input("obj_id"),
        ];
        $list = ActionLogService::getUserLogList($params);

        return $this->setSuccessData($list['data'], $list['total']);
    }

    public function getUserList(Request $request){
        $userList = CmsUserService::getUserList();
        return $this->setSuccessData($userList);
    }


}
