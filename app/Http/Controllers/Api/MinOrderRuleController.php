<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Services\MinOrderRuleService;
use App\Http\Models\Cube\MinOrderRuleModel;
use App\Http\Models\Cube\OperationLogModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class MinOrderRuleController extends Controller
{
    /**
     * 获取最小订单规则列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMinOrderRuleList(Request $request)
    {
        $map = $request->all();
        $result = (new MinOrderRuleService())->getMinOrderRuleList($map);

        if (!empty($result['data'])) {
            foreach ($result['data'] as &$item) {
                $item['status_name'] = $item['status'] == MinOrderRuleModel::STATUS_ENABLE ? '启用' : '禁用';
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['update_time'] = date('Y-m-d H:i:s', $item['update_time']);
                $item['supplier'] = SupplierModel::where('supplier_id', $item['supplier_id'])->value('supplier_name');
                $item['supplier_name'] = '';
                if (!empty($item['supplier_code'])) {
                    $item['supplier_name'] = SupplierChannelModel::where('supplier_code', $item['supplier_code'])->value('supplier_name');
                }
            }
            return $this->setSuccessData($result['data'], $result['total']);
        }
        return $this->setSuccessData([]);
    }

    /**
     * 保存最小订单规则
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveMinOrderRule(Request $request)
    {
        $data = $request->all();

        // 验证数据
        if (empty($data['supplier_id'])) {
            return $this->setError('请选择渠道');
        }

        if (!empty($data['supplier_id']) && $data['supplier_id'] == 17) {
            if (empty($data['supplier_code'])) {
                return $this->setError('渠道选择为专营的情况下,供应商不能为空');
            }
        }
        if ($data['usd_amount'] === '' && $data['cny_amount'] === '') {
            return $this->setError('请输入最小订单金额');
        }

        if (!empty($data['usd_amount']) && !is_numeric($data['usd_amount'])) {
            return $this->setError('美金最小订单金额必须为数字');
        }

        if (!empty($data['cny_amount']) && !is_numeric($data['cny_amount'])) {
            return $this->setError('人民币最小订单金额必须为数字');
        }

        $result = (new MinOrderRuleService())->saveMinOrderRule($data);
        return $this->setSuccess('保存成功');
    }

    /**
     * 删除最小订单规则
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteMinOrderRule(Request $request)
    {
        $ruleId = $request->input('rule_id');
        $result = MinOrderRuleModel::where('rule_id', $ruleId)
            ->delete();

        if (!$result) {
            return $this->setError('删除失败');
        }

        // 记录操作日志
        OperationLogModel::create([
            'rule_id' => $ruleId,
            'operator_id' => request()->user->userId,
            'operation_time' => time(),
            'content' => '删除订单规则'
        ]);

        return $this->setSuccess('删除成功');
    }

    /**
     * 启用/禁用最小订单规则
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function changeStatus(Request $request)
    {
        $ruleId = $request->input('rule_id');
        $status = $request->input('status');

        if (!in_array($status, [MinOrderRuleModel::STATUS_ENABLE, MinOrderRuleModel::STATUS_DISABLE])) {
            return $this->setError('状态参数错误');
        }

        $result = MinOrderRuleModel::where('rule_id', $ruleId)
            ->update([
                'status' => $status,
                'update_uid' => request()->user->userId,
                'update_name' => request()->user->name,
                'update_time' => time(),
            ]);

        if (!$result) {
            return $this->setError('状态变更失败');
        }

        // 记录操作日志
        $statusText = $status == MinOrderRuleModel::STATUS_ENABLE ? '启用' : '禁用';
        OperationLogModel::insert([
            'rule_id' => $ruleId,
            'operator_id' => request()->user->userId,
            'operation_time' => time(),
            'type' => OperationLogModel::TYPE_MIN_ORDER_RULE,
            'content' => $statusText . '订单规则',
            'create_uid' => request()->user->userId,
            'create_time' => time(),
            'update_uid' => request()->user->userId,
            'update_time' => time(),
        ]);

        return $this->setSuccess('状态变更成功');
    }

    /**
     * 获取操作日志
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getOperationLogs(Request $request)
    {
        $ruleId = $request->input('rule_id');

        $logs = OperationLogModel::where('rule_id', $ruleId)
            ->where('type', OperationLogModel::TYPE_MIN_ORDER_RULE)
            ->orderBy('operation_time', 'desc')
            ->with('operator')
            ->get()
            ->toArray();

        foreach ($logs as &$log) {
            $log['operation_time'] = date('Y-m-d H:i:s', $log['operation_time']);
            $log['operator_name'] = $log['operator']['name'] ?? '';
        }

        return $this->setSuccessData($logs);
    }
}
