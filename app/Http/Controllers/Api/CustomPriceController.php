<?php

namespace App\Http\Controllers\Api;

use Excel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Redis;
use App\Http\Services\CustomPriceService;
use App\Http\Models\Cube\CustomPriceModel;
use App\Http\Services\PriceActivityService;
use App\Http\Models\Cube\PriceActivityModel;
use App\Exports\PriceActivityStatisticsExport;
use App\Http\Validators\PriceActivityValidator;
use App\Http\Transformers\PriceActivityTransformer;
use App\Http\Transformers\CustomPriceTransformer;

class CustomPriceController extends Controller
{
    //自定义价格列表
    public function getCustomPriceList(Request $request)
    {
        $map = $request->all();
        $result = (new CustomPriceService())->getCustomPriceList($map);
        if (!empty($result['data'])) {
            $transformer = new CustomPriceTransformer();
            $result['data'] = $transformer->listTransform($result['data']);
            return $this->setSuccessData($result['data'], $result['total']);
        }
        return $this->setSuccessData([]);
    }

    //保存自定义价格阶梯
    public function saveCustomPrice(Request $request)
    {
        $data = $request->only(['id', 'org_id', 'price_list','audit_status','audit_content']);
        if (empty($data['org_id'])) {
            return $this->setError("组织不能为空");
        }
        if (empty($data['price_list'])) {
            return $this->setError("阶梯设置不能为空");
        }

        //price_list 阶梯价格系数不能为浮点数
        foreach ($data['price_list'] as $key => $value) {
            if (!isStrictInteger($value['ratio'])) {
                return $this->setError("阶梯价格系数不能为小数");
            }
        }
        $result = CustomPriceService::saveCustomPrice($data);
        if ($result) {
            return $this->setSuccess("保存配置成功");
        } else {
            return $this->setError("保存配置失败");
        }
    }

    //审核
    public function auditCustomPrice(Request $request)
    {
        $data = $request->only(['id', 'audit_status', 'audit_content']);
        if ($data['audit_status'] == CustomPriceModel::STATUS_AUDIT_FAIL) {
            if (empty($data['audit_content'])) {
                return $this->setError("审核失败原因不能为空");
            }
        }
        $result = CustomPriceService::auditCustomPrice($data['id'], $data['audit_status'], $data['audit_content']);
        if ($result) {
            return $this->setSuccess("审核成功");
        } else {
            return $this->setError("审核失败");
        }
    }

    //启用和禁用
    public function enableCustomPrice(Request $request)
    {
        $data = $request->only(['id', 'status']);
        if (!in_array($data['status'], [CustomPriceModel::STATUS_ENABLE, CustomPriceModel::STATUS_AUDIT_FAIL])) {
            return $this->setError("审核状态错误");
        }
        $result = CustomPriceService::enableCustomPrice($data['id'], $data['status']);
        if ($result) {
            return $this->setSuccess("操作成功");
        } else {
            return $this->setError("操作失败");
        }
    }
}
