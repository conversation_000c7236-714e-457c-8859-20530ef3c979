<?php

namespace App\Http\Controllers\Sync;

use App\Http\Controllers\Controller;
use App\Http\Services\ActionLogService;
use App\Http\Services\ActivityElementService;
use App\Http\Services\CmsUserService;
use App\Http\Services\GoodsPriceSystemService;
use App\Http\Services\GoodsSalePriceGroupService;
use App\Http\Services\PriceWarningService;
use App\Http\Services\UserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Redis;

class TestController extends Controller
{
    public function test()
    {

        GoodsPriceSystemService::getPriceGroupInfoForZhuanyin("L0015307",3);

    }
}
