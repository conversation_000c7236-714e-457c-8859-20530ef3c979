<?php

namespace App\Http\Controllers;

use Facade\FlareClient\Api;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use App\Http\ApiHelper\ApiCode;
use Illuminate\Support\Facades\Log;

class Controller extends BaseController implements ApiCode
{

    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public function user()
    {
        return request()->user;
    }

    public function setSuccessData($data = [], $count = 0, $code = ApiCode::API_CODE_SUCCESS, $msg = 'ok')
    {
        $res_data = [
            "code" => $code,
            "data" => $data,
        ];

        if ($msg) {
            $res_data['msg'] = $msg;
        }
        if ($count) {
            $res_data['count'] = $count;
        }
        return response()->json($res_data);
    }

    public function setSuccess($msg = '操作成功', $code = ApiCode::API_CODE_SUCCESS, $data = [])
    {
        $res_data = [
            "code" => $code,
            "msg" => $msg,
            'data' => (object)$data,
        ];
        return response()->json($res_data);
    }

    public function setError($msg, $code = ApiCode::API_CODE_ERROR, $data = [])
    {
        $res_data = [
            "code" => $code,
            "msg" => $msg,
        ];

        if ($data) {
            $res_data['data'] = $data;
        }
        $this->logErr($msg, $code = ApiCode::API_CODE_ERROR, $data = null);
        return response()->json($res_data);
    }

    private function logErr($msg, $code = ApiCode::API_CODE_ERROR, $data = null)
    {
        $request_uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        $path_info = parse_url($request_uri);
        $err_info = [
            'domain' => isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '',
            'interface' => isset($path_info) ? $path_info['path'] : '',
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'ip' => request()->getClientIp(),
            'time' => time(),
            'other' => '',
            'request_params' => $_REQUEST,
            'msg' => $msg,
            "code" => $code,
            "data" => $data
        ];
        Log::error(json_encode($err_info, JSON_UNESCAPED_UNICODE));
    }
}
