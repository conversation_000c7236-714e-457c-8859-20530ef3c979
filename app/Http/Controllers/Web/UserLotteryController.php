<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Http\Models\Cube\LotteryModel;
use App\Http\Models\Cube\PrizeConsigneeInfoModel;
use App\Http\Models\Cube\PrizeModel;
use App\Http\Models\Cube\PrizeWinnerModel;
use App\Http\Models\Liexin\RegionModel;
use App\Http\Models\Liexin\UserAddressModel;
use App\Http\Services\LotteryService;
use App\Http\Services\PriceActivityService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class UserLotteryController extends Controller
{

    //抽奖活动列表
    public function userLotteryList(Request $request)
    {
        $createrList = (new LotteryModel())->getCreaterList();
        return view('userLottery.userLotteryList', compact('createrList'));
    }

    public function addUserLottery(Request $request)
    {
        $activityInfo = LotteryModel::select(['lottery_id', 'lottery_name'])
            ->where('status', 1)
            ->where('end_time', '>=', time())
            ->orderBy('create_time', 'desc')->get();
        $userInfo = '';
        $chooseAct = '';
        $prizeInfo = [];
        if (!empty($request['lottery_id'])) {
            $prizeInfo = PrizeModel::select(['prize_id', 'prize_name'])
                ->where('lottery_id', $request['lottery_id'])->get();
            $userInfo = $request['user_info'];
            $chooseAct = $request['lottery_id'];
        }

        $data = [
            'activity_info' => $activityInfo,
            'prize_info' => $prizeInfo,
            'user_info' => $userInfo,
            'choose_act' => $chooseAct,
        ];
        return view('userLottery.addUserLottery', $data);
    }

    public function issueUserLottery(Request $request)
    {
        $userId = $request->input('user_id');
        $prizeId = $request->input('prize_id');
        $drawType = $request->input('draw_type');
        $platform = $request->input('platform');
        $lotteryId = $request->input('lottery_id');
        $userPrizeId = $request->input('user_prize_id');
        $consignee = $request->input('consignee');
        $province = $request->input('province');
        $city = $request->input('city');
        $district = $request->input('district');
        $detail_address = $request->input('detail_address');
        $mobile = $request->input('mobile');
        $awbNo = $request->input('awb_no');
        //用于区分点击发放进入 还是选择了select之后刷新进入
        $requestType = $request->input('request_type');

        //获取用户收货信息
        //如果是从select后进入则不读取数据库
        if ($requestType) {
            $info = (object)null;
            $info->user_id = $userId;
            $info->consignee = $consignee;
            $info->province = $province;
            $info->city = $city;
            $info->district = $district;
            $info->detail_address = $detail_address;
            $info->mobile = $mobile;
            $info->awb_no = $awbNo;
        } else {
            $info = PrizeConsigneeInfoModel::select([
                'user_id',
                'consignee',
                'mobile',
                'province',
                'city',
                'district',
                'detail_address'
            ])->where('user_id', $userId)->first();
            if (empty($info)) {
                $info = UserAddressModel::select([
                    'user_id',
                    'consignee',
                    'mobile',
                    'province',
                    'city',
                    'district',
                    'detail_address'
                ])->where('user_id', $userId)->first();
            }
        }
        if (empty($info)) {
            $info = (object)null;
            $info->province = 0;
            $info->city = 0;
            $info->district = 0;
        }
        //不管从哪里取的用户地址信息 都要添加获取到的额外信息 到info 在api中写入数据时要用到这些数据
        $info->user_id = $userId ?: $info->user_id;
        $info->prize_id = $prizeId ?: $info->prize_id;
        $info->draw_type = $drawType ?: $info->draw_type;
        $info->lottery_id = $lotteryId ?: $info->lottery_id;
        $info->platform = $platform ?: $info->platform;
        $info->user_prize_id = $userPrizeId ?: $info->user_prize_id;
        $info->province = $province ?: $info->province;
        $info->city = $city ?: $info->city;
        $info->district = $district ?: $info->district;


        //获取所有地域信息
        $region_info = RegionModel::get();
        $provinces = [];
        $cities = [];
        $districts = [];
        foreach ($region_info as $region) {
            //省数组永远都不是空的
            if ($region->region_type == 1) {
                array_push($provinces, $region);
            }
            //如果选了省,则提取市
            if (!empty($info->province)) {
                if ($region->region_type == 2) {
                    if ($region->parent_id == $info->province) {
                        array_push($cities, $region);
                    }
                }
            }
            //如果选择了市，则区不空
            if (!empty($info->city)) {
                if ($region->region_type == 3) {
                    if ($region->parent_id == $info->city) {
                        array_push($districts, $region);
                    }
                }
            }
        }

        $data = [
            'info' => $info,
            'provinces' => $provinces,
            'cities' => $cities,
            'districts' => $districts,
        ];
        return view('userLottery.issueUserLottery', $data);
    }
}
