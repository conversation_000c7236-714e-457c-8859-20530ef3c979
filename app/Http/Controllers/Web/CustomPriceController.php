<?php

namespace App\Http\Controllers\Web;

use Illuminate\Http\Request;
use App\Http\Services\DataService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Http\Services\CustomPriceService;
use App\Http\Models\Cube\CustomPriceModel;
use App\Http\Services\PriceActivityService;
use App\Http\Services\GoodsSalePriceGroupService;
use App\Http\Models\Supplier\SupplierChannelModel;

//自定义价格模块
class CustomPriceController extends Controller
{

    //自定义价格活动列表
    public function customPriceList(Request $request)
    {
        $supplierList = GoodsSalePriceGroupService::getSupplierListForXmSelect();

        return view('customPrice.customPriceList', [
            'supplierList' => $supplierList,
        ]);
    }

    public function saveCustomPrice(Request $request)
    {
        $data['title'] = '添加展示价格';
        //如果有id,则代表是修改
        if ($customPriceId = $request->get('id')) {
            $customPrice = CustomPriceModel::where('id', $customPriceId)->first()->toArray();
            if (empty($customPrice)) {
                return "活动不存在";
            }
            $customPrice['price_list'] = json_decode($customPrice['price_list']??'', true);
            $customPrice['audit_price_list'] = json_decode($customPrice['audit_price_list']??'', true);
            $data['customPrice'] = $customPrice;
        }else{
            $data['customPrice'] = [];
        }
        return view('customPrice.saveCustomPrice', $data);
    }
}
