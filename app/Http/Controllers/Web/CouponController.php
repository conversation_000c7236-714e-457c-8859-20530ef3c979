<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Http\Models\Cube\CouponModel;
use App\Http\Models\Cube\CouponOpLogModel;
use App\Http\Services\CouponOpLogService;
use App\Http\Services\CouponService;
use App\Http\Services\PriceActivityService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Route;

class CouponController extends Controller
{
    //价格相关的活动(促销活动)
    public function couponList(Request $request)
    {
        $spuRedis = Redis::connection('spu');
        $createrList = (new CouponModel())->getCreaterList();
        return view('coupon.couponList', compact('createrList'));
    }

    public function couponDetail(Request $request)
    {
        $data = [];
        //如果有id,则代表是修改
        if ($couponId = $request->input('coupon_id')) {
            $couponList = (new CouponService())->getCouponListByIds([$couponId]);
            if (empty($couponList[0])) {
                return "优惠券不存在";
            }
            $data['coupon'] = $couponList[0];
            $logs = (new CouponOpLogService())->getCouponOpLogList($couponId);
            $data['logs'] = $logs;
        }
        return view('coupon.couponDetail', $data);
    }

    public function saveCoupon(Request $request)
    {
        $data = [];
        //如果有id,则代表是修改
        if ($couponId = $request->input('coupon_id')) {
            $service = new CouponService();
            $coupon = $service->getCouponById($couponId);
            if (empty($coupon)) {
                return "优惠券不存在";
            }
            $data['coupon'] = $coupon;
            $data['canal_init_value'] = (new PriceActivityService())->getCanalInitValue($coupon['canals']);
        }
        return view('coupon.saveCoupon', $data);
    }

    //发放优惠券
    public function issueCoupon(Request $request)
    {
        $couponId = $request->input('coupon_id');
        $coupon = CouponModel::where('coupon_id', $couponId)->first()->toArray();
        return view('coupon.issueCoupon', compact('coupon'));
    }

    //批量审核优惠券
    public function reviewCoupons(Request $request)
    {
        $couponIds = $request->input('coupon_ids');
        $couponIdsArr = explode(',', $couponIds);
        $couponList = (new CouponService())->getCouponListByIds($couponIdsArr);
        return view('coupon.reviewCoupons', compact('couponList', 'couponIds'));
    }

}
