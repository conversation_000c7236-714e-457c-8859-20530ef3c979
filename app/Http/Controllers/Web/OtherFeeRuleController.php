<?php

namespace App\Http\Controllers\Web;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Services\SupplierService;
use App\Http\Services\OtherFeeRuleService;
use App\Http\Models\Cube\OtherFeeRuleModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class OtherFeeRuleController extends Controller
{
    /**
     * 其他费用规则列表
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function otherFeeRuleList(Request $request)
    {
        $data = [];
        // 获取渠道列表
        $data['supplierList'] = (new SupplierService())->getSupplierIdSupplierNameMap();
        $data['supplierListForXmSelect'] = SupplierChannelModel::getSupplierListForXmSelect();
        return view('otherFeeRule.otherFeeRuleList', $data);
    }

    /**
     * 保存其他费用规则
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    public function saveOtherFeeRule(Request $request)
    {
        $data = [];
        $rule = []; // 规则数据
        // 如果有id，则代表是修改
        if ($id = $request->input('id')) {
            $service = new OtherFeeRuleService();
            $rule = $service->getOtherFeeRuleById($id);
            if (empty($rule)) {
                return "规则不存在";
            }
        }

        $data['rule'] = $rule;
        // 获取渠道列表
        $data['supplierList'] = (new SupplierService())->getSupplierIdSupplierNameMap();
        $data['supplierListForXmSelect'] = SupplierChannelModel::getSupplierListForXmSelect();
        return view('otherFeeRule.saveOtherFeeRule', $data);
    }

    /**
     * 操作日志
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function operationLog(Request $request)
    {
        $id = $request->input('id');
        return view('otherFeeRule.operationLog', ['id' => $id]);
    }
}
