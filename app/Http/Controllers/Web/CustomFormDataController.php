<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\PriceActivityService;
use Illuminate\Http\Request;

//新版活动相关,以后和价格相关的,满赠的,都是这里管理,之前的价格活动都无效了
class CustomFormDataController extends Controller
{

    //价格相关的活动(促销活动)
    public function customFormDataList(Request $request)
    {
        return view('customFormData.customFormDataList');
    }
}
