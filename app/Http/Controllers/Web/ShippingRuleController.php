<?php

namespace App\Http\Controllers\Web;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Services\SupplierService;
use App\Http\Services\ShippingRuleService;
use App\Http\Models\Cube\ShippingRuleModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class ShippingRuleController extends Controller
{
    /**
     * 运费规则列表
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function shippingRuleList(Request $request)
    {
        ini_set('memory_limit', -1);
        $data = [];
        // 获取供应商列表
        $data['supplierList'] = (new SupplierService())->getSupplierIdSupplierNameMap();
        $data['supplierListForXmSelect'] = SupplierChannelModel::getSupplierListForXmSelect();
        return view('shippingRule.shippingRuleList', $data);
    }

    /**
     * 保存运费规则
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    public function saveShippingRule(Request $request)
    {
        ini_set('memory_limit', -1);
        $data = [];
        $rule = []; // 规则数据
        // 如果有id，则代表是修改
        if ($id = $request->input('id')) {
            $service = new ShippingRuleService();
            $rule = $service->getShippingRuleById($id);
            if (empty($rule)) {
                return "运费规则不存在";
            }

            // 将JSON规则展开为字段
            if (!empty($rule->rule) && is_array($rule->rule)) {
                foreach ($rule->rule as $key => $value) {
                    $rule->$key = $value;
                }
            }
        }
        $data['rule'] = $rule;
        // 获取供应商列表
        $data['supplierList'] = (new SupplierService())->getSupplierIdSupplierNameMap();
        $data['supplierListForXmSelect'] = SupplierChannelModel::getSupplierListForXmSelect();
        return view('shippingRule.saveShippingRule', $data);
    }

    /**
     * 操作日志
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function operationLog(Request $request)
    {
        ini_set('memory_limit', -1);
        $id = $request->input('id');
        return view('shippingRule.operationLog', ['id' => $id]);
    }
}
