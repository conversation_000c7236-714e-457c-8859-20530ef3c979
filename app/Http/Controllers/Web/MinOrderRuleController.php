<?php

namespace App\Http\Controllers\Web;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Services\SupplierService;
use App\Http\Services\MinOrderRuleService;
use App\Http\Models\Cube\MinOrderRuleModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class MinOrderRuleController extends Controller
{
    /**
     * 最小订单规则列表
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function minOrderRuleList(Request $request)
    {
        $data = [];
        $data['supplierListForXmSelect'] = SupplierChannelModel::getSupplierListForXmSelect();
        $data['supplierList'] = (new SupplierService())->getSupplierIdSupplierNameMap();
        return view('minOrderRule.minOrderRuleList', $data);
    }

    /**
     * 保存最小订单规则
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|string
     */
    public function saveMinOrderRule(Request $request)
    {
        $data = [];
        $rule = []; // 规则数据
        // 如果有id，则代表是修改
        if ($ruleId = $request->input('rule_id')) {
            $service = new MinOrderRuleService();
            $rule = $service->getMinOrderRuleById($ruleId);
            if (empty($rule)) {
                return "订单规则不存在";
            }
        }

        $data['rule'] = $rule;
        $data['supplierListForXmSelect'] = SupplierChannelModel::getSupplierListForXmSelect();
        $data['supplierList'] = (new SupplierService())->getSupplierIdSupplierNameMap();
        return view('minOrderRule.saveMinOrderRule', $data);
    }

    /**
     * 查看操作日志
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function operationLog(Request $request)
    {
        $ruleId = $request->input('rule_id');
        return view('minOrderRule.operationLog', compact('ruleId'));
    }
}
