<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Http\Models\Cube\LotteryModel;
use App\Http\Models\Cube\PrizeModel;
use App\Http\Services\LotteryService;
use App\Http\Services\PriceActivityService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class LotteryController extends Controller
{

    //抽奖活动列表
    public function lotteryList(Request $request)
    {
        $createrList = (new LotteryModel())->getCreaterList();
        return view('lottery.lotteryList', compact('createrList'));
    }

    public function saveLottery(Request $request)
    {
        $data = [];
        //如果有id,则代表是修改
        $isOnline = false;
        $isCopy = $request->input('is_copy',0);
        if ($lotteryId = $request->input('lottery_id')) {
            $service = new LotteryService();
            $lottery = $service->getLotteryById($lotteryId);
            if (empty($lottery)) {
                return "抽奖不存在";
            }
            $data['lottery'] = $lottery;
            //活动过程中不可更改预算
            $cur_timestamp = time();
            $isOnline = $cur_timestamp > $lottery['start_time'] && $cur_timestamp < $lottery['end_time'];
            $data['prizeInfo'] = (new PrizeModel())->getPrizeListByLotteryId($lotteryId);
        }
        $data['is_online'] = $isOnline;
        $data['is_copy'] = $isCopy;
        return view('lottery.saveLottery', $data);
    }

    //发放抽奖
    public function issueLottery(Request $request)
    {
        $lotteryId = $request->input('lottery_id');
        $lottery = LotteryModel::where('lottery_id', $lotteryId)->first()->toArray();
        return view('lottery.issueLottery', compact('lottery'));
    }

}
