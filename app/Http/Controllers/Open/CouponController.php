<?php

namespace App\Http\Controllers\Open;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Services\CouponService;
use App\Repository\CouponRepository;

//优惠券相关开放接口
class CouponController extends Controller
{
    /**
     * 获取最佳优惠券
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getBestCoupon(Request $request)
    {
        // 获取请求数据（支持JSON格式）
        $data = $request->json()->all();

        // 从请求中提取参数
        $userId = $data['user_id'] ?? null;
        $goodsList = $data['goods_list'] ?? [];
        $orgId = $data['org_id'] ?? 1; // 默认为猪芯组织ID

        // 验证用户ID
        if (empty($userId)) {
            return $this->setError('用户ID不能为空');
        }

        // 验证商品列表
        if (empty($goodsList) || !is_array($goodsList)) {
            return $this->setError('商品列表不能为空或格式不正确');
        }

        // 验证商品列表格式
        foreach ($goodsList as $item) {
            if (!isset($item['goods_id']) || !isset($item['price'])) {
                return $this->setError('商品列表格式不正确，缺少goods_id或price字段');
            }
        }
        // 调用服务获取最佳优惠券
        $result = (new CouponService())->getBestCoupon($goodsList, $userId, $orgId);

        if (!$result['status']) {
            return $this->setError($result['message']);
        }

        return $this->setSuccessData($result['data']);
    }

    /**
     * 获取优惠券详细信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCouponInfo(Request $request)
    {
        // 获取请求数据（支持JSON格式）
        $data = $request->json()->all();

        // 从请求中提取参数
        $userCouponId = $data['user_coupon_id'] ?? 0;
        $userId = $data['user_id'] ?? null;
        $goodsTotal = $data['goods_total'] ?? [0, 0];
        $goodsList = $data['goods_list'] ?? [];
        $orgId = $data['org_id'] ?? 1;

        // 验证参数
        if (empty($userCouponId)) {
            return $this->setError('优惠券ID不能为空');
        }

        if (empty($userId)) {
            return $this->setError('用户ID不能为空');
        }

        // 计算商品总价
        $ziTotal = $goodsTotal[0];
        $lyTotal = $goodsTotal[1];

        // 获取优惠券信息
        $coupon = CouponRepository::getInfo($userCouponId);

        if (empty($coupon)) {
            return $this->setError('没有可用的优惠券');
        }

        // 计算适用的商品总价
        $goodsTotal = 0;
        switch ($coupon->coupon_mall_type) {
            case 1: // 全场通用
                $goodsTotal = $ziTotal + $lyTotal;
                break;
            case 2: // 仅自营
                $goodsTotal = $ziTotal;
                break;
            case 3: // 仅联营
                $goodsTotal = $lyTotal;
                break;
        }
        // 验证优惠券状态
        if ($coupon->status == 1) {
            return $this->setError('当前优惠券已被使用，请重新选择');
        } elseif ($coupon->status != -1) {
            return $this->setError('当前优惠券不可使用');
        } elseif ($goodsTotal > 0 && $coupon->require_amount > $goodsTotal) {
            return $this->setError('当前优惠券不可使用，未达到使用门槛');
        }
        // 获取商品详细信息
        $goodsInfo = (new CouponService())->getGoodsInfo(array_column($goodsList, 'goods_id'));
        if (empty($goodsInfo)) {
            return $this->setError('无法获取商品信息');
        }
        // 构建商品SKU列表，包含价格信息
        $skuList = (new CouponService())->buildSkuList($goodsList, $goodsInfo);
        // 检查优惠券可用性
        $checkResult = (new CouponService())->checkCouponUsability($coupon, $skuList);
        if (!$checkResult['isUsable']) {
            return $this->setError($checkResult['message']);
        }
        // 设置优惠金额
        $coupon->preferential = $checkResult['preferential'] ?? 0;

        return $this->setSuccessData($coupon);
    }
}
