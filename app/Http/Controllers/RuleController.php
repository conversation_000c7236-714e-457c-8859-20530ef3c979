<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Models\Cube\MinOrderRuleModel;
use App\Http\Services\OtherFeeRuleService;
use App\Http\Services\ShippingRuleService;
use App\Http\Models\Cube\OtherFeeRuleModel;
use App\Http\Models\Cube\ShippingRuleModel;

class RuleController extends Controller
{
    public function getSupplierFeeRule(Request $request)
    {

        $supplierId = $request->input('supplier_id');
        $supplierCode = $request->input('supplier_code');

        //先找出minOrderRule
        $query = MinOrderRuleModel::where('supplier_id', $supplierId);
        if (!empty($supplierCode)) {
            $query->where('supplier_code', $supplierCode);
        }
        $minOrderRule = $query->first();

        //再找出shippingRule
        $queryB = ShippingRuleModel::where('supplier_id', $supplierId);
        // ->where('supplier_code', $supplierCode)
        if (!empty($supplierCode)) {
            $queryB->where('supplier_code', $supplierCode);
        }
        $shippingRule = $queryB->first();

        //最后找出其他费用规则
        $queryC = OtherFeeRuleModel::where('supplier_id', $supplierId);
        // ->where('supplier_code', $supplierCode)
        if (!empty($supplierCode)) {
            $queryC->where('supplier_code', $supplierCode);
        }
        $otherFeeRule = $queryC->first();

        $minOrderRuleContent = "订单最小金额: 人民币 :" . $minOrderRule['cny_amount'] . "元; 美金: " . $minOrderRule['usd_amount'] . "美元";

        $shippingRuleContent = (new ShippingRuleService())->formatRuleContent($shippingRule['type'], $shippingRule['rule']);

        $otherFeeRuleContent['rolling_rule'] = (new OtherFeeRuleService())->formatRuleContent($otherFeeRule['rolling_rule']);
        $otherFeeRuleContent['operation_rule'] = (new OtherFeeRuleService())->formatRuleContent($otherFeeRule['operation_rule']);

        return $this->setSuccessData([
            'minOrderRule' => $minOrderRuleContent,
            'shippingRule' => $shippingRuleContent,
            'otherFeeRule' => $otherFeeRuleContent,
        ]);
    }
}
