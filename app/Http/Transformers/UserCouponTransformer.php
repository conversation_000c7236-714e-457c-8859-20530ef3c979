<?php


namespace App\Http\Transformers;


use App\Http\Models\Cms\UserInfoModel;
use App\Http\Services\SupplierService;
use Illuminate\Support\Facades\Redis;

class UserCouponTransformer extends ActivityTransformer
{
    //列表转换
    public function listTransform($userCouponList)
    {
        $redis = Redis::connection('user_new');
        foreach ($userCouponList as $key => &$userCoupon) {
            $userCoupon['create_time'] = $userCoupon['create_time'] ? date('Y-m-d H:i:s',
                $userCoupon['create_time']) : '';
            $userCoupon['use_time'] = $userCoupon['use_time'] ? date('Y-m-d H:i:s', $userCoupon['use_time']) : '';
            $userCoupon['end_time'] = $userCoupon['end_time'] ? date('Y-m-d H:i:s', $userCoupon['end_time']) : '';
            $userCoupon['status_name'] = \Arr::get(config('field.UserCouponStatus'), $userCoupon['status']);
            $userCoupon['coupon_type_name'] = \Arr::get(config('field.CouponType'),
                $userCoupon['coupon']['coupon_type']);
            if ($userCoupon['org_id'] == 1) {
                $userCoupon['user_account'] = \Arr::get($userCoupon['user'], 'mobile') ?
                    \Arr::get($userCoupon['user'], 'mobile') :
                    \Arr::get($userCoupon['user'], 'email');
            } else {
                $userCoupon['org_id'] = 3;
                $user = $redis->hget('ucenter_user', $userCoupon['user_id'] . '_' . $userCoupon['org_id']);
                if (!empty($user)) {
                    $user = json_decode($user, true);
                    $userCoupon['user_account'] = $user['mobile']?:$user['email'];
                } else {
                    $userCoupon['user_account'] = '';
                }
            }

            $userCoupon['coupon_sn'] = \Arr::get($userCoupon['coupon'], 'coupon_sn');
            $userCoupon['coupon_name'] = \Arr::get($userCoupon['coupon'], 'coupon_name');
            $userCoupon['coupon_desc'] = \Arr::get($userCoupon['coupon'], 'coupon_desc');
        }
        unset($userCoupon);
        return $userCouponList;
    }
}
