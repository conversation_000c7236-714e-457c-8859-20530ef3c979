<?php


namespace App\Http\Transformers;


use App\Http\Models\Cms\UserInfoModel;
use App\Http\Models\Liexin\UserMainModel;
use App\Http\Services\SupplierService;
use App\Http\Services\UserLotteryService;
use Carbon\Carbon;

class UserLotteryTransformer extends ActivityTransformer
{
    //列表转换
    public function listTransform($winners)
    {
        foreach ($winners as $k => $winner) {
            $winner = objectToArray($winner);
            $winner['user_account'] = (new UserMainModel)->getUserMobileOrEmailWithUserId($winner['user_id'],$winner['org_id']);
            $winner['prize_get_time'] = !empty($winner['prize_get_time']) ? Carbon::createFromTimestamp(strval($winner['prize_get_time']))->toDateTimeString() : '';
            $winner['prize_type_str'] = Config('field.PrizeType')[$winner['prize_type']];
            if ($winner['draw_type'] == 1) {
                $drawType = '前台抽奖';
            } elseif ($winner['draw_type'] == 2) {
                $drawType = '后台发放';
            } else {
                $drawType = '未知';
            }
            $winner['draw_type_str'] = $drawType;
            if ($winner['is_sent'] == 1) {
                $isSent = '已发放';
            } elseif ($winner['is_sent'] == 2) {
                $isSent = '未发放';
            } else {
                $isSent = '未知';
            }
            $winner['org_name'] = \Arr::get(config('field.OrgList'), $winner['org_id']);
            $winner['is_received'] = $isSent;
            $address = (new UserLotteryService())->getPointPrizeSendAddress($winner['user_id'], 'prize_consignee_info');
            $winner['send_address'] = !empty($address['address']) ? $address['address'] : '';
            $winner['mobile'] = !empty($address['mobile']) ? $address['mobile'] : '';
            $winner['consignee'] = !empty($address['consignee']) ? $address['consignee'] : '';
            $winners[$k] = $winner;
        }

        return $winners;
    }
}
