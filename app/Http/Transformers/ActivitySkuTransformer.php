<?php


namespace App\Http\Transformers;


use App\Http\Models\Spu\RedisModel;
use App\Http\Services\SupplierService;
use Illuminate\Support\Facades\Redis;

class ActivitySkuTransformer extends ActivityTransformer
{
    //列表转换
    public function listTransform($activitySkuList)
    {
        if (!empty($activitySkuList)) {
            $redis = new RedisModel();
            $spuRedis = Redis::connection('spu');
            foreach ($activitySkuList as $key => &$activitySku) {
                $sku = json_decode($redis->hget('sku', $activitySku['sku_id']), true);
                $activitySku = array_merge($activitySku, $sku);
                $spu = json_decode($spuRedis->hget('spu', $sku['spu_id']), true);
                $activitySku = array_merge($activitySku, $spu);
                $activitySku['goods_name'] = !empty($activitySku['goods_name']) ? $activitySku['goods_name'] : $activitySku['spu_name'];
                $brandName = $redis->hget('brand',$activitySku['brand_id']);
                $activitySku['brand_name'] = $brandName;
                $activitySku['sku_id'] = (string)$activitySku['sku_id'];
            }
            unset($sku);
        }
        return $activitySkuList;
    }
}