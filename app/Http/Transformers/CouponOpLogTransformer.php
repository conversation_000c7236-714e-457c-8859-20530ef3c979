<?php


namespace App\Http\Transformers;


use App\Http\Models\Cms\UserInfoModel;
use App\Http\Services\SupplierService;

class CouponOpLogTransformer extends ActivityTransformer
{
    //列表转换
    public function listTransform($couponLogList)
    {
        foreach ($couponLogList as $key => &$couponLog) {
            $couponLog = $this->transform($couponLog);
        }
        unset($couponLog);
        return $couponLogList;
    }

    public function transform($couponLog)
    {
        $couponLog['status_name'] = $couponLog['status'] == 1 ? '操作成功' : '操作失败';
        $couponLog['op_time'] = $couponLog['op_time'] ? date('Y-m-d H:i:s', $couponLog['op_time']) : '';
        $couponLog['op_type_name'] = \Arr::get(config('field.CouponOpLogType'), $couponLog['op_type']);
        $couponLog['operator_name'] = \Arr::get($couponLog['user_info'], 'name');
        return $couponLog;
    }
}