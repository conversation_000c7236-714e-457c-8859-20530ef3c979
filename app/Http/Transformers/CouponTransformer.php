<?php


namespace App\Http\Transformers;


use App\Http\Models\Cms\UserInfoModel;
use App\Http\Services\SupplierService;

class CouponTransformer extends ActivityTransformer
{
    //列表转换
    public function listTransform($couponList)
    {
        foreach ($couponList as $key => &$coupon) {
            $coupon = $this->transform($coupon);
        }
        unset($coupon);
        return $couponList;
    }

    public function transform($coupon)
    {
        $usableRange = config('field.CouponUsageRange');
        $coupon['create_time'] = date('Y-m-d H:i:s', $coupon['create_time']);
        $coupon['reg_start_time'] = $coupon['reg_start_time'] ? date('Y-m-d H:i:s', $coupon['reg_start_time']) : '';
        $coupon['coupon_type_name'] = \Arr::get(config('field.CouponType'), $coupon['coupon_type']);
        $coupon['time_type_name'] = \Arr::get(config('field.CouponTimeType'), $coupon['time_type']);
        $coupon['issue_type_name'] = $coupon['issue_type'] == 1 ? '买家领取' : '系统发放';
        $coupon['status_name'] = \Arr::get(config('field.CouponStatus'), $coupon['status']);
        $coupon['coupon_get_rule_name'] = \Arr::get(config('field.CouponGetRuler'), $coupon['coupon_get_rule']);
        $temp = '';
        //时间戳转日期
        if ($coupon['time_type'] == 1) {
            $coupon['start_time'] = $coupon['start_time'] ? date('Y', $coupon['start_time']) . '年' . date('m',
                    $coupon['start_time']) . '月' . date('d',
                    $coupon['start_time']) . '日' : '';
            $coupon['end_time'] = $coupon['end_time'] ? date('Y', $coupon['end_time']) . '年' . date('m',
                    $coupon['end_time']) . '月' . date('d',
                    $coupon['end_time']) . '日' : '';
        } else {
            $coupon['usable_time'] = '领取后' . $coupon['usable_time'] . '天';
        }
        //适用范围转化(旧)(留着是为了兼容旧的  ！可删！)
        foreach ($usableRange as $k => $v) {
            if (($coupon['usable_range'] & $k) == $k) {
                $temp .= $v . ',';
            }
        }
        //没有旧的就取新的(供应商+品牌)
        if (empty($temp)) {
            switch ($coupon['coupon_mall_type']) {
                case 1:
                    $temp = '全场';
                    break;
                case 2:
                    $temp = '自营';
                    break;
                case 3:
                    $temp = '专营';
                    break;
                default:
                    break;
            }
            $rangeText = \Arr::get(config('field.CouponGoodsRange'), $coupon['coupon_goods_range']);

            $temp .= ' : ' . $rangeText;
        }
        $coupon['usable_range'] = $temp;

        if ($coupon['coupon_type'] == 2) {
            $coupon['amount_rule'] = "满 {$coupon['require_amount']} 可打 " . ($coupon['sale_amount'] * 10) . " 折";
        } else {
            $coupon['amount_rule'] = "满 {$coupon['require_amount']} 可减 " . ($coupon['sale_amount']) . " 元";
        }
        //发布人
        $creater = UserInfoModel::select('name')->where('userId',
            $coupon['create_uid'])->first()->toArray();
        $coupon['creater_name'] = !empty($creater['name']) ? $creater['name'] : '';
        $coupon['org_name'] = \Arr::get(config('field.OrgList'), $coupon['org_id']);
        return $coupon;
    }
}
