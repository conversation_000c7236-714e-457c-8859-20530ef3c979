<?php


namespace App\Http\Transformers;


use App\Http\Services\SupplierService;

class CustomPriceTransformer
{
    //列表转换
    public function listTransform($customPrices)
    {
        foreach ($customPrices as $key => &$customPrice) {
            $customPrice['org_name'] = config('field.OrgList')[$customPrice['org_id']] ?? '';
            $customPrice['create_time'] = !empty($customPrice['create_time']) ? date('Y-m-d H:i:s', $customPrice['create_time']) : '';
            $customPrice['update_time'] = !empty($customPrice['update_time']) ? date('Y-m-d H:i:s', $customPrice['update_time']) : '';
            $customPrice['audit_time'] = !empty($customPrice['audit_time']) ? date('Y-m-d H:i:s', $customPrice['audit_time']) : '';
            $customPrice['status_name'] = config('field.CustomPriceStatus')[$customPrice['status']];
            $customPrice['audit_status_name'] = config('field.CustomPriceAuditStatus')[$customPrice['audit_status']];
        }
        unset($customPrice);
        return $customPrices;
    }

}
