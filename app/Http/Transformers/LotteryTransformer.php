<?php


namespace App\Http\Transformers;


use App\Http\Models\Cms\UserInfoModel;
use App\Http\Services\SupplierService;

class LotteryTransformer extends ActivityTransformer
{
    //列表转换
    public function listTransform($lotteryList)
    {
        foreach ($lotteryList as $key => &$lottery) {
            $lottery = $this->transform($lottery);
        }
        unset($lottery);
        return $lotteryList;
    }

    public function transform($lottery)
    {
        $lottery['create_time'] = $lottery['create_time'] ? date('Y-m-d H:i:s', $lottery['create_time']) : '';
        $lottery['start_time'] = $lottery['start_time'] ? date('Y年m月d日', $lottery['start_time']) : '';
        $lottery['end_time'] = $lottery['end_time'] ? date('Y年m月d日', $lottery['end_time']) : '';
        $lottery['status_name'] = \Arr::get(config('field.LotteryStatus'), $lottery['status']);
        $lottery['org_name'] = \Arr::get(config('field.OrgList'), $lottery['org_id']);
        return $lottery;
    }
}
