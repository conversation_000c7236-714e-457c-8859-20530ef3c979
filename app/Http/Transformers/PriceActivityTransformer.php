<?php


namespace App\Http\Transformers;


use App\Http\Services\SupplierService;

class PriceActivityTransformer extends ActivityTransformer
{
    //列表转换
    public function listTransform($activities)
    {
        foreach ($activities as $key => &$activity) {
            $activity['activity_time'] = date('Y-m-d', $activity['start_time']) . " ~ " . date('Y-m-d',
                    $activity['end_time']);
            $activity = $this->getUserScope($activity);
            $activity = $this->getCondition($activity);
            $activity['add_time'] = date('Y-m-d H:i:s', $activity['add_time']);
            $activity['expired'] = $activity['end_time'] < time() ? 1 : 0;
        }
        unset($activity);
        return $activities;
    }

    public function statisticsTransform($activities)
    {
        foreach ($activities as $key => &$activity) {
            $activity['activity_time'] = date('Y-m-d H:i:s', $activity['start_time']) . "~" . date('Y-m-d H:i:s',
                    $activity['end_time']);
            $activity['add_time'] = date('Y-m-d H:i:s', $activity['add_time']);
            $activity['new_user_num'] = \Arr::get($activity['statistics'], 'new_user_num', 0);
            $activity['order_num'] = \Arr::get($activity['statistics'], 'order_num', 0);
        }
        unset($activity);
        return $activities;
    }
}