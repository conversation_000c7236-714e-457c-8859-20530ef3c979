<?php


namespace App\Http\Transformers;


use App\Http\Services\SupplierService;

class CustomFormDataTransformer extends ActivityTransformer
{
    //列表转换
    public function listTransform($dataList)
    {
        foreach ($dataList as $key => &$data) {
            $data['create_time'] = date('Y-m-d H:i:s', $data['create_time']);
            $data['form_data'] = htmlspecialchars_decode($data['form_data']);
            $data['form_data'] = json_decode($data['form_data'], true);
            if (!empty($data['mobile'])) {
                $temp = substr($data['mobile'], 3, 4);
                $data['mobile'] = str_replace($temp, '****', $data['mobile']);
            }
        }
        unset($data);
        return $dataList;
    }
}