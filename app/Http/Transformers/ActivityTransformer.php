<?php


namespace App\Http\Transformers;


use App\Http\Services\SupplierService;

class ActivityTransformer
{
    //列表转换
    public function getUserScope($activity)
    {
        $activity['goods_scope'] = $activity['goods_scope'] == 1 ? '自营' : '专营';
        $supplierIds = explode(',', $activity['supplier_ids']);
        $supplierService = new SupplierService();
        $supplierMap = $supplierService->getSupplierIdSupplierNameMap();
        $suppliers = array_map(function ($supplierId) use ($supplierMap) {
            return \Arr::get($supplierMap, $supplierId, "不明");
        }, $supplierIds);
        $supplierNames = implode(',', $suppliers);
        $activity['goods_scope'] = $activity['goods_scope'] . '(' . $supplierNames . ')';
        return $activity;
    }

    //获取条件范围
    public function getCondition($activity)
    {
        $condition = [];
        if (!empty($activity['standard_brand_ids'])) {
            $condition[] = "品牌";
        }
        if (!empty($activity['class_ids'])) {
            $condition[] = "分类";
        }
        if (!empty($activity['canals'])) {
            $condition[] = "渠道标签";
        }

        if ($activity['use_type'] == 2) {
            $condition[] = "商品";
        }

        if (empty($condition)) {
            $condition = ["供应商全局"];
        }



        $activity['condition'] = implode('&', $condition);
        return $activity;
    }
}
