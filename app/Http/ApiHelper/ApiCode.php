<?php

namespace App\Http\ApiHelper;

interface ApiCode
{

    const API_CODE_SUCCESS = 0;//接口请求正常

    const API_CODE_ERROR = 1;//接口请求异常 可预测失败

    const API_CODE_NEED_LOGIN = 101; //需要登录

    const API_CODE_LOGIN_ERROR = 102; // 登录信息错误

    const API_CODE_MOBILE_NOTFOUND_ERROR = 103; // 手机号不存在

    const API_CODE_PASSWD_NOTFOUND_ERROR = 104; // 密码错误

    const API_CODE_USER_DISABLE_ERROR = 105; // 账号被封禁

}
