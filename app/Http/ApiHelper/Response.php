<?php
/**
 * Created by PhpStorm.
 * User: duwen<PERSON>
 * Date: 2021/8/11
 * Time: 2:52 PM
 */

namespace App\Http\ApiHelper;


class Response
{
    public static function setError($errMsg, $errCode = ApiCode::API_CODE_ERROR, $data = [])
    {
        return json_encode(['code' => $errCode, 'msg' => $errMsg, 'data' => $data],
            JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    public static function setSuccess($data, $code = ApiCode::API_CODE_SUCCESS, $msg = "")
    {
        return json_encode(['code' => $code, 'msg' => $msg, 'data' => $data],
            JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
}