<?php

namespace App\Http\Models\Spu;

use App\Http\Models\BaseModel;

class SupplierModel extends BaseModel
{
    public $connection = 'spu';
    public $timestamps = false;
    protected $table = 'supplier';


    const STATUS_PASS = 1;
    const TYPE_official = 0;

    public static function getSupplierList()
    {
        return self::where("type_id", 1)->where("status", self::STATUS_PASS)->where("is_type",
            self::TYPE_official)->get()->toArray();
    }

    //根据id获取多个供应商信息
    public static function getSupplierListByIds($ids)
    {
        return self::whereIn("supplier_id", $ids)->where("status", self::STATUS_PASS)->where("is_type",
            self::TYPE_official)->get()->toArray();
    }

    public function getSupplierNameBySupplierId($supplierId)
    {
        return $this->where('supplier_id', $supplierId)->value('supplier_name');
    }

}
