<?php

namespace App\Http\Models\Spu;

use App\Http\Models\BaseModel;

class StandardBrandModel extends BaseModel
{
    public $connection = 'spu';
    public $timestamps = false;
    protected $table = 'brand_standard';

    //根据standard_brand_id获取brand_name 键值对
    public static function getStandardBrandNameList($standardBrandIdList = [])
    {
        $standardBrandNameList = self::whereIn('standard_brand_id', $standardBrandIdList)
            ->pluck('brand_name', 'standard_brand_id')->toArray();
        return $standardBrandNameList;
    }
}
