<?php


namespace App\Http\Models\Self;


use App\Http\Models\BaseModel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SelfClassifyModel extends BaseModel
{
    protected $connection = 'self';
    public $timestamps = false;
    protected $table = 'self_classify';

    public function children()
    {
        return $this->hasMany(SelfClassifyModel::class, 'parent_id', 'class_id');
    }

    //获取缓存的二级分类
    public function getClassList()
    {
        $classList = Cache::get('cube_class_list');
        if (!$classList) {
            $classList = $this
                ->with([
                    'children' => function ($query) {
                        $query->select(['class_id', 'class_name', 'parent_id']);
                    },
                ])
                ->select(['class_id', 'class_name', 'parent_id'])
                ->where('status', 1)
                ->get()->toArray();
            Cache::put('cube_class_list', $classList, 3600);
            return $classList;
        }

        return $classList;
    }
}