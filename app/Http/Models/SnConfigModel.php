<?php

namespace App\Http\Models;


use Illuminate\Database\Eloquent\Model;

class SnConfigModel extends Model
{
    protected $connection = 'order';

    protected $table = 'sn_config';


    public $timestamps = false;

    public static function getSnConfigByCode($sn_code)
    {
        $res = self::where('sn_code', $sn_code)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function updateById($data, $id)
    {
        return self::where(['id' => $id])->update($data);
    }

}
