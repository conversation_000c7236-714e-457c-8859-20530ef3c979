<?php

namespace App\Http\Models\Crm;

use App\Http\Models\BaseModel;


/**
 * @property $tax_id
 * @property $tax_sn
 * @property $user_id
 * @property $user_sn
 * @property $tax_title
 * @property $inv_type
 * @property $inv_area
 * @property $first_nature
 * @property $company_address
 * @property $company_phone
 * @property $company_desc
 * @property $business_license
 * @property $inv_source
 * @property $product_use_classone_sn
 * @property $product_use_classtwo_sn
 * @property $tax_no
 * @property $bank_name
 * @property $bank_account
 * @property $consignee
 * @property $consignee_phone
 * @property $consignee_province
 * @property $consignee_city
 * @property $consignee_district
 * @property $consignee_address
 * @property $is_default
 * @property $intl_code
 * @property $invoice_file
 * @property $invoice_file_name
 * @property $audit_status
 * @property $verify_status
 * @property $create_time
 * @property $update_time
 */
class TaskInfoModel extends BaseModel
{
    protected $connection = 'mysql';
    protected $table = 'task_info';
    protected $primaryKey = 'id';
    protected $guarded = ['id'];
    public $timestamps = true;
    const CREATED_AT = 'create_time';
    const UPDATED_AT = 'update_time';


    public static function getListByWhere($where = [], $page = 1, $limit = 10)
    {
        $query = self::where($where);
        return $query->orderBy('id', 'desc')->paginate($limit, ['*'], 'p', $page)->toArray();
    }


    public static function insertData($data)
    {
        return self::insertGetId($data);
    }


    public static function updateById($id,$update){
        return self::where("id",$id)->update($update);
    }
}
