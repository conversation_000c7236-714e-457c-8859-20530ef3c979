<?php

namespace App\Http\Models\Crm;


use App\Http\Models\BaseModel;

/**
 * @property  $id
 * @property  $user_id
 * @property  $user_sn
 * @property  $mobile
 * @property  $email
 * @property  $user_name
 * @property  $reg_source
 * @property  $channel_source
 * @property  $user_nature
 * @property  $is_sendmsg
 * @property  $qq
 * @property  $wechat
 * @property  $intl_code
 * @property  $user_position
 * @property  $sale_id
 * @property  $sale_name
 * @property  $status
 * @property  $is_have_order
 * @property  $is_auto_free
 * @property  $account_type
 * @property  $reg_source_params
 * @property  $reg_time
 * @property  $last_order_time
 * @property  $create_time
 * @property  $update_time
 */
class UserModel extends BaseModel
{
    protected $connection = 'mysql';
    protected $table = 'user';
    protected $primaryKey = 'user_id';
    protected $guarded = ['user_id'];
    public $timestamps = false;

    static $page = 30;

    public static $FeedbackType = [
        5 => '搜索无结果',
        6 => '多次搜索',
        7 => '快速找货',
        8 => 'SEO落地页面反馈',
    ];

    public static function getListByWhere($where = [], $page = 1, $limit = 10)
    {
        $query = self::where($where);
        return $query->orderBy('id', 'desc')->paginate($limit, ['*'], 'p', $page)->toArray();
    }


    public static function insertData($data)
    {
        return self::insertGetId($data);
    }


    public static function updateById($id,$update){
        return self::where("id",$id)->update($update);
    }
}
