<?php

namespace App\Http\Models\Supplier;

use App\Http\Models\BaseModel;
use Illuminate\Support\Facades\Redis;

class SupplierChannelModel extends BaseModel
{
    public $connection = 'supplier';
    public $timestamps = false;
    protected $table = 'supplier_channel';

    const STATUS_PASS = 2;
    const TYPE_official = 0;

    //获取可用的供应商编码列表
    public function getSupplierCodeList()
    {
        return $this->where('is_type', self::TYPE_official)->whereIn('status', [0, -1, 1, 2])
            ->pluck('supplier_name', 'supplier_code')->toArray();
    }


    //获取可用的供应商编码列表——根据供应商编码数组
    public function getSupplierCodeNameListOfSupplierCode($supplierCodeArr)
    {
        return $this->where('is_type', self::TYPE_official)
            ->where('status', self::STATUS_PASS)
            ->whereIn('supplier_code', $supplierCodeArr)
            ->pluck('supplier_name', 'supplier_code')->toArray();
    }
    //根据id获取多个供应商信息
    public static function getSupplierListByCodes($codes)
    {
        return self::whereIn("supplier_code", $codes)->where("status", self::STATUS_PASS)->where("is_type", self::TYPE_official)->get()->toArray();
    }
    //根据id获取多个供应商信息
    public static function getSupplierListByCodesNoSatus($codes)
    {
        return self::whereIn("supplier_code", $codes)->where("is_type", self::TYPE_official)->get()->toArray();
    }

    //获取供应商列表给xm-select
    public static function getSupplierListForXmSelect()
    {
        $redis = Redis::connection('sku');
        $cacheData = $redis->get('supplier_list_for_xm_select');
        if ($cacheData) {
            return json_decode($cacheData, true);
        } else {
            $data = self::where("status", self::STATUS_PASS)->where("is_type", self::TYPE_official)->get()->toArray();
            $data = array_map(function ($item) {
                return [
                    'value' => $item['supplier_code'],
                    'name' => $item['supplier_name'] . ' (' . $item['supplier_code'] . ')'
                ];
            }, $data);
            $redis->set('supplier_list_for_xm_select', json_encode($data));
            $redis->expire('supplier_list_for_xm_select', 60 * 60 * 24);
        }

        return $data;
    }
}
