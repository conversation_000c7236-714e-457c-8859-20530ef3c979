<?php
namespace App\Http\Models\Cms;

use Illuminate\Database\Eloquent\Model;

class CmsUserDepartmentModel extends Model
{
    protected $connection = 'cms';

    protected $table = 'user_department';

    public $timestamps = false;

    public static function getDepartmentIdsParrentIds($parrent_ids = [])
    {
        $res = self::whereIn('parent_id', $parrent_ids)->pluck('department_id');
        return ($res) ? $res->toArray() : [];
    }

    public static function getDepartmentInfo($department_id)
    {
        $res = self::where('department_id', $department_id)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function getDepartmentList($departmentName, $departmentIds)
    {
        $res = self::select('department_name')->where('department_name', 'like',
            '%' . $departmentName . '%')->whereIn('department_id', $departmentIds)->get();
        return ($res) ? $res->toArray() : [];
    }

    public static function getDepartmentListByName($departmentName)
    {
        $res = self::select('department_name')->distinct('department_name')->where('department_name', 'like',
            "%{$departmentName}%")->orderByDesc('department_id')->pluck('department_name');
        return ($res) ? $res->toArray() : [];
    }

    public static function getDepartmentId($department)
    {
        $res = self::select('department_id')->distinct('department_name')->where('department_name',
            $department)->pluck('department_id');
        return ($res) ? $res->toArray() : [];
    }

    // 获取部门人员
    public static function getUserByDepartmentId($departmentId, $status = '', $filter = '')
    {
        $departmentIds = [];

        self::getSubDepartmentId($departmentId, $departmentIds);

        return CmsUserInfoModel::whereIn('department_id', $departmentIds)
            ->where(function ($query) use ($status) {
                if ($status !== '') {
                    $query->where('status', '=', $status);
                }
            })
            ->where(function ($query) use ($filter) {
                if ($filter) {
                    $query->whereRaw($filter);
                }
            })
            ->select('userId', 'name', 'status')
            ->get();
    }

    // 获取下级部门ID
    public static function getSubDepartmentId($departmentId, &$departmentIds)
    {
        // 获取下级部门
        $sub_department = self::where('parent_id', $departmentId)->select('department_id',
            'department_name')->get();
        if ($sub_department) {
            foreach ($sub_department as $v) {
                self::getSubDepartmentId($v['department_id'], $departmentIds);
            }
        }

        $departmentIds[] = $departmentId;

        return $departmentIds;
    }

    /*
     * 获取采购员部门的上级部门，只需要往上找一层
     * 比如：联营一组=》联营一部
     */
    public static function getParentDepName($dep_id){
        $info = self::select("parent_id")->where("department_id",intval($dep_id))->first();
        if(!$info){
            return [];
        }
        $parentInfo = null;
        if($info->parent_id != 0){
            $parentInfo = self::select("department_id","department_name")->where("department_id",$info->parent_id)->first();
        }

        return $parentInfo ? $parentInfo->toArray():[];
    }


}
