<?php
namespace App\Http\Models\Cms;

use Illuminate\Database\Eloquent\Model;

class CmsBusinessConfigModel extends Model
{
    protected $connection = 'cms';
    protected $table = 't_business_config';
    public $timestamps = false;
    
    public static function getInfoByUrl($url)
    {
        $res = self::where('url', $url)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function getInfoByTitle($title)
    {
        $res = self::where('title', $title)->first();
        return ($res) ? $res->toArray() : [];
    }


}
