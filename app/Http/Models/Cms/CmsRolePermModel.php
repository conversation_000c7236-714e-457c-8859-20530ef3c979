<?php
namespace App\Http\Models\Cms;

use Illuminate\Database\Eloquent\Model;

class CmsRolePermModel extends Model
{
    public $connection = 'cms';

    protected $table = 't_role_perm';

    public $timestamps = false;

    public static function getRolePermInfosByRoleIds($role_ids, $bid)
    {
        $res = self::whereIn("roleId", $role_ids)->where("bid", $bid)->get();
        return ($res) ? $res->toArray() : [];
    }

}
