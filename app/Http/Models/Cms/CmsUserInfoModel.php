<?php
namespace App\Http\Models\Cms;

use Illuminate\Database\Eloquent\Model;

class CmsUserInfoModel extends Model
{
    protected $connection = 'cms';

    protected $table = 'user_info';

    const GENDER_TYPE_MAN = 1; // 男性

    const GENDER_TYPE_WOMAN = 2; // 女性

    const STATUS_ENABLE = 0;  // 状态-正常

    public $timestamps = false;


    //获取下属用户ids
    public static function getInferiorUserIds($userId)
    {
        $departmentId = self::where('userId', $userId)->value('department_id');
        $users = CmsUserDepartmentModel::getUserByDepartmentId($departmentId);
        return array_column($users, 'userId');
    }

    public static function getSalesBuyerList($where, $department_ids, $page = 1, $limit = 10)
    {
        return self::whereIn('department_id', $department_ids)->where($where)->orderBy('userId',
            'desc')->paginate($limit, ['*'], 'p', $page)->toArray();
    }

    public static function getSalesByPrefixName($name, $department_id)
    {
        CmsDepartmentModel::getSubDepartmentId($department_id, $departmentIds);
        return self::selectRaw("userId as id,name")->whereIn('department_id', $departmentIds)->where("name", "like",
            "%" . trim($name) . "%")->orderBy('userId',
            'desc')->get()->toArray();
    }

    public static function getUserListByPrefixName($name)
    {
        return self::selectRaw("userId as id,name")->where("name", "like",
            "%" . trim($name) . "%")->orderBy('userId',
            'desc')->get()->toArray();
    }

    public static function getUserInfoById($user_id)
    {
        $res = self::where('userId', $user_id)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function getUserInfoByName($name)
    {
        $res = self::where('name', $name)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function updateByWhere($data, $where)
    {
        if (!isset($data['mtime'])) {
            $data['mtime'] = date("Y-m-d H:i:s");
        }
        return self::where($where)->update($data);
    }

    public static function getUsers($user_ids)
    {
        $res = self::whereIn('userId', $user_ids)->get();
        return ($res) ? $res->toArray() : [];
    }

    public static function getUserInfo($user_id)
    {
        $res = self::select('department_name', 'name',"department_id")->where('userId', $user_id)->first();
        return ($res) ? $res->toArray() : [];
    }

    public function getUserName($userId)
    {
        $res = $this->select('name')->where('userId', $userId)->get()->toArray();
        if (!isset($res[0])) {
            return -1;
        }
        return $res[0]['name'];
    }

    public static function getUsersByPositionId($position_id)
    {
        $res = self::where('position_id', $position_id)->where('status', self::STATUS_ENABLE)->get();
        return ($res) ? $res->toArray() : [];
    }

    public static function getUserIdsByDepartmentIds($department_ids = [])
    {
        $res = self::whereIn('department_id', $department_ids)->pluck('userId');
        return ($res) ? $res->toArray() : [];
    }

    public static function getUsersByDepartmentIdAndPositionNames($department_id, $position_names)
    {
        $res = self::where('department_id', $department_id)->where('status', self::STATUS_ENABLE)->whereIn('position_name', $position_names)->get();
        return ($res) ? $res->toArray() : [];
    }

    public static function getCreatorUserInfo($create_name)
    {
        $creator_user_info = [];
        $userInfo = self::getUserInfoByName($create_name);
        if ($userInfo) {
            $creator_user_info = [
                "uid" => $userInfo['userId'],
                "name" => $userInfo['name']
            ];
        }
        return $creator_user_info;
    }

    public static function getDepartmentList($user_ids)
    {
        $res = self::selectRaw('department_name as department , userId as user_id')->whereIn('userId',
            $user_ids)->get();
        return ($res) ? $res->toArray() : [];
    }

    // 批量获取名称
    public static function getNameByUserIds($userIds)
    {
        return self::whereIn('userId', $userIds)->pluck('name', 'userId')->toArray();
    }

    public static function getUserList($status){
        return self::where("status",$status)->pluck("name","userId")->toArray();
    }

}
