<?php

namespace App\Http\Models;


use Illuminate\Database\Eloquent\Model;

class ActionLogModel extends Model
{
    protected $connection = 'os_log';

    protected $table = 'lie_action_log';


    public $timestamps = false;


    public static function addLog($data)
    {
        return self::insertGetId($data);
    }

    //批量插入日志
    public static function addLogs($actionLogs)
    {
        return self::insert($actionLogs);
    }

    public static function getLogs($obj_type, $obj_id, $limit = 10)
    {
        return self::where(['obj_type' => $obj_type, 'obj_id' => $obj_id])
            ->orderBy('id', 'DESC')->limit($limit)->get()->toArray();
    }

    public static function getLogsByObjId($obj_type, $obj_id)
    {
        $where = [
            ['obj_type', '=', $obj_type],
            ['obj_id', '=', $obj_id],
        ];
        $res = self::where($where)->orderBy('id', 'DESC')->get();
        return ($res) ? $res->toArray() : [];
    }

    public static function getLogsByObjIds($obj_type, $obj_ids)
    {
        $where = [
            ['obj_type', '=', $obj_type],
        ];
        $res = self::where($where)->whereIn('obj_id', $obj_ids)->orderBy('id', 'DESC')->get();
        return ($res) ? $res->toArray() : [];
    }

    public static function getLogsByObjIdAndTypes($obj_type, $obj_id, $act_types)
    {
        $where = [
            ['obj_type', '=', $obj_type],
            ['obj_id', '=', $obj_id],
        ];
        $res = self::where($where)->whereIn('act_type', $act_types)->orderBy('id', 'DESC')->get();
        return ($res) ? $res->toArray() : [];
    }


    public static function getListByWhere($where = [], $page = 1, $limit = 10)
    {
        $list = self::where($where);
// $tmp = str_replace('?', '"'.'%s'.'"', $list->toSql());
// $tmp = vsprintf($tmp, $list->getBindings());
// echo $tmp;exit;
        return $list->orderBy('id', 'desc')->paginate($limit, ['*'], 'p', $page)->toArray();
    }

}
