<?php

namespace App\Http\Models\Liexin;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;

class UserMainModel extends Model
{
    public $connection = 'web';
    protected $table = 'user_main';
    protected $primaryKey = 'user_id';
    public $timestamps = false;


    public static $WORK_FUNCTION = [
        1 => "工程师",
        2 => "采购",
        3 => "企业管理",
        4 => "学生",
        5 => "其他",
        6 => "销售",
        7 => "贸易商",
        8 => "终端商",
    ];

    //根据邮箱获取用户ID
    public function getUserIdByEmail($account)
    {
        return $this->where('email', $account)->value('user_id');
    }

    //根据手机号获取用户ID
    public function getUserIdByMobile($account)
    {
        return $this->where('mobile', $account)->value('user_id');
    }

    //根据邮箱或手机号获取用户ID
    public function getUserIdWithMobileOrEmail($account)
    {
        /*
            特别提示:这里不要用网上的正则去验证手机号 因为总是在增加新手机号段
            反正不是邮箱 就是手机号
        */
        $isEmail = filter_var($account, FILTER_VALIDATE_EMAIL);
        if ($isEmail) {
            $sendObj = $this->select('user_id')->where('email',
                $account)->first();
        } else {
            $sendObj = $this->select('user_id')->where('mobile',
                $account)->first();
        }
        if (empty($sendObj)) {
            return '';
        }
        return $sendObj->user_id;
    }

    public function getUserIdWithMobileOrEmailFromUcenter($account, $orgId)
    {
        if (!in_array($orgId, [1, 3])) {
            $orgId = 3;
        }
        /*
            特别提示:这里不要用网上的正则去验证手机号 因为总是在增加新手机号段
            反正不是邮箱 就是手机号
        */
        $redis = Redis::connection('user_new');
        $isEmail = filter_var($account, FILTER_VALIDATE_EMAIL);
        if ($isEmail) {
            $key = $account . '@' . $orgId;
            $userId = $redis->hget('ucenter_email', $key);
        } else {
            $key = $account . '_' . $orgId;
            $userId = $redis->hget('ucenter_mobile', $key);
        }

        return $userId ?? 0;
    }

    //根据用户ID获取用户手机号或邮箱
    function getUserMobileOrEmailWithUserId($userId,$orgId)
    {
        $redis = Redis::connection('user_new');
        if ($orgId == 1) {
            $userInfo = '';
            if (!empty($userId)) {
                $userInfo = $this->select('mobile', 'email')->where('user_id', $userId)->first();
            }
            if (!empty($userInfo->mobile)) {
                return $userInfo->mobile;
            } elseif (!empty($userInfo->email)) {
                return $userInfo->email;
            }
            return '';
        }else{
            $user = $redis->hget('ucenter_user', $userId . '_' . $orgId);
            if (!empty($user)) {
                $user = json_decode($user, true);
                return $user['mobile']?:$user['email'];
            } else {
                return '';
            }
        }

    }

}
