<?php
namespace App\Http\Models\Liexin;

use Illuminate\Database\Eloquent\Model;

class UserWebInfoModel extends Model
{
    protected $connection = 'web';
    protected $table      = 'user_info';
    protected $primaryKey = 'ui_id';
    public $timestamps    = false;

    // 获取会员类型
    static public function getUserType($user_id)
    {
        return self::where('user_id', $user_id)->value('user_type');
    }



}