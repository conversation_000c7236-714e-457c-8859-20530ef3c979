<?php

namespace App\Http\Models\Cube;

use App\Http\Models\BaseModel;
use App\Http\Models\Cms\UserInfoModel;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class OtherFeeRuleModel extends BaseModel
{
    public $connection = 'mysql';
    protected $table = 'other_fee_rule';
    protected $primaryKey = 'id';
    public $timestamps = false;

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 2;
    const STATUS_DELETED = 3;

    // 费用配置方式
    const CONFIG_TYPE_ALL = 1; // 应用全渠道商品
    const CONFIG_TYPE_PACKAGE = 2; // 按包装方式匹配

    /**
     * 获取创建人列表
     * @return array
     */
    public function getCreaterList()
    {
        return UserInfoModel::pluck('name', 'userId')->toArray();
    }

    /**
     * 与用户信息关联
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function user_info()
    {
        return $this->hasOne(UserInfoModel::class, 'userId', 'create_uid');
    }

    public function supplier_code()
    {
        return $this->hasOne(SupplierChannelModel::class, 'supplier_code', 'supplier_code')->select('supplier_code', 'supplier_name');
    }

    public function supplier()
    {
        return $this->hasOne(SupplierModel::class, 'supplier_id', 'supplier_id');
    }

    /**
     * 获取打卷费规则内容
     * @return array
     */
    public function getRollingRuleAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    /**
     * 设置打卷费规则内容
     * @param array $value
     * @return string
     */
    public function setRollingRuleAttribute($value)
    {
        $this->attributes['rolling_rule'] = is_array($value) ? json_encode($value) : $value;
    }

    /**
     * 获取操作费规则内容
     * @return array
     */
    public function getOperationRuleAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    /**
     * 设置操作费规则内容
     * @param array $value
     * @return string
     */
    public function setOperationRuleAttribute($value)
    {
        $this->attributes['operation_rule'] = is_array($value) ? json_encode($value) : $value;
    }
}
