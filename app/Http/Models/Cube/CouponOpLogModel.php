<?php

namespace App\Http\Models\Cube;


use App\Http\Models\BaseModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Cms\UserInfoModel;


class CouponOpLogModel extends BaseModel
{
    public $connection = 'web';
    protected $table = 'coupon_op_log';
    protected $primaryKey = 'op_log_id';
    public $timestamps = false;

    const OP_TYPE_ADD = 1;
    const OP_TYPE_UPDATE = 2;
    const OP_TYPE_APPEND = 3;
    const OP_TYPE_REVIEW = 4;
    const OP_TYPE_APPEND_REVIEW = 5;

    public function user_info()
    {
        return $this->hasOne(UserInfoModel::class, 'userId', 'operator_uid');
    }

    public function insertCouponLog($couponId, $opType, $status = 1)
    {
        $data['coupon_id'] = $couponId;
        $data['status'] = $status;
        $data['op_type'] = $opType;
        $data['operator_uid'] = request()->user->userId;
        $data['op_time'] = time();
        return $this->insert($data);
    }
}
