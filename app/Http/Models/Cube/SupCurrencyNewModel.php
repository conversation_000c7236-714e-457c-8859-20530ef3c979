<?php

namespace App\Http\Models\Cube;


use App\Http\Models\BaseModel;


class SupCurrencyNewModel extends BaseModel
{
    protected $connection = 'mysql';
    protected $table = 'sup_currency';
    protected $primaryKey = 'id';
    public $timestamps = false;


    const SUPTYPE_DAIGOU = 1;//代购
    const SUPTYPE_ZHUANYING = 2;//专营

    //是否默认
    const DEFAULT_GOODS_PRICE_YES = 1;//默认售价组
    const DEFAULT_GOODS_PRICE_NO = 0;//不是默认售价组


    public static $STATUS = [
        "status_enable" =>1,
        "status_disable" => -1,
    ];

    public static $CURRENCY = [
        "1" => "人民币",
        "2" => "美元",
        "3" => "港币",
        "4" =>"欧元"
    ];

    public static $STATUS_FORMAT = [
        "-1" => "禁用",
        "1" => "启用",
    ];

    public static $IS_TAX_FORMAT = [
        "0" => "否",
        "1" => "是",
    ];

    public static function insertData($data)
    {
        foreach ($data as $field => $val) {
            if (!in_array($field, (new self)->getTableFields())) {
                unset($data[$field]);
            }
        }
        return self::insertGetId($data);
    }

    public static function isExistsSameInfo($sup_type, $supplier_name, $supplier_id, $supplier_code)
    {
        $query = self::select("*")->where("sup_type", $sup_type)
            ->where("status", self::$STATUS["status_enable"])
            ->where("supplier_name", trim($supplier_name));
        if ($sup_type == self::SUPTYPE_DAIGOU) {
            $query->where("supplier_id", $supplier_id);
        } else {
            $query->where("supplier_code", trim($supplier_code));
        }
        $info = $query->pluck("id");
        return $info ? $info->toArray() : [];
    }

    public static function updateData($where, $updateData)
    {
        foreach ($updateData as $field => $val) {
            if (!in_array($field, (new self)->getTableFields())) {
                unset($updateData[$field]);
            }
        }
//        dd($where,$updateData);
        return self::where($where)->update($updateData);
    }

    public static function getOne($id){
        $info = self::where("id",$id)->first();
        return $info ? $info->toArray() : [];
    }



}
