<?php

namespace App\Http\Models\Cube;

use App\Http\Models\BaseModel;
use App\Http\Models\Cms\UserInfoModel;

class OperationLogModel extends BaseModel
{
    public $connection = 'mysql';
    protected $table = 'rule_log';
    protected $primaryKey = 'log_id';
    public $timestamps = false;

    const TYPE_MIN_ORDER_RULE = 1;
    const TYPE_SHIPPING_RULE = 2;
    const TYPE_OTHER_FEE_RULE = 3; // 其他费用规则


    protected $fillable = [
        'rule_id', 'operator_id', 'operation_time', 'content'
    ];

    public function operator()
    {
        return $this->hasOne(UserInfoModel::class, 'userId', 'operator_id');
    }
}
