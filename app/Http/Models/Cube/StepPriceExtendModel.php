<?php

namespace App\Http\Models\Cube;


use App\Http\Models\BaseModel;



class StepPriceExtendModel extends BaseModel
{
    protected $connection = 'mysql';
    protected $table = 'step_price_extend';
    protected $primaryKey = 'step_price_ext_id';
    public $timestamps = false;
    protected $guarded = ['step_price_ext_id'];  //设置字段黑名单


    //类型：1商品售价组  2渠道折扣
    const PRICE_EXTEND_TYPE_GOODS_PRICE =  1;//1商品售价组
    const PRICE_EXTEND_TYPE_DISCOUNT =  2;// 2渠道折扣


    public static function insertData($data){
        $tablesFields = (new self)->getTableFields();
        foreach($data as $field=>$val){
            if(!in_array($field,$tablesFields)){
                unset($data[$field]);
            }
        }
        return self::insertGetId($data);
    }

    public static function getStepPirceExtInfoBySppeId($sppe_id,$type=self::PRICE_EXTEND_TYPE_GOODS_PRICE){
        $info =  self::where("sppe_id",intval($sppe_id))->where("type",$type)->first();
        return $info ? $info->toArray() : [];
    }

}
