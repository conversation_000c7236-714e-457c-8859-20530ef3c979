<?php

namespace App\Http\Models\Cube;


use App\Casts\DateTime;
use App\Http\Models\BaseModel;


/**
 *
 * @property int    $id
 * @property string $org_id 组织id
 * @property string $activity_no 活动编号
 * @property string $activity_name 活动名称
 * @property int    $activity_type 活动分类(0.其它活动,1.专题活动,2.促销活动,3.邀请有礼活动,4.抽奖活动,5.拉新活动)
 * @property string $activity_url 活动地址
 * @property int    $activity_start_time 活动开始时间
 * @property int    $activity_end_time 活动结束时间
 * @property int    $web_type 网页类型(1-网页,2-h5,3-全部都有)
 * @property int    $activity_status 活动状态(1待配置,2已配置未上线,3已上线,-1已过期)
 * @property int    $create_time 创建时间
 * @property int    $create_user_id 创建人id
 * @property string $create_user_name 创建人名称
 * @property int    $activity_enable 活动启用状态(1启用,-1未启用)
 * @property string $web_title 网页标题
 * @property string $web_keywords 网页关键词
 * @property string $web_description 网页描述
 * @property string $web_html web网页html
 * @property string $web_html_config web网页html配置项
 * @property string $h5_html h5网页html
 * @property string $h5_html_config
 * @property int    $update_time 更新时间
 *
 */
class ActivityModel extends BaseModel
{
    protected $connection = 'mysql';
    protected $table = 'activity_new';
    protected $primaryKey = 'id';
    public $timestamps = false;

    const CREATED_AT = 'create_time';
    protected $casts = [
        'create_time' => DateTime::class,
        'activity_start_time' => DateTime::class,
        'activity_end_time' => DateTime::class,
    ];
    const ACTIVITY_TYPE_OTHER = 0;
    const ACTIVITY_TYPE_TOPIC = 1;
    const ACTIVITY_TYPE_PROMOTION = 2;
    const ACTIVITY_TYPE_INVITE = 3;
    const ACTIVITY_TYPE_LOTTERY = 4;
    const ACTIVITY_TYPE_PULL_NEW = 5;
    const ACTIVITY_STATUS_WAIT_CONFIG = 1;
    const ACTIVITY_STATUS_CONFIG_NOT_ONLINE = 2;
    const ACTIVITY_STATUS_ONLINE = 3;
    const ACTIVITY_STATUS_EXPIRED = -1;
    const ACTIVITY_ENABLE_ENABLE = 1;
    const ACTIVITY_ENABLE_DISABLE = -1;
    const WEB_TYPE_WEB = 1;
    const WEB_TYPE_H5 = 2;
    const WEB_TYPE_ALL = 3;
    const ACTIVITY_STATUS = [
        self::ACTIVITY_STATUS_WAIT_CONFIG => '待配置',
        self::ACTIVITY_STATUS_CONFIG_NOT_ONLINE => '未上线',
        self::ACTIVITY_STATUS_ONLINE => '已上线',
        self::ACTIVITY_STATUS_EXPIRED => '已过期',
    ];
    const ACTIVITY_TYPE = [
        self::ACTIVITY_TYPE_OTHER => '其它活动',
        self::ACTIVITY_TYPE_TOPIC => '专题活动',
        self::ACTIVITY_TYPE_PROMOTION => '促销活动',
        self::ACTIVITY_TYPE_INVITE => '邀请有礼活动',
        self::ACTIVITY_TYPE_LOTTERY => '抽奖活动',
        self::ACTIVITY_TYPE_PULL_NEW => '拉新活动',
    ];
    const ACTIVITY_ENABLE = [
        self::ACTIVITY_ENABLE_ENABLE => '启用',
        self::ACTIVITY_ENABLE_DISABLE => '禁用',
    ];
    const WEB_TYPE = [
        self::WEB_TYPE_WEB => '网页',
        self::WEB_TYPE_H5 => 'H5',
        self::WEB_TYPE_ALL => '全部都有',
    ];

    protected $appends = [
        'activity_status_name',
        'activity_type_name',
        'activity_enable_name',
    ];

    const CUBE_ACTIVITY_VIEW_ALL = "cube_activity_viewAllList";//查看所有

    public function getActivityStatusNameAttribute()
    {
        return self::ACTIVITY_STATUS[$this->activity_status] ?? '';
    }

    public function getActivityTypeNameAttribute()
    {
        return self::ACTIVITY_TYPE[$this->activity_type] ?? '';
    }

    public function getActivityEnableNameAttribute()
    {
        return self::ACTIVITY_ENABLE[$this->activity_enable] ?? '';
    }

    public static function getListByWhere($where = [], $page = 1, $limit = 10)
    {
        $query = self::where($where);
        return $query->orderBy('id', 'desc')->paginate($limit, ['*'], 'p', $page)->toArray();
    }


    public static function insertData($data)
    {
        return self::insertGetId($data);
    }


    public static function updateById($id, $update)
    {
        if (empty($update['update_time'])) {
            $update['update_time'] = time();
        }
        return self::where("id", $id)->update($update);
    }

    public static function getOneById($id)
    {
        return self::where("id", $id)->first();
        return ($res) ? $res->toArray() : [];
    }
    public static function getOneByUrl($url)
    {
        return self::where("activity_url", $url)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function getOneByWhere($where)
    {
        return self::where($where)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function deleteById($id)
    {
        return self::where("id", $id)->delete();
    }

    public static function getTodayTaskCount()
    {
        $today = date('Y-m-d');
        $where = [];
        $where[] = ['create_time', '>=', strtotime($today)];
        return self::where($where)->count();
    }



}
