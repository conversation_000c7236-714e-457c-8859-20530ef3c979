<?php

namespace App\Http\Models\Cube;

use App\Http\Models\BaseModel;
use Faker\Provider\Base;
use Illuminate\Database\Eloquent\Model;

class PriceWarningModel extends BaseModel
{
    protected $connection = 'mysql';
    public $timestamps = false;
    protected $table = 'price_warning';
    protected $primaryKey = 'price_warning_id';
    protected $guarded = ['price_warning_id'];  //设置字段黑名单

    const statusPending =  1;
    const statusIgnore = 2;
    const statusProcessed = 3;

    public static $STATUS=[
        "status_pending"=>1,
        "status_ignore"=>2,
        "status_processed"=>3,
    ];

    public static $STATUS_FORMAT=[
        1=>"待处理",
        2=>"已忽略",
        3=>"已处理",
    ];

    //关联表
    public function stepPriceExtend()
    {
        //正常状态的明细
        return $this->hasOne(StepPriceExtendModel::class, 'sppe_id', 'sppe_id')->where("type",1);
    }

    //关联表
    public function stepPriceNew()
    {
        //正常状态的明细
        return $this->hasOne(StepPriceNewModel::class, 'sppe_id', 'sppe_id');
    }

    public static function getPriceWarning($priceActivityId,$sppeId){
        $info = self::where("price_activity_id",$priceActivityId)->where("sppe_id",$sppeId)->first();
        return $info ? $info->toArray() : [];
    }

    public static function getPriceWarningById($priceActivityId){
        $info = self::where("price_warning_id",$priceActivityId)->first();
        return $info ? $info->toArray() : [];
    }


    public static function delPriceWarningBypriceActivityId($priceActivityId){
        return self::where("price_activity_id",intval($priceActivityId))->delete();
    }

    public static function delPriceWarningBySppeId($sppeId){
        return self::where("sppe_id",intval($sppeId))->delete();
    }

}
