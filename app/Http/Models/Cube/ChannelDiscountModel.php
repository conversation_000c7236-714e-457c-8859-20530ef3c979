<?php

namespace App\Http\Models\Cube;


use App\Http\Models\BaseModel;



class ChannelDiscountModel extends BaseModel
{
    protected $connection = 'mysql';
    protected $table = 'channel_discount';
    protected $primaryKey = 'channel_disct_id';
    protected $guarded = ['channel_disct_id'];  //设置字段黑名单
    public $timestamps = false;


    const SUPTYPE_DAIGOU = 1;//代购
    const SUPTYPE_ZHUANYING = 2;//专营

    //是否默认
    const DEFAULT_GOODS_PRICE_YES = 1;//默认 是
    const DEFAULT_GOODS_PRICE_NO = 0;//默认 否

    const STATUS_DISABLE = 0;//禁用


    public static $STATUS=[
        "status_enable"=>1,
        "status_disable"=>0,
    ];

    public static $STATUS_FORMAT=[
        "0"=>"禁用",
        "1"=>"启用",
    ];

    //关联表
    public function stepPriceExtend()
    {
        //正常状态的明细
        return $this->hasOne(StepPriceExtendModel::class, 'sppe_id', 'channel_disct_id')->where("type",2);
    }

    public static function getChannelDisctById($channel_disct_id){
        $info =   self::select("*")->where("channel_disct_id",$channel_disct_id)->with("stepPriceExtend")->first();
        return $info ? $info->toArray() : [];
    }

    public static function isExistsSameInfo($sup_type,$order,$supplier_name,$supplier_id,$supplier_code){
        $query = self::select("*")->where("sup_type",$sup_type)->where("order",$order)
            ->where("status",self::$STATUS["status_enable"])
            ->where("supplier_name",trim($supplier_name));
        if($sup_type == self::SUPTYPE_DAIGOU){
            $query->where("supplier_id",$supplier_id);
        }else{
            $query->where("supplier_code",trim($supplier_code));
        }
        $info =  $query->first();
        return $info ? $info->toArray() : [];
    }

    public static function insertData($data){
        foreach($data as $field=>$val){
            if(!in_array($field,(new self)->getTableFields())){
                unset($data[$field]);
            }
        }
        return self::insertGetId($data);
    }

    public static function getChannelDidInfo($channelDisctId){
        $info = self::where("channel_disct_id",intval($channelDisctId))->with("stepPriceExtend")->first();
        return $info ? $info->toArray() : [];
    }


}
