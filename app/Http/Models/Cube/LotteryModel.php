<?php

namespace App\Http\Models\Cube;


use App\Http\Models\BaseModel;
use App\Http\Models\Cms\CmsUserInfoModel;
use App\Http\Models\Cms\UserInfoModel;


class LotteryModel extends BaseModel
{
    public $connection = 'web';
    protected $table = 'lottery_activity';
    protected $primaryKey = 'lottery_id';
    public $timestamps = false;

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 2;
    const STATUS_DELETED = 3;

    public function getCreaterList()
    {
        return UserInfoModel::pluck('name', 'userId')->toArray();
    }

    public function user_info()
    {
        return $this->hasOne(UserInfoModel::class, 'userId', 'publisher_id');
    }
}
