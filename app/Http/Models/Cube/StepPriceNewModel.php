<?php

namespace App\Http\Models\Cube;


use App\Http\Models\BaseModel;


class StepPriceNewModel extends BaseModel
{
    protected $connection = 'mysql';
    protected $table = 'step_price_new';
    protected $primaryKey = 'sppe_id';
    public $timestamps = false;


    const SUPTYPE_DAIGOU = 1;//代购
    const SUPTYPE_ZHUANYING = 2;//专营

    //是否默认
    const DEFAULT_GOODS_PRICE_YES = 1;//默认售价组
    const DEFAULT_GOODS_PRICE_NO = 0;//不是默认售价组


    public static $STATUS = [
        "status_enable" => 1,
        "status_disable" => 0,
    ];

    public static $STATUS_FORMAT = [
        "0" => "禁用",
        "1" => "启用",
    ];


    //关联表
    public function stepPriceExtend()
    {
        //正常状态的明细
        return $this->hasOne(StepPriceExtendModel::class, 'sppe_id', 'sppe_id')->where("type", 1);
    }


    public static function insertData($data)
    {
        foreach ($data as $field => $val) {
            if (!in_array($field, (new self)->getTableFields())) {
                unset($data[$field]);
            }
        }
        return self::insertGetId($data);
    }

    public static function isExistsSameInfo($sup_type, $order, $supplier_name, $supplier_id, $supplier_code, $org_id)
    {
        $query = self::select("*")->where("sup_type", $sup_type)->where("order", $order)
            ->where("status", self::$STATUS["status_enable"])
            ->where("supplier_name", trim($supplier_name))
            ->where("org_id", $org_id);
        if ($sup_type == self::SUPTYPE_DAIGOU) {
            $query->where("supplier_id", $supplier_id);
        } else {
            $query->where("supplier_code", trim($supplier_code));
        }
        $info = $query->first();
        return $info ? $info->toArray() : [];
    }

    public static function updateData($where, $updateData)
    {
        foreach ($updateData as $field => $val) {
            if (!in_array($field, (new self)->getTableFields())) {
                unset($updateData[$field]);
            }
        }
//        dd($where,$updateData);
        return self::where($where)->update($updateData);
    }

    public static function getStepPriceInfoById($sppe_id)
    {
        $info = self::select("*")->where("sppe_id", $sppe_id)->with("stepPriceExtend")->first();
        return $info ? $info->toArray() : [];
    }

    public static function getStepPriceInfoBySupIdOrderOfDaiGou($supplierId, $priceRatioSort, $supType = self::SUPTYPE_DAIGOU)
    {
        $info = self::where("supplier_id", intval($supplierId))->where("order", intval($priceRatioSort))
            ->where("status", self::$STATUS["status_enable"])->where("sup_type", $supType)->first();
        return $info ? $info->toArray() : [];
    }


    public static function getDefaultStepPriceInfoBySupType($supType)
    {
        return self::select("*")->where("status", self::$STATUS["status_enable"])->where("sup_type", $supType)->with("stepPriceExtend")->get()->toArray();
    }

}
