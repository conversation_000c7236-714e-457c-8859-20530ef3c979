<?php

namespace App\Http\Models\Cube;


use App\Http\Models\BaseModel;
use App\Http\Models\Spu\BrandModel;


class CouponBrandModel extends BaseModel
{
    public $connection = 'web';
    protected $table = 'coupon_brand';
    protected $primaryKey = 'coupon_brand_id';
    public $timestamps = false;

    public function brand()
    {
        return $this->hasOne(BrandModel::class, 'brand_id', 'brand_id');
    }
}
