<?php

namespace App\Http\Models\Cube;

use App\Http\Models\Cms\UserInfoModel;
use App\Http\Models\Cube\CouponBrandModel;
use Illuminate\Database\Eloquent\Model;

class CouponModel extends Model
{
    protected $connection = 'web';
    public $timestamps = false;
    protected $table = 'coupon';

    const STATUS_NEED_REVIEW = -1;
    const STATUS_CANCEL = -2;

    public function coupon_brand()
    {
        return $this->hasMany(CouponBrandModel::class, 'coupon_id', 'coupon_id');
    }

    public function coupon_class()
    {
        return $this->hasMany(CouponClassModel::class, 'coupon_id', 'coupon_id');
    }

    public function getCreaterList()
    {
        return UserInfoModel::pluck('name', 'userId')->toArray();
    }

    public function userCoupons()
    {
        return $this->hasMany(UserCouponModel::class,'coupon_id','coupon_id');
    }

}
