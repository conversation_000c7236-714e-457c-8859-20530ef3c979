<?php

namespace App\Http\Models\Cube;


use App\Http\Models\BaseModel;
use App\Http\Models\Spu\BrandModel;

/**
 * Class ActivityAndCouponSkuModel
 * @package App\Http\Models\Cube
 * @property int $id
 * @property int $main_id 主要id
 * @property int $supplier_ids 渠道id
 * @property int $canals 供应商编码
 * @property int $sku_name 商品型号
 * @property int $type 类型,1-活动价,2优惠券,3专题活动
 * @property int $create_time 创建时间
 */
class ActivityAndCouponSkuModel extends BaseModel
{
    public $connection = 'mysql';
    protected $table = 'activity_and_coupon_sku';
    protected $primaryKey = 'id';
    public $timestamps = false;
    //CREATE TABLE `lie_activity_and_coupon_sku` (
    //  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '主键',
    //  `main_id` int(10) NOT NULL COMMENT '主要id,可能是价格活动id,也可能是优惠券id',
    //  `supplier_ids` varchar(255) NOT NULL DEFAULT '' COMMENT '渠道id,可以有多个,逗号隔开',
    //  `canals` varchar(255) NOT NULL DEFAULT '' COMMENT '供应商编码,可以有多个,逗号隔开',
    //  `sku_name` varchar(200) NOT NULL COMMENT '商品型号',
    //  `type` tinyint(1) unsigned NOT NULL COMMENT '类型,1-活动价,2优惠券,3专题活动',
    //  `create_time` int(10) DEFAULT NULL COMMENT '创建时间',
    //  PRIMARY KEY (`id`)
    //) ENGINE=InnoDB AUTO_INCREMENT=40254 DEFAULT CHARSET=utf8 COMMENT='活动sku和优惠券sku';

    const TYPE_PRICE_ACTIVITY = 1;
    const TYPE_COUPON = 2;
    const TYPE_ACTIVITY = 3;//专题活动

    public static function deleteByMainIdAndType($mainId, $type)
    {
        return self::where('main_id', $mainId)->where('type', $type)->delete();
    }

    public static function getSkuNameByMainIdAndType($mainId, $type)
    {
        return self::where('main_id', $mainId)->where('type', $type)->pluck('sku_name')->toArray();
    }

    public static function insertData($data)
    {
        return self::insert($data);
    }

    public static function addDataByMainIdAndType($mainId, $type, $skuName, $supplierIds = "", $canals = "")
    {
        $data = [
            'main_id'      => $mainId,
            'type'         => $type,
            'sku_name'     => $skuName,
            'supplier_ids' => $supplierIds,
            'canals'       => $canals,
            'create_time'  => time(),
        ];
        return self::insert($data);
    }
}
