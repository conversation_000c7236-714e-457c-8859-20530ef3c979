<?php

namespace App\Http\Models\Cube;


use App\Http\Models\BaseModel;

/**
 * @property int    $id
 * @property string $element_name 模板名
 * @property string $element_code 视图标记
 * @property int    $element_status 状态(1启用,-1禁用)
 * @property int    $create_time 创建时间
 */
class ActivityElementModel extends BaseModel
{
    protected $connection = 'mysql';
    protected $table = 'activity_element';
    protected $primaryKey = 'id';
    public $timestamps = false;

    const CREATED_AT = 'create_time';
    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = -1;
    const STATUS = [
        self::STATUS_ENABLE => '启用',
        self::STATUS_DISABLE => '禁用',
    ];


    protected $appends = [
        'element_status_name',
    ];

    public function getElementStatusNameAttribute()
    {
        return self::STATUS[$this->element_status] ?? '';
    }

    public static function getListByWhere($where = [], $page = 1, $limit = 10)
    {
        $query = self::where($where);
        return $query->orderBy('id', 'asc')->paginate($limit, ['*'], 'p', $page)->toArray();
    }


    public static function insertData($data)
    {
        return self::insertGetId($data);
    }


    public static function updateById($id, $update)
    {
        return self::where("id", $id)->update($update);
    }

    public static function getOneById($id)
    {
        return self::where("id", $id)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function getOneByWhere($where)
    {
        return self::where($where)->first();
        return ($res) ? $res->toArray() : [];
    }

    public static function deleteById($id)
    {
        return self::where("id", $id)->destory();
    }


}
