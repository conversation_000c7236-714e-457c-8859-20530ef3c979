<?php

namespace App\Http\Models\Cube;

use Illuminate\Database\Eloquent\Model;

class PriceActivityModel extends Model
{
    protected $connection = 'mysql';
    public $timestamps = false;
    protected $table = 'price_activity';

    const ORG_LIEXIN = 1;
    const ORG_IEDGE = 3;

    public function statistics()
    {
        return $this->hasOne(ActivityStatisticsModel::class, 'activity_id', 'id');
    }

    public static function getPriceActivityInfo($id)
    {
        $info =  self::where("id", $id)->first();
        return $info ? $info->toArray() : [];
    }

    public static function getOneById($id)
    {
        return self::where("id", $id)->first();
        return ($res) ? $res->toArray() : [];
    }
}
