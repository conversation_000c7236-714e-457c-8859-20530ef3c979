<?php

namespace App\Http\Models\Cube;

use App\Http\Models\BaseModel;
use App\Http\Models\Cms\UserInfoModel;

class MinOrderRuleModel extends BaseModel
{
    public $connection = 'mysql';
    protected $table = 'min_order_rule';
    protected $primaryKey = 'rule_id';
    public $timestamps = false;

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = -1;
    const STATUS_DELETED = 3;

    public function getCreaterList()
    {
        return UserInfoModel::pluck('name', 'userId')->toArray();
    }

    public function user_info()
    {
        return $this->hasOne(UserInfoModel::class, 'userId', 'creator_id');
    }
}
