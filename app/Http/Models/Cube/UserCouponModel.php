<?php

namespace App\Http\Models\Cube;

use App\Http\Models\Cms\UserInfoModel;
use App\Http\Models\Crm\UserModel;
use App\Http\Models\Liexin\UserMainModel;
use Illuminate\Database\Eloquent\Model;

class UserCouponModel extends Model
{
    protected $connection = 'web';
    public $timestamps = false;
    protected $table = 'user_coupon';

    const STATUS_AVAILABLE = -1;
    const STATUS_USED = 1;
    const STATUS_EXPIRED = -2;
    const STATUS_DELETED = -3;


    public function user()
    {
        return $this->hasOne(UserMainModel::class, 'user_id', 'user_id');
    }

    public function coupon()
    {
        return $this->hasOne(CouponModel::class, 'coupon_id', 'coupon_id');
    }

}