<?php

namespace App\Http\Models\Cube;

use App\Http\Models\BaseModel;
use App\Http\Models\Cms\UserInfoModel;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Models\Supplier\SupplierChannelModel;

class ShippingRuleModel extends BaseModel
{
    public $connection = 'mysql';
    protected $table = 'shipping_rule';
    protected $primaryKey = 'id';
    public $timestamps = false;

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 2;
    const STATUS_DELETED = 3;

    // 配置方式
    const TYPE_FIXED = 1; // 固定金额
    const TYPE_WEIGHT = 2; // 按重量计算

    /**
     * 获取创建人列表
     * @return array
     */
    public function getCreaterList()
    {
        return UserInfoModel::pluck('name', 'userId')->toArray();
    }

    /**
     * 与用户信息关联
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function user_info()
    {
        return $this->hasOne(UserInfoModel::class, 'userId', 'create_uid');
    }

    public function supplier_code()
    {
        return $this->hasOne(SupplierChannelModel::class, 'supplier_code', 'supplier_code')->select('supplier_code', 'supplier_name');
    }

    public function supplier()
    {
        return $this->hasOne(SupplierModel::class, 'supplier_id', 'supplier_id');
    }

    /**
     * 获取规则内容
     * @return array
     */
    public function getRuleAttribute($value)
    {
        return json_decode($value, true) ?: [];
    }

    /**
     * 设置规则内容
     * @param array $value
     * @return string
     */
    public function setRuleAttribute($value)
    {
        $this->attributes['rule'] = is_array($value) ? json_encode($value) : $value;
    }
}
