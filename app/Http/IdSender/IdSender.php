<?php

namespace App\Http\IdSender;

use App\Exceptions\InvalidRequestException;
use App\Http\Models\SnConfigModel;

class IdSender
{


    const TYPE_CUBE_CHANNELDISCOUNT = 'CUBE_CHANNELDISCOUNT';             // 魔方渠道折扣编码

    const TYPE_CUBE_GOODSSALEPRICEGROUP = 'CUBE_GOODS_PRICE';             // 商品售价组

    const TYPE_ORG_ICHUNT = 1;    // 采购组织-深圳市猎芯科技有限公司

    const TYPE_ORG_SHENMAO = 2;   // 采购组织-深贸电子有限公司

    const TYPE_LANG_CN = 1; // 合同语言类型 中文

    const TYPE_LANG_EN = 2; // 合同语言类型 英文

    // 统一sn生成器，生成sn
    public static function getSn($type = self::TYPE_CUBE_CHANNELDISCOUNT, $org = self::TYPE_ORG_ICHUNT)
    {
        $sn = '';
        switch ($type) {
            case self::TYPE_CUBE_CHANNELDISCOUNT:
            case self::TYPE_CUBE_GOODSSALEPRICEGROUP:
                $sn = self::getCommonSn($type, $org);
                break;
            default:

        }
        return $sn;
    }

    // 获取公共sn
    private static function getCommonSn($type)
    {
        $sn_config = self::getSnConfig($type);
        return $sn_config["prefix"] . "-" . self::getDate2() . self::getFilledId($sn_config);
    }



    // 当前日期
    private static function getDate()
    {
        return date("Ymd");
    }

    // 当前日期
    private static function getDate2()
    {
        return substr(date("Ymd"),2);
    }

    // 当日流水号id
    private static function getFilledId($sn_config)
    {
        $current_date = date("Y-m-d");
        if ($sn_config['current_date'] != $current_date) {
            $sn_config['number_next'] = 1;
        }

        if (strlen($sn_config['number_next']) > $sn_config['padding']) {
            $filled_id = $sn_config['number_next'];
        } else {
            $filled_id = str_pad($sn_config['number_next'], $sn_config['padding'], "0", STR_PAD_LEFT);
        }

        // 更新next_id
        $update_sn_config = [
            "current_date" => $current_date,
            "number_next"  => (int)($sn_config['number_next'] + $sn_config['number_increment'])
        ];
        SnConfigModel::updateById($update_sn_config, $sn_config['id']);
        return $filled_id;
    }

    // 获取sn配置信息
    private static function getSnConfig($type)
    {
        $sn_config = SnConfigModel::getSnConfigByCode($type);
        if (empty($sn_config)) {
            throw new InvalidRequestException("sn_config is not config.");
        }
        return $sn_config;
    }

}
