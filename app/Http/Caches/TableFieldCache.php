<?php

namespace App\Http\Caches;

use \Illuminate\Support\Facades\Redis;

// 数据库字段缓存
class TableFieldCache
{


    private $redis;

    public function __construct(string $default_instance = 'frq')
    {
        if ($default_instance) {
            $this->redis = Redis::connection($default_instance);
        } else {
            $this->redis = Redis::connection();
        }
    }

    /*
     * 获取一张表字段缓存
     */
    public function getTableFieldsCache($tableName="")
    {
        return $this->redis->hget("pur_table_field_cache", $tableName);
    }


    /*
     * 获取所有表字段缓存
     */
    public function getAllTableFieldsCache()
    {
        return $this->redis->hgetAll("pur_table_field_cache");
    }


    /*
     * 设置一张表缓存
     */
    public function setTableFieldsCache($tableName = "",$val="")
    {
        return $this->redis->hset("pur_table_field_cache", $tableName,$val);
    }


    /*
     * 删除一张表字段缓存
     */
    public function deleteTableFieldsCache($tableName="")
    {
        return $this->redis->hDel("pur_table_field_cache", $tableName);
    }


    /*
     * 删除所有表字段缓存
     */
    public function deleteAllTableFieldsCache()
    {
        return $this->redis->del("pur_table_field_cache");
    }



}