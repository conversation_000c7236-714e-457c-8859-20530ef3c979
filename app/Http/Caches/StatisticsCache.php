<?php

namespace App\Http\Caches;

use Carbon\Carbon;
use \Illuminate\Support\Facades\Redis;

// 税率cache
class StatisticsCache
{

    const _KEY_RATE = 'erp_rate';  // hash 结构，一个用户一个key

    private $redis;

    public function __construct($defaultInstance = 'frq')
    {
        if ($defaultInstance) {
            $this->redis = Redis::connection($defaultInstance);
        } else {
            $this->redis = Redis::connection();
        }
    }

    public function getIndexStatistics()
    {
        return $this->redis->get('pur_index_statistics');
    }

    public function setIndexStatistics($statistics)
    {
        $ttl = 1;
        $this->redis->set('pur_index_statistics', json_encode($statistics));
        $this->redis->expire('pur_index_statistics', $ttl);
    }

}
