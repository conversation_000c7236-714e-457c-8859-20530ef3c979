<?php

namespace App\Http\Caches;

use \Illuminate\Support\Facades\Redis;

// 税率cache
class SpuCache
{

    const _KEY_RATE = 'erp_rate';  // hash 结构，一个用户一个key

    private $redis;

    public function __construct(string $default_instance = 'sku')
    {
        if ($default_instance) {
            $this->redis = Redis::connection($default_instance);
        } else {
            $this->redis = Redis::connection();
        }
    }

    public function getSpu($spuId)
    {
        $spuRedis = Redis::connection('spu');
        $spu = json_decode($spuRedis->hget('spu', $spuId), true);
        if ($spu) {
            $brandId = $spu['brand_id'];
            $brand = $this->redis->hget('brand', $brandId);
            $spu['brand_name'] = $brand;
        }
        return $spu;
    }
}
