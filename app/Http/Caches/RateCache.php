<?php

namespace App\Http\Caches;

use \Illuminate\Support\Facades\Redis;

// 税率cache
class RateCache
{

    const _KEY_RATE = 'erp_rate';  // hash 结构，一个用户一个key

    private $redis;

    public function __construct(string $default_instance = 'frq')
    {
        if ($default_instance) {
            $this->redis = Redis::connection($default_instance);
        } else {
            $this->redis = Redis::connection();
        }
    }

    public function getRate($k = "美元")
    {
        return $this->redis->hget(self::_KEY_RATE, $k);
    }

    public function getRateInfo()
    {
        return $this->redis->hgetall(self::_KEY_RATE);
    }

}