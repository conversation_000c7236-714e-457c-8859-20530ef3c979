<?php

namespace App\Providers;

use App\Http\Models\PurchasePlanModel;
use App\Http\Models\ReturnMaterialModel;
use App\Observers\PurchasePlanObserver;
use App\Observers\ReturnMaterialObserver;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
//        $this->_dbQueryBuilderMacro();
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        //
        date_default_timezone_set("Asia/Shanghai");
    }

}
