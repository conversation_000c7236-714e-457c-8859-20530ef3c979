<?php


namespace App\Presenters;


use Arr;

class SingleSelectPresenter
{
    public function render($name, $text, $value = null, $data = [0 => '禁用', 1 => '启用'], $option = [])
    {
        $hideNotSelect = Arr::get($option, 'hide_not_select', false);
        $isRequired = Arr::get($option, 'required', false);
        $disable = Arr::get($option, 'disabled', false);
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $html = <<<EOF
                    <label class="layui-form-label">
                   $requiredHtml
                     $text
                    </label>
                     <div class="layui-input-block">
                        {$this->itemRender($data, $name, $value, $disable, $hideNotSelect)}
                    </div>
EOF;
        return $html;
    }

    public function itemRender($data, $name, $value, $disable, $hideNotSelect)
    {
        $checked = '';
        $itemsHtml = '';
        $disabled = $disable ? 'disabled' : '';
        foreach ($data as $v => $item) {
            if ($value !== null) {
                $checked = ($v == $value) ? "checked" : '';
            }
            if (!$hideNotSelect) {
                $itemsHtml = $itemsHtml . "<input type='radio' lay-filter='${name}' name='${name}' id='${name}' value='${v}' title='${item}' $checked $disabled>";
            }else{
                if ($value == $v) {
                    $itemsHtml = $itemsHtml . "<input type='radio' lay-filter='${name}' name='${name}' id='${name}' value='${v}' title='${item}' $checked $disabled>";
                }
            }
        }
        return $itemsHtml;
    }
}
