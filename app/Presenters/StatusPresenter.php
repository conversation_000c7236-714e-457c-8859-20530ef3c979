<?php


namespace App\Presenters;


class StatusPresenter
{
    public function render($name, $text, $status = null, $data = [0 => '禁用', 1 => '启用'], $option = [])
    {
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: red">*</span>' : "";
        $isDisabled = \Arr::get($option,'disabled',false);
        $disabledHtml = $isDisabled ? 'disabled' : '';
        $html = <<<EOF
                    <label class="layui-form-label">
                   $requiredHtml
                     $text
                    </label>
                     <div class="layui-input-inline">
                        <select name="$name" lay-filter="$name" $disabledHtml lay-search="true">
                        {$this->optionsRender($data, $status)}
                        </select>
                    </div>
EOF;
        return $html;
    }

    public function optionsRender($data, $status)
    {
        $optionsHtml = ' <option value="">请选择</option>';
        $checked = '';
        foreach ($data as $key => $value) {
            if ($status !== null) {
                $checked = ($key == $status) ? "selected='selected'" : '';
            }
            $optionsHtml = $optionsHtml . "<option value='$key' $checked>$value</option>";
        }
        return $optionsHtml;
    }
}
