<?php


namespace App\Presenters;


use App\Http\Services\StandardBrandService;

class StandardBrandNameListPresenter
{
    public function render($name, $fieldText, $value = null, $option = [])
    {
        //因为这个组件是专门给标准品牌用的,所以可以直接去获取标准列表展示出来
        $standardBrandNameList = '';
        if ($value) {
            $standardBrandNameList = (new StandardBrandService())->getStandardBrandNameListByBrandIds($value);
        }
        $isRequired = \Arr::get($option, 'required', false);
        $requiredHtml = $isRequired ? '<span style="color: red;margin-right: 3px">*</span>' : '';
        $html = <<<EOF
         <label class="layui-form-label">
                $requiredHtml $fieldText</label>
            <div class="layui-input-block" style="width: 80%">
                <input type="hidden" name="$name" value="$value" id="$name">
                    <div class="layui-col-md7">
                        <textarea  rows="7" placeholder="标准品牌名称,多个用英文逗号隔开" class="layui-textarea" id="${name}_name_list">$standardBrandNameList</textarea>
                        <blockquote class="layui-elem-quote" id="valid_${name}_name_list">$standardBrandNameList</blockquote>
                        <span style="color: red" id="invalid_${name}_name_list"></span>
                    </div>
                    <div class="layui-col-md5">
                        <button type="button" class="layui-btn-sm layui-btn" id="check_and_add_${name}_name_list" style="margin-left: 10px;">验证并加入</button>
                        <p style="margin-top: 130px;margin-left: 10px"></p>
                    </div>
            </div>
<script>

    $(document).on('click','#check_and_add_${name}_name_list',function () {
        let standardBrandNameList = $('#${name}_name_list').val();
        let url = '/api/commonData/checkStandardBrandNameList';
        layer.load();
        var self = $(this);
        $.ajax({
            url: url,
            type: 'POST',
            async: true,
            data: {
                standard_brand_name_list: standardBrandNameList
            },
            dataType: 'json',
            timeout: 20000,
            success: function (res) {
                let brandCount = 0;
                layer.closeAll();
                if (res.code === 0) {
                    //得到结果后还要操作div
                    let data = res.data;
                    let invalidBrandNameList = data.invalid_brand_name_list;
                    let validBrandIds = data.valid_brand_ids;
                    let validBrandNameList = data.valid_brand_name_list;

                    let standardBrandIds = $('#${name}').val();
                    standardBrandIds = standardBrandIds.split(',');
                    standardBrandIds = validBrandIds;
                    standardBrandIds = array_unique(array_remove_empty(standardBrandIds));
                    $('#${name}').val(standardBrandIds.join(','));

//                    let validStandardBrandNameList = $('#valid_${name}_name_list').text();
//                    validStandardBrandNameList = validStandardBrandNameList.split(',');
                    let validStandardBrandNameList = validBrandNameList;
                    validStandardBrandNameList = array_unique(array_remove_empty(validStandardBrandNameList));
                    brandCount = validStandardBrandNameList.length;
                    $('#valid_${name}_name_list').text(validStandardBrandNameList.join(','));
                    // $('#${name}_name_list').val(validStandardBrandNameList.join(','));

                    // let invalidStandardBrandNameList = $('#invalid_${name}_name_list').val();
                    // let invalidStandardBrandNameList = '';
                    // invalidStandardBrandNameList = invalidStandardBrandNameList.split(',');
                    // invalidStandardBrandNameList = $.merge(invalidStandardBrandNameList, invalidBrandNameList);
                    $('#invalid_${name}_name_list').text('');
                    let invalidStandardBrandNameList = invalidBrandNameList;
                    invalidStandardBrandNameList = array_unique(array_remove_empty(invalidStandardBrandNameList));
                    if (invalidStandardBrandNameList.length > 0){
                         let text = '品牌名称 : ' + invalidStandardBrandNameList.join('、') + ' 未匹配到标准品牌';
                    $('#invalid_${name}_name_list').text(text);
                    $('#invalid_${name}_name_list').val(invalidStandardBrandNameList.join('、'));
                    }
                    self.next().text(brandCount+'个');
                   
                } else {
                    layer.msg(res.msg, {icon: 5});
                }
            },
            error: function () {
                layer.closeAll();
                layer.msg('网络错误', {icon: 5});
            }
        });
    });

</script>
EOF;
        return $html;
    }
}