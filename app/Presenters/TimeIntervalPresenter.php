<?php


namespace App\Presenters;


class TimeIntervalPresenter
{
    public function render($name, $text)
    {
        $time = request()->get($name);
        $html = <<<EOF
                    <label class="layui-form-label" style="min-width: 80px">$text</label>
                    <div class="layui-input-inline" style="min-width: 260px">
                        <input type="text" name="{$name}"  autocomplete="off" class="layui-input">
                    </div>
                   <script>
                    window.onload = function(){
                    layui.use('laydate', function(){
                       let laydate = layui.laydate;
                       laydate.render({ 
                         elem: 'input[name=$name]'
                           ,type: 'datetime'
                           ,trigger:'click'
                           ,range: '~' //或 range: '~' 来自定义分割字符
                           ,value: '$time'
                       });
                     });
                    }
                    </script>
EOF;

        return $html;
    }
}