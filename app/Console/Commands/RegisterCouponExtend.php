<?php

namespace App\Console\Commands;

use App\Http\Models\Cube\CouponModel;
use App\Http\Models\Cube\UserCouponModel;
use App\Http\Models\Liexin\UserMainModel;
use App\Http\Services\MessageService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class RegisterCouponExtend extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'RegisterCouponExtend';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //需求上线时间
        $startTime = 1719504000;
        //找出包含新客价活动描述的优惠券
        $couponIds = CouponModel::where('coupon_desc', 'like', '%新客活动%')
            //有效
            ->where('status', 1)
            //领取规则是注册可领取
            ->where('coupon_get_rule', 1)
            //未过期
            ->where('end_time', '>', time())
            //组织id猎芯
            ->where('org_id', 1)->pluck('coupon_id')->toArray();
        //找出这个时间段后面注册的用户并且coupon_extend != 3 的
        $userList = UserMainModel::whereIn('create_device', [1, 2, 4, 6, 7])
            ->where('create_time', '>', $startTime)
            ->where('mobile', '!=', '')
            ->whereIn('coupon_extend', [0, 1, 2])->select([
                'mobile',
                'user_id',
                'coupon_extend'
            ])->get()->toArray();
        dump("一共有" . count($userList) . "位用户需要处理");
        foreach ($userList as $user) {
            if (strlen($user['mobile']) != 11) {
                dump("检测到非正式手机号,跳过 : " . $user['user_id']);
                //continue;
                UserMainModel::where('user_id', $user['user_id'])->update([
                    'coupon_extend' => -1
                ]);
            }
            //找出是否符合发送短信或者延长要求
            //如果已经使用过其中的一个券,那就不用管了
            $isUsed = UserCouponModel::whereIn('coupon_id', $couponIds)
                ->where('user_id', $user['user_id'])
                ->where('end_time', '>', time())
                ->where('use_time', '!=', '')->count();
            if ($isUsed) {
                dump("检测到已使用券的用户,跳过 : " . $user['user_id']);
                //continue;
                UserMainModel::where('user_id', $user['user_id'])->update([
                    'coupon_extend' => -1
                ]);
                continue;
            }
            //获取其中一条记录
            $userCoupon = UserCouponModel::where('user_id', $user['user_id'])->select([
                'user_id',
                'end_time',
            ])->first();
            if (empty($userCoupon)) {
                dump("检测到没有领券的用户,跳过 : " . $user['user_id']);
                //continue;
                UserMainModel::where('user_id', $user['user_id'])->update([
                    'coupon_extend' => -1
                ]);
                continue;
            }
            $userCoupon = $userCoupon->toArray();
            //检查状态
            //if ($userCoupon['end_time'] < time()) {
            //    //已经过期了,不做处理
            //    dump("用户优惠券已经过期,跳过 : " . $user['user_id']);
            //    UserMainModel::where('user_id', $user['user_id'])->update([
            //        'coupon_extend' => -1
            //    ]);
            //    continue;
            //}
            switch ($user['coupon_extend']) {
                //未处理和进行延长后的第二次提醒
                case 0:
                case 2:
                    //再判断过期时间如果是距现在少于等于7天,那么就要去发送短信通知
                    if (($userCoupon['end_time'] - time()) <= 86400 * 7 && ($userCoupon['end_time'] - time()) >= 86400 * 0) {
                        dump('进行提醒处理' . $user['mobile']);
                        //break;
                        //发送短信
                        $result = MessageService::sendSms('register_coupon_extend', [
                            'content' => '尊敬的客户，您的新客优惠券包即将过期，请及时使用，详情点击m.ichunt.com/#/user/coupon'
                        ], $user['mobile']);
                        if ($result) {
                            UserMainModel::where('user_id', $user['user_id'])->update([
                                //如果原始状态是0,那么就是完全没有处理过,到这里就是
                                'coupon_extend' => $user['coupon_extend'] == 0 ? 1 : 3
                            ]);
                        }
                    } else {
                        if ($userCoupon['end_time'] <= time()) {
                            dump('已经过期' . $user['mobile']);
                            //break;
                            UserMainModel::where('user_id', $user['user_id'])->update([
                                'coupon_extend' => 1
                            ]);
                        } else {
                            //还没有过期
                            //dump('还没有到快过期时间' . $user['mobile']);
                            break;
                        }

                    }
                    break;
                case 1:
                    //已经发送过一次提醒了,再判断是否过期,如果过期,则延长时间以及发送短信
                    if ($userCoupon['end_time'] > time()) {
                        break;
                    }
                    dump('进行延长处理' . $user['mobile']);
                    break;
                    $userCouponList = UserCouponModel::where('user_id', $user['user_id'])->whereIn('coupon_id', $couponIds)->select([
                        'user_coupon_id',
                        'coupon_id',
                        'end_time'
                    ]);
                    foreach ($userCouponList as $userCoupon) {
                        $coupon = CouponModel::where('coupon_id', $userCoupon['coupon_id'])->select([
                            'coupon_id',
                            'usable_time',
                            'end_time'
                        ]);
                        //没有过多少天过期的都属于不规范新客券,直接跳过
                        if (empty($coupon['usable_time'])) {
                            continue;
                        }
                        //去延长时间
                        UserCouponModel::where('user_coupon_id', $userCoupon['user_coupon_id'])->update([
                            'end_time' => $userCoupon['end_time'] + $coupon['usable_time'] * 86400
                        ]);
                    }

                    //发送延长通知短信
                    $result = MessageService::sendSms('register_coupon_extend', [
                        'content' => '【猎芯网】尊敬的猎芯网用户您好，您的新客优惠券包已经过期，猎哥偷偷给您上福利，新客优惠券包延期30天，现在下单立享优惠https://m.ichunt.com/'
                    ], $user['mobile']);
                    if ($result && $dataChange) {
                        UserMainModel::where('user_id', $user['user_id'])->update([
                            'coupon_extend' => 2
                        ]);
                    }
                    break;
                default:
            }

        }
    }
}
