<?php

namespace App\Console\Commands;

use App\Http\Models\Cube\ActivityModel;
use App\Http\Models\PayableModel;
use App\Http\Models\PurchaseOrderModel;
use App\Http\Services\PayableService;
use App\Http\Services\PurchaseUserService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DateInterval;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ActivityUpdateStatus extends Command
{
    /**
     * 更新活动上下架
     *
     * @var string
     */
    protected $signature = 'command:activity_update_status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新活动上下架';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        echo date("Y-m-d H:i:s") . " start to  activity_update_status." . "\n";
        $activityModel = new ActivityModel();
        //将启用的活动,并且开始时间小于当前时间,结束时间大于当前时间的活动,状态改为上架,ActivityModel的注解有字段
        $activityModel
            ->where('activity_enable', ActivityModel::ACTIVITY_ENABLE_ENABLE)
            ->where('activity_start_time', '<=', Carbon::now()->getTimestamp())
            ->where('activity_end_time', '>=', Carbon::now()->getTimestamp())
            ->update(['activity_status' => ActivityModel::ACTIVITY_STATUS_ONLINE]);

        //将启用的活动,结束时间小于当前时间的活动,状态改为过期
        $activityModel
            ->where('activity_enable', ActivityModel::ACTIVITY_ENABLE_ENABLE)
            ->where('activity_end_time', '<', Carbon::now()->getTimestamp())
            ->update(['activity_status' => ActivityModel::ACTIVITY_STATUS_EXPIRED]);

        echo date("Y-m-d H:i:s") . " end to  activity_update_status." . "\n";
    }

}
