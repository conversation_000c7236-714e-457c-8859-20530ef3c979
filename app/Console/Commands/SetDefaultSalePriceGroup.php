<?php

namespace App\Console\Commands;

use App\Http\Models\Cube\ActivityModel;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Services\GoodsSalePriceGroupService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DateInterval;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;

class SetDefaultSalePriceGroup extends Command
{
    /**
     * 更新活动上下架
     *
     * @var string
     */
    protected $signature = 'SetDefaultSalePriceGroup:Command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '设置默认的售价组';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {

        //设置代购默认售价组
//        GoodsSalePriceGroupService::SetDefaultSalePriceGroup(1);

        //设置代购全局默认售价组
//        GoodsSalePriceGroupService::setDaiGouGlobalDefaultSalePriceGroup();


        //设置专营默认售价组
//        GoodsSalePriceGroupService::SetDefaultSalePriceGroup(2);

        //设置专营全局售价组
//        GoodsSalePriceGroupService::setZhuanYingGlobalDefaultSalePriceGroup();


        Redis::connection(config("config.ladder_price_redis_connection_name"))
            ->del("magic_cube_price_rule_channel");

        Redis::connection(config("config.ladder_price_redis_connection_name"))
            ->del("magic_cube_price_rule_v2");

        //同步默认售价组  专营和代购  到缓存redis中
        GoodsSalePriceGroupService::syncSalePriceGroupMysqlDataToRedis(1);
        GoodsSalePriceGroupService::syncSalePriceGroupMysqlDataToRedis(2);



    }





}
