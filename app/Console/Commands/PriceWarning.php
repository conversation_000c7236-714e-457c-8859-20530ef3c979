<?php

namespace App\Console\Commands;

use App\Http\Models\Cube\ActivityModel;
use App\Http\Models\Cube\PriceActivityModel;
use App\Http\Models\Cube\PriceWarningModel;
use App\Http\Services\MessageService;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use DateInterval;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class PriceWarning extends Command
{
    /**
     * 更新活动上下架
     *
     * @var string
     */
    protected $signature = 'PriceWarning:Command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '价格预警';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        $list = PriceWarningModel::select("*")->where("status",1)->get()->toArray();
        $count = count($list);
        $msg = sprintf('当前存在%s 条 价格预警未处理，请尽快前往【营销魔方系统-定价管理-价格预警记录】处理。（http://http://cube.ichunt.net）',$count);

        MessageService::sendDinDinAndEmailById("xxxxx",["content"=>$msg],132654);

    }


}
