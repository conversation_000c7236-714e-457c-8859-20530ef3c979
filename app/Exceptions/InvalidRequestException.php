<?php

namespace App\Exceptions;

use App\Http\ApiHelper\Response;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/*
 *无效请求异常
 */

class InvalidRequestException extends \Exception
{

    public function __construct($message = "", $code = 200)
    {
        parent::__construct($message, $code);
    }

    public function render(Request $request)
    {
        $request_uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        $path_info = parse_url($request_uri);
        $err_info = [
            'domain' => isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '',
            'interface' => isset($path_info) ? $path_info['path'] : '',
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'ip' => request()->getClientIp(),
            'time' => time(),
            'other' => '',
            'request_params' => $_REQUEST,
            'msg' => $this->getMessage(),
            "code" => $this->getCode(),
        ];
        Log::error(json_encode($err_info, JSON_UNESCAPED_UNICODE));
        return response()->json(json_decode(Response::setError($this->message), true));
    }
}
