<?php

namespace App\Exceptions;

use App\Http\ApiHelper\Response;
use Illuminate\Http\Request;


/*
 * 系统内部错误
 * 数据库错误
 *
 */

class InternalException extends \Exception
{

    public function __construct($message = "", $code = 200)
    {
        parent::__construct($message, $code);
    }

    public function render(Request $request)
    {
        return response()->json(json_decode(Response::setError($this->message), true));
    }
}
