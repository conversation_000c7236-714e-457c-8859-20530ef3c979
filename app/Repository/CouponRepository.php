<?php

namespace App\Repository;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Http\Models\Cube\UserCouponModel;

class CouponRepository
{
    public function getAvailableCoupons($userId, $orgId = 1)
    {
        return UserCouponModel::query()
            ->with(['coupon'])
            ->where('user_id', $userId)
            ->where('org_id', $orgId)
            ->where('status', UserCouponModel::STATUS_AVAILABLE)
            ->whereHas('coupon', function ($query) {
                $query->where('start_time', '<=', \time());
            })
            ->where('end_time', '>=', \time())
            ->orderBy('end_time', 'asc')
            ->get();
    }

    /**
     * 获取优惠券详细信息
     */
    public static function getInfo($userCouponId, $userId = 0)
    {
        $query = DB::connection('web')->table('user_coupon')
            ->join('coupon', 'coupon.coupon_id', '=', 'user_coupon.coupon_id')
            ->leftJoin('coupon_brand', 'coupon_brand.coupon_id', '=', 'coupon.coupon_id')
            ->where('user_coupon.user_coupon_id', $userCouponId)
            ->selectRaw('
           lie_user_coupon.user_coupon_id,
           lie_user_coupon.order_sn,
           lie_user_coupon.create_time,
           lie_user_coupon.end_time,
           lie_user_coupon.use_time,
           lie_user_coupon.coupon_type,
           lie_user_coupon.status,
           lie_coupon.coupon_sn,
           lie_coupon.coupon_id,
           lie_coupon.coupon_name,
           lie_coupon.require_amount,
           lie_coupon.sale_amount,
           lie_coupon.total_receive_num,
           lie_coupon.day_receive_num,
           lie_coupon.coupon_mall_type,
           lie_coupon.coupon_goods_range,
           lie_coupon.selected_supplier_id,
           lie_coupon.selected_supplier,
           lie_coupon.selected_brand,
           lie_coupon.coupon_get_rule,
           lie_coupon.reg_start_time,
           lie_coupon.max_preferential_amount,
           lie_coupon.exclude_brand_ids,
           lie_coupon.supplier_ids,
           lie_coupon.canals,
            GROUP_CONCAT(lie_coupon_brand.brand_id) as selected_brand_id
        ');

        if (!empty($userId)) {
            $query->where('user_coupon.user_id', $userId);
        }

        return $query->groupBy('user_coupon.user_coupon_id')
            ->first();
    }
}
