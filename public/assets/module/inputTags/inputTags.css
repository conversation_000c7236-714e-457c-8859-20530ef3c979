em {
    font-style: normal;
}

#tags {
    width: 100%;
    margin: 0 auto;
    padding: 10px;
    color: #777;
    border: 1px solid #d5d5d5;
    background-color: #fff;
    float: left;
    box-sizing: border-box;
}

#tags span {
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    position: relative;
    display: inline-block;
    height: 16px;
    margin-right: 3px;
    margin-bottom: 3px;
    padding: 4px 22px 5px 9px;
    cursor: pointer;
    transition: all .2s ease 0s;
    vertical-align: baseline;
    white-space: nowrap;
    color: #fff;
    background-color: #009688;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, .15);
}

#tags .close {
    font-size: 12px;
    font-weight: bold;
    line-height: 20px;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    float: none;
    width: 18px;
    padding: 0;
    cursor: pointer;
    text-align: center;
    opacity: 1;
    color: #fff;
    border: 0 none;
    background: transparent none repeat scroll 0 0;
    text-shadow: none;
}

#tags .close:hover {
    background: #ffb800;
}

#inputTags[type='text'],
#inputTags[type='text']:focus {
    line-height: 25px;
    display: inline;
    width: 150px;
    margin: 0;
    padding: 0 6px;
    border: 0 none;
    outline: 0 none;
    box-shadow: none;
}

.albtn {
    line-height: 30px;
    display: block;
    width: 100px;
    height: 30px;
    margin: 0 auto;
    cursor: pointer;
    text-align: center;
    color: #fff;
    background: #ffb800;
}
