/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.5 (2019-12-19)
 */
!function(l){"use strict";var x=function(){return(x=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function u(n,e){var t={};for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&e.indexOf(r)<0&&(t[r]=n[r]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(n);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(n,r[o])&&(t[r[o]]=n[r[o]])}return t}function w(){}function y(n){return n}var i=function(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}},b=function(n){return function(){return n}};function d(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}function m(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return!t.apply(null,n)}}function o(n){return function(){throw new Error(n)}}function t(n){return n()}function n(){return f}var e,c=b(!1),a=b(!0),f=(e={fold:function(n,e){return n()},is:c,isSome:c,isNone:a,getOr:g,getOrThunk:s,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:b(null),getOrUndefined:b(undefined),or:g,orThunk:s,map:n,each:w,bind:n,exists:c,forall:a,filter:n,equals:r,equals_:r,toArray:function(){return[]},toString:b("none()")},Object.freeze&&Object.freeze(e),e);function r(n){return n.isNone()}function s(n){return n()}function g(n){return n}function S(n,t){return jn(n,function(n,e){return{k:e,v:t(n,e)}})}function p(n,e){return Vn.call(n,e)}function h(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};function r(n){return Number(e.replace(t,"$"+n))}return zn(r(1),r(2))}function v(n,e){return function(){return e===n}}function T(n,e){return function(){return e===n}}function O(e){return function(n){return function(n){if(null===n)return"null";var e=typeof n;return"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e}(n)===e}}function k(n,e){return-1<function(n,e){return se.call(n,e)}(n,e)}function E(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1}function C(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}}function D(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var i=n[r];e(i,r)&&t.push(i)}return t}function M(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t}function I(n,e,t){return C(n,function(n){t=e(t,n)}),t}function R(n,e){for(var t=0,r=n.length;t<r;t++){var o=n[t];if(e(o,t))return Fn.some(o)}return Fn.none()}function A(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return Fn.some(t)}return Fn.none()}function F(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!ie(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);le.apply(e,n[t])}return e}function B(n,e){var t=de(n,e);return F(t)}function V(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0}function N(n){var e=fe.call(n,0);return e.reverse(),e}function j(n,e){return D(n,function(n){return!k(e,n)})}function _(n){return[n]}function P(n,e){var t=String(e).toLowerCase();return R(n,function(n){return n.search(t)})}function H(n,e){return-1!==n.indexOf(e)}function z(e){return function(n){return H(n,e)}}function L(){return be.get()}function G(n,e){Ye(n,n.element(),e,{})}function U(n,e,t){Ye(n,n.element(),e,t)}function $(n){G(n,Pe())}function W(n,e,t){Ye(n,e,t,{})}function X(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}}function q(n){return n.dom().nodeName.toLowerCase()}function Y(e){return function(n){return function(n){return n.dom().nodeType}(n)===e}}function K(n){var e=tt(n)?n.dom().parentNode:n.dom();return e!==undefined&&null!==e&&e.ownerDocument.body.contains(e)}function J(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(e.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+e.length+']", got '+t.length+" arguments");var r={};return C(e,function(n,e){r[n]=b(t[e])}),r}}function Q(n){return n.slice(0).sort()}function Z(e,n){if(!ie(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");C(n,function(n){if(!re(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})}function nn(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return Z("required",o),Z("optional",i),function(n){var t=Q(n);R(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}(u),function(e){var t=Bn(e);V(o,function(n){return k(t,n)})||function(n,e){throw new Error("All required keys ("+Q(n).join(", ")+") were not specified. Specified keys were: "+Q(e).join(", ")+".")}(o,t);var n=D(t,function(n){return!k(u,n)});0<n.length&&function(n){throw new Error("Unsupported keys for object: "+Q(n).join(", "))}(n);var r={};return C(o,function(n){r[n]=b(e[n])}),C(i,function(n){r[n]=b(Object.prototype.hasOwnProperty.call(e,n)?Fn.some(e[n]):Fn.none())}),r}}function en(n,e,t){return 0!=(n.compareDocumentPosition(e)&t)}function tn(n,e){var t=n.dom();if(t.nodeType!==ut)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}function rn(n){return n.nodeType!==ut&&n.nodeType!==ct||0===n.childElementCount}function on(n,e){var t=e===undefined?l.document:e.dom();return rn(t)?[]:de(t.querySelectorAll(n),Je.fromDom)}function un(n,e){var t=e===undefined?l.document:e.dom();return rn(t)?Fn.none():Fn.from(t.querySelector(n)).map(Je.fromDom)}function cn(n,e){return n.dom()===e.dom()}function an(n){return Je.fromDom(n.dom().ownerDocument)}function fn(n){return Fn.from(n.dom().parentNode).map(Je.fromDom)}function sn(n,e){var t=n.dom().childNodes;return Fn.from(t[e]).map(Je.fromDom)}function ln(e,t){fn(e).each(function(n){n.dom().insertBefore(t.dom(),e.dom())})}function dn(n,e){(function(n){return Fn.from(n.dom().nextSibling).map(Je.fromDom)})(n).fold(function(){fn(n).each(function(n){ft(n,e)})},function(n){ln(n,e)})}function mn(e,t){(function(n){return sn(n,0)})(e).fold(function(){ft(e,t)},function(n){e.dom().insertBefore(t.dom(),n.dom())})}function gn(e,n){C(n,function(n){ft(e,n)})}function pn(n){n.dom().textContent="",C(at(n),function(n){st(n)})}function hn(n,e){ft(n.element(),e.element())}function vn(e,n){var t=e.components();!function(n){C(n.components(),function(n){return st(n.element())}),pn(n.element()),n.syncComponents()}(e);var r=j(t,n);C(r,function(n){lt(n),e.getSystem().removeFromWorld(n)}),C(n,function(n){n.getSystem().isConnected()?hn(e,n):(e.getSystem().addToWorld(n),hn(e,n),K(e.element())&&dt(n)),e.syncComponents()})}function yn(e){var n=fn(e.element()).bind(function(n){return e.getSystem().getByDom(n).toOption()});!function(n){lt(n),st(n.element()),n.getSystem().removeFromWorld(n)}(e),n.each(function(n){n.syncComponents()})}function bn(u){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o=n[r];for(var i in o)bt.call(o,i)&&(t[i]=u(t[i],o[i]))}return t}}function xn(n){return St.defaultedThunk(b(n))}function wn(e){return function(n){return p(n,e)?Fn.from(n[e]):Fn.none()}}function Sn(n,e){return wn(e)(n)}function Tn(n,e){var t={};return t[n]=e,t}function On(n,e){return function(n,t){var r={};return Nn(n,function(n,e){k(t,e)||(r[e]=n)}),r}(n,e)}function kn(n,e){return function(e,t){return function(n){return p(n,e)?n[e]:t}}(n,e)}function En(n,e){return Tn(n,e)}function Cn(n){return function(n){var e={};return C(n,function(n){e[n.key]=n.value}),e}(n)}function Dn(n,e){var t=function(n){var e=[],t=[];return C(n,function(n){n.fold(function(n){e.push(n)},function(n){t.push(n)})}),{errors:e,values:t}}(n);return 0<t.errors.length?function(n){return vt.error(F(n))}(t.errors):function(n,e){return 0===n.length?vt.value(e):vt.value(xt(e,wt.apply(undefined,n)))}(t.values,e)}function Mn(n,e){return function(n,e){return p(n,e)&&n[e]!==undefined&&null!==n[e]}(n,e)}var In,Rn,An=function(t){function n(){return o}function e(n){return n(t)}var r=b(t),o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:a,isNone:c,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:n,orThunk:n,map:function(n){return An(n(t))},each:function(n){n(t)},bind:e,exists:e,forall:e,filter:function(n){return n(t)?o:f},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(c,function(n){return e(t,n)})}};return o},Fn={some:An,none:n,from:function(n){return null===n||n===undefined?f:An(n)}},Bn=Object.keys,Vn=Object.hasOwnProperty,Nn=function(n,e){for(var t=Bn(n),r=0,o=t.length;r<o;r++){var i=t[r];e(n[i],i)}},jn=function(n,r){var o={};return Nn(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},_n=function(n,t){var r=[];return Nn(n,function(n,e){r.push(t(n,e))}),r},Pn=function(n){function e(){return t}var t=n;return{get:e,set:function(n){t=n},clone:function(){return Pn(e())}}},Hn=function(){return zn(0,0)},zn=function(n,e){return{major:n,minor:e}},Ln={nu:zn,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?Hn():h(n,t)},unknown:Hn},Gn="Edge",Un="Chrome",$n="Opera",Wn="Firefox",Xn="Safari",qn=function(n){var e=n.current;return{current:e,version:n.version,isEdge:v(Gn,e),isChrome:v(Un,e),isIE:v("IE",e),isOpera:v($n,e),isFirefox:v(Wn,e),isSafari:v(Xn,e)}},Yn={unknown:function(){return qn({current:undefined,version:Ln.unknown()})},nu:qn,edge:b(Gn),chrome:b(Un),ie:b("IE"),opera:b($n),firefox:b(Wn),safari:b(Xn)},Kn="Windows",Jn="Android",Qn="Linux",Zn="Solaris",ne="FreeBSD",ee=function(n){var e=n.current;return{current:e,version:n.version,isWindows:T(Kn,e),isiOS:T("iOS",e),isAndroid:T(Jn,e),isOSX:T("OSX",e),isLinux:T(Qn,e),isSolaris:T(Zn,e),isFreeBSD:T(ne,e)}},te={unknown:function(){return ee({current:undefined,version:Ln.unknown()})},nu:ee,windows:b(Kn),ios:b("iOS"),android:b(Jn),linux:b(Qn),osx:b("OSX"),solaris:b(Zn),freebsd:b(ne)},re=O("string"),oe=O("object"),ie=O("array"),ue=O("boolean"),ce=O("function"),ae=O("number"),fe=Array.prototype.slice,se=Array.prototype.indexOf,le=Array.prototype.push,de=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var i=n[o];r[o]=e(i,o)}return r},me=(ce(Array.from)&&Array.from,function(n,t){return P(n,t).map(function(n){var e=Ln.detect(n.versionRegexes,t);return{current:n.name,version:e}})}),ge=function(n,t){return P(n,t).map(function(n){var e=Ln.detect(n.versionRegexes,t);return{current:n.name,version:e}})},pe=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,he=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return H(n,"edge/")&&H(n,"chrome")&&H(n,"safari")&&H(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,pe],search:function(n){return H(n,"chrome")&&!H(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return H(n,"msie")||H(n,"trident")}},{name:"Opera",versionRegexes:[pe,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:z("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:z("firefox")},{name:"Safari",versionRegexes:[pe,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(H(n,"safari")||H(n,"mobile/"))&&H(n,"applewebkit")}}],ve=[{name:"Windows",search:z("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return H(n,"iphone")||H(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:z("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:z("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:z("linux"),versionRegexes:[]},{name:"Solaris",search:z("sunos"),versionRegexes:[]},{name:"FreeBSD",search:z("freebsd"),versionRegexes:[]}],ye={browsers:b(he),oses:b(ve)},be=Pn(function(n,e){var t=ye.browsers(),r=ye.oses(),o=me(t,n).fold(Yn.unknown,Yn.nu),i=ge(r,n).fold(te.unknown,te.nu);return{browser:o,os:i,deviceType:function(n,e,t,r){var o=n.isiOS()&&!0===/ipad/i.test(t),i=n.isiOS()&&!o,u=n.isiOS()||n.isAndroid(),c=u||r("(pointer:coarse)"),a=o||!i&&u&&r("(min-device-width:768px)"),f=i||u&&!a,s=e.isSafari()&&n.isiOS()&&!1===/safari/i.test(t),l=!f&&!a&&!s;return{isiPad:b(o),isiPhone:b(i),isTablet:b(a),isPhone:b(f),isTouch:b(c),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:b(s),isDesktop:b(l)}}(i,o,n,e)}}(l.navigator.userAgent,function(n){return l.window.matchMedia(n).matches})),xe=b("touchstart"),we=b("touchmove"),Se=b("touchend"),Te=b("mousedown"),Oe=b("mousemove"),ke=b("mouseup"),Ee=b("mouseover"),Ce=b("keydown"),De=b("keyup"),Me=b("input"),Ie=b("change"),Re=b("click"),Ae=b("transitionend"),Fe=b("selectstart"),Be={tap:b("alloy.tap")},Ve=b("alloy.focus"),Ne=b("alloy.blur.post"),je=b("alloy.paste.post"),_e=b("alloy.receive"),Pe=b("alloy.execute"),He=b("alloy.focus.item"),ze=Be.tap,Le=b("alloy.longpress"),Ge=b("alloy.system.init"),Ue=b("alloy.system.attached"),$e=b("alloy.system.detached"),We=b("alloy.focusmanager.shifted"),Xe=b("alloy.highlight"),qe=b("alloy.dehighlight"),Ye=function(n,e,t,r){var o=x({target:e},r);n.getSystem().triggerEvent(t,e,S(o,b))},Ke=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:b(n)}},Je={fromHtml:function(n,e){var t=(e||l.document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw l.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return Ke(t.childNodes[0])},fromTag:function(n,e){var t=(e||l.document).createElement(n);return Ke(t)},fromText:function(n,e){var t=(e||l.document).createTextNode(n);return Ke(t)},fromDom:Ke,fromPoint:function(n,e,t){var r=n.dom();return Fn.from(r.elementFromPoint(e,t)).map(Ke)}},Qe=(l.Node.ATTRIBUTE_NODE,l.Node.CDATA_SECTION_NODE,l.Node.COMMENT_NODE,l.Node.DOCUMENT_NODE),Ze=(l.Node.DOCUMENT_TYPE_NODE,l.Node.DOCUMENT_FRAGMENT_NODE,l.Node.ELEMENT_NODE),nt=l.Node.TEXT_NODE,et=(l.Node.PROCESSING_INSTRUCTION_NODE,l.Node.ENTITY_REFERENCE_NODE,l.Node.ENTITY_NODE,l.Node.NOTATION_NODE,"undefined"!=typeof l.window?l.window:Function("return this;")(),Y(Ze)),tt=Y(nt),rt=X(function(){return ot(Je.fromDom(l.document))}),ot=function(n){var e=n.dom().body;if(null===e||e===undefined)throw new Error("Body is not available yet");return Je.fromDom(e)},it=function(n,e){return en(n,e,l.Node.DOCUMENT_POSITION_CONTAINED_BY)},ut=Ze,ct=Qe,at=(L().browser.isIE(),function(n){return de(n.dom().childNodes,Je.fromDom)}),ft=(J("element","offset"),function(n,e){n.dom().appendChild(e.dom())}),st=function(n){var e=n.dom();null!==e.parentNode&&e.parentNode.removeChild(e)},lt=function(n){G(n,$e());var e=n.components();C(e,lt)},dt=function(n){var e=n.components();C(e,dt),G(n,Ue())},mt=function(n,e,t){n.getSystem().addToWorld(e),t(n.element(),e.element()),K(n.element())&&dt(e),n.syncComponents()},gt=function(n,e,t){t(n,e.element());var r=at(e.element());C(r,function(n){e.getByDom(n).each(dt)})},pt=function(t){return{is:function(n){return t===n},isValue:a,isError:c,getOr:b(t),getOrThunk:b(t),getOrDie:b(t),or:function(n){return pt(t)},orThunk:function(n){return pt(t)},fold:function(n,e){return e(t)},map:function(n){return pt(n(t))},mapError:function(n){return pt(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOption:function(){return Fn.some(t)}}},ht=function(t){return{is:c,isValue:c,isError:a,getOr:y,getOrThunk:function(n){return n()},getOrDie:function(){return o(String(t))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return ht(t)},mapError:function(n){return ht(n(t))},each:w,bind:function(n){return ht(t)},exists:c,forall:a,toOption:Fn.none}},vt={value:pt,error:ht,fromOption:function(n,e){return n.fold(function(){return ht(e)},pt)}},yt=function(u){if(!ie(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return C(u,function(n,r){var e=Bn(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],i=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!ie(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=Bn(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!V(c,function(n){return k(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){l.console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},bt=Object.prototype.hasOwnProperty,xt=bn(function(n,e){return oe(n)&&oe(e)?xt(n,e):e}),wt=bn(function(n,e){return e}),St=yt([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Tt=St.strict,Ot=St.asOption,kt=St.defaultedThunk,Et=St.mergeWithThunk,Ct=(yt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){return wn(n)}),Dt=function(n,e){return Sn(n,e)};(Rn=In=In||{})[Rn.Error=0]="Error",Rn[Rn.Value=1]="Value";function Mt(n,e,t){return n.stype===In.Error?e(n.serror):t(n.svalue)}function It(n){return{stype:In.Value,svalue:n}}function Rt(n){return{stype:In.Error,serror:n}}function At(n){return i(dr,F)(n)}function Ft(n){return oe(n)&&100<Bn(n).length?" removed due to size":JSON.stringify(n,null,2)}function Bt(n,e){return dr([{path:n,getErrorInfo:e}])}function Vt(n,e,t){return Sn(e,t).fold(function(){return function(n,e,t){return Bt(n,function(){return'Could not find valid *strict* value for "'+e+'" in '+Ft(t)})}(n,t,e)},sr)}function Nt(n,e,t){var r=Sn(n,e).fold(function(){return t(n)},y);return sr(r)}function jt(u,c,n,a){return n.fold(function(r,t,n,o){function i(n){var e=o.extract(u.concat([r]),a,n);return pr(e,function(n){return Tn(t,a(n))})}function e(n){return n.fold(function(){var n=Tn(t,a(Fn.none()));return sr(n)},function(n){var e=o.extract(u.concat([r]),a,n);return pr(e,function(n){return Tn(t,a(Fn.some(n)))})})}return n.fold(function(){return mr(Vt(u,c,r),i)},function(n){return mr(Nt(c,r,n),i)},function(){return mr(function(n,e){return sr(Sn(n,e))}(c,r),e)},function(n){return mr(function(e,n,t){var r=Sn(e,n).map(function(n){return!0===n?t(e):n});return sr(r)}(c,r,n),e)},function(n){var e=n(c),t=pr(Nt(c,r,b({})),function(n){return xt(e,n)});return mr(t,i)})},function(n,e){var t=e(c);return sr(Tn(n,a(t)))})}function _t(r){return{extract:function(e,n,t){return gr(r(t,n),function(n){return function(n,e){return Bt(n,function(){return e})}(e,n)})},toString:function(){return"val"},toDsl:function(){return br.itemOf(r)}}}function Pt(n){var i=Sr(n),u=M(n,function(e,n){return n.fold(function(n){return xt(e,En(n,!0))},b(e))},{});return{extract:function(n,e,t){var r=ue(t)?[]:function(e){var n=Bn(e);return D(n,function(n){return Mn(e,n)})}(t),o=D(r,function(n){return!Mn(u,n)});return 0===o.length?i.extract(n,e,t):function(n,e){return Bt(n,function(){return"There are unsupported fields: ["+e.join(", ")+"] specified"})}(n,o)},toString:i.toString,toDsl:i.toDsl}}function Ht(t,i){function u(n,e){return function(o){return{extract:function(t,r,n){var e=de(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return yr(e)},toString:function(){return"array("+o.toString()+")"},toDsl:function(){return br.arrOf(o)}}}(_t(t)).extract(n,y,e)}return{extract:function(t,r,o){var n=Bn(o),e=u(t,n);return mr(e,function(n){var e=de(n,function(n){return wr.field(n,n,Tt(),i)});return Sr(e).extract(t,r,o)})},toString:function(){return"setOf("+i.toString()+")"},toDsl:function(){return br.setOf(t,i)}}}function zt(e,t,r,n,o){return Dt(n,o).fold(function(){return function(n,e,t){return Bt(n,function(){return'The chosen schema: "'+t+'" did not exist in branches: '+Ft(e)})}(e,n,o)},function(n){return n.extract(e.concat(["branch: "+o]),t,r)})}function Lt(n,o){return{extract:function(e,t,r){return Dt(r,n).fold(function(){return function(n,e){return Bt(n,function(){return'Choice schema did not contain choice key: "'+e+'"'})}(e,n)},function(n){return zt(e,t,r,o,n)})},toString:function(){return"chooseOn("+n+"). Possible values: "+Bn(o)},toDsl:function(){return br.choiceOf(n,o)}}}function Gt(e){return _t(function(n){return e(n).fold(dr,sr)})}function Ut(e,n){return Ht(function(n){return ar(e(n))},n)}function $t(n,e,t){return fr(function(n,e,t,r){var o=e.extract([n],t,r);return hr(o,function(n){return{input:r,errors:n}})}(n,e,y,t))}function Wt(n){return n.fold(function(n){throw new Error(Cr(n))},y)}function Xt(n,e,t){return Wt($t(n,e,t))}function qt(n,e){return Lt(n,S(e,Sr))}function Yt(n){return kr(n,n,Tt(),Tr())}function Kt(n,e){return kr(n,n,Tt(),e)}function Jt(n,e){return kr(n,n,Tt(),Sr(e))}function Qt(n){return kr(n,n,Ot(),Tr())}function Zt(n,e){return kr(n,n,Ot(),e)}function nr(n,e){return Zt(n,Sr(e))}function er(n,e){return Zt(n,Pt(e))}function tr(n,e){return kr(n,n,xn(e),Tr())}function rr(n,e,t){return kr(n,n,xn(e),t)}function or(n,e){return Or(n,e)}function ir(n,e){return cn(n.element(),e.event().target())}var ur,cr,ar=function(n){return n.fold(Rt,It)},fr=function(n){return Mt(n,vt.error,vt.value)},sr=It,lr=function(n){var e=[],t=[];return C(n,function(n){Mt(n,function(n){return t.push(n)},function(n){return e.push(n)})}),{values:e,errors:t}},dr=Rt,mr=function(n,e){return n.stype===In.Value?e(n.svalue):n},gr=function(n,e){return n.stype===In.Error?e(n.serror):n},pr=function(n,e){return n.stype===In.Value?{stype:In.Value,svalue:e(n.svalue)}:n},hr=function(n,e){return n.stype===In.Error?{stype:In.Error,serror:e(n.serror)}:n},vr=function(n,e){var t=lr(n);return 0<t.errors.length?At(t.errors):function(n,e){return 0<n.length?sr(xt(e,wt.apply(undefined,n))):sr(e)}(t.values,e)},yr=function(n){var e=lr(n);return 0<e.errors.length?At(e.errors):sr(e.values)},br=yt([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),xr=yt([{field:["name","presence","type"]},{state:["name"]}]),wr=yt([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Sr=function(r){return{extract:function(n,e,t){return function(e,t,n,r){var o=de(n,function(n){return jt(e,t,n,r)});return vr(o,{})}(n,t,r,e)},toString:function(){return"obj{\n"+de(r,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return br.objOf(de(r,function(n){return n.fold(function(n,e,t,r){return xr.field(n,t,r)},function(n,e){return xr.state(n)})}))}}},Tr=b(_t(sr)),Or=wr.state,kr=wr.field,Er=_t(sr),Cr=function(n){return"Errors: \n"+function(n){var e=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return de(e,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors)+"\n\nInput object: "+Ft(n.input)},Dr=b(Er),Mr=(ur=ce,cr="function",_t(function(n){var e=typeof n;return ur(n)?sr(n):dr("Expected type: "+cr+" but got: "+e)}));function Ir(n,e,t,r,o){return n(t,r)?Fn.some(t):ce(o)&&o(t)?Fn.none():e(t,r,o)}function Rr(n,e,t){for(var r=n.dom(),o=ce(t)?t:b(!1);r.parentNode;){r=r.parentNode;var i=Je.fromDom(r);if(e(i))return Fn.some(i);if(o(i))break}return Fn.none()}function Ar(n,e,t){return Ir(function(n,e){return e(n)},Rr,n,e,t)}function Fr(n,o){var i=function(n){for(var e=0;e<n.childNodes.length;e++){var t=Je.fromDom(n.childNodes[e]);if(o(t))return Fn.some(t);var r=i(n.childNodes[e]);if(r.isSome())return r}return Fn.none()};return i(n.dom())}function Br(n){if(!Mn(n,"can")&&!Mn(n,"abort")&&!Mn(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return Xt("Extracting event.handler",Pt([tr("can",b(!0)),tr("abort",b(!1)),tr("run",w)]),n)}function Vr(t){var n=function(e,r){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return I(e,function(n,e){return n&&r(e).apply(undefined,t)},!0)}}(t,function(n){return n.can}),e=function(e,r){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return I(e,function(n,e){return n||r(e).apply(undefined,t)},!1)}}(t,function(n){return n.abort});return Br({can:n,abort:e,run:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];C(t,function(n){n.run.apply(undefined,e)})}})}function Nr(n){return Cn(n)}function jr(n,e){return{key:n,value:Br({abort:e})}}function _r(n,e){return{key:n,value:Br({run:e})}}function Pr(n,t,r){return{key:n,value:Br({run:function(n,e){t.apply(undefined,[n,e].concat(r))}})}}function Hr(n){return function(t){return{key:n,value:Br({run:function(n,e){ir(n,e)&&t(n,e)}})}}}function zr(n,e,t){return function(t,r){return _r(t,function(n,e){n.getSystem().getByUid(r).each(function(n){!function(n,e,t,r){n.getSystem().triggerEvent(t,e,r.event())}(n,n.element(),t,e)})})}(n,e.partUids[t])}function Lr(n){return _r(n,function(n,e){e.cut()})}function Gr(n,e){var t=n.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Io(i)}},n}function Ur(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}}function $r(t,r,o){return Do(function(n,e){o(n,t,r)})}function Wr(o,i,u){return function(n,e,t){var r=t.toString(),o=r.indexOf(")")+1,i=r.indexOf("("),u=r.substring(i+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:e,parameters:Io(u.slice(0,1).concat(u.slice(3)))}},n}(function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];var r=[t].concat(n);return t.config({name:b(o)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+o+". Using API: "+u)},function(n){var e=Array.prototype.slice.call(r,1);return i.apply(undefined,[t,n.config,n.state].concat(e))})},u,i)}function Xr(n){return{key:n,value:undefined}}function qr(n){var e=Xt("Creating behaviour: "+n.name,Vo,n);return function(n,e,t,r,o,i){var u=Pt(n),c=nr(e,[er("config",n)]);return Ro(u,c,e,t,r,o,i)}(e.fields,e.name,e.active,e.apis,e.extra,e.state)}function Yr(n,e,t){if(!(re(t)||ue(t)||ae(t)))throw l.console.error("Invalid call to Attr.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")}function Kr(n,e,t){Yr(n.dom(),e,t)}function Jr(n,e){var t=n.dom();Nn(e,function(n,e){Yr(t,e,n)})}function Qr(n,e){var t=n.dom().getAttribute(e);return null===t?undefined:t}function Zr(n,e){var t=n.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(e)}function no(n,e){n.dom().removeAttribute(e)}function eo(n,e){var t=Qr(n,e);return t===undefined||""===t?[]:t.split(" ")}function to(n){return n.dom().classList!==undefined}function ro(n,e){return function(n,e,t){var r=eo(n,e).concat([t]);return Kr(n,e,r.join(" ")),!0}(n,"class",e)}function oo(n,e){return function(n,e,t){var r=D(eo(n,e),function(n){return n!==t});return 0<r.length?Kr(n,e,r.join(" ")):no(n,e),!1}(n,"class",e)}function io(n,e){to(n)?n.dom().classList.add(e):ro(n,e)}function uo(n){0===(to(n)?n.dom().classList:function(n){return eo(n,"class")}(n)).length&&no(n,"class")}function co(n,e){to(n)?n.dom().classList.remove(e):oo(n,e),uo(n)}function ao(n,e){return to(n)&&n.dom().classList.contains(e)}function fo(n,e,t){co(n,t),io(n,e)}function so(n){n.dom().focus()}function lo(n){n.dom().blur()}function mo(n){var e=n!==undefined?n.dom():l.document;return Fn.from(e.activeElement).map(Je.fromDom)}function go(e){return mo(an(e)).filter(function(n){return e.dom().contains(n.dom())})}function po(n){return n.dom().innerHTML}function ho(n,e){var t=an(n).dom(),r=Je.fromDom(t.createDocumentFragment()),o=function(n,e){var t=(e||l.document).createElement("div");return t.innerHTML=n,at(Je.fromDom(t))}(e,t);gn(r,o),pn(n),ft(n,r)}function vo(n){return function(n,e){return Je.fromDom(n.dom().cloneNode(e))}(n,!1)}function yo(n){return function(n){var e=Je.fromTag("div"),t=Je.fromDom(n.dom().cloneNode(!0));return ft(e,t),po(e)}(vo(n))}function bo(n){return yo(n)}function xo(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e}function wo(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return Fn.none()}var So,To,Oo,ko=function(n,e,t){return Ar(n,function(n){return e(n).isSome()},t).bind(e)},Eo=Hr(Ue()),Co=Hr($e()),Do=Hr(Ge()),Mo=(So=Pe(),function(n){return _r(So,n)}),Io=function(n){return de(n,function(n){return function(n,e){return function(n,e,t){return""===e||!(n.length<e.length)&&n.substr(t,t+e.length)===e}(n,e,n.length-e.length)}(n,"/*")?n.substring(0,n.length-"/*".length):n})},Ro=function(t,n,r,o,e,i,u){function c(n){return Mn(n,r)?n[r]():Fn.none()}var a=S(e,function(n,e){return Wr(r,n,e)}),f=S(i,function(n,e){return Gr(n,e)}),s=x(x(x({},f),a),{revoke:d(Xr,r),config:function(n){var e=Xt(r+"-config",t,n);return{key:r,value:{config:e,me:s,configAsRaw:X(function(){return Xt(r+"-config",t,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,t){return c(n).bind(function(e){return Dt(o,"exhibit").map(function(n){return n(t,e.config,e.state)})}).getOr(Ur({}))},name:function(){return r},handlers:function(n){return c(n).map(function(n){return kn("events",function(n,e){return{}})(o)(n.config,n.state)}).getOr({})}});return s},Ao={init:function(){return Fo({readState:function(){return"No State required"}})}},Fo=function(n){return n},Bo=function(n){return Cn(n)},Vo=Pt([Yt("fields"),Yt("name"),tr("active",{}),tr("apis",{}),tr("state",Ao),tr("extra",{})]),No=Pt([Yt("branchKey"),Yt("branches"),Yt("name"),tr("active",{}),tr("apis",{}),tr("state",Ao),tr("extra",{})]),jo=b(undefined),_o=/* */Object.freeze({toAlpha:function(n,e,t){fo(n.element(),e.alpha,e.omega)},toOmega:function(n,e,t){fo(n.element(),e.omega,e.alpha)},isAlpha:function(n,e,t){return ao(n.element(),e.alpha)},isOmega:function(n,e,t){return ao(n.element(),e.omega)},clear:function(n,e,t){co(n.element(),e.alpha),co(n.element(),e.omega)}}),Po=[Yt("alpha"),Yt("omega")],Ho=qr({fields:Po,name:"swapping",apis:_o}),zo=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Lo=tinymce.util.Tools.resolve("tinymce.ThemeManager"),Go=function(n){var e=l.document.createElement("a");e.target="_blank",e.href=n.href,e.rel="noreferrer noopener";var t=l.document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,l.window,0,0,0,0,0,!1,!1,!1,!1,0,null),l.document.body.appendChild(e),e.dispatchEvent(t),l.document.body.removeChild(e)},Uo={formatChanged:b("formatChanged"),orientationChanged:b("orientationChanged"),dropupDismissed:b("dropupDismissed")},$o=/* */Object.freeze({events:function(e){return Nr([_r(_e(),function(o,i){var u=e.channels,n=function(n,e){return e.universal()?n:D(n,function(n){return k(e.channels(),n)})}(Bn(u),i);C(n,function(n){var e=u[n],t=e.schema,r=Xt("channel["+n+"] data\nReceiver: "+bo(o.element()),t,i.data());e.onReceive(o,r)})})])}}),Wo="unknown";(Oo=To=To||{})[Oo.STOP=0]="STOP",Oo[Oo.NORMAL=1]="NORMAL",Oo[Oo.LOGGING=2]="LOGGING";function Xo(e,n,t){switch(Dt(yi.get(),e).orThunk(function(){var n=Bn(yi.get());return wo(n,function(n){return-1<e.indexOf(n)?Fn.some(yi.get()[n]):Fn.none()})}).getOr(To.NORMAL)){case To.NORMAL:return t(xi());case To.LOGGING:var r=function(e,t){var r=[],o=(new Date).getTime();return{logEventCut:function(n,e,t){r.push({outcome:"cut",target:e,purpose:t})},logEventStopped:function(n,e,t){r.push({outcome:"stopped",target:e,purpose:t})},logNoParent:function(n,e,t){r.push({outcome:"no-parent",target:e,purpose:t})},logEventNoHandlers:function(n,e){r.push({outcome:"no-handlers-left",target:e})},logEventResponse:function(n,e,t){r.push({outcome:"response",purpose:t,target:e})},write:function(){var n=(new Date).getTime();k(["mousemove","mouseover","mouseout",Ge()],e)||l.console.log(e,{event:e,time:n-o,target:t.dom(),sequence:de(r,function(n){return k(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+bo(n.target)+")":n.outcome})})}}}(e,n),o=t(r);return r.write(),o;case To.STOP:return!0}}function qo(n,e,t){return Xo(n,e,t)}function Yo(n,e,t){return function(){var n=new Error;if(n.stack===undefined)return;var e=n.stack.split("\n");R(e,function(e){return 0<e.indexOf("alloy")&&!E(bi,function(n){return-1<e.indexOf(n)})}).getOr(Wo)}(),kr(e,e,t,Gt(function(t){return vt.value(function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t.apply(undefined,n)})}))}function Ko(n){return Yo(0,n,xn(w))}function Jo(n){return Yo(0,n,xn(Fn.none))}function Qo(n){return Yo(0,n,Tt())}function Zo(n){return Yo(0,n,Tt())}function ni(n,e){return or(n,b(e))}function ei(n){return or(n,y)}function ti(n,e,t){var r=e.aria;r.update(n,r,t.get())}function ri(e,n,t){n.toggleClass.each(function(n){t.get()?io(e.element(),n):co(e.element(),n)})}function oi(n,e,t){Di(n,e,t,!t.get())}function ii(n,e,t){t.set(!0),ri(n,e,t),ti(n,e,t)}function ui(n,e,t){t.set(!1),ri(n,e,t),ti(n,e,t)}function ci(n,e,t){Di(n,e,t,e.selected)}function ai(){function n(n,e){e.stop(),$(n)}return[_r(Re(),n),_r(ze(),n),Lr(xe()),Lr(Te())]}function fi(n,e){e.ignore||(so(n.element()),e.onFocus(n))}function si(n){return n.style!==undefined&&ce(n.style.getPropertyValue)}function li(n,e,t){if(!re(t))throw l.console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);si(n)&&n.style.setProperty(e,t)}function di(n,e){var t=n.dom();Nn(e,function(n,e){li(t,e,n)})}function mi(n,e){var t=n.dom(),r=l.window.getComputedStyle(t).getPropertyValue(e),o=""!==r||K(n)?r:Ui(t,e);return null===o?undefined:o}function gi(n,e){var t=n.dom(),r=Ui(t,e);return Fn.from(r).filter(function(n){return 0<n.length})}function pi(n,e){!function(n,e){si(n)&&n.style.removeProperty(e)}(n.dom(),e),Zr(n,"style")&&""===function(n){return n.replace(/^\s+|\s+$/g,"")}(Qr(n,"style"))&&no(n,"style")}function hi(n){return n.dom().offsetWidth}var vi,yi=Pn({}),bi=["alloy/data/Fields","alloy/debugging/Debugging"],xi=b({logEventCut:w,logEventStopped:w,logNoParent:w,logEventNoHandlers:w,logEventResponse:w,write:w}),wi=b([Yt("menu"),Yt("selectedMenu")]),Si=b([Yt("item"),Yt("selectedItem")]),Ti=(b(Sr(Si().concat(wi()))),b(Sr(Si()))),Oi=Jt("initSize",[Yt("numColumns"),Yt("numRows")]),ki=b(Oi),Ei=[Kt("channels",Ut(vt.value,Pt([Qo("onReceive"),tr("schema",Dr())])))],Ci=qr({fields:Ei,name:"receiving",active:$o}),Di=function(n,e,t,r){(r?ii:ui)(n,e,t)},Mi=/* */Object.freeze({onLoad:ci,toggle:oi,isOn:function(n,e,t){return t.get()},on:ii,off:ui,set:Di}),Ii=/* */Object.freeze({exhibit:function(n,e,t){return Ur({})},events:function(n,e){var t=function(e,t,r){return Mo(function(n){r(n,e,t)})}(n,e,oi),r=$r(n,e,ci);return Nr(F([n.toggleOnExecute?[t]:[],[r]]))}}),Ri=function(n,e,t){Kr(n.element(),"aria-expanded",t)},Ai=[tr("selected",!1),Qt("toggleClass"),tr("toggleOnExecute",!0),rr("aria",{mode:"none"},qt("mode",{pressed:[tr("syncWithExpanded",!1),ni("update",function(n,e,t){Kr(n.element(),"aria-pressed",t),e.syncWithExpanded&&Ri(n,e,t)})],checked:[ni("update",function(n,e,t){Kr(n.element(),"aria-checked",t)})],expanded:[ni("update",Ri)],selected:[ni("update",function(n,e,t){Kr(n.element(),"aria-selected",t)})],none:[ni("update",w)]}))],Fi=qr({fields:Ai,name:"toggling",active:Ii,apis:Mi,state:(vi=!1,{init:function(){var e=Pn(vi);return{get:function(){return e.get()},set:function(n){return e.set(n)},clear:function(){return e.set(vi)},readState:function(){return e.get()}}}})}),Bi=function(t,r){return Ci.config({channels:En(Uo.formatChanged(),{onReceive:function(n,e){e.command===t&&r(n,e.state)}})})},Vi=function(n){return Ci.config({channels:En(Uo.orientationChanged(),{onReceive:n})})},Ni=function(n,e){return{key:n,value:{onReceive:e}}},ji="tinymce-mobile",_i={resolve:function(n){return ji+"-"+n},prefix:b(ji)},Pi=/* */Object.freeze({focus:fi,blur:function(n,e){e.ignore||lo(n.element())},isFocused:function(n){return function(n){var e=an(n).dom();return n.dom()===e.activeElement}(n.element())}}),Hi=/* */Object.freeze({exhibit:function(n,e){var t=e.ignore?{}:{attributes:{tabindex:"-1"}};return Ur(t)},events:function(t){return Nr([_r(Ve(),function(n,e){fi(n,t),e.stop()})].concat(t.stopMousedown?[_r(Te(),function(n,e){e.event().prevent()})]:[]))}}),zi=[Ko("onFocus"),tr("stopMousedown",!1),tr("ignore",!1)],Li=qr({fields:zi,name:"focusing",active:Hi,apis:Pi}),Gi=function(n,e,t){var r=n.dom();li(r,e,t)},Ui=function(n,e){return si(n)?n.style.getPropertyValue(e):""};function $i(r,o){function n(n){var e=o(n);if(e<=0||null===e){var t=mi(n,r);return parseFloat(t)||0}return e}function i(o,n){return I(n,function(n,e){var t=mi(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)}return{set:function(n,e){if(!ae(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom();si(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:i,max:function(n,e,t){var r=i(n,t);return r<e?e-r:0}}}function Wi(n){return yu.get(n)}function Xi(n,e,t){return D(function(n,e){for(var t=ce(e)?e:c,r=n.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=Je.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}(n,t),e)}function qi(n,e){return D(function(e){return fn(e).map(at).map(function(n){return D(n,function(n){return!cn(e,n)})}).getOr([])}(n),e)}function Yi(n,e){return on(e,n)}function Ki(n){return un(n)}function Ji(n,e,t){return Rr(n,function(n){return tn(n,e)},t)}function Qi(n,e){return un(e,n)}function Zi(n,e,t){return Ir(tn,Ji,n,e,t)}function nu(n,e,t){var r=N(n.slice(0,e)),o=N(n.slice(e+1));return R(r.concat(o),t)}function eu(n,e,t){var r=N(n.slice(0,e));return R(r,t)}function tu(n,e,t){var r=n.slice(0,e),o=n.slice(e+1);return R(o.concat(r),t)}function ru(n,e,t){var r=n.slice(e+1);return R(r,t)}function ou(t){return function(n){var e=n.raw();return k(t,e.which)}}function iu(n){return function(e){return V(n,function(n){return n(e)})}}function uu(n){return!0===n.raw().shiftKey}function cu(n){return!0===n.raw().ctrlKey}function au(n,e){return{matches:n,classification:e}}function fu(n,e,t,r){var o=n+e;return r<o?t:o<t?r:o}function su(n,e,t){return n<=e?e:t<=n?t:n}function lu(t,r,n,o){var e=Yi(t.element(),"."+r.highlightClass);C(e,function(e){E(o,function(n){return n.element()===e})||(co(e,r.highlightClass),t.getSystem().getByDom(e).each(function(n){r.onDehighlight(t,n),G(n,qe())}))})}function du(n,e,t,r){lu(n,e,0,[r]),xu(n,e,t,r)||(io(r.element(),e.highlightClass),e.onHighlight(n,r),G(r,Xe()))}function mu(t,e,n,r){var o=Yi(t.element(),"."+e.itemClass);return A(o,function(n){return ao(n,e.highlightClass)}).bind(function(n){var e=fu(n,r,0,o.length-1);return t.getSystem().getByDom(o[e]).toOption()})}function gu(n,e,t){e.exists(function(e){return t.exists(function(n){return cn(n,e)})})||U(n,We(),{prevFocus:e,newFocus:t})}function pu(){function o(n){return go(n.element())}return{get:o,set:function(n,e){var t=o(n);n.getSystem().triggerFocus(e,n.element());var r=o(n);gu(n,t,r)}}}var hu,vu,yu=$i("height",function(n){var e=n.dom();return K(n)?e.getBoundingClientRect().height:e.offsetHeight}),bu=m(uu),xu=function(n,e,t,r){return ao(r.element(),e.highlightClass)},wu=function(n,e,t,r){var o=Yi(n.element(),"."+e.itemClass);return Fn.from(o[r]).fold(function(){return vt.error("No element found with index "+r)},n.getSystem().getByDom)},Su=function(e,n,t){return Qi(e.element(),"."+n.itemClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},Tu=function(e,n,t){var r=Yi(e.element(),"."+n.itemClass);return(0<r.length?Fn.some(r[r.length-1]):Fn.none()).bind(function(n){return e.getSystem().getByDom(n).toOption()})},Ou=function(e,n,t){var r=Yi(e.element(),"."+n.itemClass);return xo(de(r,function(n){return e.getSystem().getByDom(n).toOption()}))},ku=/* */Object.freeze({dehighlightAll:function(n,e,t){return lu(n,e,0,[])},dehighlight:function(n,e,t,r){xu(n,e,t,r)&&(co(r.element(),e.highlightClass),e.onDehighlight(n,r),G(r,qe()))},highlight:du,highlightFirst:function(e,t,r){Su(e,t).each(function(n){du(e,t,r,n)})},highlightLast:function(e,t,r){Tu(e,t).each(function(n){du(e,t,r,n)})},highlightAt:function(e,t,r,n){wu(e,t,r,n).fold(function(n){throw new Error(n)},function(n){du(e,t,r,n)})},highlightBy:function(e,t,r,n){var o=Ou(e,t);R(o,n).each(function(n){du(e,t,r,n)})},isHighlighted:xu,getHighlighted:function(e,n,t){return Qi(e.element(),"."+n.highlightClass).bind(function(n){return e.getSystem().getByDom(n).toOption()})},getFirst:Su,getLast:Tu,getPrevious:function(n,e,t){return mu(n,e,0,-1)},getNext:function(n,e,t){return mu(n,e,0,1)},getCandidates:Ou}),Eu=[Yt("highlightClass"),Yt("itemClass"),Ko("onHighlight"),Ko("onDehighlight")],Cu=qr({fields:Eu,name:"highlighting",apis:ku});(vu=hu=hu||{}).OnFocusMode="onFocus",vu.OnEnterOrSpaceMode="onEnterOrSpace",vu.OnApiMode="onApi";function Du(n,e,t,i,u){function c(e,t,n,r,o){return function(n,e){return R(n,function(n){return n.matches(e)}).map(function(n){return n.classification})}(n(e,t,r,o),t.event()).bind(function(n){return n(e,t,r,o)})}var r={schema:function(){return n.concat([tr("focusManager",pu()),rr("focusInside","onFocus",Gt(function(n){return k(["onFocus","onEnterOrSpace","onApi"],n)?vt.value(n):vt.error("Invalid value for focusInside")})),ni("handler",r),ni("state",e),ni("sendFocusIn",u)])},processKey:c,toEvents:function(r,o){var n=r.focusInside!==hu.OnFocusMode?Fn.none():u(r).map(function(t){return _r(Ve(),function(n,e){t(n,r,o),e.stop()})});return Nr(n.toArray().concat([_r(Ce(),function(n,e){c(n,e,t,r,o).fold(function(){!function(e,t){var n=ou([32].concat([13]))(t.event());r.focusInside===hu.OnEnterOrSpaceMode&&n&&ir(e,t)&&u(r).each(function(n){n(e,r,o),t.stop()})}(n,e)},function(n){e.stop()})}),_r(De(),function(n,e){c(n,e,i,r,o).each(function(n){e.stop()})})]))}};return r}function Mu(n){function i(n,e){var t=n.visibilitySelector.bind(function(n){return Zi(e,n)}).getOr(e);return 0<Wi(t)}function e(e,t){(function(n,e){var t=Yi(n.element(),e.selector),r=D(t,function(n){return i(e,n)});return Fn.from(r[e.firstTabstop])})(e,t).each(function(n){t.focusManager.set(e,n)})}function u(e,n,t,r,o){return o(n,t,function(n){return function(n,e){return i(n,e)&&n.useTabstopAt(e)}(r,n)}).fold(function(){return r.cyclic?Fn.some(!0):Fn.none()},function(n){return r.focusManager.set(e,n),Fn.some(!0)})}function c(e,n,t,r){var o=Yi(e.element(),t.selector);return function(n,e){return e.focusManager.get(n).bind(function(n){return Zi(n,e.selector)})}(e,t).bind(function(n){return A(o,d(cn,n)).bind(function(n){return u(e,o,n,t,r)})})}var t=[Qt("onEscape"),Qt("onEnter"),tr("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),tr("firstTabstop",0),tr("useTabstopAt",b(!0)),Qt("visibilitySelector")].concat([n]),r=b([au(iu([uu,ou([9])]),function(n,e,t,r){var o=t.cyclic?nu:eu;return c(n,0,t,o)}),au(ou([9]),function(n,e,t,r){var o=t.cyclic?tu:ru;return c(n,0,t,o)}),au(ou([27]),function(e,t,n,r){return n.onEscape.bind(function(n){return n(e,t)})}),au(iu([bu,ou([13])]),function(e,t,n,r){return n.onEnter.bind(function(n){return n(e,t)})})]),o=b([]);return Du(t,Ao.init,r,o,function(){return Fn.some(e)})}function Iu(n){return"input"===q(n)&&"radio"!==Qr(n,"type")||"textarea"===q(n)}function Ru(n,e,t){return Iu(t)&&ou([32])(e.event())?Fn.none():function(n,e,t){return W(n,t,Pe()),Fn.some(!0)}(n,0,t)}function Au(n,e){return Fn.some(!0)}function Fu(n,e,t){return t.execute(n,e,n.element())}function Bu(n){var t=Pn(Fn.none());return Fo({readState:function(){return t.get().map(function(n){return{numRows:n.numRows(),numColumns:n.numColumns()}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,e){t.set(Fn.some({numRows:b(n),numColumns:b(e)}))},getNumRows:function(){return t.get().map(function(n){return n.numRows()})},getNumColumns:function(){return t.get().map(function(n){return n.numColumns()})}})}function Vu(e,t){return function(n){return"rtl"===Yc(n)?t:e}}function Nu(i){return function(n,e,t,r){var o=i(n.element());return Kc(o,n,e,t,r)}}function ju(n,e){var t=Vu(n,e);return Nu(t)}function _u(n,e){var t=Vu(e,n);return Nu(t)}function Pu(o){return function(n,e,t,r){return Kc(o,n,e,t,r)}}function Hu(n){return!function(n){return n.offsetWidth<=0&&n.offsetHeight<=0}(n.dom())}function zu(n,e,t){var r=d(cn,e),o=Yi(n,t);return function(e,n){return A(e,n).map(function(n){return na({index:n,candidates:e})})}(D(o,Hu),r)}function Lu(n,e){return A(n,function(n){return cn(e,n)})}function Gu(t,n,r,e){return e(Math.floor(n/r),n%r).bind(function(n){var e=n.row()*r+n.column();return 0<=e&&e<t.length?Fn.some(t[e]):Fn.none()})}function Uu(o,n,i,u,c){return Gu(o,n,u,function(n,e){var t=n===i-1?o.length-n*u:u,r=fu(e,c,0,t-1);return Fn.some({row:b(n),column:b(r)})})}function $u(i,n,u,c,a){return Gu(i,n,c,function(n,e){var t=fu(n,a,0,u-1),r=t===u-1?i.length-t*c:c,o=su(e,0,r-1);return Fn.some({row:b(t),column:b(o)})})}function Wu(e,t,n){Qi(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})}function Xu(o){return function(n,e,t,r){return zu(n,e,t.selector).bind(function(n){return o(n.candidates(),n.index(),r.getNumRows().getOr(t.initSize.numRows),r.getNumColumns().getOr(t.initSize.numColumns))})}}function qu(n,e,t,r){return t.captureTab?Fn.some(!0):Fn.none()}function Yu(n,e,t,o){var i=function(n,e,t){var r=fu(e,o,0,t.length-1);return r===n?Fn.none():function(n){return"button"===q(n)&&"disabled"===Qr(n,"disabled")}(t[r])?i(n,r,t):Fn.from(t[r])};return zu(n,t,e).bind(function(n){var e=n.index(),t=n.candidates();return i(e,e,t)})}function Ku(e,t,r){return function(n,e){return e.focusManager.get(n).bind(function(n){return Zi(n,e.selector)})}(e,r).bind(function(n){return r.execute(e,t,n)})}function Ju(e,t){t.getInitial(e).orThunk(function(){return Qi(e.element(),t.selector)}).each(function(n){t.focusManager.set(e,n)})}function Qu(n,e,t){return Yu(n,t.selector,e,-1)}function Zu(n,e,t){return Yu(n,t.selector,e,1)}function nc(r){return function(n,e,t){return r(n,e,t).bind(function(){return t.executeOnMove?Ku(n,e,t):Fn.some(!0)})}}function ec(n,e,t,r){return t.onEscape(n,e)}function tc(n,e,t){return Fn.from(n[e]).bind(function(n){return Fn.from(n[t]).map(function(n){return da({rowIndex:e,columnIndex:t,cell:n})})})}function rc(n,e,t,r){var o=n[e].length,i=fu(t,r,0,o-1);return tc(n,e,i)}function oc(n,e,t,r){var o=fu(t,r,0,n.length-1),i=n[o].length,u=su(e,0,i-1);return tc(n,o,u)}function ic(n,e,t,r){var o=n[e].length,i=su(t+r,0,o-1);return tc(n,e,i)}function uc(n,e,t,r){var o=su(t+r,0,n.length-1),i=n[o].length,u=su(e,0,i-1);return tc(n,o,u)}function cc(e,t){t.previousSelector(e).orThunk(function(){var n=t.selectors;return Qi(e.element(),n.cell)}).each(function(n){t.focusManager.set(e,n)})}function ac(n,e){return function(o,t,i){var u=i.cycles?n:e;return Zi(t,i.selectors.row).bind(function(n){var e=Yi(n,i.selectors.cell);return Lu(e,t).bind(function(t){var r=Yi(o,i.selectors.row);return Lu(r,n).bind(function(n){var e=function(n,e){return de(n,function(n){return Yi(n,e.selectors.cell)})}(r,i);return u(e,n,t).map(function(n){return n.cell()})})})})}}function fc(e,t,r){return r.focusManager.get(e).bind(function(n){return r.execute(e,t,n)})}function sc(e,t){Qi(e.element(),t.selector).each(function(n){t.focusManager.set(e,n)})}function lc(n,e,t){return Yu(n,t.selector,e,-1)}function dc(n,e,t){return Yu(n,t.selector,e,1)}function mc(e,n){return function(n,e,t){return rr(n,e,Sr(t))}(e,{},de(n,function(n){return function(e,t){return kr(e,e,Ot(),_t(function(n){return dr("The field: "+e+" is forbidden. "+t)}))}(n.name(),"Cannot configure "+n.name()+" for "+e)}).concat([or("dump",y)]))}function gc(n){return n.dump}function pc(n,e){return x(x({},n.dump),Bo(e))}function hc(n,e,t,r){return t.uiType===_a?function(n,e,t,r){return n.exists(function(n){return n!==t.owner})?Pa.single(!0,b(t)):Dt(r,t.name).fold(function(){throw new Error("Unknown placeholder component: "+t.name+"\nKnown: ["+Bn(r)+"]\nNamespace: "+n.getOr("none")+"\nSpec: "+JSON.stringify(t,null,2))},function(n){return n.replace()})}(n,0,t,r):Pa.single(!1,b(t))}function vc(e,t,n,r){var o=S(r,function(n,e){return function(n,e){var t=!1;return{name:b(n),required:function(){return e.fold(function(n,e){return n},function(n,e){return n})},used:function(){return t},replace:function(){if(!0===t)throw new Error("Trying to use the same placeholder more than once: "+n);return t=!0,e}}}(e,n)}),i=function(e,t,n,r){return B(n,function(n){return Ha(e,t,n,r)})}(e,t,n,o);return Nn(o,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))}),i}function yc(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++Ua+String(e)}function bc(n){function e(n){return n.name}return n.fold(e,e,e,e)}function xc(t,r){return function(n){var e=Xt("Converting part type",r,n);return t(e)}}function wc(n,e,t,r){return xt(e.defaults(n,t,r),t,{uid:n.partUids[e.name]},e.overrides(n,t,r))}function Sc(o,n){var e={};return C(n,function(n){(function(n){return n.fold(Fn.some,Fn.none,Fn.some,Fn.some)})(n).each(function(t){var r=cf(o,t.pname);e[t.name]=function(n){var e=Xt("Part: "+t.name+" in "+o,Sr(t.schema),n);return x(x({},r),{config:n,validated:e})}})}),e}function Tc(n,e,t){return function(n,t,e){var i={},r={};return C(e,function(n){n.fold(function(r){i[r.pname]=za(!0,function(n,e,t){return r.factory.sketch(wc(n,r,e,t))})},function(n){var e=t.parts[n.name];r[n.name]=b(n.factory.sketch(wc(t,n,e[uf()]),e))},function(r){i[r.pname]=za(!1,function(n,e,t){return r.factory.sketch(wc(n,r,e,t))})},function(o){i[o.pname]=La(!0,function(e,n,t){var r=e[o.name];return de(r,function(n){return o.factory.sketch(xt(o.defaults(e,n,t),n,o.overrides(e,n)))})})})}),{internals:b(i),externals:b(r)}}(0,e,t)}function Oc(n,e,t){return vc(Fn.some(n),e,e.components,t)}function kc(n,e,t){var r=e.partUids[t];return n.getSystem().getByUid(r).toOption()}function Ec(n,e,t){return kc(n,e,t).getOrDie("Could not find part: "+t)}function Cc(e,n){var t=function(n){return de(n,bc)}(n);return Cn(de(t,function(n){return{key:n,value:e+"-"+n}}))}function Dc(e){return kr("partUids","partUids",Et(function(n){return Cc(n.uid,e)}),Dr())}function Mc(n){return En(af,n)}function Ic(r){return function(n,e){var t=e.toString(),r=t.indexOf(")")+1,o=t.indexOf("("),i=t.substring(o+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:Io(i.slice(1))}},n}(function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return r.apply(undefined,[n.getApis()].concat([n].concat(e)))},r)}function Rc(n){return yc(n)}function Ac(n,e,t,r,o){var i=function(n,e){return(0<n.length?[Jt("parts",n)]:[]).concat([Yt("uid"),tr("dom",{}),tr("components",[]),ei("originalSpec"),tr("debug.sketcher",{})]).concat(e)}(r,o);return Xt(n+" [SpecSchema]",Pt(i.concat(e)),t)}function Fc(n,e,t,r,o){var i=pf(o),u=function(n){return B(n,function(n){return n.fold(Fn.none,Fn.some,Fn.none,Fn.none).map(function(n){return Jt(n.name,n.schema.concat([ei(uf())]))}).toArray()})}(t),c=Dc(t),a=Ac(n,e,i,u,[c]),f=Tc(0,a,t);return r(a,Oc(n,a,f.internals()),i,f.externals())}var Bc,Vc,Nc,jc,_c,Pc,Hc,zc,Lc,Gc,Uc=Mu(or("cyclic",b(!1))),$c=Mu(or("cyclic",b(!0))),Wc=[tr("execute",Ru),tr("useSpace",!1),tr("useEnter",!0),tr("useControlEnter",!1),tr("useDown",!1)],Xc=Du(Wc,Ao.init,function(n,e,t,r){var o=t.useSpace&&!Iu(n.element())?[32]:[],i=t.useEnter?[13]:[],u=t.useDown?[40]:[],c=o.concat(i).concat(u);return[au(ou(c),Fu)].concat(t.useControlEnter?[au(iu([cu,ou([13])]),Fu)]:[])},function(n,e,t,r){return t.useSpace&&!Iu(n.element())?[au(ou([32]),Au)]:[]},function(){return Fn.none()}),qc=/* */Object.freeze({flatgrid:Bu,init:function(n){return n.state(n)}}),Yc=function(n){return"rtl"===mi(n,"direction")?"rtl":"ltr"},Kc=function(e,t,n,r,o){return r.focusManager.get(t).bind(function(n){return e(t.element(),n,r,o)}).map(function(n){return r.focusManager.set(t,n),!0})},Jc=Pu,Qc=Pu,Zc=Pu,na=nn(["index","candidates"],[]),ea=[Yt("selector"),tr("execute",Ru),Jo("onEscape"),tr("captureTab",!1),ki()],ta=Xu(function(n,e,t,r){return Uu(n,e,t,r,-1)}),ra=Xu(function(n,e,t,r){return Uu(n,e,t,r,1)}),oa=Xu(function(n,e,t,r){return $u(n,e,t,r,-1)}),ia=Xu(function(n,e,t,r){return $u(n,e,t,r,1)}),ua=b([au(ou([37]),ju(ta,ra)),au(ou([39]),_u(ta,ra)),au(ou([38]),Jc(oa)),au(ou([40]),Qc(ia)),au(iu([uu,ou([9])]),qu),au(iu([bu,ou([9])]),qu),au(ou([27]),function(n,e,t,r){return t.onEscape(n,e)}),au(ou([32].concat([13])),function(e,t,r,n){return function(n,e){return e.focusManager.get(n).bind(function(n){return Zi(n,e.selector)})}(e,r).bind(function(n){return r.execute(e,t,n)})})]),ca=b([au(ou([32]),Au)]),aa=Du(ea,Bu,ua,ca,function(){return Fn.some(Wu)}),fa=[Yt("selector"),tr("getInitial",Fn.none),tr("execute",Ru),Jo("onEscape"),tr("executeOnMove",!1),tr("allowVertical",!0)],sa=b([au(ou([32]),Au)]),la=Du(fa,Ao.init,function(n,e,t,r){var o=[37].concat(t.allowVertical?[38]:[]),i=[39].concat(t.allowVertical?[40]:[]);return[au(ou(o),nc(ju(Qu,Zu))),au(ou(i),nc(_u(Qu,Zu))),au(ou([13]),Ku),au(ou([32]),Ku),au(ou([27]),ec)]},sa,function(){return Fn.some(Ju)}),da=nn(["rowIndex","columnIndex","cell"],[]),ma=[Jt("selectors",[Yt("row"),Yt("cell")]),tr("cycles",!0),tr("previousSelector",Fn.none),tr("execute",Ru)],ga=ac(function(n,e,t){return rc(n,e,t,-1)},function(n,e,t){return ic(n,e,t,-1)}),pa=ac(function(n,e,t){return rc(n,e,t,1)},function(n,e,t){return ic(n,e,t,1)}),ha=ac(function(n,e,t){return oc(n,t,e,-1)},function(n,e,t){return uc(n,t,e,-1)}),va=ac(function(n,e,t){return oc(n,t,e,1)},function(n,e,t){return uc(n,t,e,1)}),ya=b([au(ou([37]),ju(ga,pa)),au(ou([39]),_u(ga,pa)),au(ou([38]),Jc(ha)),au(ou([40]),Qc(va)),au(ou([32].concat([13])),function(e,t,r){return go(e.element()).bind(function(n){return r.execute(e,t,n)})})]),ba=b([au(ou([32]),Au)]),xa=Du(ma,Ao.init,ya,ba,function(){return Fn.some(cc)}),wa=[Yt("selector"),tr("execute",Ru),tr("moveOnTab",!1)],Sa=b([au(ou([38]),Zc(lc)),au(ou([40]),Zc(dc)),au(iu([uu,ou([9])]),function(n,e,t){return t.moveOnTab?Zc(lc)(n,e,t):Fn.none()}),au(iu([bu,ou([9])]),function(n,e,t){return t.moveOnTab?Zc(dc)(n,e,t):Fn.none()}),au(ou([13]),fc),au(ou([32]),fc)]),Ta=b([au(ou([32]),Au)]),Oa=Du(wa,Ao.init,Sa,Ta,function(){return Fn.some(sc)}),ka=[Jo("onSpace"),Jo("onEnter"),Jo("onShiftEnter"),Jo("onLeft"),Jo("onRight"),Jo("onTab"),Jo("onShiftTab"),Jo("onUp"),Jo("onDown"),Jo("onEscape"),tr("stopSpaceKeyup",!1),Qt("focusIn")],Ea=Du(ka,Ao.init,function(n,e,t){return[au(ou([32]),t.onSpace),au(iu([bu,ou([13])]),t.onEnter),au(iu([uu,ou([13])]),t.onShiftEnter),au(iu([uu,ou([9])]),t.onShiftTab),au(iu([bu,ou([9])]),t.onTab),au(ou([38]),t.onUp),au(ou([40]),t.onDown),au(ou([37]),t.onLeft),au(ou([39]),t.onRight),au(ou([32]),t.onSpace),au(ou([27]),t.onEscape)]},function(n,e,t){return t.stopSpaceKeyup?[au(ou([32]),Au)]:[]},function(n){return n.focusIn}),Ca=Uc.schema(),Da=$c.schema(),Ma=la.schema(),Ia=aa.schema(),Ra=xa.schema(),Aa=Xc.schema(),Fa=Oa.schema(),Ba=Ea.schema(),Va=(Gc=Xt("Creating behaviour: "+(Lc={branchKey:"mode",branches:/* */Object.freeze({acyclic:Ca,cyclic:Da,flow:Ma,flatgrid:Ia,matrix:Ra,execution:Aa,menu:Fa,special:Ba}),name:"keying",active:{events:function(n,e){return n.handler.toEvents(n,e)}},apis:{focusIn:function(e,t,r){t.sendFocusIn(t).fold(function(){e.getSystem().triggerFocus(e.element(),e.element())},function(n){n(e,t,r)})},setGridSize:function(n,e,t,r,o){Mn(t,"setGridSize")?t.setGridSize(r,o):l.console.error("Layout does not support setGridSize")}},state:qc}).name,No,Lc),Bc=qt(Gc.branchKey,Gc.branches),Vc=Gc.name,Nc=Gc.active,jc=Gc.apis,_c=Gc.extra,Pc=Gc.state,zc=nr(Vc,[Zt("config",Hc=Bc)]),Ro(Hc,zc,Vc,Nc,jc,_c,Pc)),Na=mc,ja=pc,_a="placeholder",Pa=yt([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Ha=function(i,u,c,a){return hc(i,0,c,a).fold(function(n,e){var t=e(u,c.config,c.validated),r=Dt(t,"components").getOr([]),o=B(r,function(n){return Ha(i,u,n,a)});return[x(x({},t),{components:o})]},function(n,e){var t=e(u,c.config,c.validated);return c.validated.preprocess.getOr(y)(t)})},za=Pa.single,La=Pa.multiple,Ga=b(_a),Ua=0,$a=yt([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Wa=tr("factory",{sketch:y}),Xa=tr("schema",[]),qa=Yt("name"),Ya=kr("pname","pname",kt(function(n){return"<alloy."+yc(n.name)+">"}),Dr()),Ka=or("schema",function(){return[Qt("preprocess")]}),Ja=tr("defaults",b({})),Qa=tr("overrides",b({})),Za=Sr([Wa,Xa,qa,Ya,Ja,Qa]),nf=Sr([Wa,Xa,qa,Ya,Ja,Qa]),ef=Sr([Wa,Ka,qa,Yt("unit"),Ya,Ja,Qa]),tf=xc($a.required,Za),rf=xc($a.optional,nf),of=xc($a.group,ef),uf=b("entirety"),cf=function(n,e){return{uiType:Ga(),owner:n,name:e}},af=yc("alloy-premade"),ff=b("alloy-id-"),sf=b("data-alloy-id"),lf=ff(),df=sf(),mf=function(n,e){Object.defineProperty(n.dom(),df,{value:e,writable:!0})},gf=function(n){var e=et(n)?n.dom()[df]:null;return Fn.from(e)},pf=function(n){return n.hasOwnProperty("uid")?n:x(x({},n),{uid:Rc("uid")})};function hf(n){var e=Xt("Sketcher for "+n.name,Hs,n),t=S(e.apis,Ic),r=S(e.extraApis,function(n,e){return Gr(n,e)});return x(x({name:b(e.name),partFields:b([]),configFields:b(e.configFields),sketch:function(n){return function(n,e,t,r){var o=pf(r);return t(Ac(n,e,o,[],[]),o)}(e.name,e.configFields,e.factory,n)}},t),r)}function vf(n){var e=Xt("Sketcher for "+n.name,zs,n),t=Sc(e.name,e.partFields),r=S(e.apis,Ic),o=S(e.extraApis,function(n,e){return Gr(n,e)});return x(x({name:b(e.name),partFields:b(e.partFields),configFields:b(e.configFields),sketch:function(n){return Fc(e.name,e.configFields,e.partFields,e.factory,n)},parts:b(t)},r),o)}function yf(n){var e=Je.fromHtml(n),t=at(e),r=function(n){var e=n.dom().attributes!==undefined?n.dom().attributes:[];return I(e,function(n,e){var t;return"class"===e.name?n:x(x({},n),((t={})[e.name]=e.value,t))},{})}(e),o=function(n){return Array.prototype.slice.call(n.dom().classList,0)}(e),i=0===t.length?{}:{innerHtml:po(e)};return x({tag:q(e),classes:o,attributes:r},i)}function bf(n){return{dom:Us(n)}}function xf(n){return Bo([Fi.config({toggleClass:_i.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Bi(n,function(n,e){(e?Fi.on:Fi.off)(n)})])}function wf(n,e){var t=e.ui.registry.getAll().icons;return Fn.from(t[n]).fold(function(){return Us('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item ${prefix}-icon-'+n+' ${prefix}-icon"></span>')},function(n){return Us('<span class="${prefix}-toolbar-button ${prefix}-toolbar-group-item">'+n+"</span>")})}function Sf(e){return rf({name:e+"-edge",overrides:function(n){return n.model.manager.edgeActions[e].fold(function(){return{}},function(r){return{events:Nr([Pr(xe(),r,[n]),Pr(Te(),r,[n]),Pr(Oe(),function(n,e,t){t.mouseIsDown.get()&&r(n,t)},[n])])}})}})}function Tf(n,e,t){e.store.manager.onLoad(n,e,t)}function Of(n,e,t){e.store.manager.onUnload(n,e,t)}function kf(){var n=Pn(null);return Fo({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})}function Ef(){var i=Pn({}),u=Pn({});return Fo({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return Dt(i.get(),n).orThunk(function(){return Dt(u.get(),n)})},update:function(n){var e=i.get(),t=u.get(),r={},o={};C(n,function(e){r[e.value]=e,Dt(e,"meta").each(function(n){Dt(n,"text").each(function(n){o[n]=e})})}),i.set(x(x({},e),r)),u.set(x(x({},t),o))},clear:function(){i.set({}),u.set({})}})}function Cf(n,e,t,r){var o=e.store;t.update([r]),o.setValue(n,r),e.onSetValue(n,r)}function Df(n,e){sl.set(n,e)}function Mf(n){return sl.get(n)}function If(n){var e=n.event().raw();if(function(n){return-1!==n.type.indexOf("touch")}(e)){var t=e;return t.touches!==undefined&&1===t.touches.length?Fn.some(t.touches[0]).map(function(n){return dl(n.clientX,n.clientY)}):Fn.none()}var r=e;return r.clientX!==undefined?Fn.some(r).map(function(n){return dl(n.clientX,n.clientY)}):Fn.none()}function Rf(n){return n.model.minX}function Af(n){return n.model.minY}function Ff(n){return n.model.minX-1}function Bf(n){return n.model.minY-1}function Vf(n){return n.model.maxX}function Nf(n){return n.model.maxY}function jf(n){return n.model.maxX+1}function _f(n){return n.model.maxY+1}function Pf(n,e,t){return e(n)-t(n)}function Hf(n){return Pf(n,Vf,Rf)}function zf(n){return Pf(n,Nf,Af)}function Lf(n){return Hf(n)/2}function Gf(n){return zf(n)/2}function Uf(n){return n.stepSize}function $f(n){return n.snapToGrid}function Wf(n){return n.snapStart}function Xf(n){return n.rounded}function qf(n,e){return n[e+"-edge"]!==undefined}function Yf(n){return qf(n,"left")}function Kf(n){return qf(n,"right")}function Jf(n){return qf(n,"top")}function Qf(n){return qf(n,"bottom")}function Zf(n){return n.model.value.get()}function ns(n){return{x:b(n)}}function es(n){return{y:b(n)}}function ts(n,e){return{x:b(n),y:b(e)}}function rs(n,e){U(n,ml(),{value:e})}function os(n,e,t,r){return n<e?n:t<n?t:n===e?e-1:Math.max(e,n-r)}function is(n,e,t,r){return t<n?n:n<e?e:n===t?t+1:Math.min(t,n+r)}function us(n,e,t){return Math.max(e,Math.min(t,n))}function cs(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.step,u=n.snap,c=n.snapStart,a=n.rounded,f=n.hasMinEdge,s=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=f?e-1:e,p=s?t+1:t;if(o<l)return g;if(d<o)return p;var h=function(n,e,t){return Math.min(t,Math.max(n,e))-e}(o,l,d),v=us(h/m*r+e,g,p);return u&&e<=v&&v<=t?function(u,t,c,a,n){return n.fold(function(){var n=u-t,e=Math.round(n/a)*a;return us(t+e,t-1,c+1)},function(n){var e=(u-n)%a,t=Math.round(e/a),r=Math.floor((u-n)/a),o=Math.floor((c-n)/a),i=n+Math.min(o,r+t)*a;return Math.max(n,i)})}(v,e,t,i,c):a?Math.round(v):v}function as(n){var e=n.min,t=n.max,r=n.range,o=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,c=n.maxBound,a=n.maxOffset,f=n.centerMinEdge,s=n.centerMaxEdge;return o<e?i?0:f:t<o?u?c:s:(o-e)/r*a}function fs(n){return n.element().dom().getBoundingClientRect()}function ss(n,e){return n[e]}function ls(n){var e=fs(n);return ss(e,gl)}function ds(n){var e=fs(n);return ss(e,"right")}function ms(n){var e=fs(n);return ss(e,"top")}function gs(n){var e=fs(n);return ss(e,"bottom")}function ps(n){var e=fs(n);return ss(e,"width")}function hs(n){var e=fs(n);return ss(e,"height")}function vs(n,e,t){return(n+e)/2-t}function ys(n,e){var t=fs(n),r=fs(e),o=ss(t,gl),i=ss(t,"right"),u=ss(r,gl);return vs(o,i,u)}function bs(n,e){var t=fs(n),r=fs(e),o=ss(t,"top"),i=ss(t,"bottom"),u=ss(r,"top");return vs(o,i,u)}function xs(n,e){U(n,ml(),{value:e})}function ws(n){return{x:b(n)}}function Ss(n,e,t){var r={min:Rf(e),max:Vf(e),range:Hf(e),value:t,step:Uf(e),snap:$f(e),snapStart:Wf(e),rounded:Xf(e),hasMinEdge:Yf(e),hasMaxEdge:Kf(e),minBound:ls(n),maxBound:ds(n),screenRange:ps(n)};return cs(r)}function Ts(t){return function(n,e){return function(n,e,t){var r=(0<n?is:os)(Zf(t).x(),Rf(t),Vf(t),Uf(t));return xs(e,ws(r)),Fn.some(r)}(t,n,e).map(function(){return!0})}}function Os(n,e,t,r,o,i){var u=function(e,n,t,r,o){var i=ps(e),u=r.bind(function(n){return Fn.some(ys(n,e))}).getOr(0),c=o.bind(function(n){return Fn.some(ys(n,e))}).getOr(i),a={min:Rf(n),max:Vf(n),range:Hf(n),value:t,hasMinEdge:Yf(n),hasMaxEdge:Kf(n),minBound:ls(e),minOffset:0,maxBound:ds(e),maxOffset:i,centerMinEdge:u,centerMaxEdge:c};return as(a)}(e,i,t,r,o);return ls(e)-ls(n)+u}function ks(n,e){U(n,ml(),{value:e})}function Es(n){return{y:b(n)}}function Cs(n,e,t){var r={min:Af(e),max:Nf(e),range:zf(e),value:t,step:Uf(e),snap:$f(e),snapStart:Wf(e),rounded:Xf(e),hasMinEdge:Jf(e),hasMaxEdge:Qf(e),minBound:ms(n),maxBound:gs(n),screenRange:hs(n)};return cs(r)}function Ds(t){return function(n,e){return function(n,e,t){var r=(0<n?is:os)(Zf(t).y(),Af(t),Nf(t),Uf(t));return ks(e,Es(r)),Fn.some(r)}(t,n,e).map(function(){return!0})}}function Ms(n,e,t,r,o,i){var u=function(e,n,t,r,o){var i=hs(e),u=r.bind(function(n){return Fn.some(bs(n,e))}).getOr(0),c=o.bind(function(n){return Fn.some(bs(n,e))}).getOr(i),a={min:Af(n),max:Nf(n),range:zf(n),value:t,hasMinEdge:Jf(n),hasMaxEdge:Qf(n),minBound:ms(e),minOffset:0,maxBound:gs(e),maxOffset:i,centerMinEdge:u,centerMaxEdge:c};return as(a)}(e,i,t,r,o);return ms(e)-ms(n)+u}function Is(n,e){U(n,ml(),{value:e})}function Rs(n,e){return{x:b(n),y:b(e)}}function As(t,r){return function(n,e){return function(n,e,t,r){var o=0<n?is:os,i=e?Zf(r).x():o(Zf(r).x(),Rf(r),Vf(r),Uf(r)),u=e?o(Zf(r).y(),Af(r),Nf(r),Uf(r)):Zf(r).y();return Is(t,Rs(i,u)),Fn.some(i)}(t,r,n,e).map(function(){return!0})}}function Fs(e,t,r,n){return Ws.forToolbar(t,function(){var n=r();e.setContextToolbar([{label:t+" group",items:n}])},{},n)}function Bs(n){return[function(o){function i(n){return n<0?"black":360<n?"white":"hsl("+n+", 100%, 50%)"}return Vl.sketch({dom:Us('<div class="${prefix}-slider ${prefix}-hue-slider-container"></div>'),components:[Vl.parts()["left-edge"](bf('<div class="${prefix}-hue-slider-black"></div>')),Vl.parts().spectrum({dom:Us('<div class="${prefix}-slider-gradient-container"></div>'),components:[bf('<div class="${prefix}-slider-gradient"></div>')],behaviours:Bo([Fi.config({toggleClass:_i.resolve("thumb-active")})])}),Vl.parts()["right-edge"](bf('<div class="${prefix}-hue-slider-white"></div>')),Vl.parts().thumb({dom:Us('<div class="${prefix}-slider-thumb"></div>'),behaviours:Bo([Fi.config({toggleClass:_i.resolve("thumb-active")})])})],onChange:function(n,e,t){var r=i(t.x());Gi(e.element(),"background-color",r),o.onChange(n,e,r)},onDragStart:function(n,e){Fi.on(e)},onDragEnd:function(n,e){Fi.off(e)},onInit:function(n,e,t,r){var o=i(r.x());Gi(e.element(),"background-color",o)},stepSize:10,model:{mode:"x",minX:0,maxX:360,getInitialValue:function(){return{x:function(){return o.getInitialValue()}}}},sliderBehaviours:Bo([Vi(Vl.refresh)])})}(n)]}function Vs(n){var e=n.selection.getStart(),t=Je.fromDom(e),r=Je.fromDom(n.getBody()),o=function(e,n){return(et(n)?Fn.some(n):fn(n).filter(et)).map(function(n){return Ar(n,function(n){return gi(n,"font-size").isSome()},e).bind(function(n){return gi(n,"font-size")}).getOrThunk(function(){return mi(n,"font-size")})}).getOr("")}(function(n){return cn(r,n)},t);return R(Pl,function(n){return o===n}).getOr("medium")}function Ns(n){return[bf('<span class="${prefix}-toolbar-button ${prefix}-icon-small-font ${prefix}-icon"></span>'),function(n){return _l({onChange:n.onChange,sizes:zl,category:"font",getInitialValue:n.getInitialValue})}(n),bf('<span class="${prefix}-toolbar-button ${prefix}-icon-large-font ${prefix}-icon"></span>')]}function js(n){var e=function t(n){return n.uid!==undefined}(n)&&Mn(n,"uid")?n.uid:Rc("memento");return{get:function(n){return n.getSystem().getByUid(e).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(e).toOption()},asSpec:function(){return x(x({},n),{uid:e})}}}var _s,Ps,Hs=Pt([Yt("name"),Yt("factory"),Yt("configFields"),tr("apis",{}),tr("extraApis",{})]),zs=Pt([Yt("name"),Yt("factory"),Yt("configFields"),Yt("partFields"),tr("apis",{}),tr("extraApis",{})]),Ls=hf({name:"Button",factory:function(n){function t(e){return Dt(n.dom,"attributes").bind(function(n){return Dt(n,e)})}var e=function(n){return Nr(F([n.map(function(t){return Mo(function(n,e){t(n),e.stop()})}).toArray(),ai()]))}(n.action),r=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:e,behaviours:ja(n.buttonBehaviours,[Li.config({}),Va.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==r)return{role:t("role").getOr("button")};var n=t("type").getOr("button"),e=t("role").map(function(n){return{role:n}}).getOr({});return x({type:n},e)}()},eventOrder:n.eventOrder}},configFields:[tr("uid",undefined),Yt("dom"),tr("components",[]),Na("buttonBehaviours",[Li,Va]),Qt("action"),Qt("role"),tr("eventOrder",{})]}),Gs=qr({fields:[],name:"unselecting",active:/* */Object.freeze({events:function(n){return Nr([jr(Fe(),b(!0))])},exhibit:function(n,e){return Ur({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Us=function(n){var e=function(n,r){return n.replace(/\$\{([^{}]*)\}/g,function(n,e){var t=r[e];return function(n){var e=typeof n;return"string"==e||"number"==e}(t)?t.toString():n})}(n,{prefix:_i.prefix()});return yf(e)},$s=function(n,e,t,r){return Ls.sketch({dom:wf(n,r),action:e,buttonBehaviours:xt(Bo([Gs.config({})]),t)})},Ws={forToolbar:$s,forToolbarCommand:function(n,e){return $s(e,function(){n.execCommand(e)},{},n)},forToolbarStateAction:function(n,e,t,r){var o=xf(t);return $s(e,r,o,n)},forToolbarStateCommand:function(n,e){var t=xf(e);return $s(e,function(){n.execCommand(e)},t,n)},getToolbarIconButton:wf},Xs=rf({schema:[Yt("dom")],name:"label"}),qs=Sf("top-left"),Ys=Sf("top"),Ks=Sf("top-right"),Js=Sf("right"),Qs=Sf("bottom-right"),Zs=Sf("bottom"),nl=Sf("bottom-left"),el=[Xs,Sf("left"),Js,Ys,Zs,qs,Ks,nl,Qs,tf({name:"thumb",defaults:b({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:Nr([zr(xe(),n,"spectrum"),zr(we(),n,"spectrum"),zr(Se(),n,"spectrum"),zr(Te(),n,"spectrum"),zr(Oe(),n,"spectrum"),zr(ke(),n,"spectrum")])}}}),tf({schema:[or("mouseIsDown",function(){return Pn(!1)})],name:"spectrum",overrides:function(t){function r(e,n){return o.getValueFromEvent(n).map(function(n){return o.setValueFrom(e,t,n)})}var o=t.model.manager;return{behaviours:Bo([Va.config({mode:"special",onLeft:function(n){return o.onLeft(n,t)},onRight:function(n){return o.onRight(n,t)},onUp:function(n){return o.onUp(n,t)},onDown:function(n){return o.onDown(n,t)}}),Li.config({})]),events:Nr([_r(xe(),r),_r(we(),r),_r(Te(),r),_r(Oe(),function(n,e){t.mouseIsDown.get()&&r(n,e)})])}}})],tl=/* */Object.freeze({onLoad:Tf,onUnload:Of,setValue:function(n,e,t,r){e.store.manager.setValue(n,e,t,r)},getValue:function(n,e,t){return e.store.manager.getValue(n,e,t)},getState:function(n,e,t){return t}}),rl=/* */Object.freeze({events:function(t,r){var n=t.resetOnDom?[Eo(function(n,e){Tf(n,t,r)}),Co(function(n,e){Of(n,t,r)})]:[$r(t,r,Tf)];return Nr(n)}}),ol=/* */Object.freeze({memory:kf,dataset:Ef,manual:function(){return Fo({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),il=[Qt("initialValue"),Yt("getFallbackEntry"),Yt("getDataKey"),Yt("setValue"),ni("manager",{setValue:Cf,getValue:function(n,e,t){var r=e.store,o=r.getDataKey(n);return t.lookup(o).fold(function(){return r.getFallbackEntry(o)},function(n){return n})},onLoad:function(e,t,r){t.store.initialValue.each(function(n){Cf(e,t,r,n)})},onUnload:function(n,e,t){t.clear()},state:Ef})],ul=[Yt("getValue"),tr("setValue",w),Qt("initialValue"),ni("manager",{setValue:function(n,e,t,r){e.store.setValue(n,r),e.onSetValue(n,r)},getValue:function(n,e,t){return e.store.getValue(n)},onLoad:function(e,t,n){t.store.initialValue.each(function(n){t.store.setValue(e,n)})},onUnload:w,state:Ao.init})],cl=[Qt("initialValue"),ni("manager",{setValue:function(n,e,t,r){t.set(r),e.onSetValue(n,r)},getValue:function(n,e,t){return t.get()},onLoad:function(n,e,t){e.store.initialValue.each(function(n){t.isNotSet()&&t.set(n)})},onUnload:function(n,e,t){t.clear()},state:kf})],al=[rr("store",{mode:"memory"},qt("mode",{memory:cl,manual:ul,dataset:il})),Ko("onSetValue"),tr("resetOnDom",!1)],fl=qr({fields:al,name:"representing",active:rl,apis:tl,extra:{setValueFrom:function(n,e){var t=fl.getValue(e);fl.setValue(n,t)}},state:ol}),sl=$i("width",function(n){return n.dom().offsetWidth}),ll=function(t,r){return{left:b(t),top:b(r),translate:function(n,e){return ll(t+n,r+e)}}},dl=ll,ml=b("slider.change.value"),gl="left",pl=Ts(-1),hl=Ts(1),vl=Fn.none,yl=Fn.none,bl={"top-left":Fn.none(),top:Fn.none(),"top-right":Fn.none(),right:Fn.some(function(n,e){rs(n,ns(jf(e)))}),"bottom-right":Fn.none(),bottom:Fn.none(),"bottom-left":Fn.none(),left:Fn.some(function(n,e){rs(n,ns(Ff(e)))})},xl=/* */Object.freeze({setValueFrom:function(n,e,t){var r=Ss(n,e,t),o=ws(r);return xs(n,o),r},setToMin:function(n,e){var t=Rf(e);xs(n,ws(t))},setToMax:function(n,e){var t=Vf(e);xs(n,ws(t))},findValueOfOffset:Ss,getValueFromEvent:function(n){return If(n).map(function(n){return n.left()})},findPositionOfValue:Os,setPositionFromValue:function(n,e,t,r){var o=Zf(t),i=Os(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=Mf(e.element())/2;Gi(e.element(),"left",i-u+"px")},onLeft:pl,onRight:hl,onUp:vl,onDown:yl,edgeActions:bl}),wl=Fn.none,Sl=Fn.none,Tl=Ds(-1),Ol=Ds(1),kl={"top-left":Fn.none(),top:Fn.some(function(n,e){rs(n,es(Bf(e)))}),"top-right":Fn.none(),right:Fn.none(),"bottom-right":Fn.none(),bottom:Fn.some(function(n,e){rs(n,es(_f(e)))}),"bottom-left":Fn.none(),left:Fn.none()},El=/* */Object.freeze({setValueFrom:function(n,e,t){var r=Cs(n,e,t),o=Es(r);return ks(n,o),r},setToMin:function(n,e){var t=Af(e);ks(n,Es(t))},setToMax:function(n,e){var t=Nf(e);ks(n,Es(t))},findValueOfOffset:Cs,getValueFromEvent:function(n){return If(n).map(function(n){return n.top()})},findPositionOfValue:Ms,setPositionFromValue:function(n,e,t,r){var o=Zf(t),i=Ms(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),u=Wi(e.element())/2;Gi(e.element(),"top",i-u+"px")},onLeft:wl,onRight:Sl,onUp:Tl,onDown:Ol,edgeActions:kl}),Cl=As(-1,!1),Dl=As(1,!1),Ml=As(-1,!0),Il=As(1,!0),Rl={"top-left":Fn.some(function(n,e){rs(n,ts(Ff(e),Bf(e)))}),top:Fn.some(function(n,e){rs(n,ts(Lf(e),Bf(e)))}),"top-right":Fn.some(function(n,e){rs(n,ts(jf(e),Bf(e)))}),right:Fn.some(function(n,e){rs(n,ts(jf(e),Gf(e)))}),"bottom-right":Fn.some(function(n,e){rs(n,ts(jf(e),_f(e)))}),bottom:Fn.some(function(n,e){rs(n,ts(Lf(e),_f(e)))}),"bottom-left":Fn.some(function(n,e){rs(n,ts(Ff(e),_f(e)))}),left:Fn.some(function(n,e){rs(n,ts(Ff(e),Gf(e)))})},Al=/* */Object.freeze({setValueFrom:function(n,e,t){var r=Ss(n,e,t.left()),o=Cs(n,e,t.top()),i=Rs(r,o);return Is(n,i),i},setToMin:function(n,e){var t=Rf(e),r=Af(e);Is(n,Rs(t,r))},setToMax:function(n,e){var t=Vf(e),r=Nf(e);Is(n,Rs(t,r))},getValueFromEvent:function(n){return If(n)},setPositionFromValue:function(n,e,t,r){var o=Zf(t),i=Os(n,r.getSpectrum(n),o.x(),r.getLeftEdge(n),r.getRightEdge(n),t),u=Ms(n,r.getSpectrum(n),o.y(),r.getTopEdge(n),r.getBottomEdge(n),t),c=Mf(e.element())/2,a=Wi(e.element())/2;Gi(e.element(),"left",i-c+"px"),Gi(e.element(),"top",u-a+"px")},onLeft:Cl,onRight:Dl,onUp:Ml,onDown:Il,edgeActions:Rl}),Fl=[tr("stepSize",1),tr("onChange",w),tr("onChoose",w),tr("onInit",w),tr("onDragStart",w),tr("onDragEnd",w),tr("snapToGrid",!1),tr("rounded",!0),Qt("snapStart"),Kt("model",qt("mode",{x:[tr("minX",0),tr("maxX",100),or("value",function(n){return Pn(n.mode.minX)}),Yt("getInitialValue"),ni("manager",xl)],y:[tr("minY",0),tr("maxY",100),or("value",function(n){return Pn(n.mode.minY)}),Yt("getInitialValue"),ni("manager",El)],xy:[tr("minX",0),tr("maxX",100),tr("minY",0),tr("maxY",100),or("value",function(n){return Pn({x:b(n.mode.minX),y:b(n.mode.minY)})}),Yt("getInitialValue"),ni("manager",Al)]})),mc("sliderBehaviours",[Va,fl]),or("mouseIsDown",function(){return Pn(!1)})],Bl=b("mouse.released"),Vl=vf({name:"Slider",configFields:Fl,partFields:el,factory:function(i,n,e,t){function u(n){return Ec(n,i,"thumb")}function c(n){return Ec(n,i,"spectrum")}function r(n){return kc(n,i,"left-edge")}function o(n){return kc(n,i,"right-edge")}function a(n){return kc(n,i,"top-edge")}function f(n){return kc(n,i,"bottom-edge")}function s(n,e){v.setPositionFromValue(n,e,i,{getLeftEdge:r,getRightEdge:o,getTopEdge:a,getBottomEdge:f,getSpectrum:c})}function l(n,e){h.value.set(e);var t=u(n);return s(n,t),i.onChange(n,t,e),Fn.some(!0)}function d(t){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&kc(t,i,"thumb").each(function(n){var e=h.value.get();i.onChoose(t,n,e)})}function m(n,e){e.stop(),i.mouseIsDown.set(!0),i.onDragStart(n,u(n))}function g(n,e){e.stop(),i.onDragEnd(n,u(n)),d(n)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:n,behaviours:pc(i.sliderBehaviours,[Va.config({mode:"special",focusIn:function(n){return kc(n,i,"spectrum").map(Va.focusIn).map(b(!0))}}),fl.config({store:{mode:"manual",getValue:function(n){return h.value.get()}}}),Ci.config({channels:(p={},p[Bl()]={onReceive:d},p)})]),events:Nr([_r(ml(),function(n,e){l(n,e.event().value())}),Eo(function(n,e){var t=h.getInitialValue();h.value.set(t);var r=u(n);s(n,r);var o=c(n);i.onInit(n,r,o,h.value.get())}),_r(xe(),m),_r(Se(),g),_r(Te(),m),_r(ke(),g)]),apis:{resetToMin:function(n){v.setToMin(n,i)},resetToMax:function(n){v.setToMax(n,i)},changeValue:l,refresh:s},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,e){n.resetToMin(e)},resetToMax:function(n,e){n.resetToMax(e)},refresh:function(n,e){n.refresh(e)}}}),Nl=function(n,r){var e={onChange:function(n,e,t){r.undoManager.transact(function(){r.formatter.apply("forecolor",{value:t}),r.nodeChanged()})},getInitialValue:function(){return-1}};return Fs(n,"color-levels",function(){return Bs(e)},r)},jl=Pt([Yt("getInitialValue"),Yt("onChange"),Yt("category"),Yt("sizes")]),_l=function(n){var o=Xt("SizeSlider",jl,n);return Vl.sketch({dom:{tag:"div",classes:[_i.resolve("slider-"+o.category+"-size-container"),_i.resolve("slider"),_i.resolve("slider-size-container")]},onChange:function(n,e,t){var r=t.x();!function(n){return 0<=n&&n<o.sizes.length}(r)||o.onChange(r)},onDragStart:function(n,e){Fi.on(e)},onDragEnd:function(n,e){Fi.off(e)},model:{mode:"x",minX:0,maxX:o.sizes.length-1,getInitialValue:function(){return{x:function(){return o.getInitialValue()}}}},stepSize:1,snapToGrid:!0,sliderBehaviours:Bo([Vi(Vl.refresh)]),components:[Vl.parts().spectrum({dom:Us('<div class="${prefix}-slider-size-container"></div>'),components:[bf('<div class="${prefix}-slider-size-line"></div>')]}),Vl.parts().thumb({dom:Us('<div class="${prefix}-slider-thumb"></div>'),behaviours:Bo([Fi.config({toggleClass:_i.resolve("thumb-active")})])})]})},Pl=["9px","10px","11px","12px","14px","16px","18px","20px","24px","32px","36px"],Hl={candidates:b(Pl),get:function(n){return function(e){return A(Pl,function(n){return n===e})}(Vs(n)).getOr(2)},apply:function(e,n){(function(n){return Fn.from(Pl[n])})(n).each(function(n){!function(n,e){Vs(n)!==e&&n.execCommand("fontSize",!1,e)}(e,n)})}},zl=Hl.candidates(),Ll=window.Promise?window.Promise:(_s=Gl.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){l.setTimeout(n,1)},Ps=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},Gl.prototype["catch"]=function(n){return this.then(null,n)},Gl.prototype.then=function(t,r){var o=this;return new Gl(function(n,e){$l.call(o,new Yl(t,r,n,e))})},Gl.all=function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var a=Array.prototype.slice.call(1===n.length&&Ps(n[0])?n[0]:n);return new Gl(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},Gl.resolve=function(e){return e&&"object"==typeof e&&e.constructor===Gl?e:new Gl(function(n){n(e)})},Gl.reject=function(t){return new Gl(function(n,e){e(t)})},Gl.race=function(o){return new Gl(function(n,e){for(var t=0,r=o;t<r.length;t++)r[t].then(n,e)})},Gl);function Gl(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],Kl(n,Ul(Wl,this),Ul(Xl,this))}function Ul(n,e){return function(){return n.apply(e,arguments)}}function $l(r){var o=this;null!==this._state?_s(function(){var n=o._state?r.onFulfilled:r.onRejected;if(null!==n){var e;try{e=n(o._value)}catch(t){return void r.reject(t)}r.resolve(e)}else(o._state?r.resolve:r.reject)(o._value)}):this._deferreds.push(r)}function Wl(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void Kl(Ul(e,n),Ul(Wl,this),Ul(Xl,this))}this._state=!0,this._value=n,ql.call(this)}catch(t){Xl.call(this,t)}}function Xl(n){this._state=!1,this._value=n,ql.call(this)}function ql(){for(var n=0,e=this._deferreds;n<e.length;n++){var t=e[n];$l.call(this,t)}this._deferreds=[]}function Yl(n,e,t,r){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.resolve=t,this.reject=r}function Kl(n,e,t){var r=!1;try{n(function(n){r||(r=!0,e(n))},function(n){r||(r=!0,t(n))})}catch(o){if(r)return;r=!0,t(o)}}function Jl(n){return function e(t){return new Ll(function(n){var e=new l.FileReader;e.onloadend=function(){n(e.result)},e.readAsDataURL(t)})}(n).then(function(n){return n.split(",")[1]})}function Ql(o,i){(function(n){return Jl(n)})(i).then(function(r){o.undoManager.transact(function(){var n=o.editorUpload.blobCache,e=n.create(yc("mceu"),i,r);n.add(e);var t=o.dom.createHTML("img",{src:e.blobUri()});o.insertContent(t)})})}function Zl(t){var e=js({dom:{tag:"input",attributes:{accept:"image/*",type:"file",title:""},styles:{visibility:"hidden",position:"absolute"}},events:Nr([Lr(Re()),_r(Ie(),function(n,e){(function(n){var e=n.event(),t=e.raw().target.files||e.raw().dataTransfer.files;return Fn.from(t[0])})(e).each(function(n){Ql(t,n)})})])});return Ls.sketch({dom:Ws.getToolbarIconButton("image",t),components:[e.asSpec()],action:function(n){e.get(n).element().dom().click()}})}function nd(n){return n.dom().textContent}function ed(n){return 0<n.length}function td(n){return n===undefined||null===n?"":n}function rd(n,e,t){return t.text.toOption().filter(ed).fold(function(){return function(n){return Qr(n,"href")===nd(n)}(n)?Fn.some(e):Fn.none()},Fn.some)}function od(n,e){var t=e.selection.getRng();n(),e.selection.setRng(t)}function id(n){return n.dom().value}function ud(n,e){if(e===undefined)throw new Error("Value.set was undefined");n.dom().value=e}function cd(n){return x(x({},function(n){return Bo([Li.config({onFocus:!1===n.selectOnFocus?w:function(n){var e=n.element(),t=id(e);e.dom().setSelectionRange(0,t.length)}})])}(n)),pc(n.inputBehaviours,[fl.config({store:{mode:"manual",initialValue:n.data.getOr(undefined),getValue:function(n){return id(n.element())},setValue:function(n,e){id(n.element())!==e&&ud(n.element(),e)}},onSetValue:n.onSetValue})]))}function ad(n,e){var t=js(pm.sketch({inputAttributes:{placeholder:bm.translate(e)},onSetValue:function(n,e){G(n,Me())},inputBehaviours:Bo([lm.config({find:Fn.some}),ym.config({}),Va.config({mode:"execution"})]),selectOnFocus:!1})),r=js(Ls.sketch({dom:Us('<button class="${prefix}-input-container-x ${prefix}-icon-cancel-circle ${prefix}-icon"></button>'),action:function(n){var e=t.get(n);fl.setValue(e,"")}}));return{name:n,spec:dm.sketch({dom:Us('<div class="${prefix}-input-container"></div>'),components:[t.asSpec(),r.asSpec()],containerBehaviours:Bo([Fi.config({toggleClass:_i.resolve("input-container-empty")}),lm.config({find:function(n){return Fn.some(t.get(n))}}),am("input-clearing",[_r(Me(),function(n){var e=t.get(n);(0<fl.getValue(e).length?Fi.off:Fi.on)(n)})])])})}}function fd(n,e,t){e.disabled&&wm(n,e)}function sd(n,e){return!0===e.useNative&&k(xm,q(n.element()))}function ld(n){Kr(n.element(),"disabled","disabled")}function dd(n){no(n.element(),"disabled")}function md(n){Kr(n.element(),"aria-disabled","true")}function gd(n){Kr(n.element(),"aria-disabled","false")}function pd(e,n,t){n.disableClass.each(function(n){co(e.element(),n)}),(sd(e,n)?dd:gd)(e),n.onEnabled(e)}function hd(n,e){return sd(n,e)?function(n){return Zr(n.element(),"disabled")}(n):function(n){return"true"===Qr(n.element(),"aria-disabled")}(n)}function vd(n){return"<alloy.field."+n+">"}function yd(){function e(){t.get().each(function(n){n.destroy()})}var t=Pn(Fn.none());return{clear:function(){e(),t.set(Fn.none())},isSet:function(){return t.get().isSome()},set:function(n){e(),t.set(Fn.some(n))},run:function(n){t.get().each(n)}}}function bd(){var e=Pn(Fn.none());return{clear:function(){e.set(Fn.none())},set:function(n){e.set(Fn.some(n))},isSet:function(){return e.get().isSome()},on:function(n){e.get().each(n)}}}function xd(n){function r(e,n,t){return Ls.sketch({dom:Us('<span class="${prefix}-icon-'+n+' ${prefix}-icon"></span>'),action:function(n){U(n,u,{direction:e})},buttonBehaviours:Bo([km.config({disableClass:_i.resolve("toolbar-navigation-disabled"),disabled:!t})])})}function o(n,o){var i=Yi(n.element(),"."+_i.resolve("serialised-dialog-screen"));Qi(n.element(),"."+_i.resolve("serialised-dialog-chain")).each(function(r){0<=c.state.currentScreen.get()+o&&c.state.currentScreen.get()+o<i.length&&(gi(r,"left").each(function(n){var e=parseInt(n,10),t=Mf(i[0]);Gi(r,"left",e-o*t+"px")}),c.state.currentScreen.set(c.state.currentScreen.get()+o))})}function i(e){var n=Yi(e.element(),"input");Fn.from(n[c.state.currentScreen.get()]).each(function(n){e.getSystem().getByDom(n).each(function(n){!function(n,e){n.getSystem().triggerFocus(e,n.element())}(e,n.element())})});var t=f.get(e);Cu.highlightAt(t,c.state.currentScreen.get())}var u="navigateEvent",e=Sr([Yt("fields"),tr("maxFieldIndex",n.fields.length-1),Yt("onExecute"),Yt("getInitialValue"),or("state",function(){return{dialogSwipeState:bd(),currentScreen:Pn(0)}})]),c=Xt("SerialisedDialog",e,n),a=js(Dm(function(t){return{dom:Us('<div class="${prefix}-serialised-dialog"></div>'),components:[dm.sketch({dom:Us('<div class="${prefix}-serialised-dialog-chain" style="left: 0px; position: absolute;"></div>'),components:de(c.fields,function(n,e){return e<=c.maxFieldIndex?dm.sketch({dom:Us('<div class="${prefix}-serialised-dialog-screen"></div>'),components:[r(-1,"previous",0<e),t.field(n.name,n.spec),r(1,"next",e<c.maxFieldIndex)]}):t.field(n.name,n.spec)})})],formBehaviours:Bo([Vi(function(n,e){!function(n,e){Qi(n.element(),"."+_i.resolve("serialised-dialog-chain")).each(function(n){Gi(n,"left",-c.state.currentScreen.get()*e.width+"px")})}(n,e)}),Va.config({mode:"special",focusIn:function(n){i(n)},onTab:function(n){return o(n,1),Fn.some(!0)},onShiftTab:function(n){return o(n,-1),Fn.some(!0)}}),am("form-events",[Eo(function(e,n){c.state.currentScreen.set(0),c.state.dialogSwipeState.clear();var t=f.get(e);Cu.highlightFirst(t),c.getInitialValue(e).each(function(n){fl.setValue(e,n)})}),Mo(c.onExecute),_r(Ae(),function(n,e){"left"===e.event().raw().propertyName&&i(n)}),_r(u,function(n,e){var t=e.event().direction();o(n,t)})])])}})),f=js({dom:Us('<div class="${prefix}-dot-container"></div>'),behaviours:Bo([Cu.config({highlightClass:_i.resolve("dot-active"),itemClass:_i.resolve("dot-item")})]),components:B(c.fields,function(n,e){return e<=c.maxFieldIndex?[bf('<div class="${prefix}-dot-item ${prefix}-icon-full-dot ${prefix}-icon"></div>')]:[]})});return{dom:Us('<div class="${prefix}-serializer-wrapper"></div>'),components:[a.asSpec(),f.asSpec()],behaviours:Bo([Va.config({mode:"special",focusIn:function(n){var e=a.get(n);Va.focusIn(e)}}),am("serializer-wrapper-events",[_r(xe(),function(n,e){var t=e.event();c.state.dialogSwipeState.set(Mm(t.raw().touches[0].clientX))}),_r(we(),function(n,e){var t=e.event();c.state.dialogSwipeState.on(function(n){e.event().prevent(),c.state.dialogSwipeState.set(Im(n,t.raw().touches[0].clientX))})}),_r(Se(),function(r){c.state.dialogSwipeState.on(function(n){var e=a.get(r),t=-1*Rm(n);o(e,t)})})])])}}function wd(e){function n(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+bo(e().element())+" is not in context.")}}return{debugInfo:b("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:b(!1)}}function Sd(n,o){var i={};return Nn(n,function(n,r){Nn(n,function(n,e){var t=kn(e,[])(i);i[e]=t.concat([o(r,n)])})}),i}function Td(n){return n.cHandler}function Od(n,e){return{name:b(n),handler:b(e)}}function kd(n,e,t){var r=x(x({},t),function(n,e){var t={};return C(n,function(n){t[n.name()]=n.handlers(e)}),t}(e,n));return Sd(r,Od)}function Ed(n){var i=function(n){return ce(n)?{can:b(!0),abort:b(!1),run:n}:n}(n);return function(n,e){for(var t=[],r=2;r<arguments.length;r++)t[r-2]=arguments[r];var o=[n,e].concat(t);i.abort.apply(undefined,o)?e.stop():i.can.apply(undefined,o)&&i.run.apply(undefined,o)}}function Cd(n,e,t){var r=e[t];return r?function(u,c,n,a){var e=n.slice(0);try{var t=e.sort(function(n,e){var t=n[c](),r=e[c](),o=a.indexOf(t),i=a.indexOf(r);if(-1===o)throw new Error("The ordering for "+u+" does not have an entry for "+t+".\nOrder specified: "+JSON.stringify(a,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(a,null,2));return o<i?-1:i<o?1:0});return vt.value(t)}catch(r){return vt.error([r])}}("Event: "+t,"name",n,r).map(function(n){var e=de(n,function(n){return n.handler()});return Vr(e)}):function(n,e){return vt.error(["The event ("+n+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(de(e,function(n){return n.name()}),null,2)])}(t,n)}function Dd(n){return $t("custom.definition",Sr([kr("dom","dom",Tt(),Sr([Yt("tag"),tr("styles",{}),tr("classes",[]),tr("attributes",{}),Qt("value"),Qt("innerHtml")])),Yt("components"),Yt("uid"),tr("events",{}),tr("apis",{}),kr("eventOrder","eventOrder",function(n){return St.mergeWithThunk(b(n))}({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],touchstart:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]}),Dr()),Qt("domModification")]),n)}function Md(e,n){C(n,function(n){io(e,n)})}function Id(e,n){C(n,function(n){co(e,n)})}function Rd(n,e){return function(e,n){var t=de(n,function(n){return nr(n.name(),[Yt("config"),tr("state",Ao)])}),r=$t("component.behaviours",Sr(t),e.behaviours).fold(function(n){throw new Error(Cr(n)+"\nComplete spec:\n"+JSON.stringify(e,null,2))},function(n){return n});return{list:n,data:S(r,function(n){var e=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return e}})}}(n,e)}function Ad(n){var e=function(n){var e=kn("behaviours",{})(n),t=D(Bn(e),function(n){return e[n]!==undefined});return de(t,function(n){return e[n].me})}(n);return Rd(n,e)}function Fd(n,e,t){var r=function(n){return x(x({},n.dom),{uid:n.uid,domChildren:de(n.components,function(n){return n.element()})})}(n),o=function(n){return n.domModification.fold(function(){return Ur({})},Ur)}(n),i={"alloy.base.modification":o};return function(n,e){return x(x({},n),{attributes:x(x({},n.attributes),e.attributes),styles:x(x({},n.styles),e.styles),classes:n.classes.concat(e.classes)})}(r,0<e.length?function(e,n,t,r){var o=x({},n);C(t,function(n){o[n.name()]=n.exhibit(e,r)});function i(n){return M(n,function(n,e){return x(x({},e.modification),n)},{})}var u=Sd(o,function(n,e){return{name:n,modification:e}}),c=M(u.classes,function(n,e){return e.modification.concat(n)},[]),a=i(u.attributes),f=i(u.styles);return Ur({classes:c,attributes:a,styles:f})}(t,i,e,r):o)}function Bd(n,e,t){var r={"alloy.base.behaviour":function(n){return n.events}(n)};return function(n,e,t,r){var o=kd(n,t,r);return Pm(o,e)}(t,n.eventOrder,e,r).getOrDie()}function Vd(t){function n(){return s}var r=Pn(jm),e=Wt(Dd(t)),o=Ad(t),i=function(n){return n.list}(o),u=function(n){return n.data}(o),c=function(n){var e=Je.fromTag(n.tag);Jr(e,n.attributes),Md(e,n.classes),di(e,n.styles),n.innerHtml.each(function(n){return ho(e,n)});var t=n.domChildren;return gn(e,t),n.value.each(function(n){ud(e,n)}),n.uid,mf(e,n.uid),e}(Fd(e,i,u)),a=Bd(e,i,u),f=Pn(e.components),s={getSystem:r.get,config:function(n){var e=u;return(ce(e[n.name()])?e[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(t,null,2))})()},hasConfigured:function(n){return ce(u[n.name()])},spec:b(t),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return e.apis},connect:function(n){r.set(n)},disconnect:function(){r.set(wd(n))},element:b(c),syncComponents:function(){var n=at(c),e=B(n,function(n){return r.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(e)},components:f.get,events:b(a)};return s}function Nd(n){var e=Nm(n),t=e.events,r=u(e,["events"]),o=function(n){var e=kn("components",[])(n);return de(e,Lm)}(r),i=x(x({},r),{events:x(x({},Vm),t),components:o});return vt.value(Vd(i))}function jd(n){var e=Je.fromText(n);return Hm({element:e})}function _d(n){(go(n.element()).isNone()||Li.isFocused(n))&&(Li.isFocused(n)||Li.focus(n),U(n,Um,{item:n}))}function Pd(n){U(n,$m,{item:n})}function Hd(n,e,t,r){var o=n.getSystem().build(r);mt(n,o,t)}function zd(n,e,t,r){var o=rg(n);R(o,function(n){return cn(r.element(),n.element())}).each(yn)}function Ld(e,n,t,r,o){var i=rg(e);return Fn.from(i[r]).map(function(n){return zd(e,0,0,n),o.each(function(n){Hd(e,0,function(n,e){!function(n,e,t){sn(n,t).fold(function(){ft(n,e)},function(n){ln(n,e)})}(n,e,r)},n)}),n})}function Gd(n,e){var t={};Nn(n,function(n,e){C(n,function(n){t[n]=e})});var r=e,o=function(n){return jn(n,function(n,e){return{k:n,v:e}})}(e),i=S(o,function(n,e){return[e].concat(ig(t,r,o,e))});return S(t,function(n){return Dt(i,n).getOr([n])})}function Ud(n,e,t,r){return Dt(e.routes,r.start).bind(function(n){return Dt(n,r.destination)})}function $d(t,r,n){(function(e,t,r){return lg(e,t).bind(function(n){return sg(e,t,r,n)})})(t,r,n).each(function(n){var e=n.transition;co(t.element(),e.transitionClass),no(t.element(),r.destinationAttr)})}function Wd(n,e,t,r){$d(n,e,t),Zr(n.element(),e.stateAttr)&&Qr(n.element(),e.stateAttr)!==r&&e.onFinish(n,r),Kr(n.element(),e.stateAttr,r)}function Xd(n){return Dt(n,"format").getOr(n.title)}function qd(n){return Mn(n,"items")?function(n){var e=xt(On(n,["items"]),{menu:!0}),t=wg(n.items);return{item:e,menus:xt(t.menus,En(n.title,t.items)),expansions:xt(t.expansions,En(n.title,n.title))}}(n):{item:n,menus:{},expansions:{}}}function Yd(n){var e=n.replace(/\|/g," ").trim();return 0<e.length?e.split(/\s+/):[]}function Kd(n){var e=n.toolbar!==undefined?n.toolbar:kg;return ie(e)?Eg(e):Yd(e)}function Jd(n){function e(){n.stopPropagation()}function t(){n.preventDefault()}var r=Je.fromDom(n.target),o=i(t,e);return function(n,e,t,r,o,i,u){return{target:b(n),x:b(e),y:b(t),stop:r,prevent:o,kill:i,raw:b(u)}}(r,n.clientX,n.clientY,e,t,o,n)}function Qd(n,e,t,r,o){var i=function(e,t){return function(n){e(n)&&t(Jd(n))}}(t,r);return n.dom().addEventListener(e,i,o),{unbind:d(Mg,n,e,i,o)}}function Zd(n,e,t){return function(n,e,t,r){return Qd(n,e,t,r,!1)}(n,e,Ig,t)}function nm(n,e,t){return function(n,e,t,r){return Qd(n,e,t,r,!0)}(n,e,Ig,t)}function em(n){var e=n.matchMedia("(orientation: portrait)").matches;return{isPortrait:b(e)}}var tm,rm,om=function(n){var e=Je.fromDom(n.selection.getStart());return Zi(e,"a")},im={getInfo:function(n){return om(n).fold(function(){return function(n){return{url:"",text:n.selection.getContent({format:"text"}),title:"",target:"",link:Fn.none()}}(n)},function(n){return function(n){var e=nd(n),t=Qr(n,"href"),r=Qr(n,"title"),o=Qr(n,"target");return{url:td(t),text:e!==t?td(e):"",title:td(r),target:td(o),link:Fn.some(n)}}(n)})},applyInfo:function(e,o){o.url.toOption().filter(ed).fold(function(){!function(e,n){n.link.bind(y).each(function(n){e.execCommand("unlink")})}(e,o)},function(t){var r=function(n,e){var t={};return t.href=n,e.title.toOption().filter(ed).each(function(n){t.title=n}),e.target.toOption().filter(ed).each(function(n){t.target=n}),t}(t,o);o.link.bind(y).fold(function(){var n=o.text.toOption().filter(ed).getOr(t);e.insertContent(e.dom.createHTML("a",r,e.dom.encode(n)))},function(e){var n=rd(e,t,o);Jr(e,r),n.each(function(n){!function(n,e){n.dom().textContent=e}(e,n)})})})},query:om},um=L(),cm=function(n,e){(um.os.isAndroid()?od:t)(e,n)},am=function(n,e){return{key:n,value:{config:{},me:function(n,e){var t=Nr(e);return qr({fields:[Yt("enabled")],name:n,active:{events:b(t)}})}(n,e),configAsRaw:b({}),initialConfig:{},state:Ao}}},fm=/* */Object.freeze({getCurrent:function(n,e,t){return e.find(n)}}),sm=[Yt("find")],lm=qr({fields:sm,name:"composing",apis:fm}),dm=hf({name:"Container",factory:function(n){var e=n.dom,t=e.attributes,r=u(e,["attributes"]);return{uid:n.uid,dom:x({tag:"div",attributes:x({role:"presentation"},t)},r),components:n.components,behaviours:gc(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[tr("components",[]),mc("containerBehaviours",[]),tr("events",{}),tr("domModification",{}),tr("eventOrder",{})]}),mm=hf({name:"DataField",factory:function(t){return{uid:t.uid,dom:t.dom,behaviours:ja(t.dataBehaviours,[fl.config({store:{mode:"memory",initialValue:t.getInitialValue()}}),lm.config({find:Fn.some})]),events:Nr([Eo(function(n,e){fl.setValue(n,t.getInitialValue())})])}},configFields:[Yt("uid"),Yt("dom"),Yt("getInitialValue"),Na("dataBehaviours",[fl,lm])]}),gm=b([Qt("data"),tr("inputAttributes",{}),tr("inputStyles",{}),tr("tag","input"),tr("inputClasses",[]),Ko("onSetValue"),tr("styles",{}),tr("eventOrder",{}),mc("inputBehaviours",[fl,Li]),tr("selectOnFocus",!0)]),pm=hf({name:"Input",configFields:gm(),factory:function(n,e){return{uid:n.uid,dom:function(n){return{tag:n.tag,attributes:x({type:"text"},n.inputAttributes),styles:n.inputStyles,classes:n.inputClasses}}(n),components:[],behaviours:cd(n),eventOrder:n.eventOrder}}}),hm=/* */Object.freeze({exhibit:function(n,e){return Ur({attributes:Cn([{key:e.tabAttr,value:"true"}])})}}),vm=[tr("tabAttr","data-alloy-tabstop")],ym=qr({fields:vm,name:"tabstopping",active:hm}),bm=tinymce.util.Tools.resolve("tinymce.util.I18n"),xm=["input","button","textarea","select"],wm=function(e,n,t){n.disableClass.each(function(n){io(e.element(),n)}),(sd(e,n)?ld:md)(e),n.onDisabled(e)},Sm=/* */Object.freeze({enable:pd,disable:wm,isDisabled:hd,onLoad:fd,set:function(n,e,t,r){(r?wm:pd)(n,e,t)}}),Tm=/* */Object.freeze({exhibit:function(n,e,t){return Ur({classes:e.disabled?e.disableClass.map(_).getOr([]):[]})},events:function(t,n){return Nr([jr(Pe(),function(n,e){return hd(n,t)}),$r(t,n,fd)])}}),Om=[tr("disabled",!1),tr("useNative",!0),Qt("disableClass"),Ko("onDisabled"),Ko("onEnabled")],km=qr({fields:Om,name:"disabling",active:Tm,apis:Sm}),Em=[mc("formBehaviours",[fl])],Cm=function(r,n,e){return{uid:r.uid,dom:r.dom,components:n,behaviours:pc(r.formBehaviours,[fl.config({store:{mode:"manual",getValue:function(n){var e=function(n,e){var t=n.getSystem();return S(e.partUids,function(n,e){return b(t.getByUid(n))})}(n,r);return S(e,function(n,e){return n().bind(function(n){return function(n,e){return n.fold(function(){return vt.error(e)},vt.value)}(lm.getCurrent(n),"missing current")}).map(fl.getValue)})},setValue:function(t,n){Nn(n,function(e,n){kc(t,r,n).each(function(n){lm.getCurrent(n).each(function(n){fl.setValue(n,e)})})})}}})]),apis:{getField:function(n,e){return kc(n,r,e).bind(lm.getCurrent)}}}},Dm=(Ic(function(n,e,t){return n.getField(e,t)}),function(n){var t,e=(t=[],{field:function(n,e){return t.push(n),function(n,e,t){return{uiType:Ga(),owner:n,name:e,config:t,validated:{}}}("form",vd(n),e)},record:function(){return t}}),r=n(e),o=e.record(),i=de(o,function(n){return tf({name:n,pname:vd(n)})});return Fc("form",Em,i,Cm,r)}),Mm=function(n){return{xValue:n,points:[]}},Im=function(n,e){if(e===n.xValue)return n;var t=0<e-n.xValue?1:-1,r={direction:t,xValue:e};return{xValue:e,points:(0===n.points.length?[]:n.points[n.points.length-1].direction===t?n.points.slice(0,n.points.length-1):n.points).concat([r])}},Rm=function(n){if(0===n.points.length)return 0;var e=n.points[0].direction,t=n.points[n.points.length-1].direction;return-1===e&&-1===t?-1:1===e&&1===t?1:0},Am=X(function(t,r){return[{label:"the link group",items:[xd({fields:[ad("url","Type or paste URL"),ad("text","Link text"),ad("title","Link title"),ad("target","Link target"),function(n){return{name:n,spec:mm.sketch({dom:{tag:"span",styles:{display:"none"}},getInitialValue:function(){return Fn.none()}})}}("link")],maxFieldIndex:["url","text","title","target"].length-1,getInitialValue:function(){return Fn.some(im.getInfo(r))},onExecute:function(n){var e=fl.getValue(n);im.applyInfo(r,e),t.restoreToolbar(),r.focus()}})]}]}),Fm=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strikethrough",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Alignment",items:[{title:"Left",icon:"alignleft",format:"alignleft"},{title:"Center",icon:"aligncenter",format:"aligncenter"},{title:"Right",icon:"alignright",format:"alignright"},{title:"Justify",icon:"alignjustify",format:"alignjustify"}]}],Bm=Nr([(tm=Ve(),rm=function(n,e){var t=e.event().originator(),r=e.event().target();return!function(n,e,t){return cn(e,n.element())&&!cn(e,t)}(n,t,r)||(l.console.warn(Ve()+" did not get interpreted by the desired target. \nOriginator: "+bo(t)+"\nTarget: "+bo(r)+"\nCheck the "+Ve()+" event handlers"),!1)},{key:tm,value:Br({can:rm})})]),Vm=/* */Object.freeze({events:Bm}),Nm=y,jm=wd(),_m=function(n,e){return function(n,e){return{cHandler:n,purpose:b(e)}}(d.apply(undefined,[n.handler].concat(e)),n.purpose())},Pm=function(n,i){var e=_n(n,function(r,o){return(1===r.length?vt.value(r[0].handler()):Cd(r,i,o)).map(function(n){var e=Ed(n),t=1<r.length?D(i[o],function(e){return E(r,function(n){return n.name()===e})}).join(" > "):r[0].name();return En(o,function(n,e){return{handler:n,purpose:b(e)}}(e,t))})});return Dn(e,{})},Hm=function(n){var e=Xt("external.component",Pt([Yt("element"),Qt("uid")]),n),t=Pn(wd());e.uid.each(function(n){mf(e.element,n)});var r={getSystem:t.get,config:Fn.none,hasConfigured:b(!1),connect:function(n){t.set(n)},disconnect:function(){t.set(wd(function(){return r}))},getApis:function(){return{}},element:b(e.element),spec:b(n),readState:b("No state"),syncComponents:w,components:b([]),events:b({})};return Mc(r)},zm=Rc,Lm=function(e){return function(n){return Dt(n,af)}(e).fold(function(){var n=e.hasOwnProperty("uid")?e:x({uid:zm("")},e);return Nd(n).getOrDie()},function(n){return n})},Gm=Mc,Um="alloy.item-hover",$m="alloy.item-focus",Wm=b(Um),Xm=b($m),qm=[Yt("data"),Yt("components"),Yt("dom"),tr("hasSubmenu",!1),Qt("toggling"),Na("itemBehaviours",[Fi,Li,Va,fl]),tr("ignoreFocus",!1),tr("domModification",{}),ni("builder",function(n){return{dom:n.dom,domModification:x(x({},n.domModification),{attributes:x(x(x({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:ja(n.itemBehaviours,[n.toggling.fold(Fi.revoke,function(n){return Fi.config(x({aria:{mode:"checked"}},n))}),Li.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){Pd(n)}}),Va.config({mode:"execution"}),fl.config({store:{mode:"memory",initialValue:n.data}}),am("item-type-events",function a(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;var r=Array(n),o=0;for(e=0;e<t;e++)for(var i=arguments[e],u=0,c=i.length;u<c;u++,o++)r[o]=i[u];return r}(ai(),[_r(Ee(),_d),_r(He(),Li.focus)]))]),components:n.components,eventOrder:n.eventOrder}}),tr("eventOrder",{})],Ym=[Yt("dom"),Yt("components"),ni("builder",function(n){return{dom:n.dom,components:n.components,events:Nr([function(n){return _r(n,function(n,e){e.stop()})}(He())])}})],Km=b([tf({name:"widget",overrides:function(e){return{behaviours:Bo([fl.config({store:{mode:"manual",getValue:function(n){return e.data},setValue:function(){}}})])}}})]),Jm=[Yt("uid"),Yt("data"),Yt("components"),Yt("dom"),tr("autofocus",!1),tr("ignoreFocus",!1),Na("widgetBehaviours",[fl,Li,Va]),tr("domModification",{}),Dc(Km()),ni("builder",function(t){function r(n){return kc(n,t,"widget").map(function(n){return Va.focusIn(n),n})}function n(n,e){return Iu(e.event().target())||t.autofocus&&e.setSource(n.element()),Fn.none()}var e=Tc(0,t,Km()),o=Oc("item-widget",t,e.internals());return{dom:t.dom,components:o,domModification:t.domModification,events:Nr([Mo(function(n,e){r(n).each(function(n){e.stop()})}),_r(Ee(),_d),_r(He(),function(n,e){t.autofocus?r(n):Li.focus(n)})]),behaviours:ja(t.widgetBehaviours,[fl.config({store:{mode:"memory",initialValue:t.data}}),Li.config({ignore:t.ignoreFocus,onFocus:function(n){Pd(n)}}),Va.config({mode:"special",focusIn:t.autofocus?function(n){r(n)}:jo(),onLeft:n,onRight:n,onEscape:function(n,e){return Li.isFocused(n)||t.autofocus?(t.autofocus&&e.setSource(n.element()),Fn.none()):(Li.focus(n),Fn.some(!0))}})])}})],Qm=qt("type",{widget:Jm,item:qm,separator:Ym}),Zm=b([of({factory:{sketch:function(n){var e=Xt("menu.spec item",Qm,n);return e.builder(e)}},name:"items",unit:"item",defaults:function(n,e){return e.hasOwnProperty("uid")?e:x(x({},e),{uid:Rc("item")})},overrides:function(n,e){return{type:e.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),ng=b([Yt("value"),Yt("items"),Yt("dom"),Yt("components"),tr("eventOrder",{}),mc("menuBehaviours",[Cu,fl,lm,Va]),rr("movement",{mode:"menu",moveOnTab:!0},qt("mode",{grid:[ki(),ni("config",function(n,e){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:e.initSize.numColumns,numRows:e.initSize.numRows},focusManager:n.focusManager}})],matrix:[ni("config",function(n,e){return{mode:"matrix",selectors:{row:e.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),Yt("rowSelector")],menu:[tr("moveOnTab",!0),ni("config",function(n,e){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:e.moveOnTab,focusManager:n.focusManager}})]})),Kt("markers",Ti()),tr("fakeFocus",!1),tr("focusManager",pu()),Ko("onHighlight")]),eg=b("alloy.menu-focus"),tg=vf({name:"Menu",configFields:ng(),partFields:Zm(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:pc(n.menuBehaviours,[Cu.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),fl.config({store:{mode:"memory",initialValue:n.value}}),lm.config({find:Fn.some}),Va.config(n.movement.config(n,n.movement))]),events:Nr([_r(Xm(),function(e,t){var n=t.event();e.getSystem().getByDom(n.target()).each(function(n){Cu.highlight(e,n),t.stop(),U(e,eg(),{menu:e,item:n})})}),_r(Wm(),function(n,e){var t=e.event().item();Cu.highlight(n,t)})]),components:e,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),rg=function(n,e){return n.components()},og=qr({fields:[],name:"replacing",apis:/* */Object.freeze({append:function(n,e,t,r){Hd(n,0,ft,r)},prepend:function(n,e,t,r){Hd(n,0,mn,r)},remove:zd,replaceAt:Ld,replaceBy:function(e,n,t,r,o){var i=rg(e);return A(i,r).bind(function(n){return Ld(e,0,0,n,o)})},set:function(e,n,t,r){!function(n,t){var r=an(t),e=mo(r).bind(function(e){function n(n){return cn(e,n)}return n(t)?Fn.some(t):Fr(t,n)}),o=n(t);e.each(function(e){mo(r).filter(function(n){return cn(n,e)}).fold(function(){so(e)},w)})}(function(){var n=de(r,e.getSystem().build);vn(e,n)},e.element())},contents:rg})}),ig=function(t,r,o,n){return Dt(o,n).bind(function(n){return Dt(t,n).bind(function(n){var e=ig(t,r,o,n);return Fn.some([n].concat(e))})}).getOr([])},ug=function(n){return"prepared"===n.type?Fn.some(n.menu):Fn.none()},cg={init:function(){function o(t){return function(n,e){for(var t=Bn(n),r=0,o=t.length;r<o;r++){var i=t[r],u=n[i];if(e(u,i,n))return Fn.some(u)}return Fn.none()}(i.get(),function(n,e){return n===t})}var i=Pn({}),u=Pn({}),c=Pn({}),a=Pn(Fn.none()),f=Pn({}),s=function(n){return e(n).bind(ug)},e=function(n){return Dt(u.get(),n)},t=function(n){return Dt(i.get(),n)};return{setMenuBuilt:function(n,e){var t;u.set(x(x({},u.get()),((t={})[n]={type:"prepared",menu:e},t)))},setContents:function(n,e,t,r){a.set(Fn.some(n)),i.set(t),u.set(e),f.set(r);var o=Gd(r,t);c.set(o)},expand:function(t){return Dt(i.get(),t).map(function(n){var e=Dt(c.get(),t).getOr([]);return[n].concat(e)})},refresh:function(n){return Dt(c.get(),n)},collapse:function(n){return Dt(c.get(),n).bind(function(n){return 1<n.length?Fn.some(n.slice(1)):Fn.none()})},lookupMenu:e,lookupItem:t,otherMenus:function(n){var e=f.get();return j(Bn(e),n)},getPrimary:function(){return a.get().bind(s)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),c.set({}),a.set(Fn.none())},isClear:function(){return a.get().isNone()},getTriggeringPath:function(n,r){var e=D(t(n).toArray(),function(n){return s(n).isSome()});return Dt(c.get(),n).bind(function(n){var t=N(e.concat(n));return function(n){for(var e=[],t=0;t<n.length;t++){var r=n[t];if(!r.isSome())return Fn.none();e.push(r.getOrDie())}return Fn.some(e)}(B(t,function(n,e){return function(n,t,r){return s(n).bind(function(e){return o(n).bind(function(n){return t(n).map(function(n){return{triggeredMenu:e,triggeringItem:n,triggeringPath:r}})})})}(n,r,t.slice(0,e+1)).fold(function(){return a.get().is(n)?[]:[Fn.none()]},function(n){return[Fn.some(n)]})}))})}}},extractPreparedMenu:ug},ag=b("collapse-item"),fg=hf({name:"TieredMenu",configFields:[Zo("onExecute"),Zo("onEscape"),Qo("onOpenMenu"),Qo("onOpenSubmenu"),Qo("onRepositionMenu"),Ko("onCollapseMenu"),tr("highlightImmediately",!0),Jt("data",[Yt("primary"),Yt("menus"),Yt("expansions")]),tr("fakeFocus",!1),Ko("onHighlight"),Ko("onHover"),Jt("markers",[Yt("backgroundMenu")].concat(wi()).concat(Si())),Yt("dom"),tr("navigateOnHover",!0),tr("stayInDom",!1),mc("tmenuBehaviours",[Va,Cu,lm,og]),tr("eventOrder",{})],apis:{collapseMenu:function(n,e){n.collapseMenu(e)},highlightPrimary:function(n,e){n.highlightPrimary(e)},repositionMenus:function(n,e){n.repositionMenus(e)}},factory:function(c,n){function r(r,o,n){return S(n,function(n,e){function t(){return tg.sketch(x(x({dom:n.dom},n),{value:e,items:n.items,markers:c.markers,fakeFocus:c.fakeFocus,onHighlight:c.onHighlight,focusManager:c.fakeFocus?function(){function o(n){return Cu.getHighlighted(n).map(function(n){return n.element()})}return{get:o,set:function(e,n){var t=o(e);e.getSystem().getByDom(n).fold(w,function(n){Cu.highlight(e,n)});var r=o(e);gu(e,t,r)}}}():pu()}))}return e===o?{type:"prepared",menu:r.getSystem().build(t())}:{type:"notbuilt",nbMenu:t}})}function a(n){return fl.getValue(n).value}function u(e,n){Cu.highlight(e,n),Cu.getHighlighted(n).orThunk(function(){return Cu.getFirst(n)}).each(function(n){W(e,n.element(),He())})}function f(e,n){return xo(de(n,function(n){return e.lookupMenu(n).bind(function(n){return"prepared"===n.type?Fn.some(n.menu):Fn.none()})}))}function s(e,n,t){var r=f(n,n.otherMenus(t));C(r,function(n){Id(n.element(),[c.markers.backgroundMenu]),c.stayInDom||og.remove(e,n)})}function l(n,r){var e=function(r){return o.get().getOrThunk(function(){var t={},n=Yi(r.element(),"."+c.markers.item),e=D(n,function(n){return"true"===Qr(n,"aria-haspopup")});return C(e,function(n){r.getSystem().getByDom(n).each(function(n){var e=a(n);t[e]=n})}),o.set(Fn.some(t)),t})}(n);Nn(e,function(n,e){var t=k(r,e);Kr(n.element(),"aria-expanded",t)})}function d(r,o,i){return Fn.from(i[0]).bind(function(n){return o.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return Fn.none();var e=n.menu,t=f(o,i.slice(1));return C(t,function(n){io(n.element(),c.markers.backgroundMenu)}),K(e.element())||og.append(r,Gm(e)),Id(e.element(),[c.markers.backgroundMenu]),u(r,e),s(r,o,i),Fn.some(e)})})}var m,e,o=Pn(Fn.none()),g=cg.init(),i=function(n){return S(c.data.menus,function(n,e){return B(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})};(e=m=m||{})[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent";function p(o,i,u){void 0===u&&(u=m.HighlightSubmenu);var n=a(i);return g.expand(n).bind(function(r){return l(o,r),Fn.from(r[0]).bind(function(t){return g.lookupMenu(t).bind(function(n){var e=function(n,e,t){if("notbuilt"!==t.type)return t.menu;var r=n.getSystem().build(t.nbMenu());return g.setMenuBuilt(e,r),r}(o,t,n);return K(e.element())||og.append(o,Gm(e)),c.onOpenSubmenu(o,i,e,N(r)),u===m.HighlightSubmenu?(Cu.highlightFirst(e),d(o,g,r)):(Cu.dehighlightAll(e),Fn.some(i))})})})}function h(e,t){var n=a(t);return g.collapse(n).bind(function(n){return l(e,n),d(e,g,n).map(function(n){return c.onCollapseMenu(e,t,n),n})})}function t(t){return function(e,n){return Zi(n.getSource(),"."+c.markers.item).bind(function(n){return e.getSystem().getByDom(n).toOption().bind(function(n){return t(e,n).map(function(){return!0})})})}}function v(n){return Cu.getHighlighted(n).bind(Cu.getHighlighted)}var y=Nr([_r(eg(),function(t,r){var n=r.event().item();g.lookupItem(a(n)).each(function(){var n=r.event().menu();Cu.highlight(t,n);var e=a(r.event().item());g.refresh(e).each(function(n){return s(t,g,n)})})}),Mo(function(e,n){var t=n.event().target();e.getSystem().getByDom(t).each(function(n){0===a(n).indexOf("collapse-item")&&h(e,n),p(e,n,m.HighlightSubmenu).fold(function(){c.onExecute(e,n)},function(){})})}),Eo(function(e,n){(function(n){var e=r(n,c.data.primary,c.data.menus),t=i();return g.setContents(c.data.primary,e,c.data.expansions,t),g.getPrimary()})(e).each(function(n){og.append(e,Gm(n)),c.onOpenMenu(e,n),c.highlightImmediately&&u(e,n)})})].concat(c.navigateOnHover?[_r(Wm(),function(n,e){var t=e.event().item();!function(e,n){var t=a(n);g.refresh(t).bind(function(n){return l(e,n),d(e,g,n)})}(n,t),p(n,t,m.HighlightParent),c.onHover(n,t)})]:[])),b={collapseMenu:function(e){v(e).each(function(n){h(e,n)})},highlightPrimary:function(e){g.getPrimary().each(function(n){u(e,n)})},repositionMenus:function(r){g.getPrimary().bind(function(e){return v(r).bind(function(n){var e=a(n),t=function(n){return _n(n,function(n){return n})}(g.getMenus()),r=xo(de(t,cg.extractPreparedMenu));return g.getTriggeringPath(e,function(n){return function(n,e,t){return wo(e,function(n){if(!n.getSystem().isConnected())return Fn.none();var e=Cu.getCandidates(n);return R(e,function(n){return a(n)===t})})}(0,r,n)})}).map(function(n){return{primary:e,triggeringPath:n}})}).fold(function(){(function(n){return Fn.from(n.components()[0]).filter(function(n){return"menu"===Qr(n.element(),"role")})})(r).each(function(n){c.onRepositionMenu(r,n,[])})},function(n){var e=n.primary,t=n.triggeringPath;c.onRepositionMenu(r,e,t)})}};return{uid:c.uid,dom:c.dom,markers:c.markers,behaviours:pc(c.tmenuBehaviours,[Va.config({mode:"special",onRight:t(function(n,e){return Iu(e.element())?Fn.none():p(n,e,m.HighlightSubmenu)}),onLeft:t(function(n,e){return Iu(e.element())?Fn.none():h(n,e)}),onEscape:t(function(n,e){return h(n,e).orThunk(function(){return c.onEscape(n,e).map(function(){return n})})}),focusIn:function(e,n){g.getPrimary().each(function(n){W(e,n.element(),He())})}}),Cu.config({highlightClass:c.markers.selectedMenu,itemClass:c.markers.menu}),lm.config({find:function(n){return Cu.getHighlighted(n)}}),og.config({})]),eventOrder:c.eventOrder,apis:b,events:y}},extraApis:{tieredData:function(n,e,t){return{primary:n,menus:e,expansions:t}},singleData:function(n,e){return{primary:n,menus:En(n,e),expansions:{}}},collapseItem:function(n){return{value:yc(ag()),meta:{text:n}}}}}),sg=function(n,e,t,r){return Ud(0,e,0,r).bind(function(e){return e.transition.map(function(n){return{transition:n,route:e}})})},lg=function(n,e,t){var r=n.element();return Zr(r,e.destinationAttr)?Fn.some({start:Qr(n.element(),e.stateAttr),destination:Qr(n.element(),e.destinationAttr)}):Fn.none()},dg=/* */Object.freeze({findRoute:Ud,disableTransition:$d,getCurrentRoute:lg,jumpTo:Wd,progressTo:function(t,r,o,i){!function(n,e){Zr(n.element(),e.destinationAttr)&&(Kr(n.element(),e.stateAttr,Qr(n.element(),e.destinationAttr)),no(n.element(),e.destinationAttr))}(t,r);var n=function(n,e,t,r){return{start:Qr(n.element(),e.stateAttr),destination:r}}(t,r,0,i);sg(t,r,o,n).fold(function(){Wd(t,r,o,i)},function(n){$d(t,r,o);var e=n.transition;io(t.element(),e.transitionClass),Kr(t.element(),r.destinationAttr,i)})},getState:function(n,e,t){var r=n.element();return Zr(r,e.stateAttr)?Fn.some(Qr(r,e.stateAttr)):Fn.none()}}),mg=/* */Object.freeze({events:function(o,i){return Nr([_r(Ae(),function(t,n){var r=n.event().raw();lg(t,o).each(function(e){Ud(0,o,0,e).each(function(n){n.transition.each(function(n){r.propertyName===n.property&&(Wd(t,o,i,e.destination),o.onTransition(t,e))})})})}),Eo(function(n,e){Wd(n,o,i,o.initialState)})])}}),gg=[tr("destinationAttr","data-transitioning-destination"),tr("stateAttr","data-transitioning-state"),Yt("initialState"),Ko("onTransition"),Ko("onFinish"),Kt("routes",Ut(vt.value,Ut(vt.value,Pt([er("transition",[Yt("property"),Yt("transitionClass")])]))))],pg=qr({fields:gg,name:"transitioning",active:mg,apis:dg,extra:{createRoutes:function(n){var r={};return Nn(n,function(n,e){var t=e.split("<->");r[t[0]]=En(t[1],n),r[t[1]]=En(t[0],n)}),r},createBistate:function(n,e,t){return Cn([{key:n,value:En(e,t)},{key:e,value:En(n,t)}])},createTristate:function(n,e,t,r){return Cn([{key:n,value:Cn([{key:e,value:r},{key:t,value:r}])},{key:e,value:Cn([{key:n,value:r},{key:t,value:r}])},{key:t,value:Cn([{key:n,value:r},{key:e,value:r}])}])}}}),hg=_i.resolve("scrollable"),vg={register:function(n){io(n,hg)},deregister:function(n){co(n,hg)},scrollable:b(hg)},yg=function(n,e,t,r,o){return{data:{value:n,text:e},type:"item",dom:{tag:"div",classes:o?[_i.resolve("styles-item-is-menu")]:[]},toggling:{toggleOnExecute:!1,toggleClass:_i.resolve("format-matches"),selected:t},itemBehaviours:Bo(o?[]:[Bi(n,function(n,e){(e?Fi.on:Fi.off)(n)})]),components:[{dom:{tag:"div",attributes:{style:r},innerHtml:e}}]}},bg=function(n,e,t,r){return{value:n,dom:{tag:"div"},components:[Ls.sketch({dom:{tag:"div",classes:[_i.resolve("styles-collapser")]},components:r?[{dom:{tag:"span",classes:[_i.resolve("styles-collapse-icon")]}},jd(n)]:[jd(n)],action:function(n){if(r){var e=t().get(n);fg.collapseMenu(e)}}}),{dom:{tag:"div",classes:[_i.resolve("styles-menu-items-container")]},components:[tg.parts().items({})],behaviours:Bo([am("adhoc-scrollable-menu",[Eo(function(n,e){Gi(n.element(),"overflow-y","auto"),Gi(n.element(),"-webkit-overflow-scrolling","touch"),vg.register(n.element())}),Co(function(n){pi(n.element(),"overflow-y"),pi(n.element(),"-webkit-overflow-scrolling"),vg.deregister(n.element())})])])}],items:e,menuBehaviours:Bo([pg.config({initialState:"after",routes:pg.createTristate("before","current","after",{transition:{property:"transform",transitionClass:"transitioning"}})})])}},xg=function(r){var n=function(r,o){var n=bg("Styles",[].concat(de(r.items,function(n){return yg(Xd(n),n.title,n.isSelected(),n.getPreview(),Mn(r.expansions,Xd(n)))})),o,!1),e=S(r.menus,function(n,e){var t=de(n,function(n){return yg(Xd(n),n.title,n.isSelected!==undefined&&n.isSelected(),n.getPreview!==undefined?n.getPreview():"",Mn(r.expansions,Xd(n)))});return bg(e,t,o,!0)}),t=xt(e,En("styles",n));return{tmenu:fg.tieredData("styles",t,r.expansions)}}(r.formats,function(){return e}),e=js(fg.sketch({dom:{tag:"div",classes:[_i.resolve("styles-menu")]},components:[],fakeFocus:!0,stayInDom:!0,onExecute:function(n,e){var t=fl.getValue(e);return r.handle(e,t.value),Fn.none()},onEscape:function(){return Fn.none()},onOpenMenu:function(n,e){var t=Mf(n.element());Df(e.element(),t),pg.jumpTo(e,"current")},onOpenSubmenu:function(n,e,t){var r=Mf(n.element()),o=Ji(e.element(),'[role="menu"]').getOrDie("hacky"),i=n.getSystem().getByDom(o).getOrDie();Df(t.element(),r),pg.progressTo(i,"before"),pg.jumpTo(t,"after"),pg.progressTo(t,"current")},onCollapseMenu:function(n,e,t){var r=Ji(e.element(),'[role="menu"]').getOrDie("hacky"),o=n.getSystem().getByDom(r).getOrDie();pg.progressTo(o,"after"),pg.progressTo(t,"current")},navigateOnHover:!1,highlightImmediately:!0,data:n.tmenu,markers:{backgroundMenu:_i.resolve("styles-background-menu"),menu:_i.resolve("styles-menu"),selectedMenu:_i.resolve("styles-selected-menu"),item:_i.resolve("styles-item"),selectedItem:_i.resolve("styles-selected-item")}}));return e.asSpec()},wg=function(n){return M(n,function(n,e){var t=qd(e);return{menus:xt(n.menus,t.menus),items:[t.item].concat(n.items),expansions:xt(n.expansions,t.expansions)}},{menus:{},expansions:{},items:[]})},Sg={expand:wg},Tg=function(r,n){function o(n){return function(){return r.formatter.match(n)}}function i(n){return function(){return r.formatter.getCssText(n)}}var e=Dt(n,"style_formats").getOr(Fm),t=function(n){return de(n,function(n){if(Mn(n,"items")){var e=t(n.items);return xt(function(n){return xt(n,{isSelected:b(!1),getPreview:b("")})}(n),{items:e})}return Mn(n,"format")?function(n){return xt(n,{isSelected:o(n.format),getPreview:i(n.format)})}(n):function(n){var e=yc(n.title),t=xt(n,{format:e,isSelected:o(e),getPreview:i(e)});return r.formatter.register(e,t),t}(n)})};return t(e)},Og=function(t,n,r){var e=function(e,n){var t=function(n){return B(n,function(n){return n.items===undefined?!Mn(n,"format")||e.formatter.canApply(n.format)?[n]:[]:0<t(n.items).length?[n]:[]})},r=t(n);return Sg.expand(r)}(t,n);return xg({formats:e,handle:function(n,e){t.undoManager.transact(function(){Fi.isOn(n)?t.formatter.remove(e):t.formatter.apply(e)}),r()}})},kg=["undo","bold","italic","link","image","bullist","styleselect"],Eg=function(n){return B(n,function(n){return ie(n)?Eg(n):Yd(n)})},Cg=function(e,r){function n(n){return function(){return Ws.forToolbarCommand(r,n)}}function t(n){return function(){return Ws.forToolbarStateCommand(r,n)}}function o(n,e,t){return function(){return Ws.forToolbarStateAction(r,n,e,t)}}function i(){return Og(r,h,function(){r.fire("scrollIntoView")})}function u(n,e){return{isSupported:function(){var e=r.ui.registry.getAll().buttons;return n.forall(function(n){return Mn(e,n)})},sketch:e}}var c=n("undo"),a=n("redo"),f=t("bold"),s=t("italic"),l=t("underline"),d=n("removeformat"),m=o("unlink","link",function(){r.execCommand("unlink",null,!1)}),g=o("unordered-list","ul",function(){r.execCommand("InsertUnorderedList",null,!1)}),p=o("ordered-list","ol",function(){r.execCommand("InsertOrderedList",null,!1)}),h=Tg(r,r.settings);return{undo:u(Fn.none(),c),redo:u(Fn.none(),a),bold:u(Fn.none(),f),italic:u(Fn.none(),s),underline:u(Fn.none(),l),removeformat:u(Fn.none(),d),link:u(Fn.none(),function(){return function(e,t){return Ws.forToolbarStateAction(t,"link","link",function(){var n=Am(e,t);e.setContextToolbar(n),cm(t,function(){e.focusToolbar()}),im.query(t).each(function(n){t.selection.select(n.dom())})})}(e,r)}),unlink:u(Fn.none(),m),image:u(Fn.none(),function(){return Zl(r)}),bullist:u(Fn.some("bullist"),g),numlist:u(Fn.some("numlist"),p),fontsizeselect:u(Fn.none(),function(){return function(n,e){var t={onChange:function(n){Hl.apply(e,n)},getInitialValue:function(){return Hl.get(e)}};return Fs(n,"font-size",function(){return Ns(t)},e)}(e,r)}),forecolor:u(Fn.none(),function(){return Nl(e,r)}),styleselect:u(Fn.none(),function(){return Ws.forToolbar("style-formats",function(n){r.fire("toReading"),e.dropup().appear(i,Fi.on,n)},Bo([Fi.config({toggleClass:_i.resolve("toolbar-button-selected"),toggleOnExecute:!1,aria:{mode:"pressed"}}),Ci.config({channels:Cn([Ni(Uo.orientationChanged(),Fi.off),Ni(Uo.dropupDismissed(),Fi.off)])})]),r)})}},Dg=function(n,t){var e=Kd(n),r={};return B(e,function(n){var e=!Mn(r,n)&&Mn(t,n)&&t[n].isSupported()?[t[n].sketch()]:[];return r[n]=!0,e})},Mg=function(n,e,t,r){n.dom().removeEventListener(e,t,r)},Ig=b(!0),Rg=tinymce.util.Tools.resolve("tinymce.util.Delay"),Ag=em,Fg=function(r,e){var n=Je.fromDom(r),o=null,t=Zd(n,"orientationchange",function(){Rg.clearInterval(o);var n=em(r);e.onChange(n),i(function(){e.onReady(n)})}),i=function(n){Rg.clearInterval(o);var e=r.innerHeight,t=0;o=Rg.setInterval(function(){e!==r.innerHeight?(Rg.clearInterval(o),n(Fn.some(r.innerHeight))):20<t&&(Rg.clearInterval(o),n(Fn.none())),t++},50)};return{onAdjustment:i,destroy:function(){t.unbind()}}},Bg=function(n){var e=L().os.isiOS(),t=em(n).isPortrait();return e&&!t?n.screen.height:n.screen.width};function Vg(n){var e=n.raw();return e.touches===undefined||1!==e.touches.length?Fn.none():Fn.some(e.touches[0])}function Ng(t){var r=Pn(Fn.none()),o=Pn(!1),i=function n(t,r){var o=null;return{cancel:function(){null!==o&&(l.clearTimeout(o),o=null)},schedule:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];o=l.setTimeout(function(){t.apply(null,n),o=null},r)}}}(function(n){t.triggerEvent(Le(),n),o.set(!0)},400),u=Cn([{key:xe(),value:function(t){return Vg(t).each(function(n){i.cancel();var e={x:b(n.clientX),y:b(n.clientY),target:t.target};i.schedule(t),o.set(!1),r.set(Fn.some(e))}),Fn.none()}},{key:we(),value:function(n){return i.cancel(),Vg(n).each(function(e){r.get().each(function(n){!function(n,e){var t=Math.abs(n.clientX-e.x()),r=Math.abs(n.clientY-e.y());return 5<t||5<r}(e,n)||r.set(Fn.none())})}),Fn.none()}},{key:Se(),value:function(e){i.cancel();return r.get().filter(function(n){return cn(n.target(),e.target())}).map(function(n){return o.get()?(e.prevent(),!1):t.triggerEvent(ze(),e)})}}]);return{fireIfReady:function(e,n){return Dt(u,n).bind(function(n){return n(e)})}}}var jg=function(t){var e=Ng({triggerEvent:function(n,e){t.onTapContent(e)}});return{fireTouchstart:function(n){e.fireIfReady(n,"touchstart")},onTouchend:function(){return Zd(t.body(),"touchend",function(n){e.fireIfReady(n,"touchend")})},onTouchmove:function(){return Zd(t.body(),"touchmove",function(n){e.fireIfReady(n,"touchmove")})}}},_g=6<=L().os.version.major,Pg=function(r,e,t){function o(n){return!cn(n.start(),n.finish())||n.soffset()!==n.foffset()}function n(){var n=r.doc().dom().hasFocus()&&r.getSelection().exists(o);t.getByDom(e).each(!0===(n||mo(u).filter(function(n){return"input"===q(n)}).exists(function(n){return n.dom().selectionStart!==n.dom().selectionEnd}))?Fi.on:Fi.off)}var i=jg(r),u=an(e),c=[Zd(r.body(),"touchstart",function(n){r.onTouchContent(),i.fireTouchstart(n)}),i.onTouchmove(),i.onTouchend(),Zd(e,"touchstart",function(n){r.onTouchToolstrip()}),r.onToReading(function(){lo(r.body())}),r.onToEditing(w),r.onScrollToCursor(function(n){n.preventDefault(),r.getCursorBox().each(function(n){var e=r.win(),t=n.top()>e.innerHeight||n.bottom()>e.innerHeight?n.bottom()-e.innerHeight+50:0;0!=t&&e.scrollTo(e.pageXOffset,e.pageYOffset+t)})})].concat(!0==_g?[]:[Zd(Je.fromDom(r.win()),"blur",function(){t.getByDom(e).each(Fi.off)}),Zd(u,"select",n),Zd(r.doc(),"selectionchange",n)]);return{destroy:function(){C(c,function(n){n.unbind()})}}},Hg=function(n,e){var t=parseInt(Qr(n,e),10);return isNaN(t)?0:t};function zg(n){return Cp.getOption(n)}function Lg(n){return function(n){return zg(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()}(n)||k(Dp,q(n))}function Gg(n,e,t){var r=n.document.createRange();return function(t,n){n.fold(function(n){t.setStartBefore(n.dom())},function(n,e){t.setStart(n.dom(),e)},function(n){t.setStartAfter(n.dom())})}(r,e),function(t,n){n.fold(function(n){t.setEndBefore(n.dom())},function(n,e){t.setEnd(n.dom(),e)},function(n){t.setEndAfter(n.dom())})}(r,t),r}function Ug(n,e,t,r,o){var i=n.document.createRange();return i.setStart(e.dom(),t),i.setEnd(r.dom(),o),i}function $g(n){return{left:b(n.left),top:b(n.top),right:b(n.right),bottom:b(n.bottom),width:b(n.width),height:b(n.height)}}function Wg(n,e,t){return e(Je.fromDom(t.startContainer),t.startOffset,Je.fromDom(t.endContainer),t.endOffset)}function Xg(n,e){return function(n,e){var t=e.ltr();return t.collapsed?e.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Bp.rtl(Je.fromDom(n.endContainer),n.endOffset,Je.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Wg(0,Bp.ltr,t)}):Wg(0,Bp.ltr,t)}(0,function(o,n){return n.match({domRange:function(n){return{ltr:b(n),rtl:Fn.none}},relative:function(n,e){return{ltr:X(function(){return Gg(o,n,e)}),rtl:X(function(){return Fn.some(Gg(o,e,n))})}},exact:function(n,e,t,r){return{ltr:X(function(){return Ug(o,n,e,t,r)}),rtl:X(function(){return Fn.some(Ug(o,t,r,n,e))})}}})}(n,e))}function qg(n,e,t){return e>=n.left&&e<=n.right&&t>=n.top&&t<=n.bottom}function Yg(t,r,n,e,o){function i(n){var e=t.dom().createRange();return e.setStart(r.dom(),n),e.collapse(!0),e}var u=function(n){return Cp.get(n)}(r).length,c=function(n,e,t,r,o){if(0===o)return 0;if(e===r)return o-1;for(var i=r,u=1;u<o;u++){var c=n(u),a=Math.abs(e-c.left);if(t<=c.bottom){if(t<c.top||i<a)return u-1;i=a}}return 0}(function(n){return i(n).getBoundingClientRect()},n,e,o.right,u);return i(c)}function Kg(n){return Fr(n,Lg)}function Jg(n){return Np(n,Lg)}function Qg(n,e){return e-n.left<n.right-e}function Zg(n,e,t){var r=n.dom().createRange();return r.selectNode(e.dom()),r.collapse(t),r}function np(e,n,t){var r=e.dom().createRange();r.selectNode(n.dom());var o=r.getBoundingClientRect(),i=Qg(o,t);return(!0===i?Kg:Jg)(n).map(function(n){return Zg(e,n,i)})}function ep(n,e,t){var r=e.dom().getBoundingClientRect(),o=Qg(r,t);return Fn.some(Zg(n,e,o))}function tp(n,e,t,r){var o=n.dom().createRange();o.selectNode(e.dom());var i=o.getBoundingClientRect();return function(n,e,t,r){var o=n.dom().createRange();o.selectNode(e.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,t)),c=Math.max(i.top,Math.min(i.bottom,r));return Vp(n,e,u,c)}(n,e,Math.max(i.left,Math.min(i.right,t)),Math.max(i.top,Math.min(i.bottom,r)))}function rp(n,e){var t=q(n);return"input"===t?Rp.after(n):k(["br","img"],t)?0===e?Rp.before(n):Rp.after(n):Rp.on(n,e)}function op(n,e,t,r){var o=function(n,e,t,r){var o=an(n).dom().createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o}(n,e,t,r),i=cn(n,t)&&e===r;return o.collapsed&&!i}function ip(n,e,t,r,o){!function(n,e){Fn.from(n.getSelection()).each(function(n){n.removeAllRanges(),n.addRange(e)})}(n,Ug(n,e,t,r,o))}function up(n,e,t,r,o){!function(u,n){Xg(u,n).match({ltr:function(n,e,t,r){ip(u,n,e,t,r)},rtl:function(n,e,t,r){var o=u.getSelection();if(o.setBaseAndExtent)o.setBaseAndExtent(n.dom(),e,t.dom(),r);else if(o.extend)try{!function(n,e,t,r,o,i){e.collapse(t.dom(),r),e.extend(o.dom(),i)}(0,o,n,e,t,r)}catch(i){ip(u,t,r,n,e)}else ip(u,t,r,n,e)}})}(n,function(n,e,t,r){var o=rp(n,e),i=rp(t,r);return Fp.relative(o,i)}(e,t,r,o))}function cp(n){var e=Je.fromDom(n.anchorNode),t=Je.fromDom(n.focusNode);return op(e,n.anchorOffset,t,n.focusOffset)?Fn.some(Mp.create(e,n.anchorOffset,t,n.focusOffset)):function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return Fn.some(Mp.create(Je.fromDom(e.startContainer),e.startOffset,Je.fromDom(t.endContainer),t.endOffset))}return Fn.none()}(n)}function ap(n){return Fn.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(cp)}function fp(n,e){return function(n){var e=n.getClientRects(),t=0<e.length?e[0]:n.getBoundingClientRect();return 0<t.width||0<t.height?Fn.some(t).map($g):Fn.none()}(function(i,n){return Xg(i,n).match({ltr:function(n,e,t,r){var o=i.document.createRange();return o.setStart(n.dom(),e),o.setEnd(t.dom(),r),o},rtl:function(n,e,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(n.dom(),e),o}})}(n,e))}function sp(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:b(2),height:n.height}}function lp(n){return{left:b(n.left),top:b(n.top),right:b(n.right),bottom:b(n.bottom),width:b(n.width),height:b(n.height)}}function dp(t){if(t.collapsed){var r=Je.fromDom(t.startContainer);return fn(r).bind(function(n){var e=Fp.exact(r,t.startOffset,n,function(n){return"img"===q(n)?1:zg(n).fold(function(){return at(n).length},function(n){return n.length})}(n));return fp(t.startContainer.ownerDocument.defaultView,e).map(sp).map(_)}).getOr([])}return de(t.getClientRects(),lp)}function mp(n,e){Kr(n,Pp,e)}function gp(n){return{top:b(n.top()),bottom:b(n.top()+n.height())}}function pp(n,e){var t=function(n){return Hg(n,Pp)}(e),r=n.innerHeight;return r<t?Fn.some(t-r):Fn.none()}function hp(n){return Fn.some(Je.fromDom(n.dom().contentWindow.document.body))}function vp(n){return Fn.some(Je.fromDom(n.dom().contentWindow.document))}function yp(n){return Fn.from(n.dom().contentWindow)}function bp(n){return yp(n).bind(ap)}function xp(n){return n.getFrame()}function wp(n,t){return function(e){return e[n].getOrThunk(function(){var n=xp(e);return function(){return t(n)}})()}}function Sp(n,e,t,r){return n[t].getOrThunk(function(){return function(n){return Zd(e,r,n)}})}function Tp(n){return{left:b(n.left),top:b(n.top),right:b(n.right),bottom:b(n.bottom),width:b(n.width),height:b(n.height)}}function Op(t,r){var o=null;return{cancel:function(){null!==o&&(l.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==o&&l.clearTimeout(o),o=l.setTimeout(function(){t.apply(null,n),o=null},r)}}}function kp(n){return"true"===Qr(n,ih)?function(n){return 0<n.dom().scrollLeft||function(n){n.dom().scrollLeft=1;var e=0!==n.dom().scrollLeft;return n.dom().scrollLeft=0,e}(n)}(n):function(n){return 0<n.dom().scrollTop||function(n){n.dom().scrollTop=1;var e=0!==n.dom().scrollTop;return n.dom().scrollTop=0,e}(n)}(n)}var Ep,Cp=function oy(t,r){var e=function(n){return t(n)?Fn.from(n.dom().nodeValue):Fn.none()};return{get:function(n){if(!t(n))throw new Error("Can only get "+r+" value of a "+r+" node");return e(n).getOr("")},getOption:e,set:function(n,e){if(!t(n))throw new Error("Can only set raw "+r+" value of a "+r+" node");n.dom().nodeValue=e}}}(tt,"text"),Dp=["img","br"],Mp={create:J("start","soffset","finish","foffset")},Ip=yt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Rp={before:Ip.before,on:Ip.on,after:Ip.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(y,y,y)}},Ap=yt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Fp={domRange:Ap.domRange,relative:Ap.relative,exact:Ap.exact,exactFromRange:function(n){return Ap.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){return function(n){return Je.fromDom(n.dom().ownerDocument.defaultView)}(function(n){return n.match({domRange:function(n){return Je.fromDom(n.startContainer)},relative:function(n,e){return Rp.getStart(n)},exact:function(n,e,t,r){return n}})}(n))},range:Mp.create},Bp=yt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Vp=function(n,e,t,r){return tt(e)?function(e,t,r,o){var n=e.dom().createRange();n.selectNode(t.dom());var i=n.getClientRects();return wo(i,function(n){return qg(n,r,o)?Fn.some(n):Fn.none()}).map(function(n){return Yg(e,t,r,o,n)})}(n,e,t,r):function(e,n,t,r){var o=e.dom().createRange(),i=at(n);return wo(i,function(n){return o.selectNode(n.dom()),qg(o.getBoundingClientRect(),t,r)?Vp(e,n,t,r):Fn.none()})}(n,e,t,r)},Np=function(n,i){var u=function(n){for(var e=at(n),t=e.length-1;0<=t;t--){var r=e[t];if(i(r))return Fn.some(r);var o=u(r);if(o.isSome())return o}return Fn.none()};return u(n)},jp=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(n){var e=n.getSelection();return e!==undefined&&0<e.rangeCount?dp(e.getRangeAt(0)):[]}),_p=function(n){n.focus();var e=Je.fromDom(n.document.body);(mo().exists(function(n){return k(["input","textarea"],q(n))})?function(n){Rg.setTimeout(function(){n()},0)}:t)(function(){mo().each(lo),so(e)})},Pp="data-"+_i.resolve("last-outer-height"),Hp=function(n,r){var e=Je.fromDom(r.document.body),t=Zd(Je.fromDom(n),"resize",function(){pp(n,e).each(function(t){(function(n){var e=jp(n);return 0<e.length?Fn.some(e[0]).map(gp):Fn.none()})(r).each(function(n){var e=function(n,e,t){return e.top()>n.innerHeight||e.bottom()>n.innerHeight?Math.min(t,e.bottom()-n.innerHeight+50):0}(r,n,t);0!==e&&r.scrollTo(r.pageXOffset,r.pageYOffset+e)})}),mp(e,n.innerHeight)});mp(e,n.innerHeight);return{toEditing:function(){_p(r)},destroy:function(){t.unbind()}}},zp={getBody:wp("getBody",hp),getDoc:wp("getDoc",vp),getWin:wp("getWin",yp),getSelection:wp("getSelection",bp),getFrame:xp,getActiveApi:function(c){var a=xp(c);return hp(a).bind(function(u){return vp(a).bind(function(i){return yp(a).map(function(o){var n=Je.fromDom(i.dom().documentElement),e=c.getCursorBox.getOrThunk(function(){return function(){return function(n){return ap(n).map(function(n){return Fp.exact(n.start(),n.soffset(),n.finish(),n.foffset())})}(o).bind(function(n){return fp(o,n).orThunk(function(){return function(n){return ap(n).filter(function(n){return cn(n.start(),n.finish())&&n.soffset()===n.foffset()}).bind(function(n){var e=n.start().dom().getBoundingClientRect();return 0<e.width||0<e.height?Fn.some(e).map(Tp):Fn.none()})}(o)})})}}),t=c.setSelection.getOrThunk(function(){return function(n,e,t,r){up(o,n,e,t,r)}}),r=c.clearSelection.getOrThunk(function(){return function(){!function(n){n.getSelection().removeAllRanges()}(o)}});return{body:b(u),doc:b(i),win:b(o),html:b(n),getSelection:d(bp,a),setSelection:t,clearSelection:r,frame:b(a),onKeyup:Sp(c,i,"onKeyup","keyup"),onNodeChanged:Sp(c,i,"onNodeChanged","SelectionChange"),onDomChanged:c.onDomChanged,onScrollToCursor:c.onScrollToCursor,onScrollToElement:c.onScrollToElement,onToReading:c.onToReading,onToEditing:c.onToEditing,onToolbarScrollStart:c.onToolbarScrollStart,onTouchContent:c.onTouchContent,onTapContent:c.onTapContent,onTouchToolstrip:c.onTouchToolstrip,getCursorBox:e}})})})}},Lp="data-ephox-mobile-fullscreen-style",Gp="position:absolute!important;",Up="top:0!important;left:0!important;margin:0!important;padding:0!important;width:100%!important;height:100%!important;overflow:visible!important;",$p=L().os.isAndroid(),Wp=function(n,e){function t(r){return function(n){var e=Qr(n,"style"),t=e===undefined?"no-styles":e.trim();t!==r&&(Kr(n,Lp,t),Kr(n,"style",r))}}var r=function(n,e,t){return Xi(n,function(n){return tn(n,e)},t)}(n,"*"),o=B(r,function(n){return function(n,e){return qi(n,function(n){return tn(n,e)})}(n,"*")}),i=function(n){var e=mi(n,"background-color");return e!==undefined&&""!==e?"background-color:"+e+"!important":"background-color:rgb(255,255,255)!important;"}(e);C(o,t("display:none!important;")),C(r,t(Gp+Up+i)),t((!0===$p?"":Gp)+Up+i)(n)},Xp=function(){var n=function(n){return on(n)}("["+Lp+"]");C(n,function(n){var e=Qr(n,Lp);"no-styles"!==e?Kr(n,"style",e):no(n,"style"),no(n,Lp)})},qp=function(){var e=Ki("head").getOrDie(),n=Ki('meta[name="viewport"]').getOrThunk(function(){var n=Je.fromTag("meta");return Kr(n,"name","viewport"),ft(e,n),n}),t=Qr(n,"content");return{maximize:function(){Kr(n,"content","width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0")},restore:function(){t!==undefined&&null!==t&&0<t.length?Kr(n,"content",t):Kr(n,"content","user-scalable=yes")}}},Yp=function(e,n){var t=qp(),r=yd(),o=yd();return{enter:function(){n.hide(),io(e.container,_i.resolve("fullscreen-maximized")),io(e.container,_i.resolve("android-maximized")),t.maximize(),io(e.body,_i.resolve("android-scroll-reload")),r.set(Hp(e.win,zp.getWin(e.editor).getOrDie("no"))),zp.getActiveApi(e.editor).each(function(n){Wp(e.container,n.body()),o.set(Pg(n,e.toolstrip,e.alloy))})},exit:function(){t.restore(),n.show(),co(e.container,_i.resolve("fullscreen-maximized")),co(e.container,_i.resolve("android-maximized")),Xp(),co(e.body,_i.resolve("android-scroll-reload")),o.clear(),r.clear()}}},Kp=function(n,e){var t=js(dm.sketch({dom:Us('<div aria-hidden="true" class="${prefix}-mask-tap-icon"></div>'),containerBehaviours:Bo([Fi.config({toggleClass:_i.resolve("mask-tap-icon-selected"),toggleOnExecute:!1})])})),r=function(t,r){var o=null;return{cancel:function(){null!==o&&(l.clearTimeout(o),o=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null===o&&(o=l.setTimeout(function(){t.apply(null,n),o=null},r))}}}(n,200);return dm.sketch({dom:Us('<div class="${prefix}-disabled-mask"></div>'),components:[dm.sketch({dom:Us('<div class="${prefix}-content-container"></div>'),components:[Ls.sketch({dom:Us('<div class="${prefix}-content-tap-section"></div>'),components:[t.asSpec()],action:function(n){r.throttle()},buttonBehaviours:Bo([Fi.config({toggleClass:_i.resolve("mask-tap-icon-selected")})])})]})]})},Jp=Sr([Jt("editor",[Yt("getFrame"),Qt("getBody"),Qt("getDoc"),Qt("getWin"),Qt("getSelection"),Qt("setSelection"),Qt("clearSelection"),Qt("cursorSaver"),Qt("onKeyup"),Qt("onNodeChanged"),Qt("getCursorBox"),Yt("onDomChanged"),tr("onTouchContent",w),tr("onTapContent",w),tr("onTouchToolstrip",w),tr("onScrollToCursor",b({unbind:w})),tr("onScrollToElement",b({unbind:w})),tr("onToEditing",b({unbind:w})),tr("onToReading",b({unbind:w})),tr("onToolbarScrollStart",y)]),Yt("socket"),Yt("toolstrip"),Yt("dropup"),Yt("toolbar"),Yt("container"),Yt("alloy"),or("win",function(n){return an(n.socket).dom().defaultView}),or("body",function(n){return Je.fromDom(n.socket.dom().ownerDocument.body)}),tr("translate",y),tr("setReadOnly",w),tr("readOnlyOnInit",b(!0))]),Qp=function(n){var e=Xt("Getting AndroidWebapp schema",Jp,n);Gi(e.toolstrip,"width","100%");var t=Lm(Kp(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}};ft(e.container,t.element());var o=Yp(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:w,enter:o.enter,exit:o.exit,destroy:w}},Zp=b([Yt("dom"),tr("shell",!0),mc("toolbarBehaviours",[og])]),nh=b([rf({name:"groups",overrides:function(n){return{behaviours:Bo([og.config({})])}}})]),eh=vf({name:"Toolbar",configFields:Zp(),partFields:nh(),factory:function(e,n,t,r){var o=function(n){return e.shell?Fn.some(n):kc(n,e,"groups")},i=e.shell?{behaviours:[og.config({})],components:[]}:{behaviours:[],components:n};return{uid:e.uid,dom:e.dom,components:i.components,behaviours:pc(e.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,e){o(n).fold(function(){throw l.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){og.set(n,e)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,e,t){n.setGroups(e,t)}}}),th=b([Yt("items"),(Ep=["itemSelector"],Jt("markers",de(Ep,Yt))),mc("tgroupBehaviours",[Va])]),rh=b([of({name:"items",unit:"item"})]),oh=vf({name:"ToolbarGroup",configFields:th(),partFields:rh(),factory:function(n,e,t,r){return{uid:n.uid,dom:n.dom,components:e,behaviours:pc(n.tgroupBehaviours,[Va.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),ih="data-"+_i.resolve("horizontal-scroll"),uh={exclusive:function(n,e){return Zd(n,"touchmove",function(n){Zi(n.target(),e).filter(kp).fold(function(){n.raw().preventDefault()},w)})},markAsHorizontal:function(n){Kr(n,ih,"true")}};function ch(){function e(n){var e=!0===n.scrollable?"${prefix}-toolbar-scrollable-group":"";return{dom:Us('<div aria-label="'+n.label+'" class="${prefix}-toolbar-group '+e+'"></div>'),tgroupBehaviours:Bo([am("adhoc-scrollable-toolbar",!0===n.scrollable?[Do(function(n,e){Gi(n.element(),"overflow-x","auto"),uh.markAsHorizontal(n.element()),vg.register(n.element())})]:[])]),components:[dm.sketch({components:[oh.parts().items({})]})],markers:{itemSelector:"."+_i.resolve("toolbar-group-item")},items:n.items}}function t(){eh.setGroups(r,o.get()),Fi.off(r)}var r=Lm(eh.sketch({dom:Us('<div class="${prefix}-toolbar"></div>'),components:[eh.parts().groups({})],toolbarBehaviours:Bo([Fi.config({toggleClass:_i.resolve("context-toolbar"),toggleOnExecute:!1,aria:{mode:"none"}}),Va.config({mode:"cyclic"})]),shell:!0})),n=Lm(dm.sketch({dom:{classes:[_i.resolve("toolstrip")]},components:[Gm(r)],containerBehaviours:Bo([Fi.config({toggleClass:_i.resolve("android-selection-context-toolbar"),toggleOnExecute:!1})])})),o=Pn([]);return{wrapper:b(n),toolbar:b(r),createGroups:function(n){return de(n,i(oh.sketch,e))},setGroups:function(n){o.set(n),t()},setContextToolbar:function(n){Fi.on(r),eh.setGroups(r,n)},restoreToolbar:function(){Fi.isOn(r)&&t()},refresh:function(){},focus:function(){Va.focusIn(r)}}}function ah(n,e){og.append(n,Gm(e))}function fh(n,e){og.remove(n,e)}function sh(e,n){return n.getAnimationRoot.fold(function(){return e.element()},function(n){return n(e)})}function lh(n){return n.dimension.property}function dh(n,e){return n.dimension.getDimension(e)}function mh(n,e){var t=sh(n,e);Id(t,[e.shrinkingClass,e.growingClass])}function gh(n,e){co(n.element(),e.openClass),io(n.element(),e.closedClass),Gi(n.element(),lh(e),"0px"),hi(n.element())}function ph(n,e){co(n.element(),e.closedClass),io(n.element(),e.openClass),pi(n.element(),lh(e))}function hh(n,e,t,r){t.setCollapsed(),Gi(n.element(),lh(e),dh(e,n.element())),hi(n.element()),mh(n,e),gh(n,e),e.onStartShrink(n),e.onShrunk(n)}function vh(n,e,t,r){var o=r.getOrThunk(function(){return dh(e,n.element())});t.setCollapsed(),Gi(n.element(),lh(e),o),hi(n.element());var i=sh(n,e);co(i,e.growingClass),io(i,e.shrinkingClass),gh(n,e),e.onStartShrink(n)}function yh(n,e,t){var r=dh(e,n.element());("0px"===r?hh:vh)(n,e,t,Fn.some(r))}function bh(n,e,t){var r=sh(n,e),o=ao(r,e.shrinkingClass),i=dh(e,n.element());ph(n,e);var u=dh(e,n.element());(o?function(){Gi(n.element(),lh(e),i),hi(n.element())}:function(){gh(n,e)})(),co(r,e.shrinkingClass),io(r,e.growingClass),ph(n,e),Gi(n.element(),lh(e),u),t.setExpanded(),e.onStartGrow(n)}function xh(n,e,t){var r=sh(n,e);return!0===ao(r,e.growingClass)}function wh(n,e,t){var r=sh(n,e);return!0===ao(r,e.shrinkingClass)}function Sh(e,t){var r=Lm(dm.sketch({dom:{tag:"div",classes:[_i.resolve("dropup")]},components:[],containerBehaviours:Bo([og.config({}),Vh.config({closedClass:_i.resolve("dropup-closed"),openClass:_i.resolve("dropup-open"),shrinkingClass:_i.resolve("dropup-shrinking"),growingClass:_i.resolve("dropup-growing"),dimension:{property:"height"},onShrunk:function(n){e(),t(),og.set(n,[])},onGrown:function(n){e(),t()}}),Vi(function(n,e){o(w)})])})),o=function(n){l.window.requestAnimationFrame(function(){n(),Vh.shrink(r)})};return{appear:function(n,e,t){!0===Vh.hasShrunk(r)&&!1===Vh.isTransitioning(r)&&l.window.requestAnimationFrame(function(){e(t),og.set(r,[n()]),Vh.grow(r)})},disappear:o,component:b(r),element:r.element}}function Th(n){return 8===n.raw().which&&!k(["input","textarea"],q(n.target()))&&!function(n,e,t){return Zi(n,e,t).isSome()}(n.target(),'[contenteditable="true"]')}function Oh(e,n){var t=Xt("Getting GUI events settings",jh,n),r=Ng(t),o=de(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return Zd(e,n,function(e){r.fireIfReady(e,n).each(function(n){n&&e.kill()}),t.triggerEvent(n,e)&&e.kill()})}),i=Pn(Fn.none()),u=Zd(e,"paste",function(e){r.fireIfReady(e,"paste").each(function(n){n&&e.kill()}),t.triggerEvent("paste",e)&&e.kill(),i.set(Fn.some(l.setTimeout(function(){t.triggerEvent(je(),e)},0)))}),c=Zd(e,"keydown",function(n){t.triggerEvent("keydown",n)?n.kill():!0===t.stopBackspace&&Th(n)&&n.prevent()}),a=function(n,e){return Nh?nm(n,"focus",e):Zd(n,"focusin",e)}(e,function(n){t.triggerEvent("focusin",n)&&n.kill()}),f=Pn(Fn.none()),s=function(n,e){return Nh?nm(n,"blur",e):Zd(n,"focusout",e)}(e,function(n){t.triggerEvent("focusout",n)&&n.kill(),f.set(Fn.some(l.setTimeout(function(){t.triggerEvent(Ne(),n)},0)))});return{unbind:function(){C(o,function(n){n.unbind()}),c.unbind(),a.unbind(),s.unbind(),u.unbind(),i.get().each(l.clearTimeout),f.get().each(l.clearTimeout)}}}function kh(n,e){var t=Dt(n,"target").map(function(n){return n()}).getOr(e);return Pn(t)}function Eh(n,r,e,t,o,i){var u=n(r,t),c=function(n,e){var t=Pn(!1),r=Pn(!1);return{stop:function(){t.set(!0)},cut:function(){r.set(!0)},isStopped:t.get,isCut:r.get,event:b(n),setSource:e.set,getSource:e.get}}(e,o);return u.fold(function(){return i.logEventNoHandlers(r,t),_h.complete()},function(e){var t=e.descHandler();return Td(t)(c),c.isStopped()?(i.logEventStopped(r,e.element(),t.purpose()),_h.stopped()):c.isCut()?(i.logEventCut(r,e.element(),t.purpose()),_h.complete()):fn(e.element()).fold(function(){return i.logNoParent(r,e.element(),t.purpose()),_h.complete()},function(n){return i.logEventResponse(r,e.element(),t.purpose()),_h.resume(n)})})}function Ch(n,e,t){var r=function(n){var e=Pn(!1);return{stop:function(){e.set(!0)},cut:w,isStopped:e.get,isCut:b(!1),event:b(n),setSource:o("Cannot set source of a broadcasted event"),getSource:o("Cannot get source of a broadcasted event")}}(e);return C(n,function(n){var e=n.descHandler();Td(e)(r)}),r.isStopped()}var Dh,Mh=function(n){return Lm(Ls.sketch({dom:Us('<div class="${prefix}-mask-edit-icon ${prefix}-icon"></div>'),action:function(){n.run(function(n){n.setReadOnly(!1)})}}))},Ih=function(){return Lm(dm.sketch({dom:Us('<div class="${prefix}-editor-socket"></div>'),components:[],containerBehaviours:Bo([og.config({})])}))},Rh=function(n,e,t,r){(!0===t?Ho.toAlpha:Ho.toOmega)(r),(t?ah:fh)(n,e)},Ah=/* */Object.freeze({refresh:function(n,e,t){if(t.isExpanded()){pi(n.element(),lh(e));var r=dh(e,n.element());Gi(n.element(),lh(e),r)}},grow:function(n,e,t){t.isExpanded()||bh(n,e,t)},shrink:function(n,e,t){t.isExpanded()&&yh(n,e,t)},immediateShrink:function(n,e,t){t.isExpanded()&&hh(n,e,t)},hasGrown:function(n,e,t){return t.isExpanded()},hasShrunk:function(n,e,t){return t.isCollapsed()},isGrowing:xh,isShrinking:wh,isTransitioning:function(n,e,t){return!0===xh(n,e)||!0===wh(n,e)},toggleGrow:function(n,e,t){(t.isExpanded()?yh:bh)(n,e,t)},disableTransitions:mh}),Fh=/* */Object.freeze({exhibit:function(n,e){var t=e.expanded;return Ur(t?{classes:[e.openClass],styles:{}}:{classes:[e.closedClass],styles:En(e.dimension.property,"0px")})},events:function(t,r){return Nr([function(n,e){return Hr(n)(e)}(Ae(),function(n,e){e.event().raw().propertyName===t.dimension.property&&(mh(n,t),r.isExpanded()&&pi(n.element(),t.dimension.property),(r.isExpanded()?t.onGrown:t.onShrunk)(n))})])}}),Bh=[Yt("closedClass"),Yt("openClass"),Yt("shrinkingClass"),Yt("growingClass"),Qt("getAnimationRoot"),Ko("onShrunk"),Ko("onStartShrink"),Ko("onGrown"),Ko("onStartGrow"),tr("expanded",!1),Kt("dimension",qt("property",{width:[ni("property","width"),ni("getDimension",function(n){return Mf(n)+"px"})],height:[ni("property","height"),ni("getDimension",function(n){return Wi(n)+"px"})]}))],Vh=qr({fields:Bh,name:"sliding",active:Fh,apis:Ah,state:/* */Object.freeze({init:function(n){var e=Pn(n.expanded);return Fo({isExpanded:function(){return!0===e.get()},isCollapsed:function(){return!1===e.get()},setCollapsed:d(e.set,!1),setExpanded:d(e.set,!0),readState:function(){return"expanded: "+e.get()}})}})}),Nh=L().browser.isFirefox(),jh=Pt([(Dh="triggerEvent",Kt(Dh,Mr)),tr("stopBackspace",!0)]),_h=yt([{stopped:[]},{resume:["element"]},{complete:[]}]),Ph=function(e,t,r,n,o,i){return Eh(e,t,r,n,o,i).fold(function(){return!0},function(n){return Ph(e,t,r,n,o,i)},function(){return!1})},Hh=function(n,e,t,r,o){var i=kh(t,r);return Ph(n,e,t,r,i,o)},zh=J("element","descHandler"),Lh=function(n,e){return{id:b(n),descHandler:b(e)}};function Gh(){var i={};return{registerId:function(r,o,n){Nn(n,function(n,e){var t=i[e]!==undefined?i[e]:{};t[o]=_m(n,r),i[e]=t})},unregisterId:function(t){Nn(i,function(n,e){n.hasOwnProperty(t)&&delete n[t]})},filterByType:function(n){return Dt(i,n).map(function(n){return _n(n,function(n,e){return Lh(e,n)})}).getOr([])},find:function(n,e,t){var r=Ct(e)(i);return ko(t,function(n){return function(t,r){return gf(r).fold(function(){return Fn.none()},function(n){var e=Ct(n);return t.bind(e).map(function(n){return zh(r,n)})})}(r,n)},n)}}}function Uh(){function r(n){var e=n.element();return gf(e).fold(function(){return function(n,e){var t=yc(lf+n);return mf(e,t),t}("uid-",n.element())},function(n){return n})}var o=Gh(),i={},u=function(n){gf(n.element()).each(function(n){delete i[n],o.unregisterId(n)})};return{find:function(n,e,t){return o.find(n,e,t)},filter:function(n){return o.filterByType(n)},register:function(n){var e=r(n);Mn(i,e)&&function(n,e){var t=i[e];if(t!==n)throw new Error('The tagId "'+e+'" is already used by: '+bo(t.element())+"\nCannot use it for: "+bo(n.element())+"\nThe conflicting element is"+(K(t.element())?" ":" not ")+"already in the DOM");u(n)}(n,e);var t=[n];o.registerId(t,e,n.events()),i[e]=n},unregister:u,getById:function(n){return Ct(n)(i)}}}var $h=function(t){function r(e){return fn(t.element()).fold(function(){return!0},function(n){return cn(e,n)})}function o(n,e){return u.find(r,n,e)}function i(t){var n=u.filter(_e());C(n,function(n){var e=n.descHandler();Td(e)(t)})}var u=Uh(),n=Oh(t.element(),{triggerEvent:function(e,t){return qo(e,t.target(),function(n){return function(n,e,t,r){var o=t.target();return Hh(n,e,t,o,r)}(o,e,t,n)})}}),c={debugInfo:b("real"),triggerEvent:function(e,t,r){qo(e,t,function(n){Hh(o,e,r,t,n)})},triggerFocus:function(e,t){gf(e).fold(function(){so(e)},function(n){qo(Ve(),e,function(n){!function(n,e,t,r,o){var i=kh(t,r);Eh(n,e,t,r,i,o)}(o,Ve(),{originator:b(t),kill:w,prevent:w,target:b(e)},e,n)})})},triggerEscape:function(n,e){c.triggerEvent("keydown",n.element(),e.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:Lm,addToGui:function(n){f(n)},removeFromGui:function(n){s(n)},addToWorld:function(n){e(n)},removeFromWorld:function(n){a(n)},broadcast:function(n){l(n)},broadcastOn:function(n,e){d(n,e)},broadcastEvent:function(n,e){m(n,e)},isConnected:b(!0)},e=function(n){n.connect(c),tt(n.element())||(u.register(n),C(n.components(),e),c.triggerEvent(Ge(),n.element(),{target:b(n.element())}))},a=function(n){tt(n.element())||(C(n.components(),a),u.unregister(n)),n.disconnect()},f=function(n){!function(n,e){mt(n,e,ft)}(t,n)},s=function(n){yn(n)},l=function(n){i({universal:b(!0),data:b(n)})},d=function(n,e){i({universal:b(!1),channels:b(n),data:b(e)})},m=function(n,e){var t=u.filter(n);return Ch(t,e)},g=function(n){return u.getById(n).fold(function(){return vt.error(new Error('Could not find component with uid: "'+n+'" in system.'))},vt.value)},p=function(n){var e=gf(n).getOr("not found");return g(e)};return e(t),{root:b(t),element:t.element,destroy:function(){n.unbind(),st(t.element())},add:f,remove:s,getByUid:g,getByDom:p,addToWorld:e,removeFromWorld:a,broadcast:l,broadcastOn:d,broadcastEvent:m}},Wh=b(_i.resolve("readonly-mode")),Xh=b(_i.resolve("edit-mode"));function qh(n){var e=Lm(dm.sketch({dom:{classes:[_i.resolve("outer-container")].concat(n.classes)},containerBehaviours:Bo([Ho.config({alpha:Wh(),omega:Xh()})])}));return $h(e)}var Yh=function(n,e){var t=Je.fromTag("input");di(t,{opacity:"0",position:"absolute",top:"-1000px",left:"-1000px"}),ft(n,t),so(t),e(t),st(t)},Kh=function(n){var e=n.getSelection();if(0<e.rangeCount){var t=e.getRangeAt(0),r=n.document.createRange();r.setStart(t.startContainer,t.startOffset),r.setEnd(t.endContainer,t.endOffset),e.removeAllRanges(),e.addRange(r)}},Jh=function(n,e){mo().each(function(n){cn(n,e)||lo(n)}),n.focus(),so(Je.fromDom(n.document.body)),Kh(n)},Qh={stubborn:function(n,e,t,r){function o(){Jh(e,r)}var i=Zd(t,"keydown",function(n){k(["input","textarea"],q(n.target()))||o()});return{toReading:function(){Yh(n,lo)},toEditing:o,onToolbarTouch:function(){},destroy:function(){i.unbind()}}},timid:function(n,e,t,r){function o(){lo(r)}return{toReading:function(){o()},toEditing:function(){Jh(e,r)},onToolbarTouch:function(){o()},destroy:w}}},Zh=function(e,r,t,o,n){function i(){r.run(function(n){n.refreshSelection()})}function u(n,e){var t=n-o.dom().scrollTop;r.run(function(n){n.scrollIntoView(t,t+e)})}function c(){r.run(function(n){n.clearSelection()})}function a(){e.getCursorBox().each(function(n){u(n.top(),n.height())}),r.run(function(n){n.syncHeight()})}var f=jg(e),s=Op(a,300),l=[e.onKeyup(function(){c(),s.throttle()}),e.onNodeChanged(i),e.onDomChanged(s.throttle),e.onDomChanged(i),e.onScrollToCursor(function(n){n.preventDefault(),s.throttle()}),e.onScrollToElement(function(n){n.element(),u(r,o)}),e.onToEditing(function(){r.run(function(n){n.toEditing()})}),e.onToReading(function(){r.run(function(n){n.toReading()})}),Zd(e.doc(),"touchend",function(n){cn(e.html(),n.target())||cn(e.body(),n.target())}),Zd(t,"transitionend",function(n){"height"===n.raw().propertyName&&function(){var e=Wi(t);r.run(function(n){n.setViewportOffset(e)}),i(),a()}()}),nm(t,"touchstart",function(n){r.run(function(n){n.highlightSelection()}),function(e){r.run(function(n){n.onToolbarTouch(e)})}(n),e.onTouchToolstrip()}),Zd(e.body(),"touchstart",function(n){c(),e.onTouchContent(),f.fireTouchstart(n)}),f.onTouchmove(),f.onTouchend(),Zd(e.body(),"click",function(n){n.kill()}),Zd(t,"touchmove",function(){e.onToolbarScrollStart()})];return{destroy:function(){C(l,function(n){n.unbind()})}}};var nv,ev,tv,rv,ov={},iv={exports:ov};nv=undefined,ev=ov,tv=iv,rv=undefined,function(n){"object"==typeof ev&&void 0!==tv?tv.exports=n():"function"==typeof nv&&nv.amd?nv([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function s(i,u,c){function a(e,n){if(!u[e]){if(!i[e]){var t="function"==typeof rv&&rv;if(!n&&t)return t(e,!0);if(f)return f(e,!0);var r=new Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}var o=u[e]={exports:{}};i[e][0].call(o.exports,function(n){return a(i[e][1][n]||n)},o,o.exports,s,i,u,c)}return u[e].exports}for(var f="function"==typeof rv&&rv,n=0;n<c.length;n++)a(c[n]);return a}({1:[function(n,e,t){var r,o,i=e.exports={};function u(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}function a(n){if(r===setTimeout)return setTimeout(n,0);if((r===u||!r)&&setTimeout)return r=setTimeout,setTimeout(n,0);try{return r(n,0)}catch(e){try{return r.call(null,n,0)}catch(e){return r.call(this,n,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:u}catch(n){r=u}try{o="function"==typeof clearTimeout?clearTimeout:c}catch(n){o=c}}();var f,s=[],l=!1,d=-1;function m(){l&&f&&(l=!1,f.length?s=f.concat(s):d=-1,s.length&&g())}function g(){if(!l){var n=a(m);l=!0;for(var e=s.length;e;){for(f=s,s=[];++d<e;)f&&f[d].run();d=-1,e=s.length}f=null,l=!1,function t(n){if(o===clearTimeout)return clearTimeout(n);if((o===c||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(n);try{return o(n)}catch(e){try{return o.call(null,n)}catch(e){return o.call(this,n)}}}(n)}}function p(n,e){this.fun=n,this.array=e}function h(){}i.nextTick=function(n){var e=new Array(arguments.length-1);if(1<arguments.length)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];s.push(new p(n,e)),1!==s.length||l||a(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,e){(function(e){function r(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],s(n,this)}function o(r,o){for(;3===r._state;)r=r._value;0!==r._state?(r._handled=!0,i._immediateFn(function(){var n=1===r._state?o.onFulfilled:o.onRejected;if(null!==n){var e;try{e=n(r._value)}catch(t){return void c(o.promise,t)}u(o.promise,e)}else(1===r._state?u:c)(o.promise,r._value)})):r._deferreds.push(o)}function u(n,e){try{if(e===n)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if(e instanceof i)return n._state=3,n._value=e,void a(n);if("function"==typeof t)return void s(function r(n,e){return function(){n.apply(e,arguments)}}(t,e),n)}n._state=1,n._value=e,a(n)}catch(o){c(n,o)}}function c(n,e){n._state=2,n._value=e,a(n)}function a(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)o(n,n._deferreds[e]);n._deferreds=null}function f(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function s(n,e){var t=!1;try{n(function(n){t||(t=!0,u(e,n))},function(n){t||(t=!0,c(e,n))})}catch(r){if(t)return;t=!0,c(e,r)}}var n,t;n=this,t=setTimeout,i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(n,e){var t=new this.constructor(r);return o(this,new f(n,e,t)),t},i.all=function(n){var a=Array.prototype.slice.call(n);return new i(function(o,i){if(0===a.length)return o([]);var u=a.length;function c(e,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void t.call(n,function(n){c(e,n)},i)}a[e]=n,0==--u&&o(a)}catch(r){i(r)}}for(var n=0;n<a.length;n++)c(n,a[n])})},i.resolve=function(e){return e&&"object"==typeof e&&e.constructor===i?e:new i(function(n){n(e)})},i.reject=function(t){return new i(function(n,e){e(t)})},i.race=function(o){return new i(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},i._immediateFn="function"==typeof e?function(n){e(n)}:function(n){t(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(a,n,f){(function(n,e){var r=a("process/browser.js").nextTick,t=Function.prototype.apply,o=Array.prototype.slice,i={},u=0;function c(n,e){this._id=n,this._clearFn=e}f.setTimeout=function(){return new c(t.call(setTimeout,window,arguments),clearTimeout)},f.setInterval=function(){return new c(t.call(setInterval,window,arguments),clearInterval)},f.clearTimeout=f.clearInterval=function(n){n.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},f.enroll=function(n,e){clearTimeout(n._idleTimeoutId),n._idleTimeout=e},f.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},f._unrefActive=f.active=function(n){clearTimeout(n._idleTimeoutId);var e=n._idleTimeout;0<=e&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},e))},f.setImmediate="function"==typeof n?n:function(n){var e=u++,t=!(arguments.length<2)&&o.call(arguments,1);return i[e]=!0,r(function(){i[e]&&(t?n.apply(null,t):n.call(null),f.clearImmediate(e))}),e},f.clearImmediate="function"==typeof e?e:function(n){delete i[n]}}).call(this,a("timers").setImmediate,a("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,e,t){var r=n("promise-polyfill"),o="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:o.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});function uv(n){l.setTimeout(function(){throw n},0)}function cv(n,e,t){return Math.abs(n-e)<=t?Fn.none():n<e?Fn.some(n+t):Fn.some(n-t)}function av(e,t){return wo([{width:320,height:480,keyboard:{portrait:300,landscape:240}},{width:320,height:568,keyboard:{portrait:300,landscape:240}},{width:375,height:667,keyboard:{portrait:305,landscape:240}},{width:414,height:736,keyboard:{portrait:320,landscape:240}},{width:768,height:1024,keyboard:{portrait:320,landscape:400}},{width:1024,height:1366,keyboard:{portrait:380,landscape:460}}],function(n){return function(n,e){return n?Fn.some(e):Fn.none()}(e<=n.width&&t<=n.height,n.keyboard)}).getOr({portrait:t/5,landscape:e/4})}function fv(n){var e=Ag(n).isPortrait(),t=function(n){return av(n.screen.width,n.screen.height)}(n),r=e?t.portrait:t.landscape;return(e?n.screen.height:n.screen.width)-n.innerHeight>r?0:r}function sv(n,e){var t=an(n).dom().defaultView;return Wi(n)+Wi(e)-fv(t)}function lv(n){return Hg(n,Mv)}function dv(n,e){var t=function(n){return Qr(n,Iv)}(n);return Dv.fixed(n,t,e)}function mv(n,e){return Dv.scroller(n,e)}function gv(n){var e=lv(n);return("true"===Qr(n,Rv)?mv:dv)(n,e)}function pv(n,e,t){var r=an(n).dom().defaultView.innerHeight;return Kr(n,Av,r+"px"),r-e-t}function hv(n){var e=gi(n,"top").getOr("0");return parseInt(e,10)}function vv(n){return parseInt(n.dom().scrollTop,10)}function yv(n,e){var t=e+Vv(n)+"px";Gi(n,"top",t)}var bv=iv.exports.boltExport,xv=function(n){var t=Fn.none(),e=[],r=function(n){o()?u(n):e.push(n)},o=function(){return t.isSome()},i=function(n){C(n,u)},u=function(e){t.each(function(n){l.setTimeout(function(){e(n)},0)})};return n(function(n){t=Fn.some(n),i(e),e=[]}),{get:r,map:function(t){return xv(function(e){r(function(n){e(t(n))})})},isReady:o}},wv={nu:xv,pure:function(e){return xv(function(n){n(e)})}},Sv=function(t){function n(n){t().then(n,uv)}return{map:function(n){return Sv(function(){return t().then(n)})},bind:function(e){return Sv(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return Sv(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return wv.nu(n)},toCached:function(){var n=null;return Sv(function(){return null===n&&(n=t()),n})},toPromise:t,get:n}},Tv=function(n){return Sv(function(){return new bv(n)})},Ov=function(n){return Sv(function(){return bv.resolve(n)})},kv=function(){var f=null;return{animate:function(r,o,n,i,e,t){function u(n){c=!0,e(n)}var c=!1;Rg.clearInterval(f);function a(n){Rg.clearInterval(f),u(n)}f=Rg.setInterval(function(){var t=r();cv(t,o,n).fold(function(){Rg.clearInterval(f),u(o)},function(n){if(i(n,a),!c){var e=r();(e!==n||Math.abs(e-o)>Math.abs(t-o))&&(Rg.clearInterval(f),u(o))}})},t)}}},Ev=sv,Cv=function(n,e,t){var r=sv(e,t),o=Wi(e)+Wi(t)-r;Gi(n,"padding-bottom",o+"px")},Dv=yt([{fixed:["element","property","offsetY"]},{scroller:["element","offsetY"]}]),Mv="data-"+_i.resolve("position-y-fixed"),Iv="data-"+_i.resolve("y-property"),Rv="data-"+_i.resolve("scrolling"),Av="data-"+_i.resolve("last-window-height"),Fv=function(n){var e=Yi(n,"["+Mv+"]");return de(e,gv)},Bv=function(r,o,i,u){function n(){var n=t.innerHeight;return function(n){return Hg(n,Av)}(r)<n}function e(){if(d){var n=Wi(i),e=Wi(u),t=pv(r,n,e);Kr(r,Mv,n+"px"),Gi(r,"height",t+"px"),Cv(o,r,u)}}var t=an(r).dom().defaultView,c=function(n){var e=Qr(n,"style");di(n,{position:"absolute",top:"0px"}),Kr(n,Mv,"0px"),Kr(n,Iv,"top");return{restore:function(){Kr(n,"style",e||""),no(n,Mv),no(n,Iv)}}}(i),a=Wi(i),f=Wi(u),s=function(n,e,t){var r=Qr(t,"style");vg.register(t),di(t,{position:"absolute",height:e+"px",width:"100%",top:n+"px"}),Kr(t,Mv,n+"px"),Kr(t,Rv,"true"),Kr(t,Iv,"top");return{restore:function(){vg.deregister(t),Kr(t,"style",r||""),no(t,Mv),no(t,Rv),no(t,Iv)}}}(a,pv(r,a,f),r),l=function(n){var e=Qr(n,"style");di(n,{position:"absolute",bottom:"0px"}),Kr(n,Mv,"0px"),Kr(n,Iv,"bottom");return{restore:function(){Kr(n,"style",e||""),no(n,Mv),no(n,Iv)}}}(u),d=!0;return Cv(o,r,u),{setViewportOffset:function(n){Kr(r,Mv,n+"px"),e()},isExpanding:n,isShrinking:m(n),refresh:e,restore:function(){d=!1,c.restore(),s.restore(),l.restore()}}},Vv=lv,Nv=kv(),jv="data-"+_i.resolve("last-scroll-top"),_v=function(t,r,o){return Tv(function(n){var e=d(vv,t);Nv.animate(e,r,15,function(n){t.dom().scrollTop=n,Gi(t,"top",hv(t)+15+"px")},function(){t.dom().scrollTop=r,Gi(t,"top",o+"px"),n(r)},10)})},Pv=function(o,i){return Tv(function(n){var e=d(vv,o);Kr(o,jv,e());var t=Math.abs(i-e()),r=Math.ceil(t/10);Nv.animate(e,i,r,function(n,e){Hg(o,jv)!==o.dom().scrollTop?e(o.dom().scrollTop):(o.dom().scrollTop=n,Kr(o,jv,n))},function(){o.dom().scrollTop=i,Kr(o,jv,i),n(i)},10)})},Hv=function(i,u){return Tv(function(n){function e(n){Gi(i,"top",n+"px")}var t=d(hv,i),r=Math.abs(u-t()),o=Math.ceil(r/10);Nv.animate(t,u,o,e,function(){e(u),n(u)},10)})},zv=function(e,t,r){var o=an(e).dom().defaultView;return Tv(function(n){yv(e,r),yv(t,r),o.scrollTo(0,r),n(r)})};function Lv(i,n){return n(function(t){var r=[],o=0;0===i.length?t([]):C(i,function(n,e){n.get(function(e){return function(n){r[e]=n,++o>=i.length&&t(r)}}(e))})})}function Gv(n,r){return n.fold(function(n,e,t){return function(n,e,t,r){return Gi(n,e,t+r+"px"),Ov(r)}(n,e,r,t)},function(n,e){return function(n,e,t){var r=e+t,o=gi(n,"top").getOr(t),i=r-parseInt(o,10),u=n.dom().scrollTop+i;return _v(n,u,r)}(n,r,e)})}function Uv(e,t,n,r,o,i){var u=function f(t){var r=Pn(wv.pure({}));return{start:function(e){var n=wv.nu(function(n){return t(e).get(n)});r.set(n)},idle:function(n){r.get().get(function(){n()})}}}(function(n){return zv(e,t,n)}),c=Op(function(){u.idle(function(){Wv(n,r.pageYOffset).get(function(){(function(){var n=jp(i);return Fn.from(n[0]).bind(function(n){var e=n.top()-t.dom().scrollTop;return e>r.innerHeight+5||e<-5?Fn.some({top:b(e),bottom:b(e+n.height())}):Fn.none()})})().each(function(n){t.dom().scrollTop=t.dom().scrollTop+n.top()}),u.start(0),o.refresh()})})},1e3),a=Zd(Je.fromDom(r),"scroll",function(){r.pageYOffset<0||c.throttle()});return Wv(n,r.pageYOffset).get(y),{unbind:a.unbind}}var $v=function(n,e,t,r,o){var i=Ev(e,t),u=d(Kh,n);i<r||i<o?Pv(e,e.dom().scrollTop-i+o).get(u):r<0&&Pv(e,e.dom().scrollTop+r).get(u)},Wv=function(n,e){var t=Fv(n);return function(n){return Lv(n,Tv)}(de(t,function(n){return Gv(n,e)}))},Xv=function(n){var t=n.cWin(),e=n.ceBody(),r=n.socket(),o=n.toolstrip(),i=n.toolbar(),u=n.contentElement(),c=n.keyboardType(),a=n.outerWindow(),f=n.dropup(),s=Bv(r,e,o,f),l=c(n.outerBody(),t,rt(),u,o,i),d=Fg(a,{onChange:w,onReady:s.refresh});d.onAdjustment(function(){s.refresh()});var m=Zd(Je.fromDom(a),"resize",function(){s.isExpanding()&&s.refresh()}),g=Uv(o,r,n.outerBody(),a,s,t),p=function v(t,e){var n=t.document,r=Je.fromTag("div");function o(n){var e=Je.fromTag("span");return Md(e,[_i.resolve("layer-editor"),_i.resolve("unfocused-selection")]),di(e,{left:n.left()+"px",top:n.top()+"px",width:n.width()+"px",height:n.height()+"px"}),e}io(r,_i.resolve("unfocused-selections")),ft(Je.fromDom(n.documentElement),r);var i=Zd(r,"touchstart",function(n){n.prevent(),Jh(t,e),u()}),u=function(){pn(r)};return{update:function(){u();var n=jp(t),e=de(n,o);gn(r,e)},isActive:function(){return 0<at(r).length},destroy:function(){i.unbind(),st(r)},clear:u}}(t,u),h=function(){p.clear()};return{toEditing:function(){l.toEditing(),h()},toReading:function(){l.toReading()},onToolbarTouch:function(n){l.onToolbarTouch(n)},refreshSelection:function(){p.isActive()&&p.update()},clearSelection:h,highlightSelection:function(){p.update()},scrollIntoView:function(n,e){$v(t,r,f,n,e)},updateToolbarPadding:w,setViewportOffset:function(n){s.setViewportOffset(n),Hv(r,n).get(y)},syncHeight:function(){Gi(u,"height",u.dom().contentWindow.document.body.scrollHeight+"px")},refreshStructure:s.refresh,destroy:function(){s.restore(),d.destroy(),g.unbind(),m.unbind(),l.destroy(),p.destroy(),Yh(rt(),lo)}}},qv=function(r,n){var o=qp(),i=bd(),u=bd(),c=yd(),a=yd();return{enter:function(){n.hide();var t=Je.fromDom(l.document);zp.getActiveApi(r.editor).each(function(n){i.set({socketHeight:gi(r.socket,"height"),iframeHeight:gi(n.frame(),"height"),outerScroll:l.document.body.scrollTop}),u.set({exclusives:uh.exclusive(t,"."+vg.scrollable())}),io(r.container,_i.resolve("fullscreen-maximized")),Wp(r.container,n.body()),o.maximize(),Gi(r.socket,"overflow","scroll"),Gi(r.socket,"-webkit-overflow-scrolling","touch"),so(n.body());var e=nn(["cWin","ceBody","socket","toolstrip","toolbar","dropup","contentElement","cursor","keyboardType","isScrolling","outerWindow","outerBody"],[]);c.set(Xv(e({cWin:n.win(),ceBody:n.body(),socket:r.socket,toolstrip:r.toolstrip,toolbar:r.toolbar,dropup:r.dropup.element(),contentElement:n.frame(),cursor:w,outerBody:r.body,outerWindow:r.win,keyboardType:Qh.stubborn,isScrolling:function(){return u.get().exists(function(n){return n.socket.isScrolling()})}}))),c.run(function(n){n.syncHeight()}),a.set(Zh(n,c,r.toolstrip,r.socket,r.dropup))})},refreshStructure:function(){c.run(function(n){n.refreshStructure()})},exit:function(){o.restore(),a.clear(),c.clear(),n.show(),i.on(function(n){n.socketHeight.each(function(n){Gi(r.socket,"height",n)}),n.iframeHeight.each(function(n){Gi(r.editor.getFrame(),"height",n)}),l.document.body.scrollTop=n.scrollTop}),i.clear(),u.on(function(n){n.exclusives.unbind()}),u.clear(),co(r.container,_i.resolve("fullscreen-maximized")),Xp(),vg.deregister(r.toolbar),pi(r.socket,"overflow"),pi(r.socket,"-webkit-overflow-scrolling"),lo(r.editor.getFrame()),zp.getActiveApi(r.editor).each(function(n){n.clearSelection()})}}},Yv=function(n){var e=Xt("Getting IosWebapp schema",Jp,n);Gi(e.toolstrip,"width","100%"),Gi(e.container,"position","relative");var t=Lm(Kp(function(){e.setReadOnly(e.readOnlyOnInit()),o.enter()},e.translate));e.alloy.add(t);var r={show:function(){e.alloy.add(t)},hide:function(){e.alloy.remove(t)}},o=qv(e,r);return{setReadOnly:e.setReadOnly,refreshStructure:o.refreshStructure,enter:o.enter,exit:o.exit,destroy:w}};function Kv(n,e,t){n.system().broadcastOn([Uo.formatChanged()],{command:e,state:t})}function Jv(m){return{getNotificationManagerImpl:function(){return{open:b({progressBar:{value:w},close:w,text:w,getEl:b(null),moveTo:w,moveRel:w,settings:{}}),close:w,reposition:w,getArgs:b({})}},renderUI:function(){var n=m.getElement(),e=Zv(m);!1===function(n){return!1===n.settings.skin}(m)?(m.contentCSS.push(e.content),zo.DOM.styleSheetLoader.load(e.ui,ey(m))):ey(m)();function t(){m.fire("ScrollIntoView")}var f=L().os.isAndroid()?function c(n){var e=qh({classes:[_i.resolve("android-container")]}),t=ch(),r=yd(),o=Mh(r),i=Ih(),u=Sh(w,n);return e.add(t.wrapper()),e.add(i),e.add(u.component()),{system:b(e),element:e.element,init:function(n){r.set(Qp(n))},exit:function(){r.run(function(n){n.exit(),og.remove(i,o)})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Rh(i,o,n,e.root())},socket:b(i),dropup:b(u)}}(t):function a(n){var e=qh({classes:[_i.resolve("ios-container")]}),t=ch(),r=yd(),o=Mh(r),i=Ih(),u=Sh(function(){r.run(function(n){n.refreshStructure()})},n);return e.add(t.wrapper()),e.add(i),e.add(u.component()),{system:b(e),element:e.element,init:function(n){r.set(Yv(n))},exit:function(){r.run(function(n){og.remove(i,o),n.exit()})},setToolbarGroups:function(n){var e=t.createGroups(n);t.setGroups(e)},setContextToolbar:function(n){var e=t.createGroups(n);t.setContextToolbar(e)},focusToolbar:function(){t.focus()},restoreToolbar:function(){t.restoreToolbar()},updateMode:function(n){Rh(i,o,n,e.root())},socket:b(i),dropup:b(u)}}(t);!function(n,e){gt(n,e,dn)}(Je.fromDom(n),f.system());function s(n,e,t,r){!1===r&&m.selection.collapse();var o=i(n,e,t);f.setToolbarGroups(!0===r?o.readOnly:o.main),m.setMode(!0===r?"readonly":"design"),m.fire(!0===r?ty():ry()),f.updateMode(r)}function l(n,e){return m.on(n,e),{unbind:function(){m.off(n)}}}var r=n.ownerDocument.defaultView,d=Fg(r,{onChange:function(){f.system().broadcastOn([Uo.orientationChanged()],{width:Bg(r)})},onReady:w}),i=function(n,e,t){var r=n.get();return{readOnly:r.backToMask.concat(e.get()),main:r.backToMask.concat(t.get())}};return m.on("init",function(){f.init({editor:{getFrame:function(){return Je.fromDom(m.contentAreaContainer.querySelector("iframe"))},onDomChanged:function(){return{unbind:w}},onToReading:function(n){return l(ty(),n)},onToEditing:function(n){return l(ry(),n)},onScrollToCursor:function(e){m.on("ScrollIntoView",function(n){e(n)});return{unbind:function(){m.off("ScrollIntoView"),d.destroy()}}},onTouchToolstrip:function(){n()},onTouchContent:function(){(function(n){return go(n).bind(function(n){return f.system().getByDom(n).toOption()})})(Je.fromDom(m.editorContainer.querySelector("."+_i.resolve("toolbar")))).each($),f.restoreToolbar(),n()},onTapContent:function(n){var e=n.target();if("img"===q(e))m.selection.select(e.dom()),n.kill();else if("a"===q(e)){f.system().getByDom(Je.fromDom(m.editorContainer)).each(function(n){Ho.isAlpha(n)&&Go(e.dom())})}}},container:Je.fromDom(m.editorContainer),socket:Je.fromDom(m.contentAreaContainer),toolstrip:Je.fromDom(m.editorContainer.querySelector("."+_i.resolve("toolstrip"))),toolbar:Je.fromDom(m.editorContainer.querySelector("."+_i.resolve("toolbar"))),dropup:f.dropup(),alloy:f.system(),translate:w,setReadOnly:function(n){s(a,c,u,n)},readOnlyOnInit:function(){return!1}});var n=function(){f.dropup().disappear(function(){f.system().broadcastOn([Uo.dropupDismissed()],{})})},e={label:"The first group",scrollable:!1,items:[Ws.forToolbar("back",function(){m.selection.collapse(),f.exit()},{},m)]},t={label:"Back to read only",scrollable:!1,items:[Ws.forToolbar("readonly-back",function(){s(a,c,u,!0)},{},m)]},r=Cg(f,m),o=Dg(m.settings,r),i={label:"The extra group",scrollable:!1,items:[]},u=Pn([{label:"the action group",scrollable:!0,items:o},i]),c=Pn([{label:"The read only mode group",scrollable:!0,items:[]},i]),a=Pn({backToMask:[e],backToReadOnly:[t]});ny(f,m)}),m.on("remove",function(){f.exit()}),m.on("detach",function(){!function(e){var n=at(e.element());C(n,function(n){e.getByDom(n).each(lt)}),st(e.element())}(f.system()),f.system().destroy()}),{iframeContainer:f.socket().element().dom(),editorContainer:f.element().dom()}}}}var Qv=tinymce.util.Tools.resolve("tinymce.EditorManager"),Zv=function(n){var e=Dt(n.settings,"skin_url").fold(function(){return Qv.baseURL+"/skins/ui/oxide"},function(n){return n});return{content:e+"/content.mobile.min.css",ui:e+"/skin.mobile.min.css"}},ny=function(r,n){var e=Bn(n.formatter.get());C(e,function(e){n.formatter.formatChanged(e,function(n){Kv(r,e,n)})}),C(["ul","ol"],function(t){n.selection.selectorChanged(t,function(n,e){Kv(r,t,n)})})},ey=(b(["x-small","small","medium","large","x-large"]),function(n){function e(){n._skinLoaded=!0,n.fire("SkinLoaded")}return function(){n.initialized?e():n.on("init",e)}}),ty=b("toReading"),ry=b("toEditing");!function iy(){Lo.add("mobile",Jv)}()}(window);