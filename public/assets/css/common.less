body {
  font-size: 12px !important;
}

em {
  font-style: normal;
}

.clear:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0
}

.clear, .clr {
  display: block;
  min-height: 1%;
}

.clearfix, .clr {
  clear: both
}

.clearfix:after, .clr:after {
  content: " ";
  display: table;
  clear: both
}

.fl-l, .fl {
  float: left;
}

.fl-r, .fr {
  float: right;
}

* {
  box-sizing: border-box;
}
.f-red{color:red;}
.section-page {
  background-color: #fff;
  margin: 20px;
  padding: 20px;
  box-shadow: 2px 2px 10px #ccc;
  border-radius: 3px;
}

.alink {
  color: #1224CC !important;
  
  &:hover {
    color: #1E9FFF !important;
    cursor: pointer;
  }
}

.boxsiz {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.row {
  display: flex;
  flex-direction: row;
}

.column {
  display: flex;
  flex-direction: column;
}

.bothSide {
  justify-content: space-between;
}

.avarage {
  justify-content: space-around;
}

.rowCenter {
  justify-content: center;
}

.verCenter {
  align-items: center;
}

.section-page .layui-form-label {
  padding: 5px 15px;
}

.section-page .layui-form-item .layui-input-inline {
  width: 170px;
}

.layui-card-header {
  font-weight: bold;
}

.layui-layer-title {
  font-weight: bold;
}

.layui-input, .layui-select {
  height: 30px;
  line-height: 30px;
}

.layui-input:focus, .layui-textarea:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
  box-shadow: inset 0 1px 1px rgb(0 0 0 / 8%), 0 0 8px rgb(102 175 233 / 60%);
}

.layui-table tbody tr:hover {
  background: #f6f2f2;
}

.layui-table tbody tr.layui-table-click {
  background: #ddf2e9;
}

.layui-table-tool {
  position: relative;
  background-color: #ffffff;
  padding: 10px 0;
  
  &:before {
    position: absolute;
    width: 100%;
    height: 100%;
    border-left: 1px solid #ffffff;
    border-top: 1px solid #ffffff;
    left: -1px;
    top: -1px;
    content: '';
    z-index: -1;
  }
  
  &:after {
    position: absolute;
    width: 100%;
    height: 100%;
    border-right: 1px solid #ffffff;
    top: 0;
    right: -1px;
    content: '';
    z-index: -1;
  }
}

.layui-table-tool-self {
  right: 0px
}

.layui-textarea {
  resize: none;
  min-height: 60px !important;
}

.layui-layer-page .layui-layer-content {
  height: auto !important;
  overflow-y: hidden;
}

.layui-layer .layui-form-label {
  padding: 5px 10px;
  white-space: nowrap;
}

.layui-layer .layui-input-block {
  margin-left: 85px;
  min-height: 30px;
}

.layui-input-block {
  min-height: 30px;
}

.layui-form-radio {
  line-height: 30px;
  margin: 0 10px 0 0;
}

.layui-form-radio > i {
  font-size: 15px !important;
}

.layui-input, .layui-select, .layui-textarea {
  font-size: 12px;
}

.layui-select-auto .layui-form-select dl {
  max-height: 104px !important;
}

xm-select {
  height: 30px !important;
  line-height: 30px !important;
  min-height: 30px !important;
  box-sizing: border-box !important;
}

xm-select * {
  font-size: 12px !important;
}

.xm-select-status {
  border-color: rgb(102, 175, 233);
  outline: 0px !important;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 1px 1px inset, rgba(102, 175, 233, 0.6) 0px 0px 8px !important;
}

xm-select > .xm-label .scroll .label-content {
  padding: 1px 10px !important;
}

xm-select .xm-body .xm-option .xm-option-icon {
  font-size: 18px !important;
}

xm-select .xm-label .xm-label-block {
  height: 22px !important;
  line-height: 22px !important;
}

.mb0 {
  margin-bottom: 0;
}

.ml10 {
  margin-left: 10px;
}

.ml4 {
  margin-left: 4px;
}

.layer-box-padding {
  padding: 20px;
}

.layui-colla-title {
  font-weight: bold;
}

.text-box-area {
  padding: 5px 0;
  
  p {
    flex: 0 0 33.3%;
    
    .label-text {
      font-size: 12px;
    }
    
    .text {
      color: #0000ff;
    }
  }
}

.layui-layer {
  .layui-form-radio {
    padding-right: 5px !important;
    margin: 0 !important;
    
    > i {
      margin-right: 4px;
      font-size: 14px;
    }
  }
}

.padding5 {
  padding: 5px 0 !important;
}

label.required:before {
  content: ' *';
  color: red;
}

.w65 {
  width: 65px !important;
}

.tip-text {
  
  .iconfont {
    position: relative;
    font-size: 20px;
    color: #ffa200;
  }
  
  p {
    margin-left: 5px;
    font-size: 12px;
    color: rgb(249, 129, 25);
  }
}

.cursor {
  cursor: pointer;
}

.status {
  position: relative;
  
  &.status-success {
    color: #176A2E;
  }
  
  &.status-fail {
    color: red;
  }
}

.normal-color {
  color: #0C7700;
}

.warm-color {
  color: red;
}

.custom .layui-layer-content {
  height: auto !important;
  padding: 2px !important;
  
  .table-status {
    position: relative;
    margin: 0 !important;
    z-index: 2;
    
    th {
      white-space: nowrap;
    }
    
    td, th {
      padding: 3px 11px !important;
    }
  }
}

.table-mt {
  margin-top: 0 !important;
}

.business-license-text {
  color: #0000ff;
}

.warm-color-crm {
  color: #ff4500;
}

//layui 2.8.0的css问题
.layui-table-tool {
  z-index: 890;
}

.layui-table-body {
  max-height: calc(100vh - 350px);
}

.layui-table-view .layui-table th {
  font-weight: bold;
}

