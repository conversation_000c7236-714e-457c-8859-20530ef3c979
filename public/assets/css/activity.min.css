.cube-set {
  height: 100%;
  overflow-y: auto;
  margin-top: 45px;
  padding-bottom: 100px;
}
.cube-set .cube-set-head {
  position: fixed;
  top: 0;
  width: 100%;
  height: 45px;
  border-bottom: 1px solid #eee;
  padding: 0 20px;
  background: #FFFFFF;
  z-index: 999;
}
.cube-set .cube-set-head h2 {
  font-weight: bold;
  white-space: nowrap;
}
.cube-set .cube-set-head label {
  white-space: nowrap;
}
.cube-set .cube-set-head .ml10 {
  margin-left: 10px;
}
.cube-set .cube-set-head .page-background {
  width: 100px;
  height: 30px;
  object-fit: cover;
  margin-left: 22px;
  cursor: pointer;
}
.cube-set .left-menu {
  position: absolute;
  left: 0;
  top: 45px;
  width: 273px;
  z-index: 9999;
  background: #FFFFFF;
  padding-left: 20px;
}
.cube-set .left-menu .shousuo-box {
  position: absolute;
  left: 236px;
  top: 16px;
  cursor: pointer;
  z-index: 99;
  width: 20px;
  height: 20px;
  background: #FFFFFF;
}
.cube-set .left-menu .layui-collapse {
  border: 1px solid transparent;
}
.cube-set .left-menu .layui-colla-title {
  background: transparent;
  height: 25px;
  line-height: 25px;
}
.cube-set .left-menu .layui-colla-title em {
  font-size: 12px;
  font-weight: normal;
  color: #5f5f5f;
}
.cube-set .left-menu .layui-colla-content {
  border: 1px solid transparent;
  padding-top: 0;
  padding-bottom: 0;
}
.cube-set .left-menu .layui-colla-item {
  border: 1px solid transparent;
}
.cube-set .left-menu .system-compant .operate span {
  height: 20px;
  line-height: 24px;
  margin-left: 6px;
}
.cube-set .left-menu .package-list {
  flex-wrap: wrap;
}
.cube-set .left-menu .package-list li {
  width: 69px;
  margin-bottom: 5px;
  cursor: move;
}
.cube-set .left-menu .package-list li i {
  width: 32px;
  height: 32px;
  background: url("../images/u745.png") no-repeat center;
  background-size: cover;
}
.cube-set .left-menu .package-list li span {
  white-space: nowrap;
}
.cube-set .left-menu.shousuo-box-curr .shousuo-box {
  left: 10px;
}
.cube-set .left-menu.shousuo-box-curr .layui-tab {
  display: none;
}
.cube-set .setting-fix-width {
  position: absolute;
  width: 1190px;
  height: 100%;
  margin: 0 auto;
  left: 0;
  right: 0;
  z-index: 1;
}
.cube-set .setting-fix-width .setting {
  position: absolute;
  right: -22px;
  top: 0;
  border: 1px solid #999;
  padding-top: 5px;
  background: #FFFFFF;
  z-index: 999;
}
.cube-set .setting-fix-width .setting li {
  width: 20px;
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.4s ease-in-out;
}
.cube-set .setting-fix-width .setting li:hover {
  color: #1969F9;
}
.cube-set .cube-set-drag-area {
  margin: 0 auto 0;
}
.cube-set .cube-set-drag-area .cube-initial {
  position: relative;
  border: 1px dashed salmon;
  cursor: pointer;
  margin: 0 auto 1px;
  width: 1190px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content {
  width: 100%;
  height: 100%;
  background: #f8f8f8;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.customLayout a,
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.customLayout div {
  background: #f8f8f8;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotteryCircle .number-of-draw em {
  height: 32px;
  background: #fff;
  padding: 0 12px;
  margin: 0 10px;
  font-size: 32px;
  color: #333;
  border-radius: 2px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotteryCircle .number-of-draw span {
  font-size: 18px;
  color: #ffffff;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotteryCircle .lotteryCircle-bg {
  position: relative;
  width: 100%;
  height: 1010px;
  background: url("https://img.ichunt.com/images/ichunt/202303/09/5280cd20b5f9b931a70875cc1236766c.jpg") no-repeat center;
  background-size: cover;
  margin: 0 auto;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotteryCircle .lotteryCircle-bg .turntable-bg {
  position: relative;
  width: 708px;
  height: 708px;
  margin: 0 auto;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotteryCircle .lotteryCircle-bg .turntable-bg .turntable-pic {
  width: 708px;
  height: 708px;
  object-fit: cover;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotteryCircle .lotteryCircle-bg .turntable-bg .round_click {
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -139px;
  margin-left: -91px;
  width: 182px;
  height: 240px;
  background: url("https://img.ichunt.com/images/ichunt/202303/10/1cdbe11b0624e34f1e60b72f14637e40.png") no-repeat center;
  background-size: contain;
  z-index: 2;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotteryCircle .lotteryCircle-bg .btn-wrap {
  background: transparent;
  margin: 0 auto;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotteryCircle .lotteryCircle-bg .btn-wrap a {
  width: 150px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 14px;
  color: #333;
  background: #e0be7d;
  border-radius: 2px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .number-of-draw em {
  height: 32px;
  background: #fff;
  padding: 0 12px;
  margin: 0 10px;
  font-size: 32px;
  color: #333;
  border-radius: 2px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .number-of-draw span {
  font-size: 18px;
  color: #ffffff;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg {
  background: #0000ff;
  background-size: cover;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content {
  position: relative;
  margin: 0 auto;
  height: 700px;
  background-size: contain;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content a {
  position: absolute;
  width: 226px;
  height: 226px;
  background: #ffff00;
  z-index: -1;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content a.box1 {
  left: 0;
  top: 0;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content a.box2 {
  left: 237px;
  top: 0;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content a.box3 {
  left: 474px;
  top: 0;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content a.box4 {
  left: 474px;
  top: 237px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content a.box5 {
  left: 474px;
  top: 474px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content a.box6 {
  left: 237px;
  top: 474px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content a.box7 {
  left: 0px;
  top: 474px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content a.box8 {
  left: 0px;
  top: 237px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .lotterySquare-content .btnClick {
  position: absolute;
  left: 237px;
  top: 237px;
  width: 226px;
  height: 226px;
  background: #ffff00;
  z-index: -1;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .btn-wrap {
  background: transparent;
  margin: 0 auto;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.lotterySquare .lotterySquare-bg .btn-wrap a {
  width: 150px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  font-size: 14px;
  color: #333;
  background: #e0be7d;
  border-radius: 2px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon {
  min-height: 250px;
  padding: 50px 0;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon div {
  background: transparent;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel {
  position: relative;
  padding: 20px 0;
  background: #FFFFFF;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel .bd {
  overflow: hidden;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel .bd .picList {
  width: 20000px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel .bd .picList li {
  float: left;
  margin-right: 20px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel .bd .picList li a {
  width: 100%;
  height: 100%;
  display: block;
  background: #FFFFFF;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel .bd .picList li a img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel .hd .layui-carousel-arrow {
  opacity: 1;
  background: rgba(0, 0, 0, 0.2);
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel .hd .prev {
  position: absolute;
  top: 50%;
  left: 10px;
  margin-top: -18px;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  font-size: 20px;
  border: none 0;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  cursor: pointer;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel .hd .next {
  position: absolute;
  top: 50%;
  right: 10px;
  margin-top: -18px;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  font-size: 20px;
  border: none 0;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  cursor: pointer;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel-list .picList {
  width: 965px;
  margin: 0 auto;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel-list .picList li {
  width: 230px;
  overflow: hidden;
  margin-right: 15px;
  margin-bottom: 15px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel-list .picList li a {
  width: 100%;
  height: 100%;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.coupon .carousel-list .picList li a img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.shopList {
  min-height: 220px;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.formModule {
  width: 500px;
  min-height: 220px;
  padding: 20px;
  background: transparent;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.formModule .layui-form-label {
  width: 116px !important;
  padding: 5px 15px !important;
}
.cube-set .cube-set-drag-area .cube-initial .cube-set-drag-content.formModule .layui-input-block {
  margin-left: 116px !important;
}
.cube-set .cube-setting-layer {
  position: fixed;
  right: 5px !important;
  top: 59px;
  width: 400px;
  background: #FFFFFF;
  border: 1px solid #333;
  padding: 10px;
  z-index: 9999999 !important;
  max-height: 600px;
  overflow-y: auto !important;
  display: none;
}
.cube-set .cube-setting-layer .layui-form-mid {
  padding: 5px 0 !important;
}
.cube-set .cube-setting-layer .title {
  cursor: move;
}
.cube-set .cube-setting-layer .title .tt {
  font-weight: bold;
}
.cube-set .cube-setting-layer .title .toggle {
  cursor: pointer;
  color: #1c8eff;
}
.cube-set .cube-setting-layer .layui-form-label {
  width: 62px;
  padding: 5px 15px;
}
.cube-set .cube-setting-layer .layui-input-block {
  margin-left: 80px;
}
.cube-set .cube-setting-layer .bar-wrap {
  padding-top: 10px;
  padding-left: 63px;
}
.cube-set .cube-setting-layer .bar-wrap .tip {
  color: #999;
}
.cube-set .cube-setting-layer .bar-wrap img {
  width: 120px;
  height: 43px;
  object-fit: cover;
  cursor: pointer;
}
.cube-set .cube-setting-layer .total {
  color: #999;
  margin-left: 5px;
}
.cube-set .cube-setting-layer .checkbox-style .layui-form-checkbox[lay-skin=primary] {
  margin-top: 7px !important;
}
.cube-set .layui-input-error {
  border: 1px solid #ff5722 !important;
}
.cube-set .clearPic {
  font-size: 12px;
  color: #1224CC;
  display: inline-block;
  vertical-align: middle;
  margin-left: 3px;
}
.cube-set .clearBg {
  font-size: 12px;
  color: #1224CC;
  display: inline-block;
  vertical-align: middle;
  margin-left: 3px;
}
.wrap-search {
  position: relative;
  width: 1190px;
  margin: 0 auto;
  color: #333;
  font-size: 14px;
  background: #FFFFFF !important;
  /*广告位*/
  /*优惠劵*/
  /**搜索主体**/
  /**供应商筛选**/
  /**排序筛选**/
  /**列表区域**/
}
.wrap-search * {
  box-sizing: content-box;
}
.wrap-search .mb3 {
  margin-bottom: 3px;
}
.wrap-search .f-999 {
  color: #999;
}
.wrap-search .f-yellow {
  color: #ff3700 !important;
}
.wrap-search .wordwarpover {
  word-wrap: break-word;
  overflow: hidden;
}
.wrap-search .wordwarp {
  word-break: break-all;
}
.wrap-search .brand-mr,
.wrap-search .brand-mr .f-red {
  color: #000000 !important;
  font-size: 12px !important;
}
.wrap-search .left-ad {
  display: block;
  position: absolute;
  left: -100px;
  top: 0;
  width: 100px;
  max-height: 450px;
  z-index: 48;
  overflow: hidden;
}
.wrap-search .right-ad {
  display: block;
  position: absolute;
  right: -158px;
  top: 0;
  width: 100px;
  max-height: 450px;
  z-index: 48;
  overflow: hidden;
}
.wrap-search .foot-ad {
  display: block;
  width: 1190px;
  max-height: 70px;
  margin-top: 20px;
  overflow: hidden;
}
.wrap-search .left-ad img,
.wrap-search .right-ad img,
.wrap-search .foot-ad img {
  width: 100%;
  height: 100%;
  display: block;
}
.wrap-search .ss-coupon ul {
  padding-left: 10px;
}
.wrap-search .ss-coupon li {
  float: left;
  width: 350px;
  margin: 0 20px;
  position: relative;
  margin-bottom: 20px;
}
.wrap-search .ss-coupon li p {
  display: none;
}
.wrap-search .ss-coupon li img {
  display: block;
  width: 100%;
}
.wrap-search .ss-coupon li.act {
  display: none;
}
.wrap-search .ss-coupon .act p {
  display: block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: url(../../images/gray-bg-30.png);
}
.wrap-search .ss-coupon .act i {
  z-index: 1;
  display: block;
  width: 120px;
  height: 120px;
  position: absolute;
  top: 4px;
  right: 10px;
  background: url(../../images/template/get.png) no-repeat;
  background-size: 100% 100%;
}
.wrap-search .ss-coupon .act.actb i {
  z-index: 1;
  display: block;
  width: 120px;
  height: 120px;
  position: absolute;
  top: 4px;
  right: 10px;
  background: url(../../images/template/pot.png) no-repeat;
  background-size: 100% 100%;
}
.wrap-search .shit-head {
  background: #fff;
  padding: 12px 16px 0 16px;
}
.wrap-search .shit-head.fixedheads {
  background: #fff;
  padding: 6px 15px;
  width: 1160px;
}
.wrap-search .shit-head.fixedheads .tog-con {
  display: none !important;
}
.wrap-search .shit-head.fixedheads .supplier-item-box {
  width: 1005px !important;
}
.wrap-search .fixedheads {
  position: fixed !important;
  top: 60px;
  z-index: 10;
}
.wrap-search .supdatabox .fixedheads {
  z-index: 11;
}
.wrap-search .supplier-sort {
  font-size: 14px;
  position: relative;
}
.wrap-search .supplier-sort .supplier-title {
  font-weight: bold;
  width: 61px;
}
.wrap-search .supplier-sort .supplier-item-box {
  width: 982px;
  padding-right: 110px;
}
.wrap-search .supplier-sort .supplier-item-box .supplier-item {
  margin-left: 10px;
  cursor: pointer;
  padding-left: 6px;
  padding-right: 6px;
  float: left;
  margin-bottom: 5px;
  position: relative;
  border: 1px solid #fff;
  color: #1969F9;
}
.wrap-search .supplier-sort .supplier-item-box .supplier-item i {
  display: none;
}
.wrap-search .supplier-sort .supplier-item-box .supplier-item.act {
  border: 1px solid #EDEDED;
  background: #Fff;
  color: #1969F9;
}
.wrap-search .supplier-sort .supplier-item-box .supplier-item.act i {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: url(../images/closesipplier.png) no-repeat;
  margin-left: 5px;
}
.wrap-search .supplier-sort .czbsg {
  height: 20px;
  position: absolute;
  right: 0px;
  top: 3px;
  z-index: 1;
}
.wrap-search .supplier-sort .clear-alls {
  cursor: pointer;
  width: 55px;
  height: 20px;
  background: #FFFFFF;
  border: 1px solid #EDEDED;
  margin-left: 8px;
  font-size: 12px;
  color: #3D91C8;
  line-height: 17px;
  height: 17px;
  text-align: center;
  display: none;
}
.wrap-search .supplier-sort .tog-con {
  height: 17px;
  font-size: 12px;
  color: #3D91C8;
  line-height: 17px;
  cursor: pointer;
}
.wrap-search .supplier-sort .tog-con i {
  background: url(../images/arrow.png) no-repeat;
  display: inline-block;
  vertical-align: middle;
  width: 18px;
  height: 18px;
  position: relative;
  top: -2px;
}
.wrap-search .supplier-sort .tog-con.act i {
  transform: rotate(180deg);
}
.wrap-search .title-cut {
  padding-top: 6px;
}
.wrap-search .title-cut .total-rank {
  margin-right: 25px;
}
.wrap-search .title-cut .total-rank li {
  width: 70px;
  height: 26px;
  background: #f8f8f8;
  border: 1px solid #f8f8f8;
  font-size: 14px;
  line-height: 28px;
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  position: relative;
}
.wrap-search .title-cut .total-rank li i.icon-bottom {
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 6px;
  border-color: #D8D8D8 transparent transparent transparent;
  position: absolute;
  top: 15px;
  right: 5px;
}
.wrap-search .title-cut .total-rank li i.icon-bottom.cur {
  border-color: #1969F9 transparent transparent transparent;
}
.wrap-search .title-cut .total-rank li i.icon-top {
  width: 0px;
  height: 0px;
  border-style: solid;
  border-width: 6px;
  border-color: transparent transparent #D8D8D8 transparent;
  position: absolute;
  top: 1px;
  right: 5px;
}
.wrap-search .title-cut .total-rank li.dpsort span {
  position: relative;
  left: -5px;
}
.wrap-search .title-cut .total-rank li.act {
  background: #1969F9;
  color: #fff;
  border: 1px solid #1969F9;
}
.wrap-search .title-cut .total-rank li.act.dpsort {
  color: #1969F9;
  background: #fff;
}
.wrap-search .title-cut .total-rank li.act.top i.icon-top {
  border-color: transparent transparent #1969F9 transparent;
}
.wrap-search .title-cut .total-rank li.act.bottom i.icon-bottom {
  border-color: #1969F9 transparent transparent transparent;
}
.wrap-search .title-cut .check-b {
  margin-left: 18px;
  cursor: pointer;
  height: 28px;
  line-height: 26px;
}
.wrap-search .title-cut .check-b .checkbox {
  height: 18px;
  width: 18px;
  border-radius: 2px;
  background: #FFF;
  border: 1px solid #ededed;
  position: relative;
  top: 2px;
  margin-right: 6px;
}
.wrap-search .title-cut .check-b .checkbox .icon-check {
  background: url(../images/gou.png) no-repeat;
  background-size: 100% 100%;
  width: 18px;
  height: 18px;
  display: none;
}
.wrap-search .title-cut .check-b.act {
  color: #1969F9;
}
.wrap-search .title-cut .check-b.act .checkbox {
  border: 1px solid #1969F9;
  background: #1969F9;
}
.wrap-search .title-cut .check-b.act .checkbox .icon-check {
  display: block;
}
.wrap-search .w-155 {
  width: 155px;
}
.wrap-search .w-91 {
  width: 91px;
}
.wrap-search .w-100 {
  width: 100px;
}
.wrap-search .w-101 {
  width: 101px;
}
.wrap-search .w-92 {
  width: 92px;
}
.wrap-search .w-93 {
  width: 93px;
}
.wrap-search .w-89 {
  width: 89px;
}
.wrap-search .w-82 {
  width: 82px;
}
.wrap-search .w-80 {
  width: 80px;
}
.wrap-search .w-70 {
  width: 70px;
}
.wrap-search .w-75 {
  width: 75px;
}
.wrap-search .w-78 {
  width: 78px;
}
.wrap-search .w-69 {
  width: 69px;
}
.wrap-search .w-50 {
  width: 50px;
}
.wrap-search .w-131 {
  width: 131px;
}
.wrap-search .w-114 {
  width: 114px;
}
.wrap-search .w-165 {
  width: 165px;
}
.wrap-search .w-389 {
  width: 389px;
}
.wrap-search .w-326 {
  width: 326px;
}
.wrap-search .w-133 {
  width: 133px;
}
.wrap-search .w-405 {
  width: 405px;
}
.wrap-search .w-147 {
  width: 147px;
}
.wrap-search .w-310 {
  width: 310px;
}
.wrap-search .w-293 {
  width: 293px;
}
.wrap-search .w-235 {
  width: 235px;
}
.wrap-search .w-60 {
  width: 60px;
}
.wrap-search .w-86 {
  width: 86px;
}
.wrap-search .w-104 {
  width: 104px;
}
.wrap-search .w-79 {
  width: 79px;
}
.wrap-search .w-89 {
  width: 89px;
}
.wrap-search .w-94 {
  width: 94px;
}
.wrap-search .w-76 {
  width: 76px;
}
.wrap-search .w-210 {
  width: 210px;
}
.wrap-search .w-149 {
  width: 149px;
}
.wrap-search .list-con {
  padding: 14px;
  background: #fff;
  box-shadow: 0px 2px 4px 0px rgba(61, 145, 200, 0.09);
  /**国产推荐**/
}
.wrap-search .list-con .no_content {
  background: #fff;
  text-align: center;
  padding: 0;
  height: auto;
  margin-top: 20px;
}
.wrap-search .list-con .no_content dl {
  width: 455px;
  margin: 0 auto;
}
.wrap-search .list-con .no_content dt {
  width: 120px;
  height: 100px;
  margin-left: 151px;
}
.wrap-search .list-con .no_content dt .icon-ss-wu {
  background: url(../../images/icon_ss_wu.png) no-repeat;
  display: inline-block;
  width: 120px;
  height: 100px;
}
.wrap-search .list-con .no_content dt .icon-ss-wu-sy {
  background: url(../../images/no-data-search.png) no-repeat;
  display: inline-block;
  width: 120px;
  height: 100px;
  background-size: 100% 100%;
}
.wrap-search .list-con .no_content dd {
  line-height: 22px;
  padding-bottom: 30px;
  padding-top: 10px;
  color: #000;
  font-size: 16px;
  text-align: center;
  width: 451px;
}
.wrap-search .list-con .no_content dd h2 {
  font-size: 16px;
  font-weight: bold;
  padding-bottom: 20px;
  color: #000000;
}
.wrap-search .list-con .no_content dd p a {
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  padding: 0 38px;
}
.wrap-search .list-con .list-th {
  background: #1969F9;
  height: 28px;
  line-height: 28px;
  padding: 0px 7px;
  font-size: 12px;
  color: #fff;
}
.wrap-search .list-con .list-th.fixedheads {
  width: 1146px;
}
.wrap-search .list-con .list-th .th {
  padding-left: 5px;
  padding-right: 5px;
  position: relative;
}
.wrap-search .list-con .list-th .th::after {
  content: "|";
  position: absolute;
  right: 0px;
  color: #AEBACB;
}
.wrap-search .list-con .list-th .th.noborder::after {
  content: "";
}
.wrap-search .list-con .list-td .list-group {
  min-height: 82px;
  padding: 10px 7px;
  position: relative;
  transition: all 0.8s;
}
.wrap-search .list-con .list-td .list-group:nth-child(2n) {
  background: #F2F5F9;
}
.wrap-search .list-con .list-td .list-group:hover {
  background: #E8F2FF;
}
.wrap-search .list-con .list-td .list-group:hover .group-kf {
  left: 0px !important;
}
.wrap-search .list-con .list-td .list-group .td {
  padding-left: 5px;
  padding-right: 5px;
  position: relative;
  font-size: 12px;
  word-wrap: break-word;
  line-height: 17px;
}
.wrap-search .list-con .list-td .list-group .td .kecpn {
  position: relative;
  overflow: hidden;
  margin-top: 5px;
  height: 18px;
}
.wrap-search .list-con .list-td .list-group .td .group-kf {
  display: block;
  cursor: pointer;
  position: absolute;
  left: -160px;
  top: 0px;
  transition: all 0.6s;
}
.wrap-search .list-con .list-td .list-group .td .group-kf span {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: url(../images/qq.png) no-repeat;
  vertical-align: middle;
  position: relative;
  top: -2px;
}
.wrap-search .list-con .list-td .list-group .td .group-kf span.p2fk {
  background: url(../images/fkwh.png) no-repeat;
}
.wrap-search .list-con .list-td .list-group .td .zk {
  position: absolute;
  left: -7px;
  top: -10px;
  width: 25px;
  height: 25px;
  background: url(../images/zhek.png) no-repeat;
}
.wrap-search .list-con .list-td .list-group .td .goods-title {
  font-weight: bold;
  line-height: 17px;
  max-height: 72px;
  overflow: hidden;
  display: block;
  max-width: 131px;
  cursor: pointer;
  word-break: break-all;
  float: left;
}
.wrap-search .list-con .list-td .list-group .td .goods-title .f-red {
  color: #ff3700 !important;
}
.wrap-search .list-con .list-td .list-group .td .copygoods {
  display: inline-block;
  height: 15px;
  width: 15px;
  background: url(../images/copy.png) no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
  position: relative;
  top: 1px;
  margin-left: 5px;
  float: left;
  margin-right: 3px;
}
.wrap-search .list-con .list-td .list-group .td .ptp {
  height: 16px;
  background: #1969F9;
  color: #fff;
  line-height: 14px;
  text-align: center;
  display: inline-block;
  padding: 0 5px;
  border-radius: 2px;
}
.wrap-search .list-con .list-td .list-group .td .signcm {
  margin-left: 5px;
  margin-right: 0px;
}
.wrap-search .list-con .list-td .list-group .td .pdfurl {
  width: 18px;
  height: 18px;
  display: block;
  cursor: pointer;
  background: url(../images/pdf.png?v=20221026) no-repeat;
  background-size: 100% 100%;
  margin-right: 3px;
  position: relative;
  top: -1px;
}
.wrap-search .list-con .list-td .list-group .td .tagbox {
  margin-top: 4px;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .jx {
  width: 36px;
  height: 16px;
  line-height: 15px;
  border-radius: 0px 100px 100px 0px;
  border: 1px solid #1969F9;
  color: #1969F9;
  text-align: center;
  margin-right: 3px;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .yc {
  line-height: 15px;
  width: 60px;
  height: 16px;
  border-radius: 0px 100px 100px 0px;
  border: 1px solid #F98119;
  color: #F98119;
  text-align: center;
  margin-right: 3px;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .qh {
  line-height: 15px;
  padding: 0 5px;
  height: 16px;
  border-radius: 0px 100px 100px 0px;
  border: 1px solid #1896f0;
  color: #1896f0;
  text-align: center;
  margin-right: 3px;
  position: relative;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .qh .fcb {
  position: absolute;
  top: 16px;
  left: 0px;
  display: none;
  width: auto;
  min-width: 140px;
  height: auto;
  min-height: 72px;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px 0px rgba(61, 145, 200, 0.09);
  border: 1px solid #AEBACB;
  z-index: 6;
  padding: 6px 10px;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .qh .fcb a {
  line-height: 22px;
  overflow: auto !important;
  text-overflow: initial !important;
  white-space: normal !important;
  text-align: left;
  display: block;
  cursor: pointer;
  position: relative;
  padding-left: 18px;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .qh .fcb a::before {
  position: absolute;
  content: "";
  width: 11px;
  height: 11px;
  background: url(../images/search.png) no-repeat;
  left: 2px;
  top: 7px;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .qh .fcb a:hover {
  color: #1969F9;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .qh:hover .fcb {
  display: block;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .zktag {
  line-height: 15px;
  width: 20px;
  height: 16px;
  border-radius: 0px 100px 100px 0px;
  border: 1px solid #FF0035;
  color: #FF0035;
  text-align: center;
  margin-right: 3px;
  position: relative;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .zktag .fcb {
  position: absolute;
  width: 250px;
  padding: 10px;
  background: #FFFFFF;
  box-shadow: 0px 2px 10px 0px rgba(160, 160, 160, 0.5);
  color: #333;
  z-index: 4;
  left: 0px;
  top: 20px;
  display: none;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .zktag .fcb p {
  line-height: 24px;
  overflow: auto !important;
  text-overflow: initial !important;
  white-space: normal !important;
  text-align: left;
}
.wrap-search .list-con .list-td .list-group .td .tagbox .zktag:hover .fcb {
  display: block;
}
.wrap-search .list-con .list-td .list-group .td .kpcon {
  overflow: hidden;
}
.wrap-search .list-con .list-td .list-group .td .jt-con {
  padding-left: 6px;
}
.wrap-search .list-con .list-td .list-group .td .jt-con .w-70 {
  margin-bottom: 3px;
}
.wrap-search .list-con .list-td .list-group .td .jt-con .f-red {
  color: #ff3700 !important;
}
.wrap-search .list-con .list-td .list-group .td .jt-con .jt-group.act {
  color: #ff3700;
}
.wrap-search .list-con .list-td .list-group .td .jt-con .jt-group .xgprice {
  color: #999;
  text-decoration: line-through;
}
.wrap-search .list-con .list-td .list-group .td .jt-con .jt-group .ac-price a {
  color: #1969F9;
}
.wrap-search .list-con .list-td .list-group .td .more-con {
  margin-top: 5px;
  width: 379px;
  height: 20px;
  line-height: 18px;
  cursor: pointer;
  background: #FFFFFF;
  border-radius: 0px 0px 100px 100px;
  border: 1px solid #EDEDED;
  text-align: center;
}
.wrap-search .list-con .list-td .list-group .td .more-con span {
  color: #1969F9;
  margin-right: 5px;
}
.wrap-search .list-con .list-td .list-group .td .more-con font {
  vertical-align: middle;
  display: inline-block;
  width: 17px;
  height: 17px;
  background: url(../images/bluearrow.png) no-repeat;
}
.wrap-search .list-con .list-td .list-group .td .more-con.act font {
  transform: rotate(180deg);
  position: relative;
  top: -2px;
}
.wrap-search .list-con .list-td .list-group .td .check-b {
  cursor: pointer;
  margin-bottom: 8px;
}
.wrap-search .list-con .list-td .list-group .td .check-b .checkbox {
  height: 14px;
  width: 14px;
  border-radius: 2px;
  background: #FFF;
  border: 1px solid #ededed;
  margin-right: 4px;
}
.wrap-search .list-con .list-td .list-group .td .check-b .checkbox .icon-check {
  background: url(../images/gounew.png) no-repeat;
  background-size: 100% 100%;
  width: 14px;
  height: 14px;
  display: none;
}
.wrap-search .list-con .list-td .list-group .td .check-b.act .checkbox {
  border: 1px solid #1969F9;
  background: #fff;
}
.wrap-search .list-con .list-td .list-group .td .check-b.act .checkbox .icon-check {
  display: block;
}
.wrap-search .list-con .list-td .list-group .td .edit-input {
  width: 94px;
  height: 24px;
  background-color: #3D91C8;
  padding: 1px;
}
.wrap-search .list-con .list-td .list-group .td .edit-input span {
  float: left;
  margin-right: 0;
  color: #000000;
  font-size: 20px;
  cursor: pointer;
  background-color: #fff;
  width: 20px;
  text-align: center;
  height: 24px;
  line-height: 24px;
  position: relative;
}
.wrap-search .list-con .list-td .list-group .td .edit-input span:hover {
  background: #1969F9;
  color: #fff;
  transition: all 0.4s;
}
.wrap-search .list-con .list-td .list-group .td .edit-input input {
  padding: 0;
  width: 52px;
  text-align: center;
  margin: 0 1px;
  height: 24px;
  line-height: 24px;
  outline: 0;
  outline: none;
  display: block;
  float: left;
  background: #fff;
  border: none;
  color: #000000;
}
.wrap-search .list-con .list-td .list-group .td .total {
  color: #ff3700;
  font-weight: bold;
  margin-top: 24px;
}
.wrap-search .list-con .list-td .list-group .td .total font {
  font-weight: normal;
}
.wrap-search .list-con .list-td .list-group .td .total.be {
  margin-top: 10px;
}
.wrap-search .list-con .list-td .list-group .td .btn {
  width: 76px;
  height: 26px;
  background: #1969F9;
  border-radius: 2px;
  border: 1px solid #1969f9;
  color: #fff;
  transition: all 0.6s;
  margin-bottom: 10px;
  cursor: pointer;
  text-align: center;
  line-height: 26px;
  display: block;
}
.wrap-search .list-con .list-td .list-group .td .btn:hover {
  background: #387FFF;
}
.wrap-search .list-con .list-td .list-group .td .btn.yellowbtn {
  background: #F98119;
  border: 1px solid #F98119;
}
.wrap-search .list-con .list-td .list-group .td .btn.btn-lk {
  background: #fff;
  color: #1969f9;
}
.wrap-search .list-con .list-td .list-group .td .btn.btn-lk:hover {
  background: #1969f9;
  color: #fff;
}
.wrap-search .list-con .list-td .list-group .td .btn.btn-lk.yellowbtn {
  color: #F98119;
  border: 1px solid #F98119;
}
.wrap-search .list-con .list-td .list-group .td .btn.btn-lk.yellowbtn:hover {
  color: #fff;
  background: #F98119;
}
.wrap-search .list-con.gcbox {
  padding-top: 10px;
}
.wrap-search .list-con.gcbox .gctitle {
  line-height: 22px;
  height: 22px;
  margin-bottom: 10px;
}
.wrap-search .list-con.gcbox .gctitle span {
  font-size: 16px;
  font-weight: 600;
  color: #1969F9;
}
.wrap-search .list-con.gcbox .gctitle p {
  margin-left: 24px;
  font-size: 12px;
  margin-right: 16px;
}
.wrap-search .list-con.gcbox .gctitle div {
  font-size: 12px;
  color: #999;
}
.wrap-search .list-con.gcbox .gctitle div b {
  font-weight: bold;
  color: #1969F9;
}
.wrap-search .list-con.gcbox .gctitle font {
  cursor: pointer;
  color: #3D91C8;
}
.wrap-search .list-con.gcbox .more-con {
  width: 309px !important;
}
.wrap-search .list-con.gcbox .more-number {
  height: 28px;
  line-height: 28px;
  padding-top: 10px;
  border-top: 1px solid #F2F5F9;
  margin: 20px 0;
  text-align: center;
  margin-top: 0px;
  margin-bottom: 0px;
  font-size: 12px;
}
.wrap-search .list-con.gcbox .more-number .f-blue {
  color: #1969f9;
}
.wrap-search .list-con.gcbox .more-number .load-more {
  cursor: pointer;
  color: #1969f9;
}
.wrap-search .list-con.gcbox .mstext {
  position: relative;
  line-height: 19px;
  max-height: 59px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.wrap-search .is-search-box {
  padding: 10px 0;
}
.wrap-search .is-search-box .is-search-input {
  width: 488px;
  height: 36px;
  line-height: 36px;
  padding-left: 10px;
  font-size: 14px;
  color: #313131;
  border: 2px solid #ff0000;
  border-right: none;
  box-sizing: border-box;
  transition: all 0.4s ease-in-out;
}
.wrap-search .is-search-box .is-search-btn {
  width: 160px;
  height: 36px;
  line-height: 36px;
  background: #ff0000;
  color: #fff;
  font-size: 14px;
  box-sizing: border-box;
  border: none;
  transition: all 0.4s ease-in-out;
}
.wrap-search .is-search-box .is-search-btn:hover {
  opacity: 0.8;
}
/**数字图片**/
font[class^="asfgd"] {
  padding: 1px 4px;
  background: url(../images/number/aff-search.svg) -20px -6px no-repeat;
  background: url(../images/number/aff.png) -20px -6px no-repeat \9;
  overflow: hidden;
  position: relative;
  margin-left: -1px;
}
.jt-group.act font[class^="asfgd"],
.yellownumbers font[class^="asfgd"] {
  padding: 1px 4px;
  background: url(../images/number/aff1.svg?v=20210114) -20px -6px no-repeat;
  background: url(../images/number/aff1.png) -20px -6px no-repeat \9;
  overflow: hidden;
  position: relative;
  margin-left: -1px;
}
.status-btn {
  background: #1969F9;
  color: #FFFFFF !important;
}
/*# sourceMappingURL=activity.min.css.map */