.cube-set {
    height: 100%;
    overflow-y: auto;
    margin-top: 45px;
    padding-bottom: 100px;
    
    .cube-set-head {
        position: fixed;
        top: 0;
        width: 100%;
        height: 45px;
        border-bottom: 1px solid #eee;
        padding: 0 20px;
        background: #FFFFFF;
        z-index: 999;
        
        h2 {
            font-weight: bold;
            white-space: nowrap;
        }
        
        label {
            white-space: nowrap;
        }
        
        .ml10 {
            margin-left: 10px;
        }
        
        .page-background {
            width: 100px;
            height: 30px;
            object-fit: cover;
            margin-left: 22px;
            cursor: pointer;
        }
    }
    
    .left-menu {
        position: absolute;
        left: 0;
        top: 45px;
        width: 273px;
        z-index: 9999;
        background: #FFFFFF;
        padding-left: 20px;
        
        .shousuo-box {
            position: absolute;
            left: 236px;
            top: 16px;
            cursor: pointer;
            z-index: 99;
            width: 20px;
            height: 20px;
            background: #FFFFFF;
            
            .icon-shousuo {
            
            }
        }
        
        .layui-collapse {
            border: 1px solid transparent;
        }
        
        .layui-colla-title {
            background: transparent;
            height: 25px;
            line-height: 25px;
            
            em {
                font-size: 12px;
                font-weight: normal;
                color: #5f5f5f;
            }
        }
        
        .layui-colla-content {
            border: 1px solid transparent;
            padding-top: 0;
            padding-bottom: 0;
        }
        
        .layui-colla-item {
            border: 1px solid transparent;
        }
        
        .system-compant {
            .operate {
                span {
                    height: 20px;
                    line-height: 24px;
                    margin-left: 6px;
                }
            }
        }
        
        .package-list {
            flex-wrap: wrap;
            
            li {
                width: 69px;
                margin-bottom: 5px;
                cursor: move;
                
                i {
                    width: 32px;
                    height: 32px;
                    background: url("../images/u745.png") no-repeat center;
                    background-size: cover;
                }
                
                span {
                    white-space: nowrap;
                }
            }
        }
        
        &.shousuo-box-curr {
            .shousuo-box {
                left: 10px;
            }
            
            .layui-tab {
                display: none;
            }
        }
    }
    
    .setting-fix-width {
        position: absolute;
        width: 1190px;
        height: 100%;
        margin: 0 auto;
        left: 0;
        right: 0;
        z-index: 1;
        
        .setting {
            position: absolute;
            right: -22px;
            top: 0;
            border: 1px solid #999;
            padding-top: 5px;
            background: #FFFFFF;
            z-index: 999;
            
            li {
                width: 20px;
                margin-bottom: 5px;
                cursor: pointer;
                transition: all 0.4s ease-in-out;
                
                &:hover {
                    color: #1969F9;
                }
            }
        }
    }
    
    //拖拽区域
    .cube-set-drag-area {
        margin: 0 auto 0;
        
        .cube-initial {
            position: relative;
            border: 1px dashed salmon;
            cursor: pointer;
            margin: 0 auto 1px;
            width: 1190px;
            
            .cube-set-drag-content {
                width: 100%;
                height: 100%;
                background: #f8f8f8;
                
                &.customLayout {
                    a, div {
                        background: #f8f8f8;
                    }
                    
                    .customLayout-box {
                        background-size: cover;
                    }
                }
                
                &.lotteryCircle {
                    
                    .number-of-draw {
                        em {
                            height: 32px;
                            background: #fff;
                            padding: 0 12px;
                            margin: 0 10px;
                            font-size: 32px;
                            color: #333;
                            border-radius: 2px;
                        }
                        
                        span {
                            font-size: 18px;
                            color: #ffffff;
                        }
                    }
                    
                    .lotteryCircle-bg {
                        position: relative;
                        width: 100%;
                        height: 1010px;
                        background: url("https://img.ichunt.com/images/ichunt/202303/09/5280cd20b5f9b931a70875cc1236766c.jpg") no-repeat center;
                        background-size: cover;
                        margin: 0 auto;
                        
                        .turntable-bg {
                            position: relative;
                            width: 708px;
                            height: 708px;
                            margin: 0 auto;
                            
                            .turntable-pic {
                                width: 708px;
                                height: 708px;
                                object-fit: cover;
                            }
                            
                            .round_click {
                                position: absolute;
                                left: 50%;
                                top: 50%;
                                margin-top: -139px;
                                margin-left: -91px;
                                width: 182px;
                                height: 240px;
                                background: url("https://img.ichunt.com/images/ichunt/202303/10/1cdbe11b0624e34f1e60b72f14637e40.png") no-repeat center;
                                background-size: contain;
                                z-index: 2;
                            }
                        }
                        
                        .btn-wrap {
                            background: transparent;
                            margin: 0 auto;
                            
                            a {
                                width: 150px;
                                height: 30px;
                                line-height: 30px;
                                text-align: center;
                                font-size: 14px;
                                color: #333;
                                background: #e0be7d;
                                border-radius: 2px;
                            }
                        }
                    }
                }
                
                &.lotterySquare {
                    
                    .number-of-draw {
                        em {
                            height: 32px;
                            background: #fff;
                            padding: 0 12px;
                            margin: 0 10px;
                            font-size: 32px;
                            color: #333;
                            border-radius: 2px;
                        }
                        
                        span {
                            font-size: 18px;
                            color: #ffffff;
                        }
                    }
                    
                    .lotterySquare-bg {
                        background: #0000ff;
                        background-size: cover;
                        
                        .lotterySquare-content {
                            position: relative;
                            margin: 0 auto;
                            height: 700px;
                            background-size: contain;
                            
                            a {
                                position: absolute;
                                width: 226px;
                                height: 226px;
                                background: #ffff00;
                                z-index: -1;
                                
                                &.box1 {
                                    left: 0;
                                    top: 0;
                                }
                                
                                &.box2 {
                                    left: 237px;
                                    top: 0;
                                }
                                
                                &.box3 {
                                    left: 474px;
                                    top: 0;
                                }
                                
                                &.box4 {
                                    left: 474px;
                                    top: 237px;
                                }
                                
                                &.box5 {
                                    left: 474px;
                                    top: 474px;
                                }
                                
                                &.box6 {
                                    left: 237px;
                                    top: 474px;
                                }
                                
                                &.box7 {
                                    left: 0px;
                                    top: 474px;
                                }
                                
                                &.box8 {
                                    left: 0px;
                                    top: 237px;
                                }
                                
                            }
                            
                            .btnClick {
                                position: absolute;
                                left: 237px;
                                top: 237px;
                                width: 226px;
                                height: 226px;
                                background: #ffff00;
                                z-index: -1;
                            }
                        }
                        
                        .btn-wrap {
                            background: transparent;
                            margin: 0 auto;
                            
                            a {
                                width: 150px;
                                height: 30px;
                                line-height: 30px;
                                text-align: center;
                                font-size: 14px;
                                color: #333;
                                background: #e0be7d;
                                border-radius: 2px;
                            }
                        }
                    }
                }
                
                &.coupon {
                    min-height: 250px;
                    padding: 50px 0;
                    
                    div {
                        background: transparent;
                    }
                    
                    .carousel {
                        position: relative;
                        padding: 20px 0;
                        background: #FFFFFF;
                        
                        .bd {
                            overflow: hidden;
                            
                            .picList {
                                width: 20000px;
                                
                                li {
                                    float: left;
                                    margin-right: 20px;
                                    
                                    a {
                                        width: 100%;
                                        height: 100%;
                                        display: block;
                                        background: #FFFFFF;
                                        
                                        img {
                                            width: 100%;
                                            height: 100%;
                                            object-fit: cover;
                                        }
                                    }
                                }
                            }
                        }
                        
                        .hd {
                            .layui-carousel-arrow {
                                opacity: 1;
                                background: rgba(0, 0, 0, .2);
                            }
                            
                            .prev {
                                position: absolute;
                                top: 50%;
                                left: 10px;
                                margin-top: -18px;
                                width: 36px;
                                height: 36px;
                                line-height: 36px;
                                text-align: center;
                                font-size: 20px;
                                border: none 0;
                                border-radius: 50%;
                                background-color: rgba(0, 0, 0, .2);
                                color: #fff;
                                -webkit-transition-duration: .3s;
                                transition-duration: .3s;
                                cursor: pointer;
                            }
                            
                            .next {
                                position: absolute;
                                top: 50%;
                                right: 10px;
                                margin-top: -18px;
                                width: 36px;
                                height: 36px;
                                line-height: 36px;
                                text-align: center;
                                font-size: 20px;
                                border: none 0;
                                border-radius: 50%;
                                background-color: rgba(0, 0, 0, .2);
                                color: #fff;
                                -webkit-transition-duration: .3s;
                                transition-duration: .3s;
                                cursor: pointer;
                            }
                        }
                    }
                    
                    .carousel-list {
                        .picList {
                            width: 965px;
                            margin: 0 auto;
                            flex-wrap: wrap;
                            justify-content: flex-start;
                            
                            li {
                                width: 230px;
                                overflow: hidden;
                                margin-right: 15px;
                                margin-bottom: 15px;
                                
                                a {
                                    width: 100%;
                                    height: 100%;
                                    
                                    img {
                                        width: 100%;
                                        height: 100%;
                                        object-fit: cover;
                                    }
                                }
                            }
                        }
                    }
                }
                
                &.shopList {
                    min-height: 220px;
                }
                
                &.formModule {
                    width: 500px;
                    min-height: 220px;
                    padding: 20px;
                    background: transparent;
                    
                    .layui-form-label {
                        width: 116px !important;
                        padding: 5px 15px !important;
                    }
                    
                    .layui-input-block {
                        margin-left: 116px !important;
                    }
                }
                
            }
            
        }
        
    }
    
    //楼层配置
    .cube-setting-layer {
        position: fixed !important;
        right: 5px !important;
        top: 59px;
        width: 400px;
        background: #FFFFFF;
        border: 1px solid #333;
        padding: 10px;
        z-index: 9999999 !important;
        max-height: 600px;
        overflow-y: auto !important;
        display: none;
        
        .columnWidthDynamics {
            input {
                margin-bottom: 5px;
            }
        }
        
        .layui-form-mid {
            padding: 5px 0 !important;
        }
        
        .title {
            cursor: move;
            
            .tt {
                font-weight: bold;
            }
            
            .toggle {
                cursor: pointer;
                color: #1c8eff;
            }
        }
        
        .layui-form-label {
            width: 62px;
            padding: 5px 15px;
        }
        
        .layui-input-block {
            margin-left: 80px;
        }
        
        .bar-wrap {
            padding-top: 10px;
            padding-left: 63px;
            
            .tip {
                color: #999;
            }
            
            img {
                width: 120px;
                height: 43px;
                object-fit: cover;
                cursor: pointer;
            }
        }
        
        .total {
            color: #999;
            margin-left: 5px;
        }
        
        .checkbox-style {
            .layui-form-checkbox[lay-skin=primary] {
                margin-top: 7px !important;
            }
        }
    }
    
    .layui-input-error {
        border: 1px solid #ff5722 !important;
    }
    
    .clearPic {
        font-size: 12px;
        color: #1224CC;
        display: inline-block;
        vertical-align: middle;
        margin-left: 3px;
    }
    
    .clearBg {
        font-size: 12px;
        color: #1224CC;
        display: inline-block;
        vertical-align: middle;
        margin-left: 3px;
    }
}

.wrap-search {
    position: relative;
    width: 1190px;
    margin: 0 auto;
    color: #333;
    font-size: 14px;
    background: #FFFFFF !important;
    
    * {
        box-sizing: content-box;
    }
    
    .mb3 {margin-bottom: 3px;}
    
    .f-999 {color: #999;}
    
    .f-yellow {color: #ff3700 !important;}
    
    .wordwarpover {word-wrap: break-word;overflow: hidden;}
    
    .wordwarp {word-break: break-all;}
    
    .brand-mr, .brand-mr .f-red {color: #000000 !important;font-size: 12px !important;}
    
    /*广告位*/
    
    .left-ad {
        display: block;
        position: absolute;
        left: -100px;
        top: 0;
        width: 100px;
        max-height: 450px;
        z-index: 48;
        overflow: hidden;
    }
    
    .right-ad {
        display: block;
        position: absolute;
        right: -158px;
        top: 0;
        width: 100px;
        max-height: 450px;
        z-index: 48;
        overflow: hidden;
    }
    
    .foot-ad {
        display: block;
        width: 1190px;
        max-height: 70px;
        margin-top: 20px;
        overflow: hidden;
    }
    
    .left-ad img,
    .right-ad img,
    .foot-ad img {
        width: 100%;
        height: 100%;
        display: block;
    }
    
    /*优惠劵*/
    
    .ss-coupon ul {
        padding-left: 10px;
    }
    
    .ss-coupon li {
        float: left;
        width: 350px;
        margin: 0 20px;
        position: relative;
        margin-bottom: 20px;
    }
    
    .ss-coupon li p {
        display: none;
    }
    
    .ss-coupon li img {
        display: block;
        width: 100%;
    }
    
    .ss-coupon li.act {
        display: none;
    }
    
    .ss-coupon .act p {
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background: url(../../images/gray-bg-30.png);
    }
    
    .ss-coupon .act i {
        z-index: 1;
        display: block;
        width: 120px;
        height: 120px;
        position: absolute;
        top: 4px;
        right: 10px;
        background: url(../../images/template/get.png) no-repeat;
        background-size: 100% 100%;
    }
    
    .ss-coupon .act.actb i {
        z-index: 1;
        display: block;
        width: 120px;
        height: 120px;
        position: absolute;
        top: 4px;
        right: 10px;
        background: url(../../images/template/pot.png) no-repeat;
        background-size: 100% 100%;
    }
    
    /**搜索主体**/
    
    .shit-head {
        background: #fff;
        padding: 12px 16px 0 16px;
        
        &.fixedheads {
            .tog-con {display: none !important;}
            
            background: #fff;
            padding: 6px 15px;
            width: 1160px;
            
            .supplier-item-box {
                width: 1005px !important;
            }
        }
    }
    
    .fixedheads {
        position: fixed !important;
        top: 60px;
        z-index: 10;
    }
    
    .supdatabox .fixedheads {
        z-index: 11;
    }
    
    /**供应商筛选**/
    
    .supplier-sort {
        font-size: 14px;
        position: relative;
        
        .supplier-title {
            font-weight: bold;
            width: 61px;
        }
        
        .supplier-item-box {
            width: 982px;
            padding-right: 110px;
            
            .supplier-item {
                margin-left: 10px;
                cursor: pointer;
                padding-left: 6px;
                padding-right: 6px;
                float: left;
                margin-bottom: 5px;
                position: relative;
                border: 1px solid #fff;
                color: #1969F9;
                
                i {
                    display: none;
                }
                
                &.act {
                    border: 1px solid #EDEDED;
                    background: #Fff;
                    color: #1969F9;
                    
                    i {
                        display: inline-block;
                        width: 12px;
                        height: 12px;
                        background: url(../images/closesipplier.png) no-repeat;
                        margin-left: 5px;
                    }
                }
            }
        }
        
        .czbsg {
            height: 20px;
            position: absolute;
            right: 0px;
            top: 3px;
            z-index: 1;
        }
        
        .clear-alls {
            cursor: pointer;
            width: 55px;
            height: 20px;
            background: #FFFFFF;
            border: 1px solid #EDEDED;
            margin-left: 8px;
            font-size: 12px;
            color: #3D91C8;
            line-height: 17px;
            height: 17px;
            text-align: center;
            display: none;
        }
        
        .tog-con {
            height: 17px;
            font-size: 12px;
            color: #3D91C8;
            line-height: 17px;
            cursor: pointer;
            
            i {
                background: url(../images/arrow.png) no-repeat;
                display: inline-block;
                vertical-align: middle;
                width: 18px;
                height: 18px;
                position: relative;
                top: -2px;
                
            }
            
            &.act {
                i {
                    transform: rotate(180deg);
                }
            }
        }
        
        
    }
    
    /**排序筛选**/
    
    .title-cut {
        padding-top: 6px;
        
        .total-rank {
            margin-right: 25px;
            
            li {
                width: 70px;
                height: 26px;
                background: #f8f8f8;
                border: 1px solid #f8f8f8;
                font-size: 14px;
                line-height: 28px;
                text-align: center;
                cursor: pointer;
                font-weight: bold;
                position: relative;
                
                i.icon-bottom {
                    width: 0px;
                    height: 0px;
                    border-style: solid;
                    border-width: 6px;
                    border-color: #D8D8D8 transparent transparent transparent;
                    position: absolute;
                    top: 15px;
                    right: 5px;
                    
                    &.cur {
                        border-color: #1969F9 transparent transparent transparent;
                    }
                }
                
                i.icon-top {
                    width: 0px;
                    height: 0px;
                    border-style: solid;
                    border-width: 6px;
                    border-color: transparent transparent #D8D8D8 transparent;
                    position: absolute;
                    top: 1px;
                    right: 5px;
                }
                
                &.dpsort {
                    span {
                        position: relative;
                        left: -5px;
                    }
                }
                
                &.act {
                    background: #1969F9;
                    color: #fff;
                    border: 1px solid #1969F9;
                    
                    &.dpsort {
                        color: #1969F9;
                        background: #fff;
                    }
                    
                    &.top {
                        i.icon-top {border-color: transparent transparent #1969F9 transparent;}
                    }
                    
                    &.bottom {
                        i.icon-bottom {border-color: #1969F9 transparent transparent transparent;}
                    }
                }
                
                
            }
        }
        
        .check-b {
            margin-left: 18px;
            
            cursor: pointer;
            height: 28px;
            line-height: 26px;
            
            .checkbox {
                height: 18px;
                width: 18px;
                border-radius: 2px;
                background: #FFF;
                border: 1px solid #ededed;
                position: relative;
                top: 2px;
                margin-right: 6px;
                
                .icon-check {
                    background: url(../images/gou.png) no-repeat;
                    background-size: 100% 100%;
                    width: 18px;
                    height: 18px;
                    display: none;
                }
            }
            
            &.act {
                color: #1969F9;
                
                .checkbox {
                    border: 1px solid #1969F9;
                    background: #1969F9;
                    
                    .icon-check {
                        display: block;
                    }
                }
            }
        }
        
    }
    
    .w-155 {width: 155px;}
    
    .w-91 {width: 91px;}
    
    .w-100 {width: 100px;}
    
    .w-101 {width: 101px;}
    
    .w-92 {width: 92px;}
    
    .w-93 {width: 93px;}
    
    .w-89 {width: 89px;}
    
    .w-82 {width: 82px;}
    
    .w-80 {width: 80px;}
    
    .w-70 {width: 70px;}
    
    .w-75 {width: 75px;}
    
    .w-78 {width: 78px;}
    
    .w-69 {width: 69px;}
    
    .w-50 {width: 50px;}
    
    .w-131 {width: 131px;}
    
    .w-114 {width: 114px;}
    
    .w-165 {width: 165px;}
    
    .w-389 {width: 389px;}
    
    .w-326 {width: 326px;}
    
    .w-133 {width: 133px;}
    
    .w-405 {width: 405px;}
    
    .w-147 {width: 147px;}
    
    .w-310 {width: 310px;}
    
    .w-293 {width: 293px;}
    
    .w-235 {width: 235px;}
    
    .w-60 {width: 60px;}
    
    .w-86 {width: 86px;}
    
    .w-104 {width: 104px;}
    
    .w-79 {width: 79px;}
    
    .w-89 {width: 89px;}
    
    .w-94 {width: 94px;}
    
    .w-76 {width: 76px;}
    
    .w-210 {width: 210px;}
    
    .w-149 {width: 149px;}
    
    /**列表区域**/
    
    .list-con {
        padding: 14px;
        background: #fff;
        box-shadow: 0px 2px 4px 0px rgba(61, 145, 200, 0.09);
        
        .no_content {
            background: #fff;
            text-align: center;
            padding: 0;
            height: auto;
            margin-top: 20px;
            
            dl {
                width: 455px;
                margin: 0 auto;
            }
            
            dt {
                width: 120px; height: 100px;
                margin-left: 151px;
                
                .icon-ss-wu { background: url(../../images/icon_ss_wu.png) no-repeat; display: inline-block; width: 120px;height: 100px;}
                
                .icon-ss-wu-sy { background: url(../../images/no-data-search.png) no-repeat; display: inline-block; width: 120px;height: 100px;background-size: 100% 100%;}
            }
            
            dd {
                line-height: 22px; padding-bottom: 30px;
                padding-top: 10px;color: #000;font-size: 16px;text-align: center;
                width: 451px;
                
                h2 { font-size: 16px; font-weight: bold; padding-bottom: 20px; color: #000000; }
                
                p {
                    a { height: 40px; line-height: 40px; font-size: 14px; padding: 0 38px; }
                }
            }
        }
        
        .list-th {
            background: #1969F9;
            height: 28px;
            line-height: 28px;
            padding: 0px 7px;
            font-size: 12px;
            color: #fff;
            
            &.fixedheads {
                width: 1146px;
                
            }
            
            .th {
                padding-left: 5px;
                padding-right: 5px;
                position: relative;
                
                &::after {
                    content: "|";
                    position: absolute;
                    right: 0px;
                    color: #AEBACB;
                }
                
                &.noborder {
                    &::after {
                        content: "";
                        
                    }
                }
            }
        }
        
        .list-td {
            .list-group {
                min-height: 82px;
                padding: 10px 7px;
                position: relative;
                
                transition: all 0.8s;
                
                &:nth-child(2n) {
                    background: #F2F5F9;
                }
                
                &:hover {
                    background: #E8F2FF;
                    
                    .group-kf {
                        left: 0px !important;
                    }
                }
                
                .td {
                    padding-left: 5px;
                    padding-right: 5px;
                    position: relative;
                    font-size: 12px;
                    word-wrap: break-word;
                    line-height: 17px;
                    
                    .kecpn {
                        position: relative;
                        overflow: hidden;
                        margin-top: 5px;
                        height: 18px;
                    }
                    
                    .group-kf {
                        display: block;
                        cursor: pointer;
                        position: absolute;
                        left: -160px;
                        top: 0px;
                        transition: all 0.6s;
                        
                        span {
                            display: inline-block;
                            width: 16px;
                            height: 16px;
                            background: url(../images/qq.png) no-repeat;
                            vertical-align: middle;
                            position: relative;
                            top: -2px;
                            
                            &.p2fk {
                                background: url(../images/fkwh.png) no-repeat;
                            }
                        }
                    }
                    
                    .zk {
                        position: absolute;
                        left: -7px;
                        top: -10px;
                        width: 25px;
                        height: 25px;
                        background: url(../images/zhek.png) no-repeat;
                    }
                    
                    .goods-title {
                        font-weight: bold;
                        line-height: 17px;
                        max-height: 72px;
                        overflow: hidden;
                        display: block;
                        max-width: 131px;
                        cursor: pointer;
                        word-break: break-all;
                        
                        .f-red {
                            color: #ff3700 !important;
                        }
                        
                        float: left;
                        
                    }
                    
                    .copygoods {
                        display: inline-block;
                        height: 15px;
                        width: 15px;
                        background: url(../images/copy.png) no-repeat;
                        background-size: 100% 100%;
                        cursor: pointer;
                        position: relative;
                        top: 1px;
                        margin-left: 5px;
                        float: left;
                        margin-right: 3px;
                    }
                    
                    .ptp {
                        height: 16px;
                        background: #1969F9;
                        color: #fff;
                        line-height: 14px;
                        text-align: center;
                        display: inline-block;
                        padding: 0 5px;
                        border-radius: 2px;
                    }
                    
                    .signcm {
                        margin-left: 5px;
                        margin-right: 0px;
                    }
                    
                    .pdfurl {
                        width: 18px;
                        height: 18px;
                        display: block;
                        cursor: pointer;
                        background: url(../images/pdf.png?v=20221026) no-repeat;
                        background-size: 100% 100%;
                        margin-right: 3px;
                        position: relative;
                        top: -1px;
                    }
                    
                    .tagbox {
                        
                        margin-top: 4px;
                        
                        .jx {
                            width: 36px;
                            height: 16px;
                            line-height: 15px;
                            border-radius: 0px 100px 100px 0px;
                            border: 1px solid #1969F9;
                            color: #1969F9;
                            text-align: center;
                            margin-right: 3px;
                        }
                        
                        .yc {
                            height: 16px;
                            line-height: 15px;
                            width: 60px;
                            height: 16px;
                            border-radius: 0px 100px 100px 0px;
                            border: 1px solid #F98119;
                            color: #F98119;
                            text-align: center;
                            margin-right: 3px;
                        }
                        
                        .qh {
                            height: 16px;
                            line-height: 15px;
                            padding: 0 5px;
                            height: 16px;
                            border-radius: 0px 100px 100px 0px;
                            border: 1px solid #1896f0;
                            color: #1896f0;
                            text-align: center;
                            margin-right: 3px;
                            position: relative;
                            
                            .fcb {
                                position: absolute;
                                top: 16px;
                                left: 0px;
                                display: none;
                                width: auto;
                                min-width: 140px;
                                height: auto;
                                min-height: 72px;
                                background: #FFFFFF;
                                box-shadow: 0px 2px 4px 0px rgba(61, 145, 200, 0.09);
                                border: 1px solid #AEBACB;
                                z-index: 6;
                                padding: 6px 10px;
                                
                                a {
                                    line-height: 22px;
                                    overflow: auto !important;
                                    text-overflow: initial !important;
                                    white-space: normal !important;
                                    text-align: left;
                                    display: block;
                                    cursor: pointer;
                                    position: relative;
                                    padding-left: 18px;
                                    
                                    &::before {
                                        position: absolute;
                                        content: "";
                                        width: 11px;
                                        height: 11px;
                                        background: url(../images/search.png) no-repeat;
                                        left: 2px;
                                        top: 7px;
                                    }
                                    
                                    &:hover {color: #1969F9;}
                                }
                                
                            }
                            
                            &:hover .fcb {
                                display: block;
                            }
                        }
                        
                        .zktag {
                            height: 16px;
                            line-height: 15px;
                            width: 20px;
                            height: 16px;
                            border-radius: 0px 100px 100px 0px;
                            border: 1px solid #FF0035;
                            color: #FF0035;
                            text-align: center;
                            margin-right: 3px;
                            position: relative;
                            
                            .fcb {
                                position: absolute;
                                width: 250px;
                                padding: 10px;
                                background: #FFFFFF;
                                box-shadow: 0px 2px 10px 0px rgba(160, 160, 160, 0.5);
                                color: #333;
                                z-index: 4;
                                left: 0px;
                                top: 20px;
                                display: none;
                                
                                p {
                                    line-height: 24px;
                                    overflow: auto !important;
                                    text-overflow: initial !important;
                                    white-space: normal !important;
                                    text-align: left;
                                }
                                
                            }
                            
                            &:hover .fcb {
                                display: block;
                            }
                        }
                    }
                    
                    .kpcon {
                        overflow: hidden;
                    }
                    
                    .jt-con {
                        .w-70 {margin-bottom: 3px;}
                        
                        padding-left: 6px;
                        
                        .f-red {
                            color: #ff3700 !important;
                        }
                        
                        .jt-group {
                            &.act {color: #ff3700;
                            
                            
                            }
                            
                            .xgprice {color: #999;text-decoration: line-through;}
                            
                            .ac-price a {color: #1969F9;}
                        }
                    }
                    
                    .more-con {
                        margin-top: 5px;
                        width: 379px;
                        height: 20px;
                        line-height: 18px;
                        cursor: pointer;
                        background: #FFFFFF;
                        border-radius: 0px 0px 100px 100px;
                        border: 1px solid #EDEDED;
                        text-align: center;
                        
                        span {color: #1969F9;margin-right: 5px;}
                        
                        font {
                            vertical-align: middle;
                            display: inline-block;
                            width: 17px;
                            height: 17px;
                            background: url(../images/bluearrow.png) no-repeat;
                        }
                        
                        &.act {
                            font {
                                transform: rotate(180deg);
                                position: relative;
                                top: -2px;
                            }
                        }
                    }
                    
                    .check-b {
                        cursor: pointer;
                        margin-bottom: 8px;
                        
                        .checkbox {
                            height: 14px;
                            width: 14px;
                            border-radius: 2px;
                            background: #FFF;
                            border: 1px solid #ededed;
                            margin-right: 4px;
                            
                            .icon-check {
                                background: url(../images/gounew.png) no-repeat;
                                background-size: 100% 100%;
                                width: 14px;
                                height: 14px;
                                display: none;
                            }
                        }
                        
                        &.act {
                            .checkbox {
                                border: 1px solid #1969F9;
                                background: #fff;
                                
                                .icon-check {
                                    display: block;
                                }
                            }
                        }
                    }
                    
                    .edit-input {
                        width: 94px;
                        height: 24px;
                        background-color: #3D91C8;
                        padding: 1px;
                        
                        span {
                            float: left;
                            margin-right: 0;
                            color: #000000;
                            font-size: 20px;
                            cursor: pointer;
                            background-color: #fff;
                            width: 20px;
                            text-align: center;
                            height: 24px;
                            line-height: 24px;
                            position: relative;
                            
                            &:hover {
                                background: #1969F9;
                                color: #fff;
                                transition: all 0.4s;
                            }
                        }
                        
                        input {
                            padding: 0;
                            width: 52px;
                            text-align: center;
                            margin: 0 1px;
                            height: 24px;
                            line-height: 24px;
                            outline: 0;
                            outline: none;
                            display: block;
                            float: left;
                            background: #fff;
                            border: none;
                            color: #000000;
                        }
                    }
                    
                    .total {
                        color: #ff3700;
                        font-weight: bold;
                        margin-top: 24px;
                        
                        font {font-weight: normal;}
                        
                        &.be {
                            margin-top: 10px;
                        }
                    }
                    
                    .btn {
                        width: 76px;
                        height: 26px;
                        background: #1969F9;
                        border-radius: 2px;
                        border: 1px solid #1969f9;
                        color: #fff;
                        transition: all 0.6s;
                        margin-bottom: 10px;
                        cursor: pointer;
                        text-align: center;
                        line-height: 26px;
                        display: block;
                        
                        &:hover {
                            background: #387FFF;
                        }
                        
                        &.yellowbtn {
                            background: #F98119;
                            border: 1px solid #F98119;
                        }
                        
                        &.btn-lk {
                            background: #fff;
                            color: #1969f9;
                            
                            &:hover {
                                background: #1969f9;
                                color: #fff;
                            }
                            
                            &.yellowbtn {
                                color: #F98119;
                                border: 1px solid #F98119;
                                
                                &:hover {
                                    color: #fff;
                                    background: #F98119;
                                }
                            }
                        }
                    }
                }
                
                
            }
            
        }
        
        /**国产推荐**/
        
        &.gcbox {
            padding-top: 10px;
            
            .gctitle {
                line-height: 22px;
                height: 22px;
                margin-bottom: 10px;
                
                span {
                    font-size: 16px;
                    font-weight: 600;
                    color: #1969F9;
                }
                
                p {
                    margin-left: 24px;
                    font-size: 12px;
                    margin-right: 16px;
                }
                
                div {font-size: 12px;color: #999;
                    
                    b {font-weight: bold;color: #1969F9;}
                }
                
                font {
                    cursor: pointer;
                    color: #3D91C8;
                }
            }
            
            .more-con {
                width: 309px !important;
            }
            
            .more-number {
                height: 28px;
                line-height: 28px;
                padding-top: 10px;
                border-top: 1px solid #F2F5F9;
                margin: 20px 0;
                text-align: center;
                margin-top: 0px;
                margin-bottom: 0px;
                font-size: 12px;
                
                .f-blue {color: #1969f9;}
                
                .load-more {
                    cursor: pointer;
                    color: #1969f9;
                }
            }
            
            .mstext {
                position: relative;
                line-height: 19px;
                max-height: 59px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                overflow: hidden;
            }
        }
        
    }
    
    .is-search-box {
        padding: 10px 0;
        
        .is-search-input {
            width: 488px;
            height: 36px;
            line-height: 36px;
            padding-left: 10px;
            font-size: 14px;
            color: #313131;
            border: 2px solid #ff0000;
            border-right: none;
            box-sizing: border-box;
            transition: all 0.4s ease-in-out;
        }
        
        .is-search-btn {
            width: 160px;
            height: 36px;
            line-height: 36px;
            background: #ff0000;
            color: #fff;
            font-size: 14px;
            box-sizing: border-box;
            border: none;
            transition: all 0.4s ease-in-out;
            
            &:hover {
                opacity: 0.8;
            }
        }
    }
}

/**数字图片**/
font[class^="asfgd"] {
    padding: 1px 4px;
    background: url(../images/number/aff-search.svg) -20px -6px no-repeat;
    background: url(../images/number/aff.png) -20px -6px no-repeat \9;
    overflow: hidden;
    position: relative;
    margin-left: -1px;
}

.jt-group.act font[class^="asfgd"], .yellownumbers font[class^="asfgd"] {
    padding: 1px 4px; background: url(../images/number/aff1.svg?v=20210114) -20px -6px no-repeat;
    background: url(../images/number/aff1.png) -20px -6px no-repeat \9;
    overflow: hidden;position: relative;
    margin-left: -1px;
}

.status-btn {
    background: #1969F9;
    color: #FFFFFF !important;
}
