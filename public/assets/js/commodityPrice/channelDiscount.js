layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate','xmSelect'], function () {
    var admin = layui.admin;
    var index = layui.index;
    var form = layui.form;
    var table = layui.table;
    var xmSelect = layui.xmSelect;
    var laydate = layui.laydate;

      window.IndexController = {
        init: function () {
          this.created(this).render(this);
        },
        created: function () {
          $(".skuidsp").on("input",function(e){
            $("#brandSelect").addClass("dis")
          });


          $(".resets").click(function(){
            $("#brandSelect").removeClass("dis")
          })
          //渲染多选
          let brandSelect = xmSelect.render({
            el: '#brandSelect',
            name: 'brand_name',
            searchTips: '请输入标品或非标品',
            paging: true,
            empty: '没有查找到数据',
            prop: {
              name: 'brand_name',
              value: 'brand_name'
            },
            height: "400px",
            remoteSearch: true,
            autoRow: false,
            pageRemote: true,
            filterable: true,
            remoteMethod: function (val, cb, show, pageIndex) {
              //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
              $.ajax({
                url: '/api/commonData/searchStandardBrand',
                type: 'post',
                data: {
                  brand_ids: $('#brand_ids').val(),
                  brand_name: val,
                  page: pageIndex
                },
                dataType: 'json',
                timeout: 10000,
                success: function (res) {
                  if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
                  if (res.errcode === 0) {
                    cb(res.data, res.last_page);
                  } else {
                    layer.msg(res.errmsg, { icon: 6 });
                  }
                },
                error: function () {
                  return layer.msg('网络错误，请重试', { icon: 5 });
                }
              });
            },
            on: function (data) {

              if(data.arr.length>0){
                $(".skuidsp").attr("disabled","true").css("background","#f3f3f3")
              }
              // let brandIds = '';
              // for (let x in data.arr)  // x 为属性名
              // {
              //     brandIds = brandIds + data.arr[x].brand_id + ',';
              // }
              // $("#brand_id_condition").val(brandIds);
            }
          });
          return this;
        },
        render: function () {
          table.render({
            elem: '#list',
            url: '/api/channelDiscount/list',
            toolbar: '#toolbar',
            method: 'POST',
            // lineStyle: 'height: auto;',
            cols: [[
                { field: 'channel_disct_sn', title: '渠道折扣编码', width: 120, align: 'center', templet(d) {
                  
                    return '<a class="alink" ew-href="/web/price/getChannelDiscountPage?channel_disct_id='+d.channel_disct_id+'" ew-title="'+d.channel_disct_sn+'详情">'+d.channel_disct_sn+'</a>'
                } },
                { field: 'supplier_id_or_code', title: '供应商编码', width: 120, align: 'center' },
                { field: 'supplier_name', title: '供应商名称', width: 150, align: 'center' },
                {
                    field: '', title: '是否默认', width: 70, align: 'center', templet(d) {
                        if (d.is_default == 1) {
                            return '<span class="warm-color">是</span>'
                        } else {
                            return '<span >否</span>'
                        }
                    }
                },
                { field: 'order', title: '优先级', width: 50, align: 'center' },
                { field: 'ration_format', title: '人民币渠道折扣', width: 110, align: 'center' },
                { field: 'ration_usd_format', title: '美金渠道折扣', width: 110, align: 'center' },
    //            { field: 'goods_name', title: '参与型号', width: 160, align: 'center'},
                {
                field: '', title: '参与型号', width: 160, align: 'center',templet(d){
                        if(d.file_url){
                            return '<a target="_blank"  style="color:#1224CC !important" href="'+d.file_url+'">下载链接</a>'
                        }else{
                        return "";
                        }
    
                }
                },
                { field: 'brand', title: '参与品牌', width: 160, align: 'center' },
          
                {
                    field: '', title: '状态', width: 50, align: 'center', templet(d) {
                        if (d.status  == 1) {
                            return '<span>启用</span>'
                        } else {
                            return '<span class="warm-color">禁用</span>'
                        }
                    }
                },
                { field: 'remark', title: '备注', width: 120, align: 'center' },
                {field: 'create_time', title: '创建时间', width: 150, align: 'center'},
                {
                    field: '', title: '操作', width: 160, align: 'center', templet(d) {
                        var st_="启用";
                        if(d.status  == 1){
                            st_="禁用";
                        }
                        return '<a class="layui-btn layui-btn-xs" ew-href="/web/price/upateChannelDiscount?channel_disct_id='+d.channel_disct_id+'" ew-title="编辑'+d.channel_disct_sn+'">编辑</a><a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="statusChange">'+st_+'</a><a class="layui-btn layui-btn-xs layui-btn-warm"  ew-href="/web/price/addChannelDiscount?channel_disct_id='+d.channel_disct_id+'" ew-title="新增渠道折扣">拷贝</a>'
                    }
                },
            ]],
            parseData: LayUiTableParseData,
            done: function (res, curr, count) {
                layui.form.render();
            }
        });
     
        //查询搜索
        form.on('submit(load)', function (data) {
            table.reload('list', {
                where: data.field,
                page: {
                    curr: 1
                }
            });
        });
    
    
        //触发单元格工具事件
        table.on('tool(list)', function (obj) {
            var data = obj.data;
            switch (obj.event) {
                //改变状态
                case 'statusChange':
                    layer.confirm('是否要<em class="warm-color">' + (data.status == 1 ? '禁用' : '启用') + '</em>此渠道折扣？', {
                        skin: 'layui-layer-admin',
                        title: '提示',
                        shade: .1,
                        resize: false,
                        offset: '250px',
                        move: false
                    }, function (i) {
                        Request('/api/channelDiscount/disable', 'POST', { channel_disct_id: data.channel_disct_id}, function (res) {
                            if (res.code == 0) {
                                layer.msg('操作成功', { shift: 0, time: 2000 }, function () {
                                    layer.closeAll();
                                    table.reloadData('list', {});
                                });
                            } else {
                                layer.msg(res.msg);
                            }
                        });
                    });
                    break;
                //禁用活动
            }
        });
          return this;
        }
      }

   


 IndexController.init();
});
