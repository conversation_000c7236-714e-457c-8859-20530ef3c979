layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate'], function () {
    var admin = layui.admin;
    var index = layui.index;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;


    table.render({
        elem: '#list',
        url: '/api/rate/getRateList',
        toolbar: '#toolbar',
        method: 'GET',
        lineStyle: 'height: auto;',
        cols: [[ //表头
            {title: '序号', type: 'numbers'}
            , {field: 'currency_name', title: '原币'}
            , {field: 'target_currency_name', title: '目标币'}
            , {field: 'rate', title: '汇率值'}
            , {field: 'update_time', title: '生效日期'}

        ]], 
        parseData: LayUiTableParseData,
        done: function (res, curr, count) {
            layui.form.render();
        }
    });
    
    $("body").on("click","#tbjdrate",function(){
        Request('/api/rate/getErpRate', 'POST', { }, function (res) {
            if (res.code == 0) {
                layer.msg('同步成功', { shift: 0, time: 2000 }, function () {
                    table.reloadData('list', {});
                });
            } else {
                layer.msg(res.msg);
            }
        });
    })
    //查询搜索
    form.on('submit(load)', function (data) {
        table.reload('list', { 
            where: data.field,
            page: {
                curr: 1
            }
        });
    });


  

});
