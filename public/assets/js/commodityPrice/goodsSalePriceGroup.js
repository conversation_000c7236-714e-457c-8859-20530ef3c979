layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate'], function () {
    var admin = layui.admin;
    var index = layui.index;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;


    function rendertable(sppe_sn) {
        table.render({
            elem: '#list',
            url: '/api/goodsSalePriceGroup/list',
            toolbar: '#toolbar',
            method: 'POST',
            lineStyle: 'height: auto;',
            where: {
                sppe_sn: (sppe_sn || "")
            },
            cols: [[
                {
                    field: '', title: '操作', width: 160, align: 'center', templet(d) {
                        var st_ = "启用";
                        if (d.status == 1) {
                            st_ = "禁用";
                        }
                        var qiybtn='<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="statusChange">' + st_ + '</a>'
                        if (d.is_default == 1) {
                            qiybtn=""
                        }
                        var html_='<a class="layui-btn layui-btn-xs" ew-href="/web/price/updateGoodsSalePriceGroup?sppe_id=' + d.sppe_id + '" ew-title="编辑' + d.sppe_sn + '">编辑</a>'+qiybtn+'<a class="layui-btn layui-btn-xs layui-btn-warm"  ew-href="/web/price/addGoodsSalePriceGroup?sppe_id=' + d.sppe_id + '" ew-title="新增商品售价组">拷贝</a>'
                        return html_
                    }
                },
                { field: 'org_name', title: '组织', width: 60, align: 'center' },
                { field: 'sppe_sn', title: '售价组编码', width: 120, align: 'center' },
                { field: 'supplier_id_or_code', title: '供应商编码', width: 120, align: 'center' },
                { field: 'supplier_name', title: '供应商名称', width: 150, align: 'center' },
                {
                    field: '', title: '是否默认', width: 70, align: 'center', templet(d) {
                        if (d.is_default == 1) {
                            return '<span class="warm-color">是</span>'
                        } else {
                            return '<span >否</span>'
                        }
                    }
                },
                { field: 'order', title: '优先级', width: 50, align: 'center' },
                {
                    field: '', title: '销售利润<span class="jgsq">收起</span>', width: 250, align: 'center', templet(d) {
                        var html_ = '', arr_ = d.step_price_data || [];
                        console.log(arr_)
                        if(d.org_id==1){
                            for (var i = 0; i < arr_.length; i++) {
                                html_ += '<div class="row"><span style="color:red;">' + arr_[i].order + '：</span><span>' + arr_[i].ratio + '</span><span>' + arr_[i].ratio_usd + '</span></div>'
                            }
                        }else{
                            html_ = '<div class="row"><span>' + arr_[0].ratio + '</span></div>'
                        }
                        return '<div class="priceboxs">' + html_ + '</div>'

                    }
                },
                { field: 'goods_name', title: '参与型号', width: 160, align: 'center' , templet(d) {
                    if (d.goods_name) {
                        return '<span class="alink ylbtn" html_="'+d.goods_name+'">预览</span>'
                    } else {
                        return ''
                    }
                }},
                { field: 'brand', title: '参与品牌', width: 160, align: 'center' },
                { field: 'eccn', title: '参与ECCN', width: 160, align: 'center' },
                {
                    field: '', title: '状态', width: 50, align: 'center', templet(d) {
                        if (d.status == 1) {
                            return '<span>启用</span>'
                        } else {
                            return '<span class="warm-color">禁用</span>'
                        }
                    }
                },
                { field: 'create_time', title: '创建时间', width: 150, align: 'center' },

            ]],
            parseData: LayUiTableParseData,
            done: function (res, curr, count) {
                layui.form.render();
            }
        });
    }
    rendertable($("#sppe_sn").val())
    $("body").on("click", ".jgsq", function () {
        if ($(this).text() == "收起") {
            $(this).text("展开");
            $(".priceboxs").addClass("h360")
        } else {
            $(this).text("收起");
            $(".priceboxs").removeClass("h360")
        }
    })
    //查询搜索
    form.on('submit(load)', function (data) {
        table.reload('list', {
            where: data.field,
            page: {
                curr: 1
            }
        });
    });
    var tipsVal = '';
    $(document).on('mouseenter', '.ylbtn', function () {
        var self = this;
        var html_='<div style="word-break: break-all;">'+$(this).attr("html_")+'</div>'
        tipsVal = layer.tips(html_, self, {
            tips: [3, "#009688"],
            time: 1000000,
            area: ['400px','auto'],
            skin: 'custom'
        });
    }).on('mouseleave', '.ylbtn', function () {
        layer.close(tipsVal);
    });

    //触发单元格工具事件
    table.on('tool(list)', function (obj) {
        var data = obj.data;
        switch (obj.event) {
            //编辑活动
            case 'statusChange':
                layer.confirm('是否要<em class="warm-color">' + (data.status == 1 ? '禁用' : '启用') + '</em>此商品售价组？', {
                    skin: 'layui-layer-admin',
                    title: '提示',
                    shade: .1,
                    resize: false,
                    offset: '250px',
                    move: false
                }, function (i) {
                    Request('/api/goodsSalePriceGroup/disable', 'POST', { sppe_id: data.sppe_id }, function (res) {
                        if (res.code == 0) {
                            layer.msg('操作成功', { shift: 0, time: 2000 }, function () {
                                layer.closeAll();
                                table.reloadData('list', {});
                            });
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                });
                break;
            //禁用活动
        }
    });

});
