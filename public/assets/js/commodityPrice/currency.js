layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate'], function () {
    var admin = layui.admin;
    var index = layui.index;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;

    window.IndexController = {
        init: function () {
            this.created(this).render(this).handleBind(this);
        },
        created: function () {


            return this;
        },
        render: function () {

            table.render({
                elem: '#list',
                url: '/api/price/getCurrencyConfigList',
                toolbar: '#toolbar',
                method: 'POST',
                cols: [[
                    { type: 'numbers', title: '序号', width: 50, align: 'center' },
                    {
                        field: 'supplier_code', title: '供应商编码', width: 130, align: 'center', templet(d) {
                            if (d.sup_type == 1) {
                                return d.supplier_id;
                            } else {
                                return d.supplier_code;
                            }
                        }
                    },
                    { field: 'supplier_name', title: '供应商', width: 200, align: 'center' },
                    { field: 'currency_cn', title: '原币种', width: 160, align: 'center' },
                    { field: 'is_tax_cn', title: '是否含税', width: 80, align: 'center' },
                    { field: 'customize_rate_rmb', title: '原币种转人民币汇率', width: 160, align: 'center' },
                    { field: 'customize_rate_usd', title: '原币种转美元汇率', width: 160, align: 'center' },
                    {
                        field: 'us_to_cn', title: '美金转人民币', width: 120, align: 'center', templet(d) {
                            if (d.supplier_id == 0 && d.customize_rate_rmb == '系统') {
                                return '<input type="checkbox" name="us_to_cn" value="' + d.id + '" lay-skin="switch" lay-text="开启|关闭" ' + (d.us_to_cn == 1 ? 'checked' : '') + ' lay-filter="switchUsToCn">';
                            }

                            return '';
                        }
                    },

                    {
                        field: 'status_cn', title: '状态', width: 120, align: 'center', templet(d) {
                            if (d.status == -1) {
                                return '<span style="color: red">' + d.status_cn + '</span>'
                            } else {
                                return d.status_cn;
                            }
                        }
                    },
                    { field: 'create_time', title: '创建时间', width: 180, align: 'center' },
                    { field: 'update_time', title: '最近一次修改时间', width: 180, align: 'center' },
                    { field: 'update_user', title: '最近一次操作人', width: 180, align: 'center' },
                    {
                        field: '', title: '操作', width: 80, align: 'center', fixed: 'right', templet(d) {
                            if (d.status == 1) {
                                return `<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disableCurrencyConfig">禁用</a>`;
                            } else {
                                return `<a class="layui-btn layui-btn-xs layui-btn-primary layui-border-red" lay-event="disableCurrencyConfig">启用</a>`;
                            }
                        }
                    }
                ]],
                parseData: LayUiTableParseData,
                done: function (res, curr, count) {
                    layui.form.render();
                }
            });

            //查询搜索
            form.on('submit(getList)', function (data) {
                table.reload('list', {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
            });

            // 监听美金转人民币开关
            form.on('switch(switchUsToCn)', function (obj) {
                var id = this.value;
                var status = obj.elem.checked ? 1 : 0;

                Request('/api/price/updateUsToCnStatus', 'POST', { id: id, status: status }, function (res) {
                    if (res.code == 0) {
                        layer.msg('操作成功', { shift: 0, time: 2000 });
                    } else {
                        // 如果失败，恢复开关状态
                        obj.elem.checked = !obj.elem.checked;
                        form.render();
                        layer.msg(res.msg);
                    }
                });
            });

            //监听头工具栏事件
            table.on('toolbar(list)', function (obj) {

                var checkStatus = table.checkStatus(obj.config.id);
                var data = checkStatus.data;

                switch (obj.event) {
                    //新增活动
                    case 'addCurrencyConfig':
                        layer.open({
                            type: 1,
                            title: '新增',
                            offset: '50px',
                            area: ['700px', 'auto'],
                            shadeClose: false,
                            resize: false,
                            move: false,
                            content: $('#addCurrencyConfigHtml').html(),
                            success: function (layero, dIndex) {
                                layero.find('.layui-layer-content').css('overflow', 'visible');
                                $(".hsboxik").hide()
                                layui.form.render();
                            }
                        });
                        break;
                }
            });

            //触发单元格工具事件
            table.on('tool(list)', function (obj) {
                var data = obj.data;
                switch (obj.event) {
                    //启用，禁用
                    case 'disableCurrencyConfig':
                        if (data.status == 1) {
                            //禁用提示
                            var html = '是否要<em class="layui-font-red">禁用</em>？';
                            var type = -1;
                        } else {
                            //启用提示
                            var html = '是否要<em class="layui-font-red">启用</em>？';
                            var type = 1;
                        }
                        layer.confirm(html, {
                            skin: 'layui-layer-admin',
                            title: '删除提示',
                            resize: false,
                            offset: '250px',
                            move: false
                        }, function (i) {
                            Request('/api/price/disableCurrencyConfig', 'POST', { id: data.id, type: type }, function (res) {
                                if (res.code == 0) {
                                    layer.msg('操作成功', { shift: 0, time: 2000 }, function () {
                                        layer.closeAll();
                                        table.reload('list');
                                    });
                                } else {
                                    layer.msg(res.msg);
                                }
                            });
                        });
                        break;
                }
            });

            return this;
        },
        handleBind: function () {


            layui.form.on('select(curencychange)', function (data) {
                if (data.value == 1) {
                    $(".hsboxik").show()
                    $("select[name='customize_rate_rmb']").val(2).attr("disabled", true)
                    $(".customize_rate_rmb_val").val(1).attr("disabled", true).show()
                } else {
                    $(".hsboxik").hide()
                    $("select[name='customize_rate_rmb']").removeAttr("disabled")
                    $(".customize_rate_rmb_val").removeAttr("disabled")
                }
                if (data.value == 2) {
                    $("select[name='customize_rate_usd']").val(2).attr("disabled", true)
                    $(".customize_rate_usd_val").val(1).attr("disabled", true).show()
                } else {
                    $("select[name='customize_rate_usd']").removeAttr("disabled")
                    $(".customize_rate_usd_val").removeAttr("disabled")
                }

                //判断是不是美金,如果是的话,则展示美金转人民币开关
                //同时供应商type=2
                if (data.value == 2 && $('input[name="sup_type"]').val() == 2) {
                    $(".us_to_cn").show()
                } else {
                    $(".us_to_cn").hide()
                }

                form.render()
            })
            layui.form.on('select(curencychange11)', function (data) {
                if (data.value == 2) {
                    $(".customize_rate_rmb_val").show()
                    $(".us_to_cn").hide()
                } else {
                    $(".customize_rate_rmb_val").hide()
                    $(".us_to_cn").show()
                }


            })
            layui.form.on('select(curencychange22)', function (data) {
                if (data.value == 2) {
                    $(".customize_rate_usd_val").show()
                } else {
                    $(".customize_rate_usd_val").hide()
                }
            })

            //新增供应商特殊币种配置
            layui.form.on('submit(addCurrencyConfigSubmit)', function (data) {
                if (data.field.currency == 1) {
                    if (data.field.is_tax != 1 && data.field.is_tax != 0) {
                        layer.msg("人民币时,是否含税必填")
                        return
                    }
                }
                if (data.field.customize_rate_rmb == 2 && (Number(data.field.customize_rate_rmb_val) <= 0)) {
                    layer.msg("原币转人民币汇率自定义时,输入值必须大于0")
                    return
                }
                if (data.field.customize_rate_usd == 2 && (Number(data.field.customize_rate_usd_val) <= 0)) {
                    layer.msg("原币转美元汇率自定义时,输入值必须大于0")
                    return
                }
                var obj_xk = {}
                obj_xk.supplier_value = data.field.supplier_value
                obj_xk.currency = data.field.currency
                obj_xk.is_tax = data.field.is_tax
                obj_xk.supplier_name = data.field.supplier_name
                obj_xk.sup_type = data.field.sup_type
                obj_xk.us_to_cn = data.field.us_to_cn
                if (data.field.customize_rate_rmb == 2) {
                    obj_xk.customize_rate_rmb = data.field.customize_rate_rmb_val
                }
                if (data.field.customize_rate_usd == 2) {
                    obj_xk.customize_rate_usd = data.field.customize_rate_usd_val
                }

                Request('/api/price/currencyConfig/addCurrencyConfig', 'POST', obj_xk, function (res) {
                    admin.btnLoading($(data.elem), '');
                    if (res.code == 0) {
                        layer.msg('操作成功', { shift: 0, time: 2000 }, function () {
                            layer.closeAll();
                            table.reload('list');
                        });
                    } else {
                        admin.btnLoading($(data.elem), false);
                        layer.msg(res.msg);
                    }
                });
            });


            //供应商监听
            layui.form.on('select(supplierChange)', function (data) {
                var supplier_name = $(data.elem.options[data.elem.selectedIndex]).attr('supplier_name');
                var sup_type = $(data.elem.options[data.elem.selectedIndex]).attr('sup_type');
                layui.form.val('addCurrencyConfigForm', {
                    supplier_name: supplier_name,
                    sup_type: sup_type
                });
                //如果sup_type=2,则展示美金转人民币开关
                //同时currency=2
                if (sup_type == 2 && $('select[name="currency"]').val() == 2) {
                    $(".us_to_cn").show()
                } else {
                    $(".us_to_cn").hide()
                }
            });

            return this;
        }
    }

    IndexController.init();

});
