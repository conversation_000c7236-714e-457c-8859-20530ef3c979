layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate', 'xmSelect'], function () {
    var admin = layui.admin;
    var index = layui.index;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;
    var xmSelect = layui.xmSelect;
    var salesChange = ''; //创建人

    function getSales(id, name, data, clickClose, layVerify, layVerType, empty) {
        var salesChange = xmSelect.render({
            el: id,
            radio: true,
            searchTips: '请输入搜索',
            model: {
                label: {
                    type: 'text'
                }
            },
            toolbar: {
                show: true,
                list: ['CLEAR']
            },
            layVerify: layVerify || '',
            layVerType: layVerType || '',
            filterable: true,
            data: [],
            name: name,
            clickClose: clickClose,
            on: function (res) {

            }
        })
        Request('/api/log/getUserList', 'GET', {}, function (res) {
            let arr = [];
            let off = res.data.off; //离职
            let on = res.data.on; //在职
            if (empty) {
                on.unshift({
                    name: '无业务员',
                    value: -1
                })
            }
            arr.push({
                name: "在职",
                children: on
                
            })
            arr.push({
                name: "离职",
                children: off
            })
            salesChange.update({
                data: arr,
                autoRow: true,
            })

            if (data && data.length > 0) {
                salesChange.setValue(data)
            }
        }, false);

        return salesChange;
    }
    window.IndexController = {
        init: function () {
            this.created(this).render(this).handleBind(this);
        },
        created: function () {

            //时间初始化
            laydate.render({
                elem: '#create_time',
                type: 'date',
                range: "~"
            });

            salesChange = getSales('#operator', 'operator_id');

            return this;
        },
        render: function () {


            table.render({
                elem: '#list',
                url: '/api/log/list',
                method: 'POST',
                cols: [[
                    { type: 'numbers', title: '序号', fixed: true },
                    { field: 'act_type_val', title: '功能名称', width: 150 },
                    { field: 'create_name', title: '操作人', width: 150 },
                    { field: 'create_time', title: '操作时间', width: 150 },
                    { field: 'obj_id', title: '单据ID', width: 150 },
                    { field: 'content', title: '操作说明', width: 150 },
                    { field: 'log_data', title: '日志原始数据' }
                ]],
                done: function (res, curr, count) {
                    layui.form.render();
                }
            });
            //查询搜索
            form.on('submit(getList)', function (data) {
                table.reload('list', {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
            });


            return this;
        },
        handleBind: function () {
            //监听重置页面
            $(document).on('click', ':reset', function () {
                salesChange.setValue([]);
                $("input[name='creator_id']").val('');
            });
            return this;
        }
    }

    IndexController.init();

});