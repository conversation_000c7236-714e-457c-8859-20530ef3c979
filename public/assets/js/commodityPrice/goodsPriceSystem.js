layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate', 'xmSelect', 'soulTable'], function () {
  var admin = layui.admin;
  var index = layui.index;
  var form = layui.form;
  var table = layui.table;
  var laydate = layui.laydate;
  var xmSelect = layui.xmSelect;
  var soulTable = layui.soulTable;

  window.IndexController = {
    init: function () {
      this.created(this).render(this).handleBind(this);
    },
    created: function () {

      $(".goods_namefg").on("input",function(e){
        $(".skuidsp").attr("disabled","true").css("background","#f3f3f3")
      });
      $(".skuidsp").on("input",function(e){
        $(".goods_namefg").attr("disabled","true").css("background","#f3f3f3")
        $("#brandSelect").addClass("dis")
      });


      $(".resets").click(function(){
        $(".skuidsp").removeAttr("disabled").css("background","#fff")
        $(".goods_namefg").removeAttr("disabled").css("background","#fff")
        $("#brandSelect").removeClass("dis")
      })
      //渲染多选
      let brandSelect = xmSelect.render({
        el: '#brandSelect',
        name: 'brand_id',
        searchTips: '请输入要查找的制造商',
        paging: true,
        empty: '没有查找到数据',
        prop: {
          name: 'brand_name',
          value: 'brand_id'
        },
        height: "400px",
        remoteSearch: true,
        autoRow: false,
        pageRemote: true,
        filterable: true,
        remoteMethod: function (val, cb, show, pageIndex) {
          //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
          $.ajax({
            url: '/api/commonData/searchStandardBrand',
            type: 'post',
            data: {
              brand_ids: $('#brand_ids').val(),
              brand_name: val,
              page: pageIndex
            },
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
              if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
              if (res.errcode === 0) {
                cb(res.data, res.last_page);
              } else {
                layer.msg(res.errmsg, { icon: 6 });
              }
            },
            error: function () {
              return layer.msg('网络错误，请重试', { icon: 5 });
            }
          });
        },
        on: function (data) {

          if(data.arr.length>0){
            $(".skuidsp").attr("disabled","true").css("background","#f3f3f3")
          }
          // let brandIds = '';
          // for (let x in data.arr)  // x 为属性名
          // {
          //     brandIds = brandIds + data.arr[x].brand_id + ',';
          // }
          // $("#brand_id_condition").val(brandIds);
        }
      });
      return this;
    },
    /**
     * 页面渲染
     */
    render: function () {
      table.render({
        elem: '#list',
        url: '/api/goodsPriceSystem/list',
        method: 'GET',
        lineStyle: 'height: auto;',
        toolbar: '#toolbar',
        cols: [[
          { field: 'SKUID', title: 'SKUID', width: 160,  children: [
            {
              title: '价格展示'
              , lineStyle: 'height: auto;'
              , data: function (d) {
                // d 为当前行数据
                console.log(d)
                return [d];
              }
              , page: false
              , cols: [[
                {
                  field: 'title', title: '阶梯', width: 100, align: 'center', templet(d) {
                    var arr_ = Object.keys((d.ladder_price || {}));
                    var html_ = ''
                    arr_.forEach(function (i) {
                      html_ += '<div>' + d.ladder_price[i] + '</div>'
                    })
                    return '<div class="column">' + html_ + '</div>'

                  }
                },
                {
                  field: 'title', title: '原始价', width: 200, align: 'center', templet(d) {
                    var arr_ = d.origin_currency_symbol || [];
                    var html_ = ''
                    console.log(arr_)
                    for (var i = 0; i < arr_.length; i++) {
                      html_ += '<div class="row"><span>' + arr_[i].cny + "</span>" + "<span>" + arr_[i].usd + '</span></div>'
                    }

                    return '<div class="column priceboxs">' + html_ + '</div>'

                  }
                },
                {
                  field: 'title', title: '官方价', width: 200, align: 'center', templet(d) {
                    var arr_ = d.official_ladder_price || [];
                    var html_ = ''
                    console.log(arr_)
                    for (var i = 0; i < arr_.length; i++) {
                      html_ += '<div class="row"><span>' + arr_[i].cny + "</span>" + "<span>" + arr_[i].usd + '</span></div>'
                    }

                    return '<div class="column priceboxs">' + html_ + '</div>'

                  }
                },
                {
                  field: 'title', title: '成本价', width: 200, align: 'center', templet(d) {
                    var arr_ = d.cost_ladder_price || [];
                    var html_ = ''
                    console.log(arr_)
                    for (var i = 0; i < arr_.length; i++) {
                      html_ += '<div class="row"><span>' + arr_[i].cny + "</span>" + "<span>" + arr_[i].usd + '</span></div>'
                    }

                    return '<div class="column priceboxs">' + html_ + '</div>'

                  }
                },
                {
                  field: 'title', title: '销售利润', width: 200, align: 'center', templet(d) {
                    var arr_ = d.sale_ladder_profit || [];
                    var html_ = ''
                    console.log(arr_)

                    if(d.org_id == 1){
                      for (var i = 0; i < arr_.length; i++) {
                        html_ += '<div class="row"><span>' + arr_[i].cny + "</span>" + "<span>" + arr_[i].usd + '</span></div>'
                      }
                    }else{
                      html_ = '<div class="row"><span>' + arr_[arr_.length-1].cny + "</span>" + '</div>'
                    }

                    return '<div class="column priceboxs">' + html_ + '</div>'

                  }
                },
                {
                  field: 'title', title: '销售价', width: 200, align: 'center', templet(d) {
                    var arr_ = d.sale_ladder_price || [];
                    var html_ = ''
                    console.log(arr_)
                    for (var i = 0; i < arr_.length; i++) {
                      html_ += '<div class="row"><span>' + arr_[i].cny + "</span>" + "<span>" + arr_[i].usd + '</span></div>'
                    }

                    return '<div class="column priceboxs">' + html_ + '</div>'

                  }
                },
                {
                  field: 'title', title: '活动价', width: 200, align: 'center', templet(d) {
                    var arr_ = d.actice_ladder_price || [];
                    var html_ = ''
                    console.log(arr_)
                    for (var i = 0; i < arr_.length; i++) {
                      html_ += '<div class="row"><span>' + arr_[i].cny + "</span>" + "<span>" + arr_[i].usd + '</span></div>'
                    }

                    return '<div class="column priceboxs">' + html_ + '</div>'

                  }
                },
              ]]
              , done: function () {
                soulTable.render(this);
              }
            }

          ] },
          { field: 'goods_name', title: '型号', width: 180, align: 'center' },
          { field: 'stand_brand_name', title: '标准品牌', width: 180, align: 'center' },
          { field: 'supplier_name', title: '供应商', width: 160, align: 'center' },
          { field: 'ECCN', title: 'ECCN', align: 'center' },
//          {
//            field: 'channel_discount', title: '渠道折扣', width: 220, align: 'center', templet(d) {
//              return d.channel_discount.ratio_cny + " " + d.channel_discount.ratio_usd
//            }
//          },
        {
                    field: 'channel_discount', title: '人民币渠道折扣',width: 120,  align: 'center', templet(d) {
                      return d.channel_discount.ratio_cny
                    }
           },
           {
                   field: 'channel_discount', title: '美金渠道折扣',width: 120,  align: 'center', templet(d) {
                     return d.channel_discount.ratio_usd
                   }
          },


          {
            field: 'active_ratio', title: '活动折扣', width: 220, align: 'center', templet(d) {
              return d.active_ratio
            }
          },
          {
            field: 'goodsPriceGroups', title: '所在售价组', width: 160, align: 'center', templet(d) {
              return d.goodsPriceGroups
            }
          },
          {
            field: 'joinActivePrice', title: '参与活动价', width: 160, align: 'center', templet(d) {
              return d.joinActivePrice
            }
          },

        ]],
        parseData: LayUiTableParseData,
        done: function (res, curr, count) {
          soulTable.render(this)
        }
      });
      $("body").on("click", ".layui-table-main tr", function (e) {
        if(!$(e.target).hasClass("alink")){
          $(this).find(".childTable.layui-icon").click()
        }
      })
      $("body").on("click", ".layui-table-main .layui-form-checkbox", function (e) {
        e.stopPropagation()
      })

      $("body").on("click", ".stoppop", function (e) {
        e.stopPropagation()
      })
      //查询搜索
      form.on('submit(load)', function (data) {
        table.reload('list', {
          where: data.field,
          page: {
            curr: 1
          }
        });
      });

      return this;
    },
    /**
     * 事件绑定
     */
    handleBind: function () {


      return this;
    }
  }

  IndexController.init();

});
