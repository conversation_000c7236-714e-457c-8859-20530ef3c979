layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate'], function () {
    var admin = layui.admin;
    var index = layui.index;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;

    var channel_disct_id=getRequest("channel_disct_id")||""

    //复制新增
    if(channel_disct_id){
        console.log(channel_disct_id)
        Request('/api/goodsSalePriceGroup/getGoodsSalePriceGroupInfo', 'GET', {channel_disct_id:channel_disct_id}, function (res) {
            if (res.code == 0) {
                var data_=res.data;
               console.log(data_)
               supplier_value=data_.supplier_code||data_.supplier_id||"";
               sup_type=data_.sup_type;
               supplier_name=data_.supplier_name;
               $(".supplier_value").val(supplier_value)
               $(".is_default").val(data_.is_default)
               $('#exclude_sku_ids').val(data_.goods_name);
               $("#exclude_sku_ids_file_url").val(data_.file_url)
               if(data_.file_url){
                $("#exclude_sku_ids_file_url_href").show().attr("href",data_.file_url)
               }
               $('.eccnigs').val(data_.eccn);
               $("#valid_exclude_standard_brand_ids_name_list").text(data_.brand)
               $("#exclude_standard_brand_ids_name_list").val(data_.brand)
               $("#exclude_standard_brand_ids").val(data_.brand_ids)
               $("#zdlrds").val(data_.mini_profit_ladder)
               form.render();

               //渲染价格阶梯
                var price_arr=JSON.parse(data_.step_price_data_json);
                Object.keys(price_arr).forEach(function(ele){
                    var tr_=$($(".piicecpbody tr")[Number(ele)-1])
                    tr_.find(".rpxval1").val(price_arr[ele].ladder_price_egt50_lt200||1)
                    tr_.find(".rpxval2").val(price_arr[ele].ladder_price_egt200||1)
                    tr_.find(".rpxval3").val(price_arr[ele].ratio||1)
                    tr_.find(".rpxval4").val(price_arr[ele].ratio_usd||1)
                })
               uiRender()
            } else {
                layer.msg(res.msg);
            }
        });
        
    }



});

