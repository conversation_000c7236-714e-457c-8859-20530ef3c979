layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate'], function () {
    var admin = layui.admin;
    var index = layui.index;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;

    //时间初始化
    laydate.render({
        elem: '#warn_time',
        type: 'date',
        range: "~"
    });

    table.render({
        elem: '#list',
        url: '/api/priceWarning/list',
        toolbar: '#toolbar',
        method: 'POST',
        lineStyle: 'height: auto;',
        cols: [[
            { type: 'checkbox', width: 50 },
            { field: 'price_activity_id', title: '活动id', width: 80, align: 'center' },
            {
                field: 'goodsPriceGroups', title: '活动名称', width: 120, align: 'center', templet(d) {
                    return d.activity_name_href
                }
            },
            {
                field: 'goodsPriceGroups', title: '售价组', width: 160, align: 'center', templet(d) {
                    return d.sppe_sn_href
                }
            },
            { field: 'supplier_name', title: '供应商名称', width: 150, align: 'center' },
            { field: 'goods_name', title: '参与型号', width: 160, align: 'center' , templet(d) {
                if (d.goods_name) {
                    return '<span class="alink ylbtn" html_="'+d.goods_name+'">预览</span>'
                } else {
                    return ''
                }
            }},
            { field: 'brand', title: '参与品牌', width: 160, align: 'center' },
            { field: 'eccn', title: '参与ECCN', width: 160, align: 'center' },
            {
                field: '', title: '是否默认', width: 70, align: 'center', templet(d) {
                    if (d.is_default == 1) {
                        return '<span class="warm-color">是</span>'
                    } else {
                        return '<span >否</span>'
                    }
                }
            },

            {
                field: '', title: '销售利润<span class="jgsq">收起</span>', width: 250, align: 'center', templet(d) {
                    var html_ = '', arr_ = d.saleProfit || [];
                    for (var i = 0; i < arr_.length; i++) {
                        html_ += '<div class="row"><span style="color:red;">' + arr_[i].order + '：</span><span>￥' + arr_[i].ratio + '%</span><span>$' + arr_[i].ratio_usd + '%</span></div>'
                    }
                    return '<div class="priceboxs">' + html_ + '</div>'

                }
            },
            {
                field: '', title: '预估盈利率<span class="jgsq">收起</span>', width: 250, align: 'center', templet(d) {
                    var html_ = '', arr_ = d.estimateProfit || [];
                    for (var i = 0; i < arr_.length; i++) {
                        html_ += '<div class="row"><span style="color:red;">' + arr_[i].order + '：</span><span>￥' + arr_[i].ratio + '%</span><span>$' + arr_[i].ratio_usd + '%</span></div>'
                    }
                    return '<div class="priceboxs">' + html_ + '</div>'

                }
            },
            { field: 'status_format', title: '状态', width: 50, align: 'center' },
            { field: 'remark', title: '备注', width: 120, align: 'center' },
            { field: 'warn_time', title: '预警时间', width: 150, align: 'center' },
            { field: 'hander', title: '处理人', width: 100, align: 'center' },
            { field: 'hand_time', title: '处理时间', width: 150, align: 'center' },
            {
                field: '', title: '操作', width: 160, align: 'center', templet(d) {
                    if (d.status == 1) {
                        return '<a class="layui-btn layui-btn-xs" lay-event="yzreset">重新验证</a>'
                    } else {
                        return ''
                    }

                }
            }
        ]],
        parseData: LayUiTableParseData,
        done: function (res, curr, count) {
            layui.form.render();
        }
    });
    $("body").on("click", ".jgsq", function () {
        if ($(this).text() == "收起") {
            $(this).text("展开");
            $(".priceboxs").addClass("h360")
        } else {
            $(this).text("收起");
            $(".priceboxs").removeClass("h360")
        }
    })
    //查询搜索
    form.on('submit(load)', function (data) {
        table.reload('list', {
            where: data.field,
            page: {
                curr: 1
            }
        });
    });
    var tipsVal = '';
    $(document).on('mouseenter', '.ylbtn', function () {
        var self = this;
        var html_='<div style="word-break: break-all;">'+$(this).attr("html_")+'</div>'
        tipsVal = layer.tips(html_, self, {
            tips: [3, "#009688"],
            time: 1000000,
            area: ['400px','auto'],
            skin: 'custom'
        });
    }).on('mouseleave', '.ylbtn', function () {
        layer.close(tipsVal);
    });

    table.on('toolbar(list)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;
        switch (obj.event) {
            case 'plcl':
                if (data.length == 0) {
                    layer.msg('请勾选至少一条数据');
                    return false;
                }
                var ids = []
                for (var i = 0; i < data.length; i++) {
                    ids.push(data[i].price_warning_id)
                }
                layer.open({
                    type: 1,
                    title: '批量标记',
                    offset: '50px',
                    area: ['450px', 'auto'],
                    shadeClose: false,
                    resize: false,
                    move: false,
                    content: $('#testPop').html(),
                    success: function (layero, dIndex) {
                        layero.find('.layui-layer-content').css('overflow', 'visible');
                        layui.form.on('submit(testPopgo)', function (data) {
                            var obj_ = data.field;
                            obj_.price_warning_id = ids.join(",")
                            Request('/api/priceWarning/dealwith', 'POST', data.field, function (res) {
                                if (res.code == 0) {
                                    layer.msg('操作成功', { shift: 0, time: 2000 }, function () {
                                        layer.closeAll();
                                        table.reload('list', {});
                                    });
                                } else {
                                    layer.msg(res.msg);
                                }
                            });
                        });
                        layui.form.render();
                    }
                });
                break;
        }
    });
    //触发单元格工具事件
    table.on('tool(list)', function (obj) {
        var data = obj.data;
        switch (obj.event) {
            //编辑活动
            case 'yzreset':
                layer.confirm('是否要重新验证？', {
                    skin: 'layui-layer-admin',
                    title: '提示',
                    shade: .1,
                    resize: false,
                    offset: '250px',
                    move: false
                }, function (i) {
                    Request('/api/priceWarning/recertify', 'POST', { price_warning_id: data.price_warning_id }, function (res) {
                        if (res.code == 0) {
                            layer.msg('操作成功', { shift: 0, time: 2000 }, function () {
                                layer.closeAll();
                                table.reloadData('list', {});
                            });
                        } else {
                            layer.msg(res.msg);
                        }
                    });
                });
                break;
            //禁用活动
        }
    });

});
