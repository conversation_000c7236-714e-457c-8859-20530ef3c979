layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate'], function () {
    var admin = layui.admin;
    var index = layui.index;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;
    var supplier_name=$(".supplier_value").find("option:selected").attr("supplier_name")||"";//渠道/供应商
    var supplier_value=$(".supplier_value").val()||"";//编码 ID 
    var sup_type=$(".supplier_value").find("option:selected").attr("sup_type")||"";//代购 1 专营 2
    var channel_disct_id=getRequest("channel_disct_id")||""
    var order_=""
     //复制新增
     if(channel_disct_id){
        console.log(channel_disct_id)
        Request('/api/channelDiscount/getChannelDidInfo', 'GET', {channel_disct_id:channel_disct_id}, function (res) {
            if (res.code == 0) {
                var data_=res.data;
               console.log(data_)
               supplier_value=data_.supplier_code||data_.supplier_id||"";
               sup_type=data_.sup_type;
               supplier_name=data_.supplier_name;
               $(".supplier_value").val(supplier_value)
               $(".is_default").val(data_.is_default)
               $(".ration").val(Number(data_.ration))
               $(".ration_usd").val(Number(data_.ration_usd))
               $(".remark").val(data_.remark)
               order_=data_.order
               $('#exclude_sku_ids').val(data_.goods_name);
               $("#exclude_sku_ids_file_url").val(data_.file_url)
               if(data_.file_url){
                $("#exclude_sku_ids_file_url_href").show().attr("href",data_.file_url)
               }
               
               $("#valid_exclude_standard_brand_ids_name_list").text(data_.brand)
               $("#exclude_standard_brand_ids_name_list").val(data_.brand)
               $("#exclude_standard_brand_ids").val(data_.brand_ids)
               form.render();
               $(".inuqdinput").each(function(){
                var num_=Number($(this).val())
                $(this).next().next("div").find("font").text(((100-num_)).toFixed(2)+'%')
                })
               if(data_.is_default==1){
                $(".cybbox").hide()
                $(".order_bysort").hide()
                }else{
                    $(".cybbox").show()
                    $(".order_bysort").show()
                }
               getData()
            } else {
                layer.msg(res.msg);
            }
        });
        
    }


    $(document).ready(function () {
        layui.upload.render({
            elem: '#uploadExcludeSkuIds',
            url: oss_url + '/uploadFile?sys_type=7',
            accept: 'file',
            exts: 'csv',
            before: function () {
                layer.load(2);
            },
            choose: function (obj) {
                //将每次选择的文件追加到文件队列
                obj.preview(function (index, file, result) {
                    console.log(file)
                    // if (file.type === 'text/csv') {
                       
                    // }
                    var reader = new FileReader();
                    reader.readAsText(file);
                    reader.onload = function () {
                        var csvData = reader.result;
                        let data = $.csv.toArrays(csvData);
                        console.log(data)
                        let excludeSkuIds = [];
                        $.each(data, function (index, value) {
                            if (index !== 0) {
                                excludeSkuIds = $.merge(excludeSkuIds, value);
                            }
                        });
                        excludeSkuIds = excludeSkuIds ? excludeSkuIds.join('@€@') : '';
                        console.log(excludeSkuIds)
                        $('#exclude_sku_ids').val(excludeSkuIds);
                    }
                });
            },
            done: function (res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    let fileUrl = res.data.oss_file_url;
                    $('#exclude_sku_ids_file_url').val(fileUrl);
                    $('#exclude_sku_ids_file_url_href').show();
                    $('#exclude_sku_ids_file_url_href').attr('href', fileUrl);
                    $('#uploadExcludeSkuIds').text('重新上传');
                } else {
                    layer.msg(res.msg);
                }
            },
            error: function (index, upload) {
                layer.closeAll('loading');
                layer.msg('网络出现问题，请重试！');
            }
        });
    });


    form.on("select(supplier_value)",function(data){
        var ele_=$(data.elem).find("option:selected")
        sup_type=ele_.attr("sup_type")
        supplier_name=ele_.attr("supplier_name")
        supplier_value=ele_.val()
        getData()
    }) 
    form.on("select(is_default)",function(data){
        if(data.value==1){
            $(".cybbox").hide()
            $(".order_bysort").hide()
          }else{
            $(".cybbox").show()
            $(".order_bysort").show()
          }
        
    })   
   
    function getData(){
        getSorts()
        table.render({
            elem: '#list',
            url: '/api/channelDiscount/getRelationChanDis',
            method: 'POST',
            where:{
                supplier_value: supplier_value,
                sup_type:sup_type
            },
            cols: [[
                { field: 'channel_disct_sn', title: '渠道折扣编码',  align: 'center', templet(d) {
                  
                    return '<a class="alink" ew-href="/web/price/getChannelDiscountPage?channel_disct_id='+d.channel_disct_id+'" ew-title="'+d.channel_disct_sn+'详情">'+d.channel_disct_sn+'</a>'
                } },
                {
                    field: '', title: '是否默认',  align: 'center', templet(d) {
                        if (d.is_default == 1) {
                            return '<span>是</span>'
                        } else {
                            return '<span class="warm-color">否</span>'
                        }
                    }
                },
                { field: 'order', title: '优先级',  align: 'center' },
                { field: 'ration_format', title: '人民币渠道折扣',  align: 'center' },
                { field: 'ration_usd_format', title: '美金渠道折扣',  align: 'center' },
                { field: 'goods_name', title: '参与型号',  align: 'center'},
                { field: 'brand', title: '参与品牌',  align: 'center' },
          
                {
                    field: '', title: '状态',  align: 'center', templet(d) {
                        if (d.status  == 1) {
                            return '<span>启用</span>'
                        } else {
                            return '<span class="warm-color">禁用</span>'
                        }
                    }
                }
            ]],
            parseData: LayUiTableParseData,
            done: function (res, curr, count) {
                layui.form.render();
            }
        });
    }
    function getSorts(){
        Request('/api/channelDiscount/getPriorityLevel', 'POST', { supplier_value: supplier_value,sup_type:sup_type,order:order_}, function (res) {
            if (res.code == 0) {
                layer.closeAll();
                var html_='<option value="">请选择</option>'
                var arr_=res.data||[];
                for(var i=0;i<arr_.length;i++){
                    html_+='<option value="'+arr_[i]+'">'+arr_[i]+'</option>'
                }
                $(".order_bysort select").html(html_)
                if(order_!=""){
                    $(".order_bysort select").val(order_)
                }
                form.render()
            } else {
                layer.msg(res.msg);
            }
        });
    }
    
    
    $('body').on('input propertychange','.inuqdinput', debounce(function (e) {
        var num_=Number($(this).val())
        $(this).next().next("div").find("font").text(((100-num_)).toFixed(2)+'%')
        
    }, 300))

    $(".savebtn").click(function(){
        console.log($(".supplier_value").val())
        if(!$(".supplier_value").val()){
            layer.msg("请选择渠道/供应商")
            return
        }
        if($(".is_default").val()!=1){
            if(!$(".order_bysort select").val()){
                layer.msg("请选择优先级")
                return
            }
        }
        if(!$(".ration").val()){
            layer.msg("请输入人民币渠道折扣")
            return
        }
        if(!$(".ration_usd").val()){
            layer.msg("请输入美金渠道折扣")
            return
        }
     
        var data_={
            channel_disct_id:channel_disct_id,
            sup_type:$(".supplier_value").find("option:selected").attr("sup_type"),
            supplier_value:$(".supplier_value").val(),
            supplier_name:$(".supplier_value").find("option:selected").attr("supplier_name"),
            is_default:$(".is_default").val(),
            order:$(".order_bysort select").val(),
            brand:$("#valid_exclude_standard_brand_ids_name_list").text(),
            brand_ids:  $("#exclude_standard_brand_ids").val(),
            goods_name:$("#exclude_sku_ids").val(),
            file_url:$("#exclude_sku_ids_file_url").val(),
            remark:$(".remark").val(),
            ration_usd:$(".ration_usd").val(),
            ration:$(".ration").val()
            
        }
        Request('/api/channelDiscount/update', 'POST', data_, function (res) {
            if (res.code == 0) {
                layer.closeAll();
                layer.msg('修改成功', {shift: 0, time: 2000}, function () {
                    closeCurrentPageJumpOne('渠道折扣管理', '/web/price/channelDiscount', 1000)
                  });
                
            } else {
                layer.msg(res.msg);
            }
        });
     
    })


 
    //输入框不能输入负数


    $("body").on("input propertychange", ".inuqdinput", function (event) {

        // if (Number($(this).val() )<= 0) {
        // $(this).val(0)
        // }
         var value  = $(this).val()
        var nStrList = value.toString().split(".");
        console.log(nStrList);
        var result = nStrList.length > 1 ? nStrList[1].length : 0;
        if(result > 2){
        console.log(value);
        console.log(parseFloat(value).toFixed(2));
            $(this).val(parseFloat(value).toFixed(2))
        }

        if (Number($(this).val() )>= 100) {
            $(this).val(100)
        }
    
    })
});

