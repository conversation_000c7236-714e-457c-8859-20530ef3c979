layui.config({
    version: true,
    closeFooter: true,
    tabAutoRefresh: false,
    base: getProjectUrl() + 'assets/module/'
}).extend({
    zTree: 'zTree/zTree',
    soulTable: 'soulTable/soulTable',
    tableChild: 'soulTable/tableChild',
    tableMerge: 'soulTable/tableMerge',
    tableFilter: 'soulTable/tableFilter',
    excel: 'soulTable/excel'
}).use(['layer', 'admin', 'index', 'table', 'laydate', 'form', 'util'], function () {
    var $ = layui.jquery;
    var index = layui.index;
    var admin = layui.admin;
    var table = layui.table;
    var laydate = layui.laydate;
    var form = layui.form;
    var util = layui.util;

    laydate.set({
        rangeLinked: true
    })
//关闭当前页面 跳到指定页面
    window.closeCurrentPageJumpOne = function (titlex, urlx, refresh, time) {
        //不传时间 为0
        var time = time ? time : 0;
        setTimeout(function () {
            index.openTab({
                title: titlex,
                url: urlx
            });
            if (refresh) {
                admin.refresh(urlx);
            }
        }, time)
        setTimeout(function () {
            index.closeTab(window.location.pathname + window.location.search);
        }, (time + 500))
    }
    //正则验证
    form.verify({
        mobile: [
            /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/,
            '请输入正确的手机号'
        ],
        noNumber: function (value, item) {
            if (!isNaN(value)) {
                return '不可以输入纯数字'
            }
        }
    })

    //全局配置table
    table.set({
        page: true,
        size: 'sm',
        cellMinWidth: 80,
        defaultToolbar: ['filter'],
        limit: 15,
        limits: [15, 50, 100, 200]
    });


    //关闭当前页面 跳到指定页面
    window.closeCurrentPageJumpOne = function (titlex, urlx, refresh, time) {
        //不传时间 为0
        var time = time ? time : 0;
        setTimeout(function () {
            index.openTab({
                title: titlex,
                url: urlx
            });
            if (refresh) {
                admin.refresh(urlx);
            }
        }, time)
        setTimeout(function () {
            index.closeTab(window.location.pathname + window.location.search);
        }, (time + 500))
    }

});

/**
 * 获取当前项目的根路径，通过获取layui.js全路径截取assets之前的地址
 * */
function getProjectUrl() {
    var layuiDir = layui.cache.dir;
    if (!layuiDir) {
        var js = document.scripts,
            last = js.length - 1,
            src;
        for (var i = last; i > 0; i--) {
            if (js[i].readyState === 'interactive') {
                src = js[i].src;
                break;
            }
        }
        var jsPath = src || js[last].src;
        layuiDir = jsPath.substring(0, jsPath.lastIndexOf('/') + 1);
    }
    return layuiDir.substring(0, layuiDir.indexOf('assets'));
}

/**
 *  获取参数
 * @param variable
 * @returns {string}
 */
function getQueryVariable(variable) {
    let query = window.location.search.substring(1);
    let vars = query.split("&");
    for (let i = 0; i < vars.length; i++) {
        let pair = vars[i].split("=");
        if (pair[0] == variable) {
            return pair[1];
        }
    }
    return '';
}

/**
 * 获取地址栏参数
 * @param value
 * @returns {*}
 */
function getRequest(value) {
    var url = decodeURI(location.search);
    var object = {};
    if (url.indexOf("?") != -1) {
        var str = url.substr(1);
        var strs = str.split("&");
        for (var i = 0; i < strs.length; i++) {
            object[strs[i].split("=")[0]] = strs[i].split("=")[1]
        }
    }
    return object[value];
}

/**
 * 判断数组内元素是否全相同
 * @param array
 * @returns {boolean}
 */
function isAllEqual(array) {
    if (array.length > 0) {
        return !array.some(function (value, index) {
            return value !== array[0];
        });
    } else {
        return true;
    }
}

/**
 *
 * @param url
 * @param type
 * @param param
 * @param callback
 * @param isload
 * @constructor
 */
function Request(url, type, param, callback, isload) {
    if (isload == undefined) {
        var index = layer.load(2);
    }

    //设置携带cookie
    $.ajaxSetup({
        xhrFields: {
            withCredentials: true
        }
    });

    var params = $.extend({}, param);

    $.ajax({
        type: type,
        url: url,
        data: params,
        xhrFields: {
            withCredentials: true
        },
        timeout: 30000,
        success: function (data) {
            typeof callback == 'function' && callback(data);
            if (isload == undefined) {
                layer.close(index);
            }
        },
        error: function () {
            layer.closeAll('loading');
            layer.msg('网络出现问题，请重试！');
        }
    });
}

/**
 *
 * @param res
 * @returns {{msg: string, code: number, data: Array, count: number}|{msg: string, code: *, data: *, count: number}}
 * @constructor
 */
function LayUiTableParseData(res) {
    if (res.code == 0) {
        return {
            code: res.code,
            msg: "",
            count: res.data.total ? res.data.total : 0,
            data: res.data.list
        }
    } else {
        return {
            code: 0,
            msg: "",
            count: 0,
            data: []
        }
    }
}

/**
 * 判断数组中是否包含某个值
 * @param arr 数组
 * @param str 值
 * @returns {boolean}
 */
function contains(arr, str) {
    var i = arr.length;
    while (i--) {
        if (arr[i] == str) {
            return true;
        }
    }
    return false;
}

/**
 * 防抖函数
 * @param fn
 * @param delay
 * @returns {Function}
 */
function debounce(fn, delay) {

    // 定时器，用来 setTimeout
    var timer

    // 返回一个函数，这个函数会在一个时间区间结束后的 delay 毫秒时执行 fn 函数
    return function () {

        // 保存函数调用时的上下文和参数，传递给 fn
        var context = this
        var args = arguments

        // 每次这个返回的函数被调用，就清除定时器，以保证不执行 fn
        clearTimeout(timer)

        // 当返回的函数被最后一次调用后（也就是用户停止了某个连续的操作），
        // 再过 delay 毫秒就执行 fn
        timer = setTimeout(function () {
            fn.apply(context, args)
        }, delay)
    }
}

/**
 * 生成唯一的uuid
 * @returns {string}
 */
function uuid() {
    var temp_url = URL.createObjectURL(new Blob());
    var uuid = temp_url.toString();
    URL.revokeObjectURL(temp_url);
    return uuid.substr(uuid.lastIndexOf("/") + 1);
}

/**
 * 跳转地址
 * @param url
 */
function toUrl(url) {
    var htmlArr = [];
    $("#form").remove();
    htmlArr.push('<form action="' + url + '" method="post" id="form" style="display: none">' +
        '                    <input type="submit" value="提交">' +
        '                </form>');
    $("body").append(htmlArr.join(''));
    $("#form").attr('target', '_blank');
    $("#form").submit();
}

//数组去重
function array_unique(arr) {
    var res = [];
    for (var i = 0, len = arr.length; i < len; i++) {
        var item = arr[i];
        for (var j = 0, jLen = res.length; j < jLen; j++) {
            if (item == res[j]) break;
        }
        if (j == jLen) res.push(item);
    }
    return res;
}

function array_remove_empty(arr) {
    for (var i = 0; i < arr.length; i++) {
        if (arr[i] == "" || typeof (arr[i]) == "undefined") {
            arr.splice(i, 1);
            i = i - 1; // i - 1 ,因为空元素在数组下标 2 位置，删除空之后，后面的元素要向前补位
        }
    }
    return arr;
}

//不要删这个方法
function ajax(url, data) {
    layer.msg('加载中', {
        icon: 16
        , shade: 0.01
    });
    var result = false;
    $.ajax({
        url: url,
        type: 'post',
        data: data,
        async: false,
        dataType: 'json',
        timeout: 20000,
        success: function (resp) {
            if (resp) {
                result = resp;
            }
        }
    })
    layer.closeAll();
    return result;
}

function Prompt(content, icon) {
    layui.use('layer', function () {
        var layer = layui.layer;
        parent.layer.msg(content, {icon: icon});
    });
}

/**
 * 监控并显示input输入框 输入的字符数量
 * @param input_id      input输入框的id
 * @param length_id     显示输入数量文本dom id
 * @param limit         可输入最长数量限制
 * 注：建议在js立即执行函数中调用
 */
function limitAndShowInputLength(input_id, length_id, limit) {
    var len = $('#' + input_id).val().length;
    $('#' + length_id).html(len + '/' + limit);
    $('#' + input_id).bind('input propertychange', function () {
        var text = $(this).val();
        var len = $(this).val().length;
        if (len > limit - 1) {
            var lenText = text.substring(0, limit - 1);
            $(this).val(lenText);
        }
        $('#' + length_id).html(len + '/' + limit);
    });
}


function isNumeric(str) {
    return /^\d+$/.test(str);
}
