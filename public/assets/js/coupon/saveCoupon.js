layui.use(['table', 'form', 'laydate', 'xmSelect', 'admin', 'index'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;

    //组织id,比较核心
    let orgId = $('input[name="org_id"]:checked').val();

    let isIEdge = (orgId === '3' || orgId === '6');

    let commonXmSelect = layui.xmSelect;
    let laydate = layui.laydate;
    //判断是新增还是修改

    const currencySymbolMap = {
        3: "¥",
        6: "$"
    }

    //时间选择器
    laydate.render({
        elem: 'input[name=start_time]',
        type: 'datetime'
    });
    laydate.render({
        elem: 'input[name=end_time]',
        type: 'datetime'
    });
    laydate.render({
        elem: 'input[name=reg_start_time]',
        type: 'datetime'
    });
    laydate.render({
        elem: 'input[name=register_end_time]',
        type: 'datetime'
    });
    laydate.render({
        elem: 'input[name=effect_start_time]',
        type: 'datetime'
    });
    laydate.render({
        elem: 'input[name=effect_end_time]',
        type: 'datetime'
    });


    if (isIEdge) {
        $('input[type="radio"][name="coupon_type"]:eq(1)').next().hide();
        $('#iedge_org_select_div').show();
        $("body").on('click', function () {

        });
    }


    let currencySymbol = '¥';
    //判断显示币种符号
    form.on('select(iedge_org_id_select)', function (data) {
        currencySymbol = currencySymbolMap[data.value] ?? '¥';
        $('#iedge_org_id').val(data.value);
        $('#currency_symbol').text(currencySymbol);
        showCouponTypeDiv(1);
        $('input[type="radio"][name="coupon_type"]:eq(1)').next().hide();
        // form.render();
    });

    function getSupplierOption() {
        //渲染供应商多选
        let url = '/api/commonData/getSupplierList';
        let res = ajax(url);
        let data = res.data;
        return {
            el: '#supplier_selector',
            filterable: true,
            paging: true,
            radio: true,
            direction: 'down',
            autoRow: true,
            prop: {
                name: 'supplier_name',
                value: 'supplier_id',
            },
            pageSize: 30,
            data: data,
            on: function (data) {
                //arr:  当前多选已选中的数据
                let arr = data.arr;
                let supplierIds = '';
                for (let i in arr) {
                    supplierIds += arr[i].supplier_id + ',';
                }
                $('#selected_supplier_id').val(supplierIds);
            },
        };
    }

    let couponGetRule = $('#coupon_get_rule_value').val();
    couponGetRuleDiv(couponGetRule);
    form.on('select(coupon_get_rule)', function (data) {
        couponGetRule = data.value;
        if (data.value == 3) {
            $('#coupon_get_rule_order_time').show();
        } else {
            $('#coupon_get_rule_order_time').hide();
        }
        couponGetRuleDiv(data.value);
    });

    let couponMallType = $('#coupon_mall_type_value').val();

    function showCouponGoodsRangeValueDiv(type) {
        switch (type) {
            case "1":
                $('#selected_supplier_id_div').hide();
                $('#brand_ids_div').hide();
                $('#self_brand_ids_div').hide();
                $('#class_ids_div').hide();
                $('#self_supplier_id_div').hide();
                $('#goods_name_div').hide();
                $('#supplier_ids_div').hide();
                $('#canal_select_div').hide();
                break;
            case "2":
                $('#selected_supplier_id_div').show();
                $('#brand_ids_div').hide();
                $('#self_brand_ids_div').hide();
                $('#class_ids_div').hide();
                $('#self_supplier_id_div').hide();
                $('#goods_name_div').hide();
                $('#supplier_ids_div').hide();
                $('#canal_select_div').hide();
                break;
            case "3":
                $('#selected_supplier_id_div').hide();
                if (couponMallType == 2) {
                    $('#brand_ids_div').hide();
                    $('#self_brand_ids_div').show();
                } else {
                    $('#brand_ids_div').show();
                    $('#self_brand_ids_div').hide();
                }
                $('#class_ids_div').hide();
                $('#self_supplier_id_div').hide();
                $('#goods_name_div').hide();
                $('#supplier_ids_div').hide();
                if (couponMallType == 3) {
                    $('#supplier_ids_div').show();
                    $('#canal_select_div').show();
                } else {
                    $('#supplier_ids_div').hide();
                    $('#canal_select_div').hide();
                }
                break;
            case "4":
                $('#selected_supplier_id_div').hide();
                $('#brand_ids_div').hide();
                $('#self_brand_ids_div').hide();
                $('#class_ids_div').show();
                $('#self_supplier_id_div').hide();
                $('#goods_name_div').hide();
                $('#supplier_ids_div').hide();
                $('#canal_select_div').hide();

                break;
            case "5":
                $('#selected_supplier_id_div').hide();
                $('#brand_ids_div').hide();
                $('#self_brand_ids_div').hide();
                $('#class_ids_div').hide();
                $('#self_supplier_id_div').show();
                $('#goods_name_div').hide();
                $('#supplier_ids_div').hide();
                $('#canal_select_div').hide();
                break;
            case "6":
                $('#selected_supplier_id_div').hide();
                $('#brand_ids_div').hide();
                $('#self_brand_ids_div').hide();
                $('#class_ids_div').hide();
                $('#self_supplier_id_div').hide();
                $('#goods_name_div').show();
                if (couponMallType == 3) {
                    $('#supplier_ids_div').show();
                    $('#canal_select_div').show();
                } else {
                    $('#supplier_ids_div').hide();
                    $('#canal_select_div').hide();
                }
                break;
        }
    }

    let couponGoodsRange = $('#coupon_goods_range_value').val();
    showCouponGoodsRangeValueDiv(couponGoodsRange);
    form.on('radio(coupon_goods_range)', function (data) {
        couponGoodsRange = data.value;
        showCouponGoodsRangeValueDiv(data.value);
    });

    let selectedSupplierIdSelector = commonXmSelect.render(getSupplierOption());
    let selectedSupplierId = $('#selected_supplier_id').attr('value');
    selectedSupplierIdSelector.setValue(selectedSupplierId.split(','));


    function showCouponMallTypeDiv(type) {
        showCouponGoodsRangeValueDiv(couponGoodsRange);
        if (type == 1) {
            $('#coupon_goods_range_div').hide();
        } else {
            $('#coupon_goods_range_div').show();
        }
        var couponGoodsRangeInputs = $('#coupon_goods_range_div').find(".layui-form-radio");
        if (type == 2) {
            couponGoodsRangeInputs.eq(1).hide();
            couponGoodsRangeInputs.eq(3).show();
            couponGoodsRangeInputs.eq(4).show();

        }
        if (type == 3) {
            couponGoodsRangeInputs.eq(1).show();
            couponGoodsRangeInputs.eq(3).hide();
            couponGoodsRangeInputs.eq(4).hide();
        }
    }

    showCouponMallTypeDiv(couponMallType);
    //使用商品类型切换
    form.on('radio(coupon_mall_type)', function (data) {
        couponMallType = data.value;
        showCouponMallTypeDiv(data.value);
    });

    function showCouponTypeDiv(type) {
        if (type == 1) {
            $('#sale_amount_operate_text').html('<span>可减</span> <span style="color: red">' + currencySymbol + '</span>');
            $('#sale_amount_type_text').text('满足金额填0.01时为无门槛优惠券');
            $('#max_preferential_amount_div').hide();
        } else {
            $('#sale_amount_operate_text').html('<span>可打</span> ');
            $('#sale_amount_type_text').text('折(eg:95折请填写0.95)');
            $('#max_preferential_amount_div').show();
        }
    }

    let couponType = $('#coupon_type_value').val();
    showCouponTypeDiv(couponType);
    form.on('radio(coupon_type)', function (data) {
        couponType = data.value;
        showCouponTypeDiv(data.value);
    });

    function showTimeTypeDiv(type) {
        if (type == 1) {
            $('#time_type_1').show();
            $('#time_type_2').hide();
        } else {
            $('#time_type_1').hide();
            $('#time_type_2').show();
        }
    }

    let timeType = $('#time_type_value').val();
    showTimeTypeDiv(timeType);
    form.on('radio(time_type)', function (data) {
        timeType = data.value;
        $('#time_type_value').val(timeType);
        showTimeTypeDiv(data.value);
        couponGetRuleDiv(couponGetRule);
    });

    function couponGetRuleDiv(type) {
        if (type == 0 || type == 2) {
            $('#reg_start_time_div').hide();
            $('#order_num_div').hide();
        }
        if (type == 1) {
            $('#reg_start_time_div').show();
            if ($('#time_type_value').val() == 2) {
                $('#reg_end_time_div').show();
            } else {
                $('#reg_end_time_div').hide();
            }
            $('#order_num_div').hide();
        }

        if (type == 3) {
            $('#reg_start_time_div').hide();
            $('#order_num_div').show();
        }
    }

    //初始化 优惠券名称 及 描述的 输入字符显示与限制
    limitAndShowInputLength('coupon_name', 'coupon_name_length', 32);
    limitAndShowInputLength('coupon_desc', 'coupon_desc_length', 16);


    form.on('radio(use_type)', function (data) {
        if (data.value === '1') {
            $('#goods_select_div').hide();
            $('#brand_select_div').show();
            if (goodsScope === '1') {
                $('#class_select_div').show();
            }
        } else {
            $('#goods_select_div').show();
            $('#brand_select_div').hide();
            $('#class_select_div').hide();
        }
    });

    let uploadChange = 0;
    $(document).ready(function () {
        layui.upload.render({
            elem: '#uploadSku',
            url: oss_url + '/uploadFile?sys_type=7',
            accept: 'file',
            exts: 'csv',
            before: function () {
                layer.load(2);
            },
            choose: function (obj) {

            },
            done: function (res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    let fileUrl = res.data.oss_file_url;
                    $('#sku_file_url').val(fileUrl);
                    $('#sku_file_url_href').show();
                    $('#sku_file_url_href').attr('href', fileUrl);
                    $('#uploadSku').text('重新上传');
                    uploadChange = 1;
                } else {
                    layer.msg(res.msg);
                }
            },
            error: function (index, upload) {
                layer.closeAll('loading');
                layer.msg('网络出现问题，请重试！');
            }
        });
    });
    layui.use('form', function () {
        //监听提交
        form.on('submit(saveForm)', function (data) {
            admin.showLoading({
                type: 3,
            });
            data.field.upload_change = uploadChange;
            let requestData = data.field;
            let url = '/api/coupon/saveCoupon';
            $.post(url, requestData, function (res) {
                if (res.code === 0) {
                    admin.removeLoading();
                    layer.msg(res.msg, {icon: 6});
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 500);
                } else {
                    admin.removeLoading();
                    layer.msg(res.msg, {icon: 5});
                }
            });
        });
        $('.cancel').click(function () {
            admin.closeThisDialog();
        });
    });

    function supplierOption() {
        //渲染供应商多选
        let url = '/api/commonData/getSupplierList';
        let res = ajax(url);
        let data = res.data;
        return {
            el: '#supplier_ids_selector',
            filterable: true,
            paging: true,
            direction: 'down',
            disabled: isIEdge,
            autoRow: true,
            prop: {
                name: 'supplier_name', value: 'supplier_id',
            }, pageSize: 30, data: data, on: function (data) {
                //arr:  当前多选已选中的数据
                let arr = data.arr;
                let supplierIds = '';
                for (let i in arr) {
                    let supplierId = arr[i].supplier_id;
                    supplierIds += supplierId + ',';
                }
                if (supplierIds.indexOf('17') === -1) {
                    $('#canal_select_div').hide();
                } else {
                    $('#canal_select_div').show();
                }
                $('#supplier_ids').val(supplierIds);
            },
        };
    }

    let supplierSelector = commonXmSelect.render(supplierOption());
    let supplierIds = $('#supplier_ids').attr('value');
    if (supplierIds !== '') {
        supplierSelector.setValue(supplierIds.split(','));
    }


    //渲染渠道多选
    let canalSelector = commonXmSelect.render({
        el: '#canal_selector',
        filterable: true,
        paging: true,
        direction: 'down',
        autoRow: true,
        disabled: isIEdge,
        prop: {
            name: 'supplier_name', value: 'supplier_code',
        }, remoteSearch: true, pageRemote: true, pageSize: 30, remoteMethod: function (val, cb, show, pageIndex) {
            //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
            $.ajax({
                url: '/api/commonData/getCanalList', type: 'post', data: {
                    supplier_name: val, page: pageIndex
                }, dataType: 'json', timeout: 10000, success: function (res) {
                    if (!res) return layer.msg('网络错误，请重试', {icon: 5});
                    if (res.err_code === 0) {
                        cb(res.data, res.last_page);
                    } else {
                        layer.msg(res.err_msg, {icon: 6});
                    }
                }, error: function () {
                    return layer.msg('网络错误，请重试', {icon: 5});
                }
            });
        }, on: function (canalList) {
            //arr:  当前多选已选中的数据
            let arr = canalList.arr;
            let canals = '';
            for (let i in arr) {
                canals += arr[i].supplier_code + ',';
            }
            $('#canals').val(canals);
        },
    });

    let canalInitValue = $('#canal_init_value').val();
    canalInitValue = canalInitValue ? JSON.parse(canalInitValue) : [];
    canalSelector.setValue(canalInitValue);


    $(function () {
        //爱智还要处理页面,隐藏某些元素
        if (isIEdge) {
            let divs = $('#coupon_goods_range_div .layui-input-block').find('.layui-form-radio');
            divs.each(function (index, item) {
                if (index !== 2) {
                    $(item).hide();
                    return
                }
                $(item).find('div').text('全部');
            });
            $('#brand_ids_div').hide();
            $('#self_brand_ids_div').hide();
            $('#class_ids_div').hide();
        }
    });

});

