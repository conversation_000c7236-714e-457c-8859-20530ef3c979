layui.use(['form', 'laydate', 'element', 'table', 'index'], function () {
    const form = layui.form;
    const table = layui.table;
    const laydate = layui.laydate;
    const index = layui.index;
    let where = {};

    //日期
    laydate.render({
        elem: '#start_time',
        type: 'datetime'
    });
    laydate.render({
        elem: '#end_time',
        type: 'datetime'
    });
    table.render({
        elem: '#list',
        url: '/api/coupon/getCouponList',
        method: 'post',
        size: 'sm',
        cellMinWidth: 80,//全局定义常规单元格的最小宽度
        request: {
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        toolbar: '#toolbar',
        defaultToolbar: ['filter'],
        where: {},
        limit: 15,
        loading: true,
        cols: [[
            {type: 'checkbox', fixed: true},
            {field: 'coupon_id', title: 'ID', align: 'center', width: 50},
            {
                field: 'coupon_sn', title: '批次号', align: 'center', width: 180, templet: function (data) {
                    return "<span style='color: dodgerblue' data-name='" + data.coupon_name + "'  data-id='" + data.coupon_id + "' class='click_and_view_coupon'>" + data.coupon_sn + "</span>";
                }
            },
            {field: 'coupon_name', title: '名称', align: 'center', width: 180},
            {field: 'coupon_desc', title: '优惠券描述', align: 'center', width: 180},
            {field: 'coupon_type_name', title: '优惠券类型', width: 100, align: 'center'},
            {field: 'amount_rule', title: '优惠券面额(元)', width: 140, align: 'center'},
            {field: 'usable_range', title: '适用范围', width: 140, align: 'center'},
            {field: 'issue_type_name', title: '发放方式', align: 'center'},
            {
                field: 'start_time', title: '生效时间', align: 'center', width: 230, templet: function (data) {
                    return data.start_time ? data.start_time + '-' + data.end_time : data.usable_time;
                }
            },
            {field: 'status_name', title: '优惠券状态', align: 'center'},
            {field: 'creater_name', title: '创建人', align: 'center'},
            {field: 'create_time', title: '创建时间', align: 'center', width: 150},
            {field: 'org_name', title: '优惠券主体', align: 'center', width: 100},
            {field: 'edit', title: '操作', templet: '#edit', width: 100, fixed: 'right'},
        ]],
        id: 'list',
        page: {}
    });

    form.on('submit(load)', function (data) {
        where = data.field;
        table.reload('list', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field,
        });
    });

    table.on('toolbar(list)', function (obj) {
        let checkStatus = table.checkStatus('list');
        let data = checkStatus.data;
        switch (obj.event) {
            case 'add':
                layer.open({
                    type: 2,
                    area: ['95%', '95%'],
                    fixed: false,
                    title: '新增优惠券',
                    content: '/web/coupon/saveCoupon?coupon_id',
                    end: function () {
                        table.reload('list', {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: where,
                        });
                    }
                });
                break;
            case 'review':
                if (!data.length) {
                    layer.msg('请先选择要操作的优惠券', {icon: 5})
                } else {
                    let couponIds = [];
                    let hasInvalidStatus = false;
                    $.each(data, function (index, value) {
                        if (value.status !== -1) {
                            hasInvalidStatus = true;
                        }
                        couponIds.push(value.coupon_id);
                    });
                    if (hasInvalidStatus) {
                        layer.msg('选择的优惠券种存在不是审核中状态', {icon: 5});
                        return false;
                    }
                    couponIds = couponIds.join(',');
                    layer.open({
                        type: 2,
                        area: ['75%', '75%'],
                        fixed: false,
                        title: '审核优惠券',
                        content: '/web/coupon/reviewCoupons?coupon_ids=' + couponIds,
                        end: function () {
                            table.reload('list', {
                                page: {
                                    curr: 1 //重新从第 1 页开始
                                },
                                where: where,
                            });
                        }
                    });
                }
                break;
            case 'issue':
                if (!data.length) {
                    layer.msg('请先选择要操作的优惠券', {icon: 5})
                } else {
                    if (data.length > 1) {
                        layer.msg('该操作不支持多选', {icon: 5})
                        return;
                    }
                    console.log(data[0].status);
                    if (data[0].status !== 1) {
                        layer.msg('必须是有效状态的优惠券才可以', {icon: 5})
                        return;
                    }
                    let couponId = data[0].coupon_id;
                    layer.open({
                        type: 2,
                        area: ['95%', '95%'],
                        fixed: false,
                        title: '发放优惠券',
                        content: '/web/coupon/issueCoupon?coupon_id=' + couponId,
                        end: function () {
                        }
                    });
                }
                break;

        }
    });


    $(document).on('click', '.click_and_view_coupon', function () {
        layer.open({
            type: 2,
            area: ['95%', '95%'],
            fixed: false,
            title: '优惠券详情 - ' + $(this).attr('data-name'),
            content: '/web/coupon/couponDetail?coupon_id=' + $(this).attr('data-id'),
            end: function () {
                table.reload('list', {
                    page: {
                        curr: 1 //重新从第 1 页开始
                    },
                    where: where,
                });
            }
        });
    });

    table.on('tool(list)', function (obj) {
        let event = obj.event;
        let couponId = obj.data.coupon_id;
        let couponName = obj.data.coupon_name;
        if (event === 'edit') {
            layer.open({
                type: 2,
                area: ['95%', '95%'],
                fixed: false,
                title: '编辑优惠券 - ' + couponName,
                content: '/web/coupon/saveCoupon?coupon_id=' + couponId,
                end: function () {
                    table.reload('list', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: where,
                    });
                }
            });
        }
        if (event === 'delete') {
            layer.msg('你确定要删除该优惠券吗？', {
                icon: 5,
                time: 100000,
                btn: ['确定', '取消'],
                yes: function (index) {
                    const url = '/api/coupon/deleteCoupon';
                    const data = {
                        coupon_id: couponId
                    };
                    $.post(url, data, function (res) {
                        if (res.code === 0) {
                            table.reload('list');
                            layer.msg("删除成功", {icon: 6});
                        } else {
                            layer.msg(res.msg, {icon: 5});
                        }
                    });
                }
            });
        }
    });

});
