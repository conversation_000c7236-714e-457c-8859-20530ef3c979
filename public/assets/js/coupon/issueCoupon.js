layui.use(['table', 'form', 'laydate', 'xmSelect', 'admin', 'index', 'upload'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;
    let upload = layui.upload;

    layui.use('form', function () {
        //监听提交
        form.on('submit(saveForm)', function (data) {
            // admin.showLoading({
            //     type: 3,
            // });
            admin.btnLoading('.saveForm','发放中');
            admin.btnLoading('.closeForm','发放中');
            let requestData = data.field;
            let url = '/api/coupon/issueCoupon';
            $.ajax({
                url: url,
                type: 'post',
                data: requestData,
                dataType: 'json',
                timeout: 50000,
                success: function (res) {
                    if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
                    if (res.code === 0) {
                        admin.btnLoading('.saveForm',false);
                        admin.btnLoading('.closeForm',false);
                        layer.msg(res.msg, {icon: 6});
                        setTimeout(function () {
                            admin.closeThisDialog();
                        }, 500);
                    } else {
                        admin.btnLoading('.saveForm',false);
                        admin.btnLoading('.closeForm',false);
                        layer.msg(res.msg, {icon: 5});
                    }
                },
                error: function () {
                    admin.btnLoading('.saveForm',false);
                    admin.btnLoading('.closeForm',false);
                    return layer.msg('网络错误，请重试', { icon: 5 });
                }
            });
            return false;
        });
        $('.closeForm').click(function () {
            admin.closeThisDialog();
        });
    });

    $(document).ready(function () {
        upload.render({
            elem: '#analysisIssueCouponFile',
            url: '/api/coupon/analysisIssueCouponFile',
            accept: 'file',
            exts: 'csv',
            before: function () {
                layer.load(2);
            },
            choose: function (obj) {

            },
            done: function (res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    let text = '';
                    let newText = '';
                    console.log(res.data.valid_account_list.join(','));
                    if (res.data.valid_account_list) {
                        newText = res.data.valid_account_list.join(',');
                    }
                    text = text + newText;
                    $('#user_account_list').text(text);

                    let invalidText = '';
                    let newInvalidText = '';
                    if (res.data.invalid_account_list) {
                        newInvalidText = res.data.invalid_account_list.join(',');
                    }
                    invalidText = invalidText + newInvalidText;
                    $('#invalid_account_list').text(invalidText);
                } else {
                    layer.msg(res.msg);
                }
            },
            error: function (index, upload) {
                layer.closeAll('loading');
                layer.msg('网络出现问题，请重试！');
            }
        });
    });
});

