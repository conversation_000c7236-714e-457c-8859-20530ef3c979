layui.use(['table', 'form', 'laydate', 'xmSelect', 'admin', 'index', 'upload'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;
    let upload = layui.upload;

    layui.use('form', function () {
        //监听提交
        form.on('submit(reviewPass)', function (data) {
            layer.load(2);
            let couponIds = $('#couponIds').val();
            let reviewRemark = $('#review_remark').val();
            let url = '/api/coupon/reviewCoupons';
            $.post(url, {coupon_ids: couponIds, review_remark: reviewRemark, status: 1}, function (res) {
                if (res.code === 0) {
                    layer.closeAll();
                    layer.msg(res.msg, {icon: 6});
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 1000);
                } else {
                    layer.closeAll();
                    layer.msg(res.msg, {icon: 5});
                }
            });
            layer.closeAll();
            return false;
        });
        form.on('submit(reviewReject)', function (data) {
            layer.load(2);
            let couponIds = $('#couponIds').val();
            let reviewRemark = $('#review_remark').val();
            let url = '/api/coupon/reviewCoupons';
            $.post(url, {coupon_ids: couponIds, review_remark: reviewRemark, status: -2}, function (res) {
                if (res.code === 0) {
                    layer.closeAll();
                    layer.msg(res.msg, {icon: 6});
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 1000);
                } else {
                    layer.closeAll();
                    layer.msg(res.msg, {icon: 5});
                }
            });
            layer.closeAll();
            return false;
        });
        form.on('submit(cancel)', function (data) {
            admin.closeThisDialog();

        });
    });

    $(document).ready(function () {
        upload.render({
            elem: '#analysisIssueCouponFile',
            url: '/api/coupon/analysisIssueCouponFile',
            accept: 'file',
            exts: 'csv',
            before: function () {
                layer.load(2);
            },
            choose: function (obj) {

            },
            done: function (res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    let text = '';
                    let newText = '';
                    console.log(res.data.valid_account_list.join(','));
                    if (res.data.valid_account_list) {
                        newText = res.data.valid_account_list.join(',');
                    }
                    text = text + newText;
                    $('#user_account_list').text(text);

                    let invalidText = '';
                    let newInvalidText = '';
                    if (res.data.invalid_account_list) {
                        newInvalidText = res.data.invalid_account_list.join(',');
                    }
                    invalidText = invalidText + newInvalidText;
                    $('#invalid_account_list').text(invalidText);
                } else {
                    layer.msg(res.msg);
                }
            },
            error: function (index, upload) {
                layer.closeAll('loading');
                layer.msg('网络出现问题，请重试！');
            }
        });
    });
});

