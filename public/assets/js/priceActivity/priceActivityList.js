layui.use(['form', 'laydate', 'element', 'table', 'index', 'admin'], function () {
    let form = layui.form, table = layui.table, laydate = layui.laydate;
    let index = layui.index;
    let admin = layui.admin;
    let where = {};

    //日期
    laydate.render({
        elem: '#start_time',
        type: 'datetime'
    });
    laydate.render({
        elem: '#end_time',
        type: 'datetime'
    });
    function renders(activity_name){
        table.render({
            elem: '#list',
            url: '/api/priceActivity/getPriceActivityList',
            method: 'post',
            toolbar: '#toolbar',
            size: 'sm',
            cellMinWidth: 80,//全局定义常规单元格的最小宽度
            request: {
                limitName: 'limit' //每页数据量的参数名，默认：limit
            },
            defaultToolbar: ['filter'],
            where: {
                status: $("input[name=status]").val(),
                expire: $('input[name=expire]').val(),
                activity_name:(activity_name||"")
            },
            limit: 15,
            loading: true,
            cols: [[
                {field: 'id', title: '活动价ID', align: 'center', width: 50},
                {
                    field: 'activity_name', title: '活动名称', width: 160, align: 'center', templet: function (data) {
    
                        return "<a ew-href='/web/priceActivity/savePriceActivity?id=" + data.id +
                            "' style='color: dodgerblue' ew-title='活动详情 - " + data.activity_name + "'>" + data.activity_name + "</a>";
                    }
                },
                {field: 'sign', align: 'center', title: '营销标签'},
                {field: 'activity_time', align: 'center', title: '活动时间', width: 150},
                {
                    field: 'user_scope', align: 'center', title: '用户范围', width: 100, templet: function (data) {
                        return data.user_scope === 1 ? "全站用户" : "登陆用户";
                    }
                },
                {field: 'goods_scope', align: 'center', title: '商品类型'},
                {field: 'condition', align: 'center', title: '生效条件'},
                {
                    field: 'allow_use_coupon', align: 'center', title: '使用优惠券', width: 80, templet: function (data) {
                        return data.allow_coupon === 1 ? "允许" : "禁止";
                    }
                },
                {field: 'status', align: 'center', title: '状态', width: 80, templet: '#status'},
                {
                    field: 'new_user_num', align: 'center', title: '新用户数', width: 90, templet: function (data) {
                        return data.statistics ? data.statistics.new_user_num : 0;
                    }
                },
                {
                    field: 'order_num', align: 'center', title: '订单数量', width: 80, templet: function (data) {
                        return data.statistics ? data.statistics.order_num : 0;
                    }
                },
                {field: 'add_time', align: 'center', title: '创建时间', width: 160},
                {field: 'admin_name', align: 'center', title: '创建人', width: 130},
                {field: 'edit', title: '操作', templet: '#edit', width: 250, fixed: 'right'},
            ]],
            id: 'list',
            page: {}
        });
    }
    renders($("#activity_name").val())

    form.on('submit(load)', function (data) {
        //执行重载
        if ($(this).data('type') === 'down') {
            let url = '/api/ApiUserWriteList?';
            $.each(data.field, function (k, v) {
                if (v) url += k + '=' + v + '&';
            });
            url += 'down=true&limit=1000&p=1';
            window.location.href = url;
        } else {
            where = data.field;
            table.reload('list', {
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                where: data.field,
            });
        }
        return false;
    });

    table.on('toolbar(list)', function(obj){
        switch(obj.event){
            case 'addActivity':
                layer.open({
                    type: 2,
                    area: ['95%', '95%'],
                    fixed: false,
                    title: '新增商品活动价 ',
                    content: '/web/priceActivity/savePriceActivity',
                    end: function () {
                        table.reload('list', {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: where,
                        });
                    }
                });
                break;
        }
    });

    //点击删除
    $(document).on('click', '.delete', function () {
        let obj = $(this);
        layer.msg('你确定要删除该活动吗？', {
            icon: 5,
            time: 100000,
            btn: ['确定', '取消'],
            yes: function (index) {
                let activityId = obj.val();
                const url = '/api/priceActivity/deletePriceActivity';
                const data = {
                    activity_id: activityId
                };
                $.post(url, data, function (res) {
                    if (res.code === 0) {
                        table.reload('list', {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: where,
                        });
                       layer.msg("删除成功", {icon: 6});
                    } else {
                        layer.msg(res.msg, {icon: 5})
                    }
                });
            }
        });
    });

    table.on('tool(list)', function (obj) {
        let event = obj.event;
        let id = obj.data.id;
        let name = obj.data.activity_name;
        if (event === 'edit') {
            layer.open({
                type: 2,
                area: ['95%', '95%'],
                fixed: false,
                title: '编辑活动价 - ' + name,
                content: '/web/priceActivity/savePriceActivity?id=' + id,
                end: function () {
                    table.reload('list', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: where,
                    });
                }
            });
        }

        if (event === 'publish') {
            layer.msg('你确定要发布该活动吗？', {
                icon: 6,
                time: 100000,
                btn: ['确定', '取消'],
                yes: function (index) {
                    let activityId = id;
                    const url = '/api/priceActivity/publishPriceActivity';
                    const data = {
                        activity_id: activityId
                    };
                    $.post(url, data, function (res) {
                        if (res.code === 0) {
                            table.reload('list');
                            layer.msg("发布成功", {icon: 6});
                        }
                    });
                }
            });
        }
    });

});
