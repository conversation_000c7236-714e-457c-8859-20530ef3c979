layui.use(['table', 'form', 'laydate', 'xmSelect', 'admin', 'index'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;


    let commonXmSelect = layui.xmSelect;
    let laydate = layui.laydate;
    let goodsScope = $('#goods_scope').val();
    let useType = $('#use_type').val();
    let brandType = goodsScope;
    //判断是新增还是修改
    let activityId = $('#activity_id').val();
    let orgId = $('#org_id').val();

    //时间选择器
    laydate.render({
        elem: 'input[name=start_time]', type: 'datetime'
    });
    laydate.render({
        elem: 'input[name=end_time]', type: 'datetime'
    });

    function getSupplierOption() {
        //渲染供应商多选
        let url = '/api/commonData/getSupplierList';
        let res = ajax(url);
        let data = res.data;
        return {
            el: '#supplier_selector', filterable: true, paging: true, direction: 'down', autoRow: true, prop: {
                name: 'supplier_name', value: 'supplier_id',
            }, pageSize: 30, data: data, on: function (data) {
                //arr:  当前多选已选中的数据
                let arr = data.arr;
                let supplierIds = '';
                for (let i in arr) {
                    let supplierId = arr[i].supplier_id;
                    supplierIds += supplierId + ',';
                }
                if (supplierIds.indexOf('17') === -1) {
                    $('#canal_select_div').hide();
                } else {
                    $('#canal_select_div').show();
                }
                $('#supplier_ids').val(supplierIds);
            },
        };
    }


    let supplierSelector = commonXmSelect.render(getSupplierOption());
    let supplierIds = $('#supplier_ids').attr('value');
    supplierSelector.setValue(supplierIds.split(','));



    function controlOrgContent(value,isInit) {
        if (value === '1') {
            // 恢复美金选项显示
            $('#currency_us').next().show();
            // 确保人民币选项也显示
            $('#currency_rmb').next().show();

            // 恢复商品类型选择
            $('#zy_type').prop('disabled', false);
            $('#ly_type').prop('disabled', false);
            form.render('radio');

            if (!isInit) {
                // 恢复供应商选择
                $('#supplier_ids').val('');
                supplierSelector.setValue([]);
            }
            // 启用供应商选择器
            supplierSelector.update({
                disabled: false
            });
            $('#canal_select_div').hide();
            $('#user_scope_div').show();
            $('#use_coupon_div').show();
        } else if (value === '3') {
            // 确保人民币选项显示并选中
            $('#currency_rmb').next().show();
            $('#currency_rmb').prop('checked', true);
            $('#currency_rmb').val(1);
            $('#currency_rmb_ratio_div').show();

            // 隐藏美金选项并取消选中

            $('#currency_us').next().hide();
            $('#currency_us').prop('checked', false);
            $('#currency_us').val(0);
            $('#currency_us_ratio_div').hide();

            // 设置商品类型为专营并禁用自营选项
            $('#ly_type').prop('checked', true);
            $('#zy_type').prop('disabled', true);
            $('#ly_type').prop('disabled', true);
            form.render('radio');

            // 设置供应商为17并禁用选择器
            supplierSelector.setValue(['17']);
            $('#supplier_ids').val('17');
            // 禁用供应商选择器
            supplierSelector.update({
                disabled: true
            });
            // 显示渠道标签
            $('#canal_select_div').show();
            $('#user_scope_div').hide();
            $('#use_coupon_div').hide();
        }
    }
    controlOrgContent(orgId,1);

    form.on('radio(org_id)', function (data) {
        controlOrgContent(data.value);
    });


    //渲染分类多选
    let classUrl = '/api/commonData/getSelfClassList';
    let classRes = ajax(classUrl);
    let classList = classRes.data;
    let classSelector = commonXmSelect.render({
        el: '#class_selector', filterable: true, paging: true, direction: 'down', autoRow: true, prop: {
            name: 'class_name', value: 'class_id',
        }, toolbar: {
            show: true, showIcon: true
        }, pageSize: 100, data: classList, on: function (classList) {
            //arr:  当前多选已选中的数据
            let arr = classList.arr;
            let classIds = '';
            for (let i in arr) {
                classIds += arr[i].class_id + ',';
            }
            $('#class_ids').val(classIds);
        },
    });
    let classIds = $('#class_ids').attr('value');
    classSelector.setValue(classIds.split(','));

    //渲染渠道多选
    let canalSelector = commonXmSelect.render({
        el: '#canal_selector', filterable: true, paging: true, direction: 'down', autoRow: true, prop: {
            name: 'supplier_name', value: 'supplier_code',
        }, remoteSearch: true, pageRemote: true, pageSize: 30, remoteMethod: function (val, cb, show, pageIndex) {
            //val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
            $.ajax({
                url: '/api/commonData/getCanalList', type: 'post', data: {
                    supplier_name: val, page: pageIndex
                }, dataType: 'json', timeout: 10000, success: function (res) {
                    if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
                    if (res.err_code === 0) {
                        cb(res.data, res.last_page);
                    } else {
                        layer.msg(res.err_msg, { icon: 6 });
                    }
                }, error: function () {
                    return layer.msg('网络错误，请重试', { icon: 5 });
                }
            });
        }, on: function (canalList) {
            //arr:  当前多选已选中的数据
            let arr = canalList.arr;
            let canals = '';
            for (let i in arr) {
                canals += arr[i].supplier_code + ',';
            }
            $('#canals').val(canals);
        },
    });

    let canalInitValue = $('#canal_init_value').val();
    canalInitValue = canalInitValue ? JSON.parse(canalInitValue) : [];
    canalSelector.setValue(canalInitValue);

    $('#supplier_ziying_div').hide();
    //点击自营,隐藏相关的东西
    //还要切换品牌列表
    form.on('radio(goods_scope)', function (data) {
        goodsScope = data.value;
        layer.load();
        let brandIdsObject = $('#standard_brand_ids');
        let excludeBrandIdsObject = $('#exclude_standard_brand_ids');
        let brandIdsVal = brandIdsObject.val();
        let confirmRes = true;
        if (brandIdsVal !== '') {
            confirmRes = confirm("你确定要切换类型吗?切换类型将会清空下面设置的品牌");
        }
        if (confirmRes) {
            if (data.value === "1") {
                $('#supplier_ids_div').hide();
                $('#supplier_ziying_div').show();
                $('#supplier_ids').val(10000);
                $('#class_select_div').show();
                // $('#canal_select_div').hide();
            } else {
                $('#supplier_ids_div').show();
                $('#supplier_ziying_div').hide();
                $('#supplier_ids').val('');
                //重新渲染供应商xmSelect
                let supplierSelector = commonXmSelect.render(getSupplierOption());
                supplierSelector.setValue(supplierIds.split(','));
                $('#class_select_div').hide();
                // $('#canal_select_div').show();
            }
            //切换清空品牌选择,因为自营和专营的品牌是不一样的
            brandIdsObject.val('');
            excludeBrandIdsObject.val('');
        }
        layer.closeAll();
    });

    if (activityId && goodsScope === '1') {
        $('#supplier_ziying_div').show();
        $('#supplier_ids_div').hide();

    }
    if (goodsScope === '1' && useType == 1) {
        $('#class_select_div').show();
        // $('#canal_select_div').hide();
    }

    if (goodsScope === '1' && useType == 2) {
        $('#class_select_div').hide();
        // $('#canal_select_div').hide();
    }

    if (goodsScope === '2') {
        $('#class_select_div').hide();
        // $('#canal_select_div').show();
    }

    form.on('checkbox(currency_rmb)', function (data) {
        let divObj = $('#currency_rmb_ratio_div');
        if (data.elem.checked) {
            $('#currency_rmb').val(1);
            divObj.show();
        } else {
            $('#currency_rmb').val(0);
            divObj.hide();
        }
    });

    form.on('checkbox(currency_us)', function (data) {
        let divObj = $('#currency_us_ratio_div');
        if (data.elem.checked) {
            $('#currency_us').val(1);
            divObj.show();
        } else {
            $('#currency_us').val(0);
            divObj.hide();
        }
    });

    form.on('radio(use_type)', function (data) {
        if (data.value === '1') {
            $('#goods_select_div').hide();
            $('#brand_select_div').show();
            if (goodsScope === '1') {
                $('#class_select_div').show();
            }
        } else {
            $('#goods_select_div').show();
            $('#brand_select_div').hide();
            $('#class_select_div').hide();
        }
    });

    $(document).ready(function () {
        layui.upload.render({
            elem: '#uploadExcludeSkuIds',
            url: oss_url + '/uploadFile?sys_type=7',
            accept: 'file',
            exts: 'csv',
            before: function () {
                layer.load(2);
            },
            choose: function (obj) {
                //将每次选择的文件追加到文件队列
                obj.preview(function (index, file, result) {
                    var reader = new FileReader();
                    reader.readAsText(file);
                    reader.onload = function () {
                        var csvData = reader.result;
                        let data = $.csv.toArrays(csvData);
                        console.log(data)
                        let excludeSkuIds = [];
                        $.each(data, function (index, value) {
                            if (index !== 0) {
                                excludeSkuIds = $.merge(excludeSkuIds, value);
                            }
                        });
                        excludeSkuIds = excludeSkuIds ? excludeSkuIds.join(',') : '';
                        console.log(excludeSkuIds)
                        $('#exclude_sku_ids').val(excludeSkuIds);
                    }
                });
            },
            done: function (res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    let fileUrl = res.data.oss_file_url;
                    $('#exclude_sku_ids_file_url').val(fileUrl);
                    $('#exclude_sku_ids_file_url_href').show();
                    $('#exclude_sku_ids_file_url_href').attr('href', fileUrl);
                    $('#uploadExcludeSkuIds').text('重新上传');
                } else {
                    layer.msg(res.msg);
                }
            },
            error: function (index, upload) {
                layer.closeAll('loading');
                layer.msg('网络出现问题，请重试！');
            }
        });
    });

    let uploadChange = 0;

    $(document).ready(function () {
        layui.upload.render({
            elem: '#uploadSku',
            url: oss_url + '/uploadFile?sys_type=7',
            accept: 'file',
            exts: 'csv',
            before: function () {
                layer.load(2);
            },
            choose: function (obj) {

            },
            done: function (res) {
                layer.closeAll('loading');
                if (res.code === 0) {
                    let fileUrl = res.data.oss_file_url;
                    $('#sku_file_url').val(fileUrl);
                    $('#sku_file_url_href').show();
                    $('#sku_file_url_href').attr('href', fileUrl);
                    $('#uploadSku').text('重新上传');
                    uploadChange = 1;
                } else {
                    layer.msg(res.msg);
                }
            },
            error: function (index, upload) {
                layer.closeAll('loading');
                layer.msg('网络出现问题，请重试！');
            }
        });
    });

    layui.use('form', function () {
        //监听提交
        form.on('submit(saveForm)', function (data) {
            admin.showLoading({
                type: 3,
            });
            data.field.upload_change = uploadChange;
            let requestData = data.field;
            let url = '/api/priceActivity/savePriceActivity';
            $.post(url, requestData, function (res) {
                if (res.code === 0) {
                    admin.removeLoading();
                    layer.msg(res.msg, { icon: 6 });
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 700);
                } else {
                    admin.removeLoading();
                    layer.msg(res.msg, { icon: 5 });
                }
            }).fail(function (jqXHR, textStatus, errorThrown) {
                admin.removeLoading();
                layer.msg('网络出现问题，请重试！', { icon: 5 });
            });
            return false;
        });
        form.on('submit(closeForm)', function (data) {
            admin.closeThisDialog();
        });
    });

});

