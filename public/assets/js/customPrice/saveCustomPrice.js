layui.use(['table', 'form', 'laydate', 'xmSelect', 'admin', 'index'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;
    let table = layui.table;

    // 初始化表格
    table.render({
        elem: '#priceTable',
        data: [],
        cols: [[
            { field: 'price_name', title: '价格名称', edit: 'text' },
            { field: 'ratio', title: '人民币利润系数(%)', width: 250, edit: 'text' },
            { field: 'operation', title: '操作', width: 100, toolbar: '#tableOperation' }
        ]],
    });

    // 添加行按钮点击事件
    $('#addRow').on('click', function () {
        let data = table.cache.priceTable || [];
        data.push({
            price_name: '',
            ratio: '',
            // price: ''
        });
        table.reload('priceTable', {
            data: data
        });
    });

    // 监听工具条
    table.on('tool(priceTable)', function (obj) {
        let data = obj.data;
        if (obj.event === 'del') {
            layer.confirm('确认删除此行？', function (index) {
                let tableData = table.cache.priceTable;
                tableData.splice(obj.tr[0].rowIndex - 1, 1);
                table.reload('priceTable', {
                    data: tableData
                });
                layer.closeAll();
            });
        }
    });

    // 监听单元格编辑
    table.on('edit(priceTable)', function (obj) {

    });

    layui.use('form', function () {


        form.on('submit(auditPass)', function (data) {
            // 获取表格数据
            let tableData = table.cache.priceTable;
            let requestData = data.field;
            requestData.price_list = tableData;
            requestData.audit_status = 1;
            admin.showLoading({
                type: 3,
            });
            requestData.audit_status = 1;
            let url = '/api/customPrice/auditCustomPrice';
            $.post(url, requestData, function (res) {
                if (res.code === 0) {
                    admin.removeLoading();
                    layer.msg(res.msg, { icon: 6 });
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 700);
                } else {
                    admin.removeLoading();
                    layer.msg(res.msg, { icon: 5 });
                }
            }).fail(function (xhr, status, error) {
                admin.removeLoading();
                layer.msg('网络错误,请稍后重试', { icon: 5 });
            });
            return false;
        });
        form.on('submit(auditFail)', function (data) {
            // 获取表格数据
            let tableData = table.cache.priceTable;
            let requestData = data.field;
            requestData.price_list = tableData;
            admin.showLoading({
                type: 3,
            });
            requestData.audit_status = -1;
            let url = '/api/customPrice/auditCustomPrice';
            $.post(url, requestData, function (res) {
                if (res.code === 0) {
                    admin.removeLoading();
                    layer.msg(res.msg, { icon: 6 });
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 700);
                } else {
                    admin.removeLoading();
                    layer.msg(res.msg, { icon: 5 });
                }
            }).fail(function (xhr, status, error) {
                admin.removeLoading();
                layer.msg('网络错误,请稍后重试', { icon: 5 });
            });
            return false;
        });
        //监听提交
        form.on('submit(saveForm)', function (data) {
            // 获取表格数据
            let tableData = table.cache.priceTable;
            let requestData = data.field;
            requestData.price_list = tableData;
            admin.showLoading({
                type: 3,
            });
            let url = '/api/customPrice/saveCustomPrice';
            $.post(url, requestData, function (res) {
                if (res.code === 0) {
                    admin.removeLoading();
                    layer.msg(res.msg, { icon: 6 });
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 700);
                } else {
                    admin.removeLoading();
                    layer.msg(res.msg, { icon: 5 });
                }
            }).fail(function (xhr, status, error) {
                admin.removeLoading();
                layer.msg('网络错误,请稍后重试', { icon: 5 });
            });
            return false;
        });
        form.on('submit(closeForm)', function (data) {
            admin.closeThisDialog();
        });
    });

});

