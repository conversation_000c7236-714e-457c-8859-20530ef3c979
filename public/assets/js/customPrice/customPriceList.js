layui.use(['form', 'laydate', 'element', 'table', 'index', 'admin'], function () {
    let form = layui.form, table = layui.table, laydate = layui.laydate;
    let index = layui.index;
    let admin = layui.admin;
    let where = {};

    //日期
    laydate.render({
        elem: '#start_time',
        type: 'datetime'
    });
    laydate.render({
        elem: '#end_time',
        type: 'datetime'
    });
    function renders() {
        table.render({
            elem: '#list',
            url: '/api/customPrice/getCustomPriceList',
            method: 'post',
            toolbar: '#toolbar',
            size: 'sm',
            cellMinWidth: 80,//全局定义常规单元格的最小宽度
            request: {
                limitName: 'limit' //每页数据量的参数名，默认：limit
            },
            defaultToolbar: ['filter'],
            limit: 15,
            loading: true,
            cols: [[
                { field: 'id', title: 'ID', align: 'center', width: 80 },
                { field: 'org_name', title: '应用组织', align: 'center', width: 100 },
                {
                    field: 'price_list', title: '已生效展示价格', align: 'center', width: 160, templet: function (res) {
                        return res.status == 1 ? '<span style="color: dodgerblue" class="view_price_list">移入查看</span>' : '无';
                    }
                },
                {
                    field: 'audit_price_list', title: '待审核展示价格', align: 'center', width: 160, templet: function (res) {
                        return res.audit_status != 1 ? '<span style="color: dodgerblue" class="view_audit_price_list">移入查看</span>' : '无';
                    }
                },
                { field: 'audit_content', title: '审核备注', align: 'center', width: 180 },
                { field: 'status_name', title: '状态', align: 'center', width: 80 ,templet: function (res) {
                    return res.status == 1 ? '<span style="color: green">启用</span>' : '<span style="color: red">禁用</span>';
                }},
                { field: 'audit_status_name', title: '审核状态', align: 'center', width: 80 ,templet: function (res) {
                    switch (res.audit_status) {
                        case 0:
                            return '<span style="color: dodgerblue">待审核</span>';
                        case 1:
                            return '<span style="color: green">审核通过</span>';
                        case -1:
                            return '<span style="color: red">审核失败</span>';
                    }
                }},
                { field: 'create_name', title: '创建人', align: 'center', width: 100 },
                { field: 'update_name', title: '更新人', align: 'center', width: 100 },
                { field: 'create_time', title: '创建时间', align: 'center', width: 160 },
                { field: 'update_time', title: '更新时间', align: 'center', width: 160 },
                { field: 'audit_name', title: '审核人', align: 'center', width: 100 },
                { field: 'audit_time', title: '审核时间', align: 'center', width: 160 },
                { field: 'action', title: '操作', align: 'center', width: 180, templet: '#edit',fixed: 'right' },
            ]],
            id: 'list',
            page: {}
        });
    }
    renders($("#custom_price_name").val())

    table.on('toolbar(list)', function (obj) {
        switch (obj.event) {
            case 'addCustomPrice':
                layer.open({
                    type: 2,
                    area: ['95%', '95%'],
                    fixed: false,
                    title: '新增展示价格',
                    content: '/web/customPrice/saveCustomPrice',
                    end: function () {
                        table.reload('list', {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: where,
                        });
                    }
                });
                break;
        }
    });

    let ladderPriceTipsVal = '';
    $(document).on('mouseenter', '.view_price_list', function () {
        let self = this;
        let rowIndex = $(this).parent().parent().parent().attr('data-index');
        let data = table.cache['list'][rowIndex].price_list;
        if (!data) {
            return false;
        }
        data = JSON.parse(data);
        let htmlArr = [];
        let color = 'green';
        if (data.length > 0) {
            htmlArr.push('<table class="layui-table table-status"><thead>' +
                '<th style="text-align: center">价格名称</th>' +
                '<th style="text-align: center">人民币利润系数(%)</th>' +
                '</tr></thead><tbody>')
            for (let i = 0; i < data.length; i++) {
                if (data[i].price_name) {
                    htmlArr.push(
                        '<tr>' +
                        '  <td style="text-align: center">' + data[i].price_name + '</td>' +
                        '  <td style="text-align: center">' + (data[i].ratio ? data[i].ratio : '') + '</td>' +
                        '</tr>');
                }
            }
        }
        htmlArr.push('</tbody></table>')
        ladderPriceTipsVal = layer.tips(htmlArr.join(''), self, {
            tips: [3, "#009688"],
            time: 1000000,
            area: ['400px', 'auto'],
            skin: 'custom'
        });
    }).on('mouseleave', '.view_price_list', function () {
        layer.close(ladderPriceTipsVal);
    });

    let auditPriceListTipsVal = '';
    $(document).on('mouseenter', '.view_audit_price_list', function () {
        let self = this;
        let rowIndex = $(this).parent().parent().parent().attr('data-index');
        let data = table.cache['list'][rowIndex].audit_price_list;
        if (!data) {
            return false;
        }
        data = JSON.parse(data);
        console.log(data);
        let htmlArr = [];
        let color = 'green';
        if (data.length > 0) {
            htmlArr.push('<table class="layui-table table-status"><thead>' +
                '<th style="text-align: center">价格名称</th>' +
                '<th style="text-align: center">人民币利润系数(%)</th>' +
                '</tr></thead><tbody>')
            for (let i = 0; i < data.length; i++) {
                if (data[i].price_name) {
                    htmlArr.push(
                        '<tr>' +
                        '  <td style="text-align: center">' + data[i].price_name + '</td>' +
                        '  <td style="text-align: center">' + (data[i].ratio ? data[i].ratio : '') + '</td>' +
                        '</tr>');
                }
            }
        }
        htmlArr.push('</tbody></table>')
        auditPriceListTipsVal = layer.tips(htmlArr.join(''), self, {
            tips: [3, "#009688"],
            time: 1000000,
            area: ['400px', 'auto'],
            skin: 'custom'
        });
    }).on('mouseleave', '.view_audit_price_list', function () {
        layer.close(auditPriceListTipsVal);
    });



    form.on('submit(load)', function (data) {
        table.reload('list', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field,
        });
    });

    table.on('tool(list)', function (obj) {
        let event = obj.event;
        let id = obj.data.id;
        if (event === 'edit' || event === 'audit') {
            let isAudit = event === 'audit' ? 1 : 0;
            layer.open({
                type: 2,
                area: ['95%', '95%'],
                fixed: false,
                title: '编辑展示价格 - ' + (isAudit ? '审核' : '修改'),
                content: '/web/customPrice/saveCustomPrice?id=' + id + '&is_audit=' + isAudit,
                end: function () {
                    table.reload('list', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: where,
                    });
                }
            });
        }
        if (event === 'enable' || event === 'disable') {
            let text = event === 'enable' ? '启用' : '禁用';
            layer.confirm('你确定要' + text + '该展示价格配置吗？', {
                icon: 5,
                time: 100000,
                btn: ['确定', '取消'],
            }, function (index) {
                $.post('/api/customPrice/enableCustomPrice', {
                    id: id,
                    status: event === 'enable' ? 1 : -1
                }, function (res) {
                    if (res.code === 0) {
                        layer.msg(res.msg, { icon: 6 });
                        table.reload('list', {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                        });
                    } else {
                        layer.msg(res.msg, { icon: 5 });
                    }
                }).fail(function (res) {
                    layer.msg(res.msg, { icon: 5 });
                });
            });
        }
    });

});
