layui.use(['form', 'laydate', 'element', 'table', 'index', 'admin'], function () {
    const form = layui.form;
    const admin = layui.admin;
    const table = layui.table;
    const laydate = layui.laydate;
    const index = layui.index;
    let where = {};

    table.render({
        elem: '#list',
        url: '/api/userLottery/getUserLotteryList',
        method: 'post',
        size: 'sm',
        cellMinWidth: 80,//全局定义常规单元格的最小宽度
        request: {
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        toolbar: '#toolbar',
        defaultToolbar: ['filter'],
        where: {},
        limit: 15,
        loading: true,
        cols: [[
            {field: 'user_prize_id', title: '抽奖编号', align: 'center', width: 100},
            {field: 'org_name', title: '组织', align: 'center', width: 70},
            {field: 'user_account', title: '用户账号', align: 'center', width: 120},
            {field: 'user_id', title: '用户ID', align: 'center', width: 100},
            {field: 'lottery_name', title: '抽奖名称', align: 'center', width: 150},
            {field: 'prize_type_str', title: '奖品类型', align: 'center', width: 80},
            {field: 'prize_name', title: '奖品名称', align: 'center', width: 150},
            {field: 'prize_get_time', title: '中奖时间', align: 'center', width: 150},
            {field: 'is_received', title: '领奖状态', align: 'center', width: 80},
            {field: 'draw_type_str', title: '抽奖类型', align: 'center', width: 80},
            {field: 'consignee', title: '收货人名称', align: 'center', width: 100},
            {field: 'mobile', title: '收货人号码', align: 'center', width: 100},
            {field: 'send_address', title: '发放地址', align: 'center'},
            {field: 'edit', title: '操作', templet: '#edit', width: 70, fixed: 'right'},
        ]],
        id: 'list',
        page: {}
    });

    form.on('submit(load)', function (data) {
        where = data.field;
        table.reload('list', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field,
        });
    });

    table.on('toolbar(list)', function (obj) {
        let checkStatus = table.checkStatus('list');
        let data = checkStatus.data;
        switch (obj.event) {
            case "add":
                layer.open({
                    type: 2,
                    area: ['70%', '70%'],
                    fixed: false,
                    title: '新增中奖名单',
                    content: '/web/userLottery/addUserLottery',
                    end: function () {
                        table.reload('list', {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: where,
                        });
                    }
                });
                break;
        }
    });

    table.on('tool(list)', function (obj) {
        let event = obj.event;
        let data = obj.data;
        if (event === "issue") {
            if (data.prize_type === 1) {
                layer.open({
                    type: 2,
                    area: ['50%', '50%'],
                    fixed: false,
                    title: '发放奖品',
                    content: "/web/userLottery/issueUserLottery?user_id=" + data.user_id + "&prize_id=" + data.prize_id + "&lottery_id=" + data.lottery_id + "&draw_type=" + data.draw_type + "&platform=" + data.platform + "&user_prize_id=" + data.user_prize_id,
                    end: function () {
                        table.reload('list', {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: where,
                        });
                    }
                });
            }

            if (data.prize_type === 3) {
                $.ajax({
                    type: 'post',
                    url: '/mktapi/issueprizeother',
                    timeout: 10000, //超时时间设置，单位毫秒
                    data: {
                        user_prize_id: obj.user_prize_id,
                        lottery_id: obj.lottery_id
                    },
                    dataType: 'json',
                    success: function (resp) {
                        if (!resp) {
                            layer.msg('网络异常，请重试', {icon: 5});
                            return false;
                        }
                        if (resp.code === 0) {
                            layer.msg(resp.msg, {icon: 6});
                            admin.closeThisDialog();
                        } else {
                            layer.msg(resp.msg, {icon: 5});
                            return false;
                        }
                    },

                    error: function (jqXHR, textStatus, errorThrown) {
                        layer.msg("网络异常，请重试", {icon: 5});
                    }

                });
            }
        }

    });
});
