layui.use(['table', 'form', 'laydate', 'xmSelect', 'admin', 'index', 'upload'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;
    let upload = layui.upload;

    layui.use('form', function () {
        //监听提交
        form.on('submit(saveForm)', function (data) {
            event.preventDefault();
            $.ajax({
                type: 'post',
                url: '/api/userLottery/addUserLottery',
                timeout: 10000, //超时时间设置，单位毫秒
                data: {
                    lottery_id: $('#choose-activity option:selected').val(),
                    lottery_name: $('#choose-activity option:selected').text(),
                    user_info: $('#user-info').val(),
                    prize_id: $('#choose-prize option:selected').val(),
                    prize_name: $('#choose-prize option:selected').text(),
                    platform: 2,
                    draw_type: 2,
                    is_sent: 2,
                },
                dataType: 'json',
                success: function (resp) {
                    if (!resp) {
                        layer.msg('网络异常，请重试', {icon: 5});
                        return false;
                    }
                    if (resp.code === 0) {
                        layer.msg(resp.msg, {icon: 6});
                        admin.closeThisDialog();
                    } else {
                        layer.msg(resp.msg, {icon: 5});
                        return false;
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    layer.msg("网络异常，请重试", {icon: 5});
                },

            });
        });
        $('.cancel').click(function () {
            admin.closeThisDialog();
        });
    });

    form.on('select(choose-activity)', function (data) {
        window.location.href = "/web/userLottery/addUserLottery?lottery_id=" + data.value + "&user_info=" + $('#user-info').val();
    });


});

