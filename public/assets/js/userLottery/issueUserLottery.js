layui.use(['form', 'admin'], function () {
    var form = layui.form;
    var admin = layui.admin;

    form.on('select', function () {
        var user_id = $('#user-id').val();
        var prize_id = $('#prize-id').val();
        var draw_type = $('#draw-type').val();
        var platform = $('#platform').val();
        var lottery_id = $('#lottery-id').val();
        var user_prize_id = $('#user-prize-id').val();
        var consignee = $('#consignee').val();
        var province = $('#province option:selected').val();
        var city = $('#city option:selected').val();
        var district = $('#district option:selected').val();
        var detail_address = $('#detail-address').text();
        var mobile = $('#mobile').val();
        var awb_no = $('#awb_no').val();
        window.location.href = "/web/userLottery/issueUserLottery?user_id=" + user_id + "&consignee=" + consignee +
            "&province=" + province + "&city=" + city + "&district=" + district + "&detail_address=" + detail_address +
            "&mobile=" + mobile + "&awb_no=" + awb_no + "&prize_id=" + prize_id + "&draw_type=" + draw_type + "&platform=" + platform + "&lottery_id=" + lottery_id + "&user_prize_id=" + user_prize_id + "&request_type=" + 1;
    });

    //监听提交
    form.on('submit(issue-button)', function (data) {
        issuePrize(JSON.stringify(data.field));
        return false;
    });

    function issuePrize(data) {
        $.ajax({
            type: 'post',
            url: '/api/userLottery/issueUserLottery',
            timeout: 10000, //超时时间设置，单位毫秒
            data: {
                data: data,
            },
            dataType: 'json',
            success: function (resp) {
                if (!resp) {
                    layer.msg('网络异常，请重试', {icon: 5});
                    return false;
                }
                if (resp.code === 0) {
                    layer.msg(resp.msg, {icon: 6});
                    admin.closeThisDialog();
                } else {
                    layer.msg(resp.msg, {icon: 5});
                    return false;
                }
            },

            error: function (jqXHR, textStatus, errorThrown) {
                layer.msg("网络异常，请重试", {icon: 5});
            },

        });
    }

});
