layui.use(['form', 'laydate', 'element', 'table', 'index', 'xmSelect'], function () {
    const form = layui.form;
    const table = layui.table;
    const layer = layui.layer;
    const index = layui.index;
    const xmSelect = layui.xmSelect;
    let where = {};

    table.render({
        elem: '#list',
        url: '/api/shippingRule/getShippingRuleList',
        method: 'post',
        size: 'sm',
        cellMinWidth: 80,
        request: {
            limitName: 'limit'
        },
        toolbar: '#toolbar',
        defaultToolbar: ['filter', 'exports', 'print'],
        cols: [[
            { field: 'id', title: '序号', align: 'center', width: 80 },
            {
                field: 'supplier_id', title: '渠道', align: 'center', width: 80, templet: function (d) {
                    return d.supplier ? d.supplier.supplier_name : '';
                }
            },
            {
                field: 'supplier_code', title: '供应商编码', align: 'center', width: 100, templet: function (d) {
                    return d.supplier_code ? d.supplier_code.supplier_code : '';
                }
            },
            {
                field: 'supplier_name', title: '供应商名称', align: 'center', width: 250, templet: function (d) {
                    return d.supplier_code ? d.supplier_code.supplier_name : '';
                }
            },
            {
                field: 'status', title: '状态', align: 'center', width: 80, templet: function (d) {
                    if (d.status == 1) {
                        return '<span class="layui-badge layui-bg-green">启用</span>';
                    } else if (d.status == -1) {
                        return '<span class="layui-badge layui-btn-danger">禁用</span>';
                    } else {
                        return '<span class="layui-badge">未知</span>';
                    }
                },
            },
            {
                field: 'type_name', title: '配置方式', align: 'center', width: 120
            },
            {
                field: 'rule_format', title: '规则内容', align: 'center', templet: function (d) {
                    if (!d.rule_format) {
                        return '';
                    }
                    return '<a target="" style="color:#1224CC !important" href="javascript:void(0)" class="view-rule" data-content="' + d.rule_format.replace(/"/g, '&quot;') + '">查看规则</a>';
                }
            },
            { field: 'create_name', title: '创建人', align: 'center', width: 80 },
            { field: 'create_time', title: '创建时间', align: 'center', width: 150 },
            { field: 'update_name', title: '更新人', align: 'center', width: 80 },
            { field: 'update_time', title: '更新时间', align: 'center', width: 150 },
            { field: 'edit', title: '操作', templet: '#edit', width: 180, fixed: 'right' },
        ]],
        id: 'list',
        page: {}
    });

    form.on('submit(load)', function (data) {
        where = data.field;
        table.reload('list', {
            page: {
                curr: 1
            },
            where: data.field,
        });
    });

    form.on('submit(reset)', function (data) {
        where = {};
        window.location.reload();
    });

    table.on('toolbar(list)', function (obj) {
        let checkStatus = table.checkStatus('list');
        let data = checkStatus.data;
        switch (obj.event) {
            case "add":
                layer.open({
                    type: 2,
                    area: ['80%', '90%'],
                    fixed: false,
                    title: '新增运费规则',
                    content: '/web/shippingRule/saveShippingRule',
                    end: function () {
                        table.reload('list', {
                            page: {
                                curr: 1
                            },
                            where: where,
                        });
                    }
                });
                break;
        }
    });

    table.on('tool(list)', function (obj) {
        let data = obj.data;
        if (obj.event === 'edit') {
            layer.open({
                type: 2,
                area: ['70%', '70%'],
                fixed: false,
                title: '编辑运费规则',
                content: '/web/shippingRule/saveShippingRule?id=' + data.id,
                end: function () {
                    table.reload('list', {
                        page: {
                            curr: 1
                        },
                        where: where,
                    });
                }
            });
        } else if (obj.event === 'disable') {
            layer.confirm('确定要禁用该运费规则吗？', function (index) {
                $.ajax({
                    url: '/api/shippingRule/changeStatus',
                    type: 'post',
                    data: {
                        id: data.id,
                        status: -1
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, { icon: 6 });
                            table.reload('list', {
                                page: {
                                    curr: 1
                                },
                                where: where,
                            });
                        } else {
                            layer.msg(res.msg, { icon: 5 });
                        }
                    }
                });
                layer.close(index);
            });
        } else if (obj.event === 'enable') {
            layer.confirm('确定要启用该运费规则吗？', function (index) {
                $.ajax({
                    url: '/api/shippingRule/changeStatus',
                    type: 'post',
                    data: {
                        id: data.id,
                        status: 1
                    },
                    dataType: 'json',
                    success: function (res) {
                        if (res.code === 0) {
                            layer.msg(res.msg, { icon: 6 });
                            table.reload('list', {
                                page: {
                                    curr: 1
                                },
                                where: where,
                            });
                        } else {
                            layer.msg(res.msg, { icon: 5 });
                        }
                    }
                });
                layer.close(index);
            });
        } else if (obj.event === 'log') {
            layer.open({
                type: 2,
                area: ['70%', '70%'],
                fixed: false,
                title: '操作日志',
                content: '/web/shippingRule/operationLog?id=' + data.id,
            });
        }
    });

    // 鼠标悬停显示规则内容
    let ruleContentTips = null;
    $(document).on('mouseenter', '.view-rule', function () {
        const content = $(this).data('content').replace(/\<br\>/g, '<br/>');
        ruleContentTips = layer.tips(content, this, {
            tips: [3, '#009688'],
            time: 0,
            area: ['400px', 'auto'],
            maxHeight: 400
        });
    }).on('mouseleave', '.view-rule', function () {
        layer.close(ruleContentTips);
    });

    // 点击按钮显示规则内容
    $(document).on('click', '.view-rule', function () {
        const content = $(this).data('content').replace(/\<br\>/g, '<br/>');
        layer.open({
            type: 1,
            title: '规则内容',
            area: ['500px', 'auto'],
            content: '<div style="padding: 20px;">' + content + '</div>'
        });
        return false; // 防止事件冒泡
    });
});
