layui.use(['form', 'layer', 'admin', 'index'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;

    form.on('submit(saveForm)', function (data) {
        verifyDataAndSave(data.field);
    });

    $('.cancel').click(function () {
        admin.closeThisDialog();
    });

    //监听supplier_id变化,如果值为17,那么就展示supplier_code,否则隐藏
    form.on('select(supplier_id)', function (data) {
        if (data.value == 17) {
            $('#supplier_code_selector').show();
        } else {
            $('#supplier_code_selector').hide();
        }
    });

    // 监听运费类型单选框变化
    form.on('radio(type)', function (data) {
        switch (data.value) {
            case '1':
                $('#rule_div > div:first').show().siblings().hide();
                break;
            case '2':
                $('#rule_div > div:nth-child(2)').show().siblings().hide();
                break;
            case '3':
                $('#rule_div > div:nth-child(3)').show().siblings().hide();
                break;
            case '4':
                $('#rule_div > div:nth-child(4)').show().siblings().hide();
                break;
        }
    });


    /*
    order_amount_section的逻辑
    */

    form.on('switch(is_change_rate_cny)', function (data) {
        const $row = $(this).closest('tr');
        const $inputs = $row.find('input[type="number"]');
        let value = data.elem.checked ? 1 : -1;
        if (data.elem.checked) {
            $inputs.val('').attr('readonly', true).addClass('layui-disabled');
        } else {
            $inputs.attr('readonly', false).removeClass('layui-disabled');
        }
        $('#is_change_rate_cny').val(value);
    });

    form.on('switch(is_change_rate_usd)', function (data) {
        const $row = $(this).closest('tr');
        const $inputs = $row.find('input[type="number"]');
        let value = data.elem.checked ? 1 : -1;
        if (data.elem.checked) {
            $inputs.val('').attr('readonly', true).addClass('layui-disabled');
        } else {
            $inputs.attr('readonly', false).removeClass('layui-disabled');
        }
        $('#is_change_rate_usd').val(value);
    });

    $(document).ready(function () {
        // 检查CNY开关状态
        if ($('#is_change_rate_cny').prop('checked')) {
            const $row = $('#is_change_rate_cny').closest('tr');
            const $inputs = $row.find('input[type="number"]');
            $inputs.val('').attr('readonly', true).addClass('layui-disabled');
        }

        // 检查USD开关状态
        if ($('#is_change_rate_usd').prop('checked')) {
            const $row = $('#is_change_rate_usd').closest('tr');
            const $inputs = $row.find('input[type="number"]');
            $inputs.val('').attr('readonly', true).addClass('layui-disabled');
        }
    });


    /*
    order_amount_ladder_section的逻辑
    */

    form.on('switch(ladder_is_change_rate_cny)', function (data) {
        const $row = $(this).closest('tr');
        const $inputs = $row.find('input[type="number"]');
        let value = data.elem.checked ? 1 : -1;
        if (data.elem.checked) {
            $inputs.val('').attr('readonly', true).addClass('layui-disabled');
        } else {
            $inputs.attr('readonly', false).removeClass('layui-disabled');
        }
        $('#ladder_is_change_rate_cny').val(value);
    });

    $(document).ready(function () {
        // 检查CNY开关状态
        if ($('#ladder_is_change_rate_cny').prop('checked')) {
            const $row = $('#ladder_is_change_rate_cny').closest('tr');
            const $inputs = $row.find('input[type="number"]');
            $inputs.val('').attr('readonly', true).addClass('layui-disabled');
        }

        // 检查USD开关状态
        if ($('#ladder_is_change_rate_usd').prop('checked')) {
            const $row = $('#ladder_is_change_rate_usd').closest('tr');
            const $inputs = $row.find('input[type="number"]');
            $inputs.val('').attr('readonly', true).addClass('layui-disabled');
        }
    });

    form.on('switch(ladder_is_change_rate_usd)', function (data) {
        const $row = $(this).closest('tr');
        const $inputs = $row.find('input[type="number"]');
        let value = data.elem.checked ? 1 : -1;
        if (data.elem.checked) {
            $inputs.val('').attr('readonly', true).addClass('layui-disabled');
        } else {
            $inputs.attr('readonly', false).removeClass('layui-disabled');
        }
        $('#ladder_is_change_rate_usd').val(value);
    });

    /*
    order_sku_num_section的逻辑
    */
    form.on('switch(count_is_change_rate_cny)', function (data) {
        // 获取当前switch所在的列索引
        const colIndex = $(this).closest('td').index();
        if (data.elem.checked) {
            // 禁用CNY列所有input并清空值
            $('#sku-count-table tbody tr:not(:first)').each(function () {
                const input = $(this).find('td').eq(colIndex).find('input');
                input.val('');  // 清空值
                input.attr('readonly', true).addClass('layui-disabled');
            });
        } else {
            // 启用CNY列所有input
            $('#sku-count-table tbody tr:not(:first)').each(function () {
                $(this).find('td').eq(colIndex).find('input')
                    .attr('readonly', false)
                    .removeClass('layui-disabled');
            });
        }
        $('#count_is_change_rate_cny').val(data.elem.checked ? 1 : -1);
    });

    form.on('switch(count_is_change_rate_usd)', function (data) {
        // 获取当前switch所在的列索引
        const colIndex = $(this).closest('td').index();
        if (data.elem.checked) {
            // 禁用USD列所有input并清空值
            $('#sku-count-table tbody tr:not(:first)').each(function () {
                const input = $(this).find('td').eq(colIndex).find('input');
                input.val('');  // 清空值
                input.attr('readonly', true).addClass('layui-disabled');
            });
        } else {
            // 启用USD列所有input
            $('#sku-count-table tbody tr:not(:first)').each(function () {
                $(this).find('td').eq(colIndex).find('input')
                    .attr('readonly', false)
                    .removeClass('layui-disabled');
            });
        }
        $('#count_is_change_rate_usd').val(data.elem.checked ? 1 : -1);
    });

    // 添加型号数量行
    $(document).on('click', '.add-sku-count', function () {
        // 检查上一行的最大数量是否已填写
        let lastMaxCount = $('.sku-count-item:last').find('.max-count').val();
        if (!lastMaxCount) {
            layer.msg('请先填写上一行的最大数量', {icon: 5});
            return;
        }

        let index = $('.sku-count-item').length;

        // 新行的最小数量等于上一行的最大数量 + 1
        let newMinCount = parseInt(lastMaxCount) + 1;

        let newRow = `
            <tr class="sku-count-item">
                <td>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="number" name="sku_count[${index}][min_count]" value="${newMinCount}" class="layui-input min-count layui-disabled" readonly step="1" min="0">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="number" name="sku_count[${index}][max_count]" value="" class="layui-input max-count" step="1" min="0" placeholder="留空代表无限">
                    </div>
                </td>
                <td>
                    <input type="number" name="sku_count[${index}][cny_fee]" value="" class="layui-input" step="0.01" min="0">
                </td>
                <td>
                    <input type="number" name="sku_count[${index}][usd_fee]" value="" class="layui-input" step="0.01" min="0">
                </td>
                <td>
                    <div class="layui-btn-group">
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal add-sku-count"><i class="layui-icon">&#xe654;</i></button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-sku-count"><i class="layui-icon">&#xe640;</i></button>
                    </div>
                </td>
            </tr>
        `;
        let $newRow = $(newRow);
        $('#sku-count-body').append($newRow);

        // 检查CNY开关状态
        if ($('#count_is_change_rate_cny').prop('checked')) {
            const cnyColIndex = $('#count_is_change_rate_cny').closest('td').index();
            const cnyInput = $newRow.find('td').eq(cnyColIndex).find('input');
            cnyInput.val('');
            cnyInput.attr('readonly', true).addClass('layui-disabled');
        }

        // 检查USD开关状态
        if ($('#count_is_change_rate_usd').prop('checked')) {
            const usdColIndex = $('#count_is_change_rate_usd').closest('td').index();
            const usdInput = $newRow.find('td').eq(usdColIndex).find('input');
            usdInput.val('');
            usdInput.attr('readonly', true).addClass('layui-disabled');
        }
    });

    $(document).ready(function () {
        let checked = $('#count_is_change_rate_cny').prop('checked');
        // 检查CNY开关状态
        const colIndex = $('#count_is_change_rate_cny').closest('td').index();
        if (checked) {
            // 禁用CNY列所有input并清空值
            $('#sku-count-table tbody tr:not(:first)').each(function () {
                const input = $(this).find('td').eq(colIndex).find('input');
                input.val('');  // 清空值
                input.attr('readonly', true).addClass('layui-disabled');
            });
        } else {
            // 启用CNY列所有input
            $('#sku-count-table tbody tr:not(:first)').each(function () {
                $(this).find('td').eq(colIndex).find('input')
                    .attr('readonly', false)
                    .removeClass('layui-disabled');
            });
        }
        $('#count_is_change_rate_cny').val(checked ? 1 : -1);

        // 检查USD开关状态
        checked = $('#count_is_change_rate_usd').prop('checked');
        const usdColIndex = $('#count_is_change_rate_usd').closest('td').index();
        if (checked) {
            // 禁用USD列所有input并清空值
            $('#sku-count-table tbody tr:not(:first)').each(function () {
                const input = $(this).find('td').eq(usdColIndex).find('input');
                input.val('');  // 清空值
                input.attr('readonly', true).addClass('layui-disabled');
            });
        } else {
            // 启用USD列所有input
            $('#sku-count-table tbody tr:not(:first)').each(function () {
                $(this).find('td').eq(usdColIndex).find('input')
                    .attr('readonly', false)
                    .removeClass('layui-disabled');
            });
        }
        $('#count_is_change_rate_usd').val(checked ? 1 : -1);
    });

    // 删除型号数量行
    $(document).on('click', '.remove-sku-count', function () {
        // 如果只有一行，不允许删除
        if ($('.sku-count-item').length > 1) {
            // 检查是否是最后一行
            let currentRow = $(this).closest('tr');
            if (currentRow.is(':last-child')) {
                currentRow.remove();
                // 重新排序索引
                $('.sku-count-item').each(function (index) {
                    $(this).find('input').each(function () {
                        let name = $(this).attr('name');
                        let newName = name.replace(/sku_count\[\d+\]/, `sku_count[${index}]`);
                        $(this).attr('name', newName);
                    });
                });
            } else {
                layer.msg('只能删除最后一行', {icon: 5});
            }
        } else {
            layer.msg('至少保留一行型号数量配置', {icon: 5});
        }
    });

    // 初始化第一行的最小数量为1且不可编辑
    $(document).ready(function() {
        $('.sku-count-item:first').find('.min-count')
            .val('1')
            .addClass('layui-disabled')
            .attr('readonly', true);
    });


    /*
    order_sku_weight_section的逻辑
    */
    form.on('switch(weight_is_change_rate_cny)', function (data) {
      // 获取当前switch所在的列索引
      const colIndex = $(this).closest('td').index();
      if (data.elem.checked) {
          // 禁用CNY列所有input并清空值
          $('#sku-weight-table tbody tr:not(:first)').each(function () {
              const input = $(this).find('td').eq(colIndex).find('input');
              input.val('');  // 清空值
              input.attr('readonly', true).addClass('layui-disabled');
          });
      } else {
          // 启用CNY列所有input
          $('#sku-weight-table tbody tr:not(:first)').each(function () {
              $(this).find('td').eq(colIndex).find('input')
                  .attr('readonly', false)
                  .removeClass('layui-disabled');
          });
      }
      $('#weight_is_change_rate_cny').val(data.elem.checked ? 1 : -1);
    });

    form.on('switch(weight_is_change_rate_usd)', function (data) {
      // 获取当前switch所在的列索引
      const colIndex = $(this).closest('td').index();
      if (data.elem.checked) {
          // 禁用CNY列所有input并清空值
          $('#sku-weight-table tbody tr:not(:first)').each(function () {
              const input = $(this).find('td').eq(colIndex).find('input');
              input.val('');  // 清空值
              input.attr('readonly', true).addClass('layui-disabled');
          });
      } else {
          // 启用CNY列所有input
          $('#sku-weight-table tbody tr:not(:first)').each(function () {
              $(this).find('td').eq(colIndex).find('input')
                  .attr('readonly', false)
                  .removeClass('layui-disabled');
          });
      }
      $('#weight_is_change_rate_usd').val(data.elem.checked ? 1 : -1);
    });

    // 添加型号重量行
    $(document).on('click', '.add-sku-weight', function () {
        // 检查上一行的最大重量是否已填写
        let lastMaxWeight = $('.sku-weight-item:last').find('.max-weight').val();
        if (!lastMaxWeight) {
            layer.msg('请先填写上一行的最大重量', {icon: 5});
            return;
        }

        let index = $('.sku-weight-item').length;

        // 新行的最小重量等于上一行的最大重量
        let newMinWeight = parseFloat(lastMaxWeight);

        let newRow = `
            <tr class="sku-weight-item">
                <td>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="number" name="sku_weight[${index}][min_weight]" value="${newMinWeight.toFixed(4)}" class="layui-input min-weight layui-disabled" readonly step="0.0001" min="0">
                    </div>
                    <div class="layui-form-mid">-</div>
                    <div class="layui-input-inline" style="width: 100px;">
                        <input type="number" name="sku_weight[${index}][max_weight]" value="" class="layui-input max-weight" step="0.0001" min="0" placeholder="留空代表无限">
                    </div>
                </td>
                <td>
                    <input type="number" name="sku_weight[${index}][cny_fee]" value="" class="layui-input" step="0.01" min="0">
                </td>
                <td>
                    <input type="number" name="sku_weight[${index}][usd_fee]" value="" class="layui-input" step="0.01" min="0">
                </td>
                <td>
                    <div class="layui-btn-group">
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-normal add-sku-weight"><i class="layui-icon">&#xe654;</i></button>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger remove-sku-weight"><i class="layui-icon">&#xe640;</i></button>
                    </div>
                </td>
            </tr>
        `;
        let $newRow = $(newRow);
        $('#sku-weight-body').append($newRow);

        // 检查CNY开关状态
        if ($('#weight_is_change_rate_cny').prop('checked')) {
            const cnyColIndex = $('#weight_is_change_rate_cny').closest('td').index();
            const cnyInput = $newRow.find('td').eq(cnyColIndex).find('input');
            cnyInput.val('');
            cnyInput.attr('readonly', true).addClass('layui-disabled');
        }

        // 检查USD开关状态
        if ($('#weight_is_change_rate_usd').prop('checked')) {
            const usdColIndex = $('#weight_is_change_rate_usd').closest('td').index();
            const usdInput = $newRow.find('td').eq(usdColIndex).find('input');
            usdInput.val('');
            usdInput.attr('readonly', true).addClass('layui-disabled');
        }
    });

    // 删除型号重量行
    $(document).on('click', '.remove-sku-weight', function () {
        // 如果只有一行，不允许删除
        if ($('.sku-weight-item').length > 1) {
            // 检查是否是最后一行
            let currentRow = $(this).closest('tr');
            if (currentRow.is(':last-child')) {
                currentRow.remove();
                // 重新排序索引
                $('.sku-weight-item').each(function (index) {
                    $(this).find('input').each(function () {
                        let name = $(this).attr('name');
                        let newName = name.replace(/sku_weight\[\d+\]/, `sku_weight[${index}]`);
                        $(this).attr('name', newName);
                    });
                });
            } else {
                layer.msg('只能从最后一行开始删除', {icon: 5});
            }
        } else {
            layer.msg('至少保留一行型号重量配置', {icon: 5});
        }
    });

    // 监听型号重量相关的input，补全四位小数
    $(document).on('blur', '.sku-weight-item input[type="number"]', function () {
        const value = $(this).val();
        if (value !== '') {
            // 检查是否是max_weight的输入框
            if ($(this).hasClass('max-weight')) {
                // 转为数字并固定4位小数
                const formattedValue = Number(value).toFixed(4);
                $(this).val(formattedValue);
            }
        }
    });

    // 初始化第一行的最小重量为0且不可编辑
    $(document).ready(function() {
        $('.sku-weight-item:first').find('.min-weight')
            .val('0.0000')
            .addClass('layui-disabled')
            .attr('readonly', true);
    });

    // 限制输入，最多允许4位小数
    $(document).on('input', '.sku-weight-item input[type="number"]', function () {
        const value = $(this).val();
        if (value !== '') {
            // 检查是否是min_weight或max_weight的输入框
            if ($(this).hasClass('min-weight') || $(this).hasClass('max-weight')) {
                const parts = value.split('.');
                if (parts.length > 1 && parts[1].length > 4) {
                    // 如果小数位超过4位，截取前4位
                    $(this).val(Number(value).toFixed(4));
                }
            }
        }
    });

    $(document).ready(function () {
        let checked = $('#weight_is_change_rate_cny').prop('checked');
        // 检查CNY开关状态
        const colIndex = $('#weight_is_change_rate_cny').closest('td').index();
        if (checked) {
            // 禁用CNY列所有input并清空值
            $('#sku-weight-table tbody tr:not(:first)').each(function () {
                const input = $(this).find('td').eq(colIndex).find('input');
                input.val('');  // 清空值
                input.attr('readonly', true).addClass('layui-disabled');
            });
        } else {
            // 启用CNY列所有input
            $('#sku-weight-table tbody tr:not(:first)').each(function () {
                $(this).find('td').eq(colIndex).find('input')
                    .attr('readonly', false)
                    .removeClass('layui-disabled');
            });
        }
        $('#weight_is_change_rate_cny').val(checked ? 1 : -1);

        // 检查USD开关状态
        checked = $('#weight_is_change_rate_usd').prop('checked');
        const usdColIndex = $('#weight_is_change_rate_usd').closest('td').index();
        if (checked) {
            // 禁用USD列所有input并清空值
            $('#sku-weight-table tbody tr:not(:first)').each(function () {
                const input = $(this).find('td').eq(usdColIndex).find('input');
                input.val('');  // 清空值
                input.attr('readonly', true).addClass('layui-disabled');
            });
        } else {
            // 启用USD列所有input
            $('#sku-weight-table tbody tr:not(:first)').each(function () {
                $(this).find('td').eq(usdColIndex).find('input')
                    .attr('readonly', false)
                    .removeClass('layui-disabled');
            });
        }
        $('#weight_is_change_rate_usd').val(checked ? 1 : -1);
    });


    function verifyDataAndSave(formData) {
        saveShippingRule(formData);
    }

    function saveShippingRule(formData) {
        admin.btnLoading('.saveForm', '保存中');

        $.ajax({
            url: '/api/shippingRule/saveShippingRule',
            type: 'post',
            data: formData,
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (!res) return layer.msg('网络错误，请重试', { icon: 5 });
                if (res.code === 0) {
                    admin.btnLoading('.saveForm', false);
                    admin.btnLoading('.cancel', false);
                    layer.msg(res.msg, { icon: 6 });
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 500);
                } else {
                    admin.btnLoading('.saveForm', false);
                    admin.btnLoading('.cancel', false);
                    layer.msg(res.msg, { icon: 5 });
                }
            },
            error: function () {
                admin.btnLoading('.saveForm', false);
                admin.btnLoading('.cancel', false);
                return layer.msg('网络错误，请重试', { icon: 5 });
            }
        });
    }
});
