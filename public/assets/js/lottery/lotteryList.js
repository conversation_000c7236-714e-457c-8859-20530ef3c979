layui.use(['form', 'laydate', 'element', 'table', 'index'], function () {
    const form = layui.form;
    const table = layui.table;
    const laydate = layui.laydate;
    const index = layui.index;
    let where = {};

    table.render({
        elem: '#list',
        url: '/api/lottery/getLotteryList',
        method: 'post',
        size: 'sm',
        cellMinWidth: 80,//全局定义常规单元格的最小宽度
        request: {
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        toolbar: '#toolbar',
        defaultToolbar: ['filter'],
        where: {},
        limit: 15,
        loading: true,
        cols: [[
            {field: 'lottery_id', title: 'ID', align: 'center', width: 50},
            {field: 'org_name', title: '组织名称', align: 'center',width: 60},
            {field: 'lottery_name', title: '抽奖名称', align: 'center'},
            {
                field: 'time_range', title: '生效时间', align: 'center', width: 230, templet: function (data) {
                    return data.start_time + ' - ' + data.end_time;
                }
            },
            {field: 'status_name', title: '抽奖状态', align: 'center', width: 80},
            {
                field: 'create_name', title: '创建人', align: 'center', width: 100, templet: function (data) {
                    return data.user_info ? data.user_info.name : '';
                }
            },
            {field: 'create_time', title: '创建时间', align: 'center', width: 160},
            {field: 'edit', title: '操作', templet: '#edit', width: 150, fixed: 'right'},
        ]],
        id: 'list',
        page: {}
    });

    form.on('submit(load)', function (data) {
        where = data.field;
        table.reload('list', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field,
        });
    });
    table.on('toolbar(list)', function (obj) {
        let checkStatus = table.checkStatus('list');
        let data = checkStatus.data;
        switch (obj.event) {
            case "add":
                layer.open({
                    type: 2,
                    area: ['95%', '95%'],
                    fixed: false,
                    title: '新增抽奖活动',
                    content: '/web/lottery/saveLottery',
                    end: function () {
                        table.reload('list', {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: where,
                        });
                    }
                });
                break;
        }
    });
    table.on('tool(list)', function (obj) {
        let event = obj.event;
        let lotteryId = obj.data.lottery_id;
        let lotteryName = obj.data.lottery_name;
        if (event === 'edit') {
            layer.open({
                type: 2,
                area: ['95%', '95%'],
                fixed: false,
                title: '编辑抽奖活动 - ' + lotteryName,
                content: '/web/lottery/saveLottery?lottery_id=' + lotteryId,
                end: function () {
                    table.reload('list', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: where,
                    });
                }
            });
        }
        if (event === 'delete') {
            layer.msg('你确定要删除该抽奖活动吗？', {
                icon: 5,
                time: 100000,
                btn: ['确定', '取消'],
                yes: function (index) {
                    const url = '/api/lottery/deleteLottery';
                    const data = {
                        lottery_id: lotteryId
                    };
                    $.post(url, data, function (res) {
                        if (res.code === 0) {
                            table.reload('list');
                            layer.msg("删除成功", {icon: 6});
                        }
                    });
                }
            });
        }
        if (event === 'copy') {
            layer.open({
                type: 2,
                area: ['95%', '95%'],
                fixed: false,
                title: '复制抽奖活动',
                content: '/web/lottery/saveLottery?is_copy=1&lottery_id=' + lotteryId,
                end: function () {
                    table.reload('list', {
                        page: {
                            curr: 1 //重新从第 1 页开始
                        },
                        where: where,
                    });
                }
            });
        }
    });

});
