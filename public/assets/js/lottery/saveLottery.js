layui.use(['table', 'form', 'laydate', 'xmSelect', 'admin', 'index', 'upload'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;
    let upload = layui.upload;

    //组织id,核心的判断
    let orgId = $('input[name="org_id"]:checked').val();
    let isIEdge = (orgId === '3' || orgId === '6' || orgId === '11' || orgId === '12');
    if (isIEdge) {
        //隐藏特定div
        $('input[type="radio"][name="coupon_type"]:eq(1)').next().hide();
        $('#iedge_org_select_div').show();
        $('.iedge_div').show();
        $('.liexin_div').hide();

        //隐藏特定选项
        let divList = $('#qualify_get_ruler_div div');
        divList.eq(0).hide();
        divList.eq(2).hide();
        divList.eq(4).hide();

        let isFirstLogin = $('#is_first_login').val();
        let qualifyLogin = $('#qualify_login').val();
        if (qualifyLogin == 1) {
            $('#is_first_login_div').show();
            $('.is_first_login_div').show();
        } else {
            $('#is_first_login_div').hide();
            $('.is_first_login_div').hide();
        }

        $('#qualify_login').on('input', function () {
            if ($(this).val() == 1) {
                $('#is_first_login_div').show();
                $('.is_first_login_div').show();
            } else {
                $('#is_first_login_div').hide();
                $('.is_first_login_div').hide();
            }
        });
    } else {
        $('.liexin_div').show();
        $('.iedge_div').hide();
        $('#is_first_login_div').hide();
        $('.is_first_login_div').hide();
    }

    form.on('select(iedge_org_id_select)', function (data) {
        $('#iedge_org_id').val(data.value);
    });

    layui.use('form', function () {
        //监听提交
        form.on('submit(saveForm)', function (data) {
            verifyDataAndCreateLottery(data.field);
        });
        $('.cancel').click(function () {
            admin.closeThisDialog();
        });
    });

    let laydate = layui.laydate;
    //时间选择器
    laydate.render({
        elem: 'input[name=start_time]',
        type: 'date'
    });
    laydate.render({
        elem: 'input[name=end_time]',
        type: 'date'
    });

    /**
     * 显隐不同类型的 次数限制
     * @param rule_value
     * @param is_checked
     */
    function hideOrShowRules(rule_value, is_checked) {
        switch (rule_value) {
            case '1':
                if (is_checked) {
                    $('#qualify_register_div_id').show();
                } else {
                    $('#qualify_register').val(0);
                    $('#qualify_register_div_id').hide();
                }
                break;
            case '2':
                if (is_checked) {
                    $('#qualify_login_div_id').show();
                    $('#qualify_login_day_div_id').show();
                } else {
                    $('#qualify_login').val(0);
                    $('#qualify_login_day').val(0);
                    $('#qualify_login_div_id').hide();
                    $('#qualify_login_day_div_id').hide();
                }
                break;
            case '3':
                if (is_checked) {
                    $('#qualify_share_div_id').show();
                } else {
                    $('#qualify_share').val(0);
                    $('#qualify_share_div_id').hide();
                }
                break;
            case '4':
                if (is_checked) {
                    $('#qualify_order_div_id').show();
                    $('#activity_url_div_id').show();
                    $('#pay_suc_banner_div_id').show();
                } else {
                    $('#qualify_order').val(0);
                    $('#activity_url').val('');
                    $('#obj-paysucimg').val('');
                    $('#qualify_order_div_id').hide();
                    $('#activity_url_div_id').hide();
                    $('#pay_suc_banner_div_id').hide();
                }
                break;
            case '5':
                if (is_checked) {
                    $('#qualify_follow_div_id').show();
                } else {
                    $('#qualify_follow').val(0);
                    $('#qualify_follow_div_id').hide();
                }
                break;
            default :
                break;
        }
    }

    //监听抽奖资格获取渠道复选框
    form.on('checkbox(qualify_get_rule)', function (data) {
        console.log(data);
        //根据当前选中 显隐每种次数限制组
        hideOrShowRules(data.value, data.elem.checked);
    });


    //普通图片上传
    var uploadInst = upload.render({
        elem: '.upload-img',
        url: oss_url + '/uploadFile?sys_type=7',
        field: 'file',
        exts: 'jpg|png|bmp',
        before: function (obj) {
            layer.msg('加载中', {
                icon: 16,
                shade: 0.01
            });
            var item = this.item;
            //预读本地文件示例，不支持ie8
            obj.preview(function (index, file, result) {
                $('#' + item.attr('preview')).attr('src', result); //图片链接（base64）
            });
        },
        done: function (res) {
            if (res.code === 0) {
                let fileUrl = res.data.oss_file_url;
                let fileName = res.data.file_name;
                layer.msg('上传成功', {icon: 6});
                var item = this.item;
                $('#' + item.attr('data-obj')).val(fileUrl);
                $('#' + item.attr('data-obj') + '-name').val(fileName);
                $('#' + item.attr('data-obj') + '-href').attr('href', fileUrl);
                $('#' + item.attr('data-obj') + '-href').text(fileName);
                return false;
            } else {
                layer.msg('上传失败', {icon: 5});
                return false;
            }

        },
        error: function (res) {
            layer.msg('上传失败', {icon: 5});
            return false;
        }
    });


    function verifyDataAndCreateLottery(form_data) {
        //表单验证
        if (form_data['qualify_get_rule_register']) {
            if (!$('#qualify_register').val()) {
                Prompt('请输入注册最多赠送抽奖资格次数', 5);
                return false;
            }
        }
        if (form_data['qualify_get_rule_login']) {
            if (!$('#qualify_login').val() && !$('#qualify_login_day').val()) {
                Prompt('请输入登陆最多赠送抽奖资格次数 或 登陆每天赠送抽奖次数', 5);
                return false;
            }
        }
        if (form_data['qualify_get_rule_share']) {
            if (!$('#qualify_share').val()) {
                Prompt('请输入分享最多赠送抽奖资格次数', 5);
                return false;
            }
        }
        if (form_data['qualify_get_rule_order']) {
            if (!$('#qualify_order').val()) {
                Prompt('请输入下单赠送抽奖资格次数', 5);
                return false;
            }
            if (!$('#activity_url').val()) {
                Prompt('请输入支付成功页要跳转的抽奖地址', 5);
                return false;
            }
            if (!$('#obj-paysucimg').val()) {
                Prompt('请上传支付成功活动banner图', 5);
                return false;
            }
        }

        if (form_data['qualify_get_rule_follow']) {
            if (!$('#qualify_follow').val()) {
                Prompt('请输入关注赠送抽奖资格次数', 5);
                return false;
            }
        }

        //奖品验证
        //新增八个奖品的验证
        var prizeArr = new Array();
        var trList = $('#add-coupon-body').children('tr');
        for (var i = 0; i < trList.length; i++) {
            var tdArr = trList.eq(i).find('td');
            var prize_id = tdArr.eq(0).text();
            var level = tdArr.eq(1).find('input').val();
            var prize_type = tdArr.eq(2).find('option:selected').val();
            var prize_name = tdArr.eq(3).find('input').val();
            var prize_value = tdArr.eq(4).find('input').val();
            var prize_num = tdArr.eq(5).find('input').val();
            var prize_send_num_day = tdArr.eq(6).find('input').val();
            var chance = tdArr.eq(7).find('input').val();
            var prize_title = tdArr.eq(8).find('input').val();
            var prize_tips = tdArr.eq(9).find('input').val();
            var prize_img = tdArr.eq(10).find('input.prize_img').val();
            var prize_img_file_name = tdArr.eq(10).find('input.prize_img_file_name').val();

            if (!level) {
                Prompt('请输入第' + (i + 1) + '个奖品等级', 5);
                return false;
            }
            if (!prize_name) {
                Prompt('请输入第' + (i + 1) + '个奖品名称', 5);
                return false;
            }
            if (!prize_value) {
                Prompt('请输入第' + (i + 1) + '个奖品价值', 5);
                return false;
            }
            if (!prize_num) {
                Prompt('请输入第' + (i + 1) + '个奖品数量', 5);
                return false;
            }
            if (!prize_send_num_day) {
                Prompt('请输入第' + (i + 1) + '个每天抽中次数限制', 5);
                return false;
            }
            if (!chance) {
                Prompt('请输入第' + (i + 1) + '个奖品抽中概率', 5);
                return false;
            } else {
                if (chance < 0 || chance > 100) {
                    Prompt('第' + (i + 1) + '个奖品抽中概率填写错误', 5);
                    return false;
                }
            }
            if (!prize_title && !isIEdge) {
                Prompt('请输入第' + (i + 1) + '个中奖标题', 5);
                return false;
            }
            if (!prize_tips && !isIEdge) {
                Prompt('请输入第' + (i + 1) + '个抽中提示', 5);
                return false;
            }
            //如果奖品数量为0，那么每天抽中数量和中奖概率则必须为0
            if (prize_num == 0) {
                if (prize_send_num_day != 0 || chance != 0) {
                    Prompt('如果第' + (i + 1) + '个奖品数量为0，那么每天抽中的数量和中奖概率也必须为0', 5);
                    return false;
                }
            }

            //生成奖品对象
            var prizeObj = {
                prize_id: prize_id,
                level: level,
                prize_type: prize_type,
                prize_name: prize_name,
                prize_value: prize_value,
                prize_num: prize_num,
                prize_send_num_day: prize_send_num_day,
                chance: chance,
                prize_title: prize_title,
                prize_tips: prize_tips,
                prize_img: prize_img,
                prize_img_file_name: prize_img_file_name,
                ex_str: $('#ex_str' + i).val()
            };
            //添加对象到奖品数组中
            prizeArr.push(prizeObj);
        }
        console.log(prizeArr);
        var prize_json = getPrizeJson(prizeArr);
        createLottery(form_data, prize_json);
    }

    function createLottery(form_data, prize_json) {
        admin.btnLoading('.saveForm', '保存中');
        var data = {
            lottery_id: $('#lottery_id').val(),
            form_data: form_data,
            prize_json: prize_json
        };

        let requestData = data;
        let url = '/api/lottery/saveLottery';
        $.ajax({
            url: url,
            type: 'post',
            data: requestData,
            dataType: 'json',
            timeout: 50000,
            success: function (res) {
                if (!res) return layer.msg('网络错误，请重试', {icon: 5});
                if (res.code === 0) {
                    admin.btnLoading('.saveForm', false);
                    admin.btnLoading('.cancel', false);
                    layer.msg(res.msg, {icon: 6});
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 500);
                } else {
                    admin.btnLoading('.saveForm', false);
                    admin.btnLoading('.cancel', false);
                    layer.msg(res.msg, {icon: 5});
                }
            },
            error: function () {
                admin.btnLoading('.saveForm', false);
                admin.btnLoading('.cancel', false);
                return layer.msg('网络错误，请重试', {icon: 5});
            }
        });
        return false;

        var resp = ajax('/api/lottery/saveLottery', data);
        if (resp && resp.code === 0) {
            layer.msg(resp.msg, {icon: 6});
            setTimeout(function () {
                admin.closeThisDialog();
            }, 1000);
        } else {
            return layer.msg(resp.msg, {icon: 5});
        }
    }

//奖品数组转json传给后台
    function getPrizeJson(prizeArr) {
        var prize_json = JSON.stringify(prizeArr);
        return prize_json;
    }


    //是否展示中奖后弹窗banner
    form.on('switch(show_success_banner)', function (data) {
        if (data.elem.checked) {
            $('#success_banner_div').show();
        } else {
            $('#success_banner_div').hide();
        }
    });


    // 执行实例
    var uploadInst = upload.render({
        elem: '#upload',
        url: oss_url + '/uploadFile?sys_type=7',
        accept: 'file',
        exts: 'jpg|png|gif|jpeg|png',
        before: function () {
            layer.load(2);
        },
        done: function (res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                $('#success_banner_image_preview').attr('src', res.data.oss_file_url);
                $('#success_banner_image').val(res.data.oss_file_url);
            } else {
                layer.msg(res.msg);
            }
        },
        error: function (index, upload) {
            layer.closeAll('loading');
            layer.msg('网络出现问题，请重试！');
        }
    });

    // 执行实例
    upload.render({
        elem: '#lottery_img_upload',
        url: oss_url + '/uploadFile?sys_type=7',
        accept: 'file',
        exts: 'jpg|png|gif|jpeg|png',
        before: function () {
            layer.load(2);
        },
        done: function (res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                $('#lottery_img_preview').attr('src', res.data.oss_file_url);
                $('#lottery_img').val(res.data.oss_file_url);
            } else {
                layer.msg(res.msg);
            }
        },
        error: function (index, upload) {
            layer.closeAll('loading');
            layer.msg('网络出现问题，请重试！');
        }
    });

    upload.render({
        elem: '#animation_img_upload',
        url: oss_url + '/uploadFile?sys_type=7',
        accept: 'file',
        exts: 'jpg|png|gif|jpeg|png',
        before: function () {
            layer.load(2);
        },
        done: function (res) {
            layer.closeAll('loading');
            if (res.code === 0) {
                $('#animation_img_preview').attr('src', res.data.oss_file_url);
                $('#animation_img').val(res.data.oss_file_url);
            } else {
                layer.msg(res.msg);
            }
        },
        error: function (index, upload) {
            layer.closeAll('loading');
            layer.msg('网络出现问题，请重试！');
        }
    });

    // 清除图片按钮点击事件
    $('#clear').on('click', function () {
        $('#success_banner_image_preview').attr('src', ''); // 清空图片预览
        $('#success_banner_image').val(''); // 清空图片预览
    });
    $('#clear_lottery_img').on('click', function () {
        $('#lottery_img_preview').attr('src', ''); // 清空图片预览
        $('#lottery_img').val(''); // 清空图片预览
    });
    $('#clear_animation_img').on('click', function () {
        $('#animation_img_preview').attr('src', ''); // 清空图片预览
        $('#animation_img').val(''); // 清空图片预览
    });

});

