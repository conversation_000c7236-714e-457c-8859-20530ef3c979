layui.use(['form', 'laydate', 'element', 'table', 'index', 'admin'], function () {
        let form = layui.form, table = layui.table, laydate = layui.laydate;
        let index = layui.index;
        let admin = layui.admin;
        let where = {};

        function renders(activity_name) {
            table.render({
                elem: '#list',
                url: '/api/customFormData/getCustomFormDataList',
                method: 'post',
                lineStyle: 'height: auto;',
                // toolbar: '#toolbar',
                size: 'sm',
                cellMinWidth: 80,//全局定义常规单元格的最小宽度
                request: {
                    limitName: 'limit' //每页数据量的参数名，默认：limit
                },
                defaultToolbar: ['filter'],
                where: {
                    status: $("input[name=status]").val(),
                    expire: $('input[name=expire]').val(),
                    activity_name: (activity_name || "")
                },
                limit: 15,
                loading: true,
                cols: [[
                    {field: 'id', title: '序号', align: 'center', width: 50},
                    {
                        field: 'activity_no', title: '活动编号', width: 150, align: 'center', templet: function (data) {
                            let activity_no = data.activity.activity_no;
                            return "<a ew-href='/web/activity/list?activity_no=" + activity_no +
                                "' style='color: dodgerblue' ew-title='活动详情 - " + activity_no + "'>" + activity_no + "</a>";
                        }
                    },
                    {
                        field: 'activity_name', title: '活动名称', align: 'center', width: 300, templet: function (data) {
                            return data.activity ? data.activity.activity_name : "";
                        }
                    },
                    {
                        field: 'mobile', align: 'center', title: '客户账号', width: 200, templet: function (data) {
                            return data.mobile ? '<span>' + data.mobile + '</span><span style="color: dodgerblue;margin-left: 10px;" class="viewMobile" type="mobile" id="' + data.id + '">查看</span>' : '';
                        }
                    },
                    {
                        field: 'company_name', title: '公司名称', align: 'center', width: 300
                    },
                    {
                        field: 'form_data_detail', title: '详细信息', align: 'left', templet: function (data) {
                            let html = '';
                            if (data.form_data) {
                                $.each(data.form_data, function (key, value) {
                                    html += '<div class = row"><p  style="text-align: left"><span>' + value.input_name + ' : ' + value.input_value + '</span></p></div>';
                                });
                                html = '<div class="priceboxs">' + html + '</div>';
                            }
                            return html;
                        }
                    },
                    {field: 'is_new_reg', align: 'center', width: 50, title: '是否新注册', templet: "#is_new_reg"},
                    {field: 'create_time', align: 'center', title: '填写时间', width: 130},
                    // {field: 'edit', title: '操作', align: 'center', templet: '#edit', width: 250, fixed: 'right'},
                ]],
                id: 'list',
                page: {}
            });
        }

        renders($("#activity_name").val())

        form.on('submit(load)', function (data) {
            where = data.field;
            table.reload('list', {
                page: {
                    curr: 1 //重新从第 1 页开始
                },
                where: data.field,
            });
            return false;
        });

        table.on('toolbar(list)', function (obj) {
            switch (obj.event) {
                case 'addActivity':

            }
        });

        table.on('tool(list)', function (obj) {
            let event = obj.event;
            let id = obj.data.id;
            let name = obj.data.activity_name;
            if (event === 'edit') {
                layer.open({
                    type: 2,
                    area: ['95%', '95%'],
                    fixed: false,
                    title: '编辑活动价 - ' + name,
                    content: '/web/customFormData/saveCustomFormData?id=' + id,
                    end: function () {
                        table.reload('list', {
                            page: {
                                curr: 1 //重新从第 1 页开始
                            },
                            where: where,
                        });
                    }
                });
            }
        });

        $(document).on('click', '.viewMobile', function () {
            if ($(this).text() === '隐藏') {
                $(this).prev().text($(this).attr('prev_text'));
                $(this).text('查看');
            } else {
                let id = $(this).attr('id');
                let type = $(this).attr('type');
                let resp = ajax('/api/customFormData/getCustomFormData', {id: id, type: type});
                if (!resp) {
                    layer.msg('网络连接失败', {'icon': 5});
                    return false;
                }
                let prevText = $(this).prev().text();
                $(this).attr('prev_text', prevText);
                if (resp.code === 0) {
                    $(this).prev().text(resp.data.mobile);
                    $(this).text('隐藏');
                    // $.get(getLogDomain() + "/api/addSensitiveClick", {
                    //     uid: getCookie("oa_user_id") || 0,
                    //     sys_id: 4,
                    //     mask_type: type || 0,
                    //     origin_id: contactId || 0,
                    //     source_from: window.location.href
                    // });
                    // console.log(resp);
                } else {
                    layer.msg(resp.msg, {'icon': 5});
                    return false;
                }
            }

        });

        $(document).on('click', '#export', function () {
            let data = form.val("filter_form");
            let params = JSON.stringify(data)
            let url = '/api/customFormData/exportCustomFormData?params=' + params;
            window.open(url, '_blank');
        });
    }
);
