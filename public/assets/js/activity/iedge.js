layui.use(['admin', 'index', 'form', 'table', 'laydate', 'element', 'upload', 'xmSelect'], function () {
  var admin = layui.admin;
  var index = layui.index;
  var form = layui.form;
  var table = layui.table;
  var laydate = layui.laydate;
  var element = layui.element;
  var upload = layui.upload;
  var xmSelect = layui.xmSelect;
  var id = getRequest('id') || '';
  var org_id = getRequest('org_id') || '';
  var from = getRequest('from') || ''; //标记pc h5
  var uploadClass = ['columnOneHref', 'columnTwoHref', 'columnThreeHref', 'columnFourHref'];
  var uploadName = ['columnOneUrl', 'columnTwoUrl', 'columnThreeUrl', 'columnFourUrl'];
  var page_color = '#FFFFFF'; //页面颜色
  var page_background = ''; //页面背景
  var page_navigation_bar = false; //顶部导航栏
  var page_right_bar = false; //右侧快键入口

  //定义基本元件名称
  var textVal = {
    customLayout: '自定义布局',
    lotteryCircle: '抽奖-圆',
    lotterySquare: '抽奖-方',
    shopList: '商品列表',
    video: '视频',
    carousel: '轮播'
  }

  window.IndexController = {
    init: function () {
      this.created(this).getActivityElementList().handleBind(this);
    },
    created: function () {
      this.getInfo(this);
      return this;
    },
    /**
     * 读取配置
     */
    getInfo() {
      Request('/api/activity/getInfo', 'GET', {id: id}, function (res) {
        if (res.code == 0) {
          if (res.data) {
            document.title = res.data.web_title; //更新页面标题
            var web_html = res.data.web_html;
            var web_html_config = res.data.web_html_config;
            //已过期
            if (res.data.activity_status == -1) {
              layer.open({
                type: 1,
                title: false,
                offset: '50px',
                closeBtn: 0,
                area: ['400px', 'auto'],
                shadeClose: false,
                resize: false,
                move: false,
                scrollbar: true,
                content: '<h2 style="padding: 10px;text-align: center;font-weight: bold;">该活动已过期，不可编辑!</h2>',
                success: function (layero, dIndex) {

                }
              });
            }
            //预览地址
            if (from == 'pc') {
              //系统元件是否选中
              var page_navigation_bar = web_html_config ? JSON.parse(web_html_config).baseConfig.page_navigation_bar : '';
              var page_right_bar = web_html_config ? JSON.parse(web_html_config).baseConfig.page_right_bar : '';
              if (page_navigation_bar == 1) {
                $("#page_navigation_bar").prop("checked", 'checked');
              }
              if (page_right_bar == 1) {
                $("#page_right_bar").prop("checked", 'checked');
              }
              $("#preview").attr('href', res.data.web_url + "?is_preview=1");
            }
            if (from == 'h5') {
              $("#preview").attr('href', res.data.h5_url + "?is_preview=1");
            }
            if (web_html_config) {
              web_html_config = JSON.parse(web_html_config);
              //页面背景图
              if (web_html_config.baseConfig && web_html_config.baseConfig.page_background) {
                $("body").css("background-image", "url(" + web_html_config.baseConfig.page_background + ")").css("background-position", "top center").css("background-repeat", "no-repeat");
                $("#page_background").attr('src', web_html_config.baseConfig.page_background).show();
                //页面背景图片预览
                layer.photos({
                  photos: '.cube-set-head',
                  anim: 5
                });
              }
              //页面颜色
              if (web_html_config.baseConfig && web_html_config.baseConfig.page_color) {
                $("body").css('background-color', web_html_config.baseConfig.page_color);
                $("#page_color").val(web_html_config.baseConfig.page_color);
              }
            }
            if (web_html) {
              $(".web-html").empty().html(web_html);
            }
            $(".cube-setting-layer").hide(); //楼层关闭
            IndexController.customLayoutChange(); //自定义布局初始化
            IndexController.shopListChange(); //商品列表-初始化
            IndexController.lotterySquareChange(); //抽奖-方初始化
            IndexController.lotteryCircleChnage(); //抽奖-圆初始化
          }
        }
      })
    },
    /**
     *获取活动元件列表
     */
    getActivityElementList: function () {
      Request('/api/activityElement/list', 'GET', {}, function (res) {
        if (res.code == 0) {
          var tpl = elementHtml.innerHTML;
          let data = [
            {
              element_code: 'customLayout',
              element_name: '自定义布局',
              element_status: 1
            },
            {
              element_code: 'lotteryCircle',
              element_name: '抽奖-圆',
              element_status: 1
            },
            {
              element_code: 'lotterySquare',
              element_name: '抽奖-方',
              element_status: 1
            },
            {
              element_code: 'shopList',
              element_name: '商品列表',
              element_status: 1
            },
            {
              element_code: 'video',
              element_name: '视频',
              element_status: 1
            },
            {
              element_code: 'carousel',
              element_name: '轮播',
              element_status: 1
            }
          ]
          layui.laytpl(tpl).render(data, function (html) {
            $("#element").append(html);
            IndexController.componentDragChange();
          });
        }
      })
      return this;
    },
    /**
     * 基本元件拖拽布局
     */
    componentDragChange: function () {
      //元件绑定拖拽事件
      var childs = document.querySelectorAll('.child');
      var body = document.body;

      childs = Array.from(childs);
      childs.forEach(function (item, index) {
        item.ondragstart = function (event) {
          console.log('我开始在被拖拽')
        }
        item.ondrag = function () {
          console.log('拖拽过程中')
        }
        item.ondragend = function (event) {
          var id = item.id;
          //根据所拖的组件选择对应的模板类型
          var tpl = layoutHtml.innerHTML;
          //商品列表只能配置一个
          if (id == 'shopList') {
            var length = $(".cube-set-drag-area").find('.shopList').length;
            if (length >= 1) {
              layer.msg('已配置商品列表，请勿重复配置');
              return false;
            }
          }

          if (id == 'lotteryCircle') {
            var length = $(".cube-set-drag-area").find('.lotteryCircle').length;
            if (length >= 1) {
              layer.msg('已配置圆盘抽奖，请勿重复配置');
              return false;
            }
          }

          if (id == 'lotterySquare') {
            var length = $(".cube-set-drag-area").find('.lotterySquare').length;
            if (length >= 1) {
              layer.msg('已配置九宫格抽奖，请勿重复配置');
              return false;
            }
          }

          layui.laytpl(tpl).render(id, function (html) {
            $(".cube-set-drag-area").append(html);
            IndexController.sortNumChange(); //更新排序
            if (id == 'customLayout') {
              //自定义布局
              IndexController.customLayoutChange();
            } else if (id == 'lotteryCircle') {
              //抽奖-圆
              IndexController.lotteryCircleChnage();
            } else if (id == 'lotterySquare') {
              //抽奖-方
              IndexController.lotterySquareChange();
            } else if (id == 'shopList') {
              //商品列表
              IndexController.shopListChange();
            } else if (id == 'video') {
              //视频
              IndexController.videoChange();
            } else if (id == 'carousel') {
              //轮播
              IndexController.carouselChange();
            } else {
              layer.msg('正在开发中，敬请期待！');
            }
          });
          console.log('拖拽结束')
        }
      })

      document.body.ondragenter = function (event) {
        event.preventDefault();
      }
      document.body.ondragover = function (event) {
        event.preventDefault();
      }

      body.ondrop = function (event) {
        event.stopPropagation();
      }

      return this;
    },
    /**
     * 更新数据
     */
    updatePcData: function () {

      var params = {
        id: id,
        html: $(".web-html").html(),
        config: {
          baseConfig: {
            page_color: $("#page_color").val(),
            page_background: $("#page_background").attr('src') || '',
            page_navigation_bar: $("#page_navigation_bar").is(':checked') ? 1 : 0,
            page_right_bar: $("#page_right_bar").is(':checked') ? 1 : 0,
          }
        }
      }

      $(".cube-set-drag-area .cube-initial").each(function (index, element) {
        var data_type = $(element).attr('data-type');
        var data = $(element).attr('data');
        var data_uuid = $(element).attr('data-uuid');
        if (data_uuid) {
          if (data) {
            var assign = $.extend({}, JSON.parse(data), {
              basic_elements: data_type
            });
            params.config[data_uuid] = assign;
          } else {
            params.config[data_uuid] = {
              basic_elements: data_type
            };
          }
        }
      });

      Request('/api/activity/updatePcData', 'POST', params, function (res) {
        if (res.code == 0) {

        } else {
          layer.msg(res.msg);
        }
      })
    },
    /**
     * 自定义布局
     * @param obj
     */
    customLayoutChange: function () {

      //楼层宽度监听
      $(document).on('input', '.customLayout .width', debounce(function (e) {
        var width = layui.form.val('floorSettingForm').width;
        var height = layui.form.val('floorSettingForm').height;
        var columns = layui.form.val('floorSettingForm').columns;
        var spacing = layui.form.val('floorSettingForm').spacing;

        if (columns == 1) {
          layui.form.val('floorSettingForm', {
            columnWidthOne: width,
            spacing: 0
          })
        }
        if (width < 1226) {
          layui.form.val('floorSettingForm', {
            columnWidthOne: 1226,
            width: 1226
          })
        }
        if (width > 1920) {
          layui.form.val('floorSettingForm', {
            columnWidthOne: 1920,
            width: 1920
          })
        }

      }, 500));

      //楼层列数监听
      layui.form.on('select(columnsChange)', function (data) {
        var width = layui.form.val('floorSettingForm').width * 1;
        var height = layui.form.val('floorSettingForm').height * 1;
        var columns = layui.form.val('floorSettingForm').columns;
        var spacing = layui.form.val('floorSettingForm').spacing * 1;
        var current = $(".cube-setting-layer").attr('data-index') * 1;

        if (columns == 1) {
          layui.form.val('floorSettingForm', {
            columnWidthOne: width,
            spacing: 0
          })
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + width + '" style="width: 55px;">');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 2) {
          let widthPx = Number(width) - Number(spacing);
          widthPx = (widthPx / 2).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 3) {
          let widthPx = Number(width) - (Number(spacing) * 2);
          widthPx = (widthPx / 3).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 4) {
          let widthPx = Number(width) - (Number(spacing) * 3);
          widthPx = (widthPx / 4).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 5) {
          let widthPx = Number(width) - (Number(spacing) * 4);
          widthPx = (widthPx / 5).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx,
            columnWidthFive: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFive" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 6) {
          let widthPx = Number(width) - (Number(spacing) * 5);
          widthPx = (widthPx / 6).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx,
            columnWidthFive: widthPx,
            columnWidthSix: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFive" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthSix" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 7) {
          let widthPx = Number(width) - (Number(spacing) * 6);
          widthPx = (widthPx / 7).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx,
            columnWidthFive: widthPx,
            columnWidthSix: widthPx,
            columnWidthSeven: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFive" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthSix" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthSeven" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        }

        //动态列数
        var htmlDynamicsArr = [];
        for (let i = 0; i < columns; i++) {
          htmlDynamicsArr.push('<div class="layui-form-item"> <label class="layui-form-label">第' + (i + 1) + '列</label> <div class="layui-input-block" style="margin-left: 62px;"> <input type="text" name="' + uploadClass[i] + '" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url"> </div> <div class="row bothSide verCenter bar-wrap"> <div> <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><a href="javascript:;" class="clearPic">清除图片</a> <p class="tip">支持扩展名png/jpg/gif/mp4</p> </div> <div> <img src="" alt="" class="pic" style="display: none"><input type="hidden" name="' + uploadName[i] + '" value=""/> </div> </div> </div>')
        }
        $(".cube-setting-layer").find(".customLayout").find('.columns-dynamics').empty().html(htmlDynamicsArr.join(''));
        IndexController.uploadChange(); //初始化上传组件
        IndexController.calcTotal();
      });

      //间距监听
      $(document).on('input', '.customLayout .spacing', debounce(function (e) {
        var width = layui.form.val('floorSettingForm').width * 1;
        var height = layui.form.val('floorSettingForm').height * 1;
        var columns = layui.form.val('floorSettingForm').columns;
        var spacing = layui.form.val('floorSettingForm').spacing * 1;

        if (columns == 1) {
          layui.form.val('floorSettingForm', {
            columnWidthOne: width,
            spacing: 0
          })
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + width + '" style="width: 55px;">');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 2) {
          let widthPx = Number(width) - Number(spacing);
          widthPx = (widthPx / 2).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 3) {
          let widthPx = Number(width) - (Number(spacing) * 2);
          widthPx = (widthPx / 3).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 4) {
          let widthPx = Number(width) - (Number(spacing) * 3);
          widthPx = (widthPx / 4).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 5) {
          let widthPx = Number(width) - (Number(spacing) * 4);
          widthPx = (widthPx / 5).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx,
            columnWidthFive: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFive" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 6) {
          let widthPx = Number(width) - (Number(spacing) * 5);
          widthPx = (widthPx / 6).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx,
            columnWidthFive: widthPx,
            columnWidthSix: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFive" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthSix" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 7) {
          let widthPx = Number(width) - (Number(spacing) * 6);
          widthPx = (widthPx / 7).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx,
            columnWidthFive: widthPx,
            columnWidthSix: widthPx,
            columnWidthSeven: widthPx
          })
          //动态列宽计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFive" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthSix" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthSeven" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        }

        IndexController.calcTotal();

      }, 500));

      //列宽监听
      $(document).on('input', '.customLayout .columnWidth', debounce(function (e) {
        IndexController.calcTotal();
      }, 500));

      //楼层保存按钮
      layui.form.on('submit(save)', function (data) {
        var width = layui.form.val('floorSettingForm').width;
        var height = layui.form.val('floorSettingForm').height;
        var columns = layui.form.val('floorSettingForm').columns;
        var spacing = layui.form.val('floorSettingForm').spacing;
        var total = $('.columnWidthDynamics').find('.total').text() * 1;
        var current = $(".cube-setting-layer").attr("data-index") * 1;
        var type = $(".cube-setting-layer").attr("data-type");
        if (columns != 1) {
          if (total != width) {
            layer.msg('请核对width之和等于' + width);
            return false;
          }
        }
        var columnWidth = {
          columnWidthOne: layui.form.val('floorSettingForm').columnWidthOne,
          columnWidthTwo: layui.form.val('floorSettingForm').columnWidthTwo,
          columnWidthThree: layui.form.val('floorSettingForm').columnWidthThree,
          columnWidthFour: layui.form.val('floorSettingForm').columnWidthFour,
          columnWidthFive: layui.form.val('floorSettingForm').columnWidthFive,
          columnWidthSix: layui.form.val('floorSettingForm').columnWidthSix,
          columnWidthSeven: layui.form.val('floorSettingForm').columnWidthSeven
        };
        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data.field)); //存储单个配置
        IndexController.render(width, height, columns, spacing, columnWidth, '', type); //视图更新
        IndexController.updatePcData(); //更新数据
      });

      //楼层热区保存按钮
      layui.form.on('submit(hotSubmit)', function (data) {
        var width = layui.form.val('floorSettingForm').width;
        var height = layui.form.val('floorSettingForm').height;
        var columns = layui.form.val('floorSettingForm').columns * 1;
        var spacing = layui.form.val('floorSettingForm').spacing;
        var total = $('.columnWidthDynamics').find('.total').text() * 1;
        var current = $(".cube-setting-layer").attr("data-index") * 1;
        var type = $(".cube-setting-layer").attr("data-type");
        var hot = [];
        var floorSettingParmam = layui.form.val('floorSettingForm');
        var columnWidth = {
          columnWidthOne: layui.form.val('floorSettingForm').columnWidthOne,
          columnWidthTwo: layui.form.val('floorSettingForm').columnWidthTwo,
          columnWidthThree: layui.form.val('floorSettingForm').columnWidthThree,
          columnWidthFour: layui.form.val('floorSettingForm').columnWidthFour,
          columnWidthFive: layui.form.val('floorSettingForm').columnWidthFive,
          columnWidthSix: layui.form.val('floorSettingForm').columnWidthSix,
          columnWidthSeven: layui.form.val('floorSettingForm').columnWidthSeven
        };

        //收集热区字段
        for (let i = 0; i < columns; i++) {
          hot.push({
            width: $(".cube-setting-layer").find('.hot-area').find('.width').eq(i).val() || 0,
            height: $(".cube-setting-layer").find('.hot-area').find('.height').eq(i).val() || 0,
            x: $(".cube-setting-layer").find('.hot-area').find('.x').eq(i).val() || 0,
            y: $(".cube-setting-layer").find('.hot-area').find('.y').eq(i).val() || 0,
            url: $(".cube-setting-layer").find('.hot-area').find('.url').eq(i).val() || '',
            pic: $(".cube-setting-layer").find('.hot-area').find('.pic').eq(i).attr('src') || ''
          })
        }
        var data = $.extend({}, floorSettingParmam, {
          hot: hot
        });
        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data)); //存储单个配置
        IndexController.render(width, height, columns, spacing, columnWidth, hot, type); //视图更新
        IndexController.updatePcData(); //更新数据
      });

      //热区-宽度监听
      $(document).on('input', '.hot-area .width', debounce(function (e) {

      }, 500));

    },
    /**
     * 抽奖-方
     * @param obj
     */
    lotterySquareChange: function () {

      //抽奖ID监听
      $(document).on('input', '.lotterySquare .lotteryId', debounce(function (e) {
        var val = $(this).val();
        IndexController.checkLottery(val, $(this).parent().parent().parent().find(':submit'));
      }, 800));

      //各奖项长/宽监听
      $(document).on('input', '.lotterySquare .width', debounce(function (e) {
        var val = $(this).val() * 1;
        if (val) {
          $(this).parent().next().find(".total-width").text((val * 3) + 22);
        }
      }, 800));

      //楼层保存按钮
      layui.form.on('submit(lotterySquareSubmit)', function (data) {
        var width = layui.form.val('lotterySquareForm').width;
        var lotterySquareBg = layui.form.val('lotterySquareForm').lotterySquareBg;
        var contactBg = layui.form.val('lotterySquareForm').contactBg;
        var prizeBg = layui.form.val('lotterySquareForm').prizeBg;
        var bg = layui.form.val('lotterySquareForm').bg;
        var type = $(".cube-setting-layer").attr("data-type");
        var current = $(".cube-setting-layer").attr("data-index") * 1;

        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data.field)); //存储单个配置

        IndexController.renderlotterySquare(width, lotterySquareBg, contactBg, prizeBg, bg, type); //视图更新

        IndexController.updatePcData(); //更新数据

      });
    },
    /**
     * 抽奖-圆
     * @param obj
     */
    lotteryCircleChnage: function () {
      //抽奖ID监听
      $(document).on('input', '.lotteryCircle .lotteryId', debounce(function (e) {
        var val = $(this).val();
        IndexController.checkLottery(val, $(this).parent().parent().parent().find(':submit'));
      }, 800));

      //奖项个数监听
      $(document).on('input', '.lotteryCircle .num', debounce(function (e) {
        var val = $(this).val() * 1;
        if (val) {
          $(this).parent().next().find('.angle').text(360 / val);
        } else {
          $(this).parent().next().find('.angle').text(0);
        }
      }, 800));

      //楼层保存按钮
      layui.form.on('submit(lotteryCircleSubmit)', function (data) {
        var num = layui.form.val('lotteryCircleForm').num;
        var lotteryCircleBg = layui.form.val('lotteryCircleForm').lotteryCircleBg;
        var contactBg = layui.form.val('lotteryCircleForm').contactBg;
        var prizeBg = layui.form.val('lotteryCircleForm').prizeBg;
        var bg = layui.form.val('lotteryCircleForm').bg;
        var type = $(".cube-setting-layer").attr("data-type");
        var current = $(".cube-setting-layer").attr("data-index") * 1;

        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data.field)); //存储单个配置

        IndexController.renderlotteryCircle(num, lotteryCircleBg, contactBg, prizeBg, bg, type); //视图更新
        IndexController.updatePcData(); //更新数据

      });
    },
    /**
     * 商品列表
     * @param obj
     */
    shopListChange: function () {

      //搜索框监听
      $(document).on('change', '#is_search', function (event) {
        if ($(this).prop('checked')) {
          $(this).next().next().show();
        } else {
          $(this).next().next().hide();
          $(this).next().next().val('');
        }
      });

      //商品选择维度监听
      layui.form.on('radio(dimensionChange)', function (data) {
        var val = data.value;
        if (val == 1) {
          //型号
          $("#goodsBox").show();
          $("#shopSort").hide();
          $("#recommendation").hide();
          IndexController.uploadChangeSKU(); //初始化sku上传
        } else if (val == 2) {
          //商品属性
          $("#goodsBox").hide();
          $("#shopSort").show();
          $("#recommendation").hide();
        } else if (val == 3) {
          $("#goodsBox").hide();
          $("#shopSort").hide();
          $("#recommendation").show();
        }
      })

      //新增行-自营
      $(document).off('click', '#addRowSelf').on('click', '#addRowSelf', function () {
        var html = '<tr><td class="classify"><input type="hidden" class="type" value="1"><input type="text" placeholder="逗号隔开" class="layui-input class_ids"></td><td class="brand"><input type="text" placeholder="逗号隔开" class="layui-input brand_ids"></td><td><button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete">删除</button></td></tr>';
        $("#rowAreaSelf").append(html);
        layui.form.render();
      });

      //商品列表删除-自营
      $(document).on('click', '#rowAreaSelf .delete', function () {
        $(this).parent().parent().remove();
      });

      //楼层保存按钮
      layui.form.on('submit(shopListSubmit)', function (data) {
        var type = $(".cube-setting-layer").attr("data-type");
        var current = $(".cube-setting-layer").attr("data-index") * 1;
        var params = {
          "org_id": org_id,//组织id
          "is_filter_brand": 0,
          "filter_brand_id": "",
          "is_filter_supplier": 0,
          "filter_supplier_id": "",
          "is_activity_price": 0,
          "activity_price_id": "",
          "is_sku_id_upload": data.field.sku_id_upload_file ? 1 : 0,
          "is_filter_goods_name": 1,
          "is_sale_time_order": data.field.dimension == 3 ? 1 : 0,//新品推荐的时候这个值等于1
          "sale_order_time": new Date().getTime(),//当前的时间戳,不传默认为当前时间
          "goods_name": 1,
          "is_search": $("#is_search").is(':checked') ? 1 : 0, //搜索框配置
          "is_search_color": $("#is_search_color").val() || '#E0E0E0', //搜索框配置颜色
          "activity_id": 1,//获取id
          "partition": data.field.partition,//分区 1无 2按分类
          "dimension": data.field.dimension,//选择维度 1型号 2商品属性 3新品推荐
          "is_choice_goods": data.field.dimension == 1 ? 1 : 0,//是否为选择商品型号
          "choice_goods_data": {
            supplier_ids: data.field.dimension == 1 ? data.field.supplier_ids : '',//供应商 多选逗号隔开
            supplier_codes: data.field.dimension == 1 ? data.field.supplier_codes : ''//渠道标签 多选逗号隔开
          },
          "is_only_group_category": 0,//是否只返回分类分组数据,如果传了,这接口等于获取分类数据的接口
          "is_group_category": data.field.partition == 2 ? 1 : 0,//是否需要分类分组
          "filter_category_id": 0,//分类id筛选,默认为0,如果筛选了就传
          "sku_id_upload_file": data.field.dimension == 1 ? data.field.sku_id_upload_file : '',//文件地址
          "sku_ids": data.field.dimension == 1 ? data.field.sku_ids : '',
          "is_sku_property": data.field.dimension == 2 ? 1 : 0,//是否设置sku属性
          "sku_property_data": []//商品属性
        }

        if (data.field.dimension == 2) {
          $("#rowAreaSelf tr").each(function (index, element) {
            params.sku_property_data.push({
              type: 2,
              class_ids: $(element).find('.class_ids').val() || '', //分类id,逗号分隔,不能超过100个
              brand_ids: $(element).find('.brand_ids').val() || '', //品牌id,逗号分隔,不能超过100个
            })
          });
        }

        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(params)); //存储单个配置

        IndexController.renderShopList(params); //视图更新

      });
    },
    /**
     * 获取专营渠道列表
     */
    getZhuanYingSupList: function (supplier_ids, supplier_codes) {
      var supplier_ids_params = [];
      var supplier_codes_params = [];

      if (supplier_ids) {
        supplier_ids_params = supplier_ids.split(',');
      }

      if (supplier_codes) {
        supplier_codes_params = supplier_codes.split(',');
      }

      Request('/api/commonData/getSupplierList', 'POST', {}, function (res) {
        if (res.code == 0) {
          var data = res.data.map((item) => {
            return {
              name: item.supplier_name,
              value: item.supplier_id
            }
          });

          xmSelect.render({
            el: '#supplier_ids',
            searchTips: '请输入搜索',
            filterable: true,
            data: data,
            name: 'supplier_ids',
            initValue: supplier_ids_params,
            paging: true,
            on: function (res) {

            }
          })

        } else {
          layer.msg(res.msg);
        }
      });


      Request('/api/activityElement/getZhuanYingSupList', 'GET', {}, function (res) {
        if (res.code == 0) {
          var data = res.data.list.map((item) => {
            return {
              name: item.supplier_name,
              value: item.supplier_code
            }
          });
          xmSelect.render({
            el: '#supplier_codes',
            searchTips: '请输入搜索',
            filterable: true,
            data: data,
            name: 'supplier_codes',
            initValue: supplier_codes_params,
            paging: true,
            on: function (res) {

            }
          })
        } else {
          layer.msg(res.msg);
        }
      });
    },
    /**
     * 上传sku ID
     */
    uploadChangeSKU: function () {

      layui.upload.render({
        elem: "#sku_id_upload",
        url: oss_url + '/uploadFile?sys_type=7',
        accept: 'file',
        exts: 'xls|xlsx',
        before: function () {
          layer.load(2);
        },
        done: function (res) {
          layer.closeAll('loading');
          if (res.code === 0) {
            layer.msg('上传成功');
            layui.form.val('shopListForm', {
              sku_id_upload_file: res.data.oss_file_url
            })
            $(this.item).text('重新上传');
            $(this.item).parent().find('.layui-icon-download-circle').attr('href', res.data.oss_file_url);
            IndexController.getSkuIdsByFile(res.data.oss_file_url);
          } else {
            layer.msg(res.msg);
          }
        },
        error: function (index, upload) {
          layer.closeAll('loading');
          layer.msg('网络出现问题，请重试！');
        }
      });

    },
    /**
     * 获取文件中的skuIds
     */
    getSkuIdsByFile: function (file_url) {
      Request('/api/activityElement/getSkuIdsByFile', 'GET', {
        file_url: file_url,
        activity_id: id
      }, function (res) {
        if (res.code === 0) {
          layui.form.val('shopListForm', {
            sku_ids: res.data
          })
        }
      })
    },
    /**
     * 视频
     */
    videoChange: function () {

      //楼层保存按钮
      layui.form.on('submit(saveVideo)', function (data) {
        var current = $(".cube-setting-layer").attr("data-index") * 1;
        var type = $(".cube-setting-layer").attr("data-type");
        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data.field)); //存储单个配置
        IndexController.renderVideo(data.field, type); //视图更新
        IndexController.updatePcData(); //更新数据
      });
    },
    /**
     * 轮播
     */
    carouselChange: function () {

      //图片个数监听
      layui.form.on('select(carouselNumChange)', function (data) {
        var num = parseInt(data.value) || 1;
        var $columnsDynamics = $('.columns-dynamics');
        // 清空之前生成的列
        $columnsDynamics.empty();

        // 根据所选图片个数动态生成列
        for (let i = 1; i <= num; i++) {
          let name = `column_${i}`; // 动态生成name属性的值
          let $div = $(`
            <div class="layui-form-item">
                <label class="layui-form-label required" style="width: 83px;">第${i}列</label>
                <div class="layui-input-block" style="margin-left: 85px;">
                    <input type="text" name="${name}" autocomplete="off" placeholder="跳转地址" class="layui-input url" lay-verify="required" lay-reqtext="请填写跳转地址" lay-vertype="tips">
                </div>
                <div class="row bothSide verCenter bar-wrap" style="padding-left: 85px;">
                    <div>
                        <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>
                        <a href="javascript:;" class="clearPic">清除图片</a>
                        <p class="tip">支持扩展名png/jpg/gif/mp4</p>
                    </div>
                    <div>
                        <img src="" alt="" class="pic" style="display: none">
                    </div>
                </div>
            </div>
        `);
          $columnsDynamics.append($div);
        }
        IndexController.uploadChange();

      });

      //楼层保存按钮
      layui.form.on('submit(saveCarousel)', function (data) {
        var current = $(".cube-setting-layer").attr("data-index") * 1;
        var type = $(".cube-setting-layer").attr("data-type");
        var picArr = [];
        var originalHeight = 0;
        // 创建一个新的Image对象
        var img = new Image();
        var $img = $(".carousel .columns-dynamics .pic").eq(0);
        img.src = $img.attr('src');
        // 监听图片加载完成事件
        img.onload = function () {
          // 当图片加载完成后，可以通过naturalHeight属性获取图片的原始高度
          originalHeight = img.naturalHeight;
          $(".carousel .columns-dynamics .pic").each(function (index, element) {
            picArr.push($(element).attr('src'));
          })
          var params = $.extend({}, data.field, {pic: picArr.join(',')}, {height: originalHeight || 0});
          $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(params)); //存储单个配置
          IndexController.renderCarousel(params, type); //视图更新
          IndexController.updatePcData(); //更新数据
        };
      });

    },
    /**
     * 列宽总和计算
     */
    calcTotal: function () {

      var columnWidthOne = layui.form.val('floorSettingForm').columnWidthOne * 1 || 0;
      var columnWidthTwo = layui.form.val('floorSettingForm').columnWidthTwo * 1 || 0;
      var columnWidthThree = layui.form.val('floorSettingForm').columnWidthThree * 1 || 0;
      var columnWidthFour = layui.form.val('floorSettingForm').columnWidthFour * 1 || 0;
      var columnWidthFive = layui.form.val('floorSettingForm').columnWidthFive * 1 || 0;
      var columnWidthSix = layui.form.val('floorSettingForm').columnWidthSix * 1 || 0;
      var columnWidthSeven = layui.form.val('floorSettingForm').columnWidthSeven * 1 || 0;
      var columns = layui.form.val('floorSettingForm').columns * 1 || 0;
      var spacing = layui.form.val('floorSettingForm').spacing * 1 || 0;

      if (columnWidthOne >= 0 && columnWidthTwo >= 0 && columnWidthThree >= 0 && columnWidthFour >= 0 && columnWidthFive >= 0 && columnWidthSix >= 0 && columnWidthSeven >= 0) {
        if (columns == 2) {
          var total = columnWidthOne + columnWidthTwo + columnWidthThree + columnWidthFour + spacing;
          $('.columnWidthDynamics').find('.total').text(total.toFixed(0));
        } else if (columns == 3) {
          var total = columnWidthOne + columnWidthTwo + columnWidthThree + columnWidthFour + (spacing * 2);
          $('.columnWidthDynamics').find('.total').text(total.toFixed(0));
        } else if (columns == 4) {
          var total = columnWidthOne + columnWidthTwo + columnWidthThree + columnWidthFour + (spacing * 3);
          $('.columnWidthDynamics').find('.total').text(total.toFixed(0));
        } else if (columns == 5) {
          var total = columnWidthOne + columnWidthTwo + columnWidthThree + columnWidthFour + columnWidthFive + (spacing * 4);
          $('.columnWidthDynamics').find('.total').text(total.toFixed(0));
        } else if (columns == 6) {
          var total = columnWidthOne + columnWidthTwo + columnWidthThree + columnWidthFour + columnWidthFive + columnWidthSix + (spacing * 5);
          $('.columnWidthDynamics').find('.total').text(total.toFixed(0));
        } else if (columns == 7) {
          var total = columnWidthOne + columnWidthTwo + columnWidthThree + columnWidthFour + columnWidthFive + columnWidthSix + columnWidthSeven + (spacing * 6);
          $('.columnWidthDynamics').find('.total').text(total.toFixed(0));
        }
      } else {
        layer.msg('数据不合法，请核对数据');
      }
    },
    /**
     * 全局楼层设置
     * @returns {string}
     */
    floorsChangeGlobal: function () {
      var data = [];
      $(".cube-set-drag-area .cube-initial").each(function (index, element) {
        data.push({
          name: textVal[$(element).attr('data-type')],
          status: $(element).is(":visible")
        })
      });

      table.render({
        elem: '#list',
        page: false,
        data: data,
        cols: [
          [
            {type: 'numbers', title: '楼层', width: 50, align: 'center'},
            {field: 'name', title: '元件名称', align: 'center'},
            {
              field: 'name', title: '状态', width: 80, align: 'center', templet(d) {
                if (d.status) {
                  return '<input type="checkbox" lay-filter="statusChange" lay-skin="switch" lay-text="启用|禁用" checked value="' + d.LAY_TABLE_INDEX + '"/>'
                } else {
                  return '<input type="checkbox" lay-filter="statusChange" lay-skin="switch" lay-text="启用|禁用"  value="' + d.LAY_TABLE_INDEX + '"/>'
                }
              }
            },
            {
              field: 'name', title: '操作', width: 120, align: 'center', templet(d) {
                if (d.LAY_TABLE_INDEX == 0) {
                  return '<a class="layui-btn layui-btn-xs global-down" lay-event="global-down" data-index="' + d.LAY_TABLE_INDEX + '">下移</a>'
                } else if (d.LAY_TABLE_INDEX == data.length - 1) {
                  return '<a class="layui-btn layui-btn-xs global-up" lay-event="global-up" data-index="' + d.LAY_TABLE_INDEX + '">上移</a>'
                } else {
                  return '<a class="layui-btn layui-btn-xs global-up" lay-event="global-up" data-index="' + d.LAY_TABLE_INDEX + '">上移</a><a class="layui-btn layui-btn-xs global-down" lay-event="global-down" data-index="' + d.LAY_TABLE_INDEX + '">下移</a>'
                }
              }
            }
          ]
        ],
        done: function (res, curr, count) {
          layui.form.render();
        }
      });

      //触发单元格工具事件
      table.on('tool(list)', function (obj) {
        var tr = obj.tr;
        switch (obj.event) {
          //上移
          case 'global-up':
            var index = $(tr).find('.global-up').attr('data-index');
            $('.cube-set-drag-area .cube-initial').eq(index).find('.setting').find('.move-up').trigger('click');
            IndexController.floorsChangeGlobal(); //更新全局楼层设置
            break;
          //下移
          case 'global-down':
            var index = $(tr).find('.global-down').attr('data-index');
            $('.cube-set-drag-area .cube-initial').eq(index).find('.setting').find('.move-down').trigger('click');
            IndexController.floorsChangeGlobal(); //更新全局楼层设置
            break;
        }
      });

    },
    /**
     * 更新楼层索引
     */
    sortNumChange: function () {
      $(".cube-set-drag-area .cube-initial").each(function (index, element) {
        $(element).attr('data-index', index);
      });
    },
    /**
     * 楼层设置读取配置-自定义布局
     * @param data
     */
    settingRender: function (data) {
      var width = data.width;
      var spacing = data.spacing || 0;
      //根据列数动态生成列宽和列数
      if (data.columns == 1) {
        layui.form.val('floorSettingForm', {
          columnWidthOne: width,
          spacing: 0
        })
        let htmlArr = [];
        htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + width + '" style="width: 55px;">');
        $(".cube-setting-layer .customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
      } else if (data.columns == 2) {
        let widthPx = Number(width) - Number(spacing);
        widthPx = (widthPx / 2).toFixed(1);
        layui.form.val('floorSettingForm', {
          columnWidthOne: widthPx,
          columnWidthTwo: widthPx
        })
        //动态列计算
        let htmlArr = [];
        htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
        $(".cube-setting-layer .customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
      } else if (data.columns == 3) {
        let widthPx = Number(width) - (Number(spacing) * 2);
        widthPx = (widthPx / 3).toFixed(1);
        layui.form.val('floorSettingForm', {
          columnWidthOne: widthPx,
          columnWidthTwo: widthPx,
          columnWidthThree: widthPx
        })
        //动态列计算
        let htmlArr = [];
        htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
        $(".cube-setting-layer .customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
      } else if (data.columns == 4) {
        let widthPx = Number(width) - (Number(spacing) * 3);
        widthPx = (widthPx / 4).toFixed(1);
        layui.form.val('floorSettingForm', {
          columnWidthOne: widthPx,
          columnWidthTwo: widthPx,
          columnWidthThree: widthPx,
          columnWidthFour: widthPx
        })
        //动态列计算
        let htmlArr = [];
        htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
        $(".cube-setting-layer .customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
      }

      //动态列数
      var htmlDynamicsArr = [];
      for (let i = 0; i < data.columns; i++) {
        htmlDynamicsArr.push('<div class="layui-form-item"> <label class="layui-form-label">第' + (i + 1) + '列</label> <div class="layui-input-block" style="margin-left: 62px;"> <input type="text" name="' + uploadClass[i] + '" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url"> </div> <div class="row bothSide verCenter bar-wrap"> <div> <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><a href="javascript:;" class="clearPic">清除图片</a><p class="tip">支持扩展名png/jpg/gif/mp4</p> </div> <div> <img src="" alt="" class="pic" style="display: none"><input type="hidden" name="' + uploadName[i] + '" value=""/> </div> </div> </div>')
      }
      $(".cube-setting-layer .customLayout").find('.columns-dynamics').empty().html(htmlDynamicsArr.join(''));

      layui.form.val('floorSettingForm', {
        width: data.width,
        height: data.height,
        columns: data.columns,
        spacing: data.spacing,
        columnWidthOne: data.columnWidthOne || '',
        columnWidthTwo: data.columnWidthTwo || '',
        columnWidthThree: data.columnWidthThree || '',
        columnWidthFour: data.columnWidthFour || '',
        columnWidthFive: data.columnWidthFive || '',
        columnWidthSix: data.columnWidthSix || '',
        columnWidthSeven: data.columnWidthSeven || '',
        columnOneHref: data.columnOneHref || '',
        columnTwoHref: data.columnTwoHref || '',
        columnThreeHref: data.columnThreeHref || '',
        columnFourHref: data.columnFourHref || '',
        columnOneUrl: data.columnOneUrl || '',
        columnTwoUrl: data.columnTwoUrl || '',
        columnThreeUrl: data.columnThreeUrl || '',
        columnFourUrl: data.columnFourUrl || ''
      })

      if (data.columnOneUrl) {
        $(".cube-setting-layer").find(".customLayout").find("input[name='columnOneUrl']").prev().attr('src', data.columnOneUrl).show();
      }
      if (data.columnTwoUrl) {
        $(".cube-setting-layer").find(".customLayout").find("input[name='columnTwoUrl']").prev().attr('src', data.columnTwoUrl).show();
      }
      if (data.columnThreeUrl) {
        $(".cube-setting-layer").find(".customLayout").find("input[name='columnThreeUrl']").prev().attr('src', data.columnThreeUrl).show();
      }
      if (data.columnFourUrl) {
        $(".cube-setting-layer").find(".customLayout").find("input[name='columnFourUrl']").prev().attr('src', data.columnFourUrl).show();
      }

      IndexController.uploadChange(); //初始化上传组件
      IndexController.calcTotal(); //列宽总和计算

    },
    /**
     * 楼层设置读取配置-自定义布局-热区
     */
    settingHotRender: function (data) {
      $(".cube-setting-layer").find(".hot-area").find('.columns').attr('disabled', 'disabled');
      //根据布局设置的列数显示热区的列数，不可编辑
      layui.form.val('hotForm', {
        columns: data.columns,
      })
      if (data.columns) {
        var columns = Number(data.columns);
        var hot = data.hot || [];
        var htmlArr = [];
        for (let i = 0; i < columns; i++) {
          htmlArr.push('<div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列宽度</label>' +
            '                            <div class="layui-input-inline">' +
            '                                <input type="text" name="width"  autocomplete="off" placeholder="请输入宽度" class="layui-input width" value="' + (hot.length > 0 ? hot[i].width : '') + '">' +
            '                            </div>' +
            '                            <div class="layui-form-mid layui-word-aux">px</div>' +
            '                        </div>' +
            '                        <div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列高度</label>' +
            '                            <div class="layui-input-inline">' +
            '                                <input type="text" name="height" autocomplete="off" placeholder="请输入高度" class="layui-input height" value="' + (hot.length > 0 ? hot[i].height : '') + '">' +
            '                            </div>' +
            '                            <div class="layui-form-mid layui-word-aux">px</div>' +
            '                        </div>' +
            '                        <div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列X轴</label>' +
            '                            <div class="layui-input-inline" style="width: 108px;">' +
            '                                <input type="text" name="x" autocomplete="off" placeholder="请输入X轴位置" class="layui-input x" value="' + (hot.length > 0 ? hot[i].x : '') + '">' +
            '                            </div>' +
            '                            <div class="layui-form-mid layui-word-aux">（可拖动热区调整）</div>' +
            '                        </div>' +
            '                        <div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列Y轴</label>' +
            '                            <div class="layui-input-inline" style="width: 108px;">' +
            '                                <input type="text" name="y"  autocomplete="off" placeholder="请输入Y轴位置" class="layui-input y" value="' + (hot.length > 0 ? hot[i].y : '') + '">' +
            '                            </div>' +
            '                            <div class="layui-form-mid layui-word-aux">（可拖动热区调整）</div>' +
            '                        </div>' +
            '                        <div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列地址</label>' +
            '                            <div class="layui-input-block" style="margin-left: 90px;">' +
            '                                <input type="text" name="columnOneHref" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url" value="' + (hot.length > 0 ? hot[i].url : '') + '">' +
            '                            </div>' +
            '                            <div class="row bothSide verCenter bar-wrap" style="padding-left: 90px;">' +
            '                                <div>' +
            '                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>' +
            '                                    <a href="javascript:;" class="clearPic">清除图片</a>' +
            '                                    <p class="tip">支持扩展名png/jpg/gif/mp4</p>' +
            '                                </div>' +
            '                                <div>' +
            '                                    <img src="' + (hot.length > 0 ? hot[i].pic : '') + '" alt="" class="pic" style="' + (hot.length > 0 ? (hot[i].pic ? 'display:block' : 'display:none') : 'display:none') + '">' +
            '                                    <input type="hidden" name="columnOneUrl" value="' + (hot.length > 0 ? hot[i].pic : '') + '">' +
            '                                </div>' +
            '                            </div>' +
            '                        </div>');

        }
        $(".cube-setting-layer").find(".hot-area").find('.hot-columns-dynamics').empty().html(htmlArr.join(''));
      }
      IndexController.uploadChange(); //初始化上传组件
    },
    /**
     * 楼层设置读取配置-抽奖-圆
     * @param data
     */
    settingRenderLotteryCircle: function (data) {
      layui.form.val('lotteryCircleForm', {
        lotteryId: data.lotteryId,
        num: data.num,
        lotteryCircleBg: data.lotteryCircleBg,
        contactBg: data.contactBg,
        prizeBg: data.prizeBg,
        bg: data.bg,
        prizeMsg: data.prizeMsg
      })

      if (data.num) {
        let num = Number(data.num);
        $(".cube-setting-layer").find(".lotteryCircle").find(".angle").text(360 / num);
      }

      if (data.lotteryCircleBg) {
        $(".cube-setting-layer").find(".lotteryCircle").find("input[name='lotteryCircleBg']").prev().attr('src', data.lotteryCircleBg).show();
      }

      if (data.contactBg) {
        $(".cube-setting-layer").find(".lotteryCircle").find("input[name='contactBg']").prev().attr('src', data.contactBg).show();
      }

      if (data.prizeBg) {
        $(".cube-setting-layer").find(".lotteryCircle").find("input[name='prizeBg']").prev().attr('src', data.prizeBg).show();
      }

      if (data.bg) {
        $(".cube-setting-layer").find(".lotteryCircle").find("input[name='bg']").prev().attr('src', data.bg).show();
      }
    },
    /**
     * 楼层设置读取配置-抽奖-方
     * @param data
     */
    settingRenderLotterySquare: function (data) {
      layui.form.val('lotterySquareForm', {
        lotteryId: data.lotteryId,
        width: data.width,
        lotterySquareBg: data.lotterySquareBg,
        contactBg: data.contactBg,
        prizeBg: data.prizeBg,
        bg: data.bg,
        prizeMsg: data.prizeMsg
      })

      if (data.width) {
        let width = data.width * 1;
        $(".cube-setting-layer").find(".lotterySquare").find('.total-width').text((width * 3) + 22);
      }

      if (data.lotterySquareBg) {
        $(".cube-setting-layer").find(".lotterySquare").find("input[name='lotterySquareBg']").prev().attr('src', data.lotterySquareBg).show();
      }

      if (data.contactBg) {
        $(".cube-setting-layer").find(".lotterySquare").find("input[name='contactBg']").prev().attr('src', data.contactBg).show();
      }

      if (data.prizeBg) {
        $(".cube-setting-layer").find(".lotterySquare").find("input[name='prizeBg']").prev().attr('src', data.prizeBg).show();
      }

      if (data.bg) {
        $(".cube-setting-layer").find(".lotterySquare").find("input[name='bg']").prev().attr('src', data.bg).show();
      }
    },
    /**
     * 楼层设置读取配置-商品列表
     * @param data
     */
    settingRenderShopList(data) {
      //搜索框
      if (data.is_search) {
        $(".cube-setting-layer").find(".shopList").find('#is_search').next().next().show().val(data.is_search_color);
        $(".cube-setting-layer").find(".shopList").find('#is_search').prop('checked', 'checked');
      }
      layui.form.val('shopListForm', {
        partition: data.partition,
        dimension: data.dimension
      })

      if (data.dimension == 1) {
        //型号
        $("#goodsBox").show();
        $("#shopSort").hide();
        $("#recommendation").hide();
        if (data.sku_id_upload_file) {
          $(".cube-setting-layer").find(".shopList").find(".layui-icon-download-circle").attr('href', data.sku_id_upload_file);
          $(".cube-setting-layer").find(".shopList").find("#sku_id_upload").text('重新上传');
        }
        IndexController.uploadChangeSKU(); //初始化sku上传
        IndexController.getZhuanYingSupList(data.choice_goods_data.supplier_ids, data.choice_goods_data.supplier_codes); //初始化供应商
      } else if (data.dimension == 2) {
        //商品属性
        $("#goodsBox").hide();
        $("#shopSort").show();
        $("#recommendation").hide();
        if (data.sku_property_data.length > 0) {
          var html = '';
          $.each(data.sku_property_data, function (index, element) {
            html += `
                    <tr>
                      <td class="classify">
                        <input type="hidden" class="type" value="1">
                        <input type="text" placeholder="逗号隔开" class="layui-input class_ids" value="${data.sku_property_data[index].class_ids}">
                      </td>
                      <td class="brand">
                        <input type="text" placeholder="逗号隔开" class="layui-input brand_ids" value="${data.sku_property_data[index].brand_ids}">
                      </td>
                      <td>
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger delete">删除</button>
                      </td>
                    </tr>
                      `
          });
          $("#rowAreaSelf").empty().html(html);
        }
      } else if (data.dimension == 3) {
        //新品推荐
        $("#goodsBox").hide();
        $("#shopSort").hide();
        $("#recommendation").show();
      }

    },
    /**
     * 楼层设置读取配置-视频
     * @param data
     */
    settingRenderVideo(data) {
      layui.form.val('videoSettingForm', {
        url: data.url
      })
    },
    /**
     * 楼层设置读取配置-轮播
     * @param data
     */
    settingRenderCarousel(data) {
      layui.form.val('carouselSettingForm', {
        num: data.num
      })
      var num = parseInt(data.num) || 1; // 确保num是一个有效的数字
      var $columnsDynamics = $(".cube-setting-layer").find(".carousel").find('.columns-dynamics'); // 获取轮播容器
      $columnsDynamics.empty();   // 清空之前生成的列
      // 将pic字符串分割成数组
      var picValue = data.pic.split(','); // 获取图片地址数组

      // 根据所选图片个数动态生成列
      for (let i = 1; i <= num; i++) {
        let name = `column_${i}`; // 动态生成name属性的值
        var hrefValue = data[name] || ''; // 获取对应的href值
        var imageSrc = picValue[i - 1] || ''; // 获取对应的图片地址
        let $div = $(`
            <div class="layui-form-item">
                <label class="layui-form-label required" style="width: 83px;">第${i}列</label>
                <div class="layui-input-block" style="margin-left: 85px;">
                    <input type="text" name="${name}" value="${hrefValue}" autocomplete="off" placeholder="跳转地址" class="layui-input url" lay-verify="required" lay-reqtext="请填写跳转地址" lay-vertype="tips">
                </div>
                <div class="row bothSide verCenter bar-wrap" style="padding-left: 85px;">
                    <div>
                        <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>
                        <a href="javascript:;" class="clearPic">清除图片</a>
                        <p class="tip">支持扩展名png/jpg/gif/mp4</p>
                    </div>
                    <div>
                        <img src="${imageSrc}" alt="" class="pic" style="display: block">
                    </div>
                </div>
            </div>
        `);
        $columnsDynamics.append($div);
      }
      IndexController.uploadChange();
    },
    /**
     * 楼层设置读取配置-表单模块
     * @param data
     */
    render: function (width, height, column, spacing, columnWidth, hot, type) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').removeAttr('href').removeAttr('target');
      if (width) {
        if (width == '1920') {
          $(".cube-set-drag-area").find(".cube-initial").eq(current).css({
            width: '100%'
          });
        } else {
          $(".cube-set-drag-area").find(".cube-initial").eq(current).css({
            width: width
          });
        }
      }
      if (height) {
        $(".cube-set-drag-area").find(".cube-initial").eq(current).css({
          height: height
        });
      }
      if (spacing != 0) {
        $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().css({
          background: $("#page_color").val() ? $("#page_color").val() : '#FFFFFF'
        });
      }
      if (column) {
        if (column == 1) {
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').addClass('cube-set-drag-content-row').empty().css({background: "#f8f8f8"});
        } else if (column == 2) {
          if (spacing != 0) {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;"></a>');
          } else {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;"></a>');
          }
        } else if (column == 3) {
          if (spacing != 0) {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;"></a>');
          } else {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;"></a>');
          }
        } else if (column == 4) {
          if (spacing != 0) {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthFour + 'px;height: 100%;"></a>');
          } else {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthFour + 'px;height: 100%;"></a>');
          }
        } else if (column == 5) {
          if (spacing != 0) {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthFour + 'px;height: 100%;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthFive + 'px;height: 100%;"></a>');
          } else {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthFour + 'px;height: 100%;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthFive + 'px;height: 100%;"></a>');
          }
        } else if (column == 6) {
          if (spacing != 0) {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthFour + 'px;height: 100%;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthFive + 'px;height: 100%;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthSix + 'px;height: 100%;"></a>');
          } else {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthFour + 'px;height: 100%;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthFive + 'px;height: 100%;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthSix + 'px;height: 100%;"></a>');
          }
        } else if (column == 7) {
          if (spacing != 0) {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthFour + 'px;height: 100%;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthFive + 'px;height: 100%;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthSix + 'px;height: 100%;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthSeven + 'px;height: 100%;"></a>');
          } else {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthFour + 'px;height: 100%;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthFive + 'px;height: 100%;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthSix + 'px;height: 100%;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthSeven + 'px;height: 100%;"></a>');
          }
        }
      }
      var pic_arr = [];
      var href_arr = [];
      $(".columns-dynamics .pic").each(function (index, element) {
        let picUrl = $(element).attr('src');
        let href_val = $(element).parent().parent().parent().find('.url').val();
        if (column == 1) {
          if ($(element).attr('src')) {
            $(".cube-set-drag-area .cube-initial").eq(current).find('.cube-set-drag-content').css("background-image", "url(" + picUrl + ")").css("background-position", "top center").css("background-repeat", "no-repeat");
          }
          if ($(element).parent().parent().parent().find('.url').val()) {
            $(".cube-set-drag-area .cube-initial").eq(current).find('.cube-set-drag-content').attr('href', href_val).attr('target', '_blank');
          }
        } else {
          if ($(element).attr('src')) {
            $(".cube-set-drag-area .cube-initial").eq(current).find('a').eq(index).css("background-image", "url(" + picUrl + ")").css("background-position", "top center").css("background-repeat", "no-repeat");
          }
          if ($(element).parent().parent().parent().find('.url').val()) {
            $(".cube-set-drag-area .cube-initial").eq(current).find('a').eq(index).attr('href', href_val).attr('target', '_blank');
          }
        }
      });

      //热区区域
      if (hot.length > 0) {
        if (column == 1) {
          var background = hot[0].pic ? 'background:url(' + hot[0].pic + ');background-position:center center;background-repeat:no-repeat;background-size:cover;' : 'background-color:rgba(0,0,0,0.5);';
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<span class="hot-box" data-href="' + hot[0].url + '" style="position: absolute;z-index:9999999999999999;max-width: 100%;max-height: 100%;cursor: move;width: ' + hot[0].width + 'px;height:' + hot[0].height + 'px;left:' + hot[0].x + 'px;top:' + hot[0].y + 'px;' + background + '"></span>');
          //启动拖拽
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').find('.hot-box').Tdrag({
            scope: $(this).parent(),
            cbMove: function (val) {
              var currentData = JSON.parse($(".cube-set-drag-area").find(".cube-initial").eq(current).attr('data'));
              $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content-row').each(function (index, element) {
                if ($(element).find('.hot-box').length > 0) {
                  var left = $(element).find(".hot-box").css('left');
                  var top = $(element).find(".hot-box").css('top');
                  var data_index = $(element).find(".hot-box").attr('data-index') * 1;
                  currentData.hot[index].x = parseFloat(left); //替换data里x轴
                  currentData.hot[index].y = parseFloat(top); //替换data里y轴
                  $(".cube-setting-layer").find(".hot-columns-dynamics").find('.x').eq(index).val(parseFloat(left));
                  $(".cube-setting-layer").find(".hot-columns-dynamics").find('.y').eq(index).val(parseFloat(top));
                }
              })
              $(".cube-set-drag-area").find(".cube-initial").eq(current).attr('data', JSON.stringify(currentData));
            },
            cbEnd: function () {
              IndexController.updatePcData();//更新数据
            }
          });
        } else {
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content a').each(function (index, element) {
            if (Number(hot[index].width) > 0 && Number(hot[index].height) > 0) {
              var background = hot[index].pic ? 'background:url(' + hot[index].pic + ');background-position:center center;background-repeat:no-repeat;background-size:cover;' : 'background-color:rgba(0,0,0,0.5);';
              $(element).empty().append('<span class="hot-box" data-index="' + index + '" data-href="' + hot[index].url + '" style="position: absolute;z-index:9999999999999999;max-width: 100%;max-height: 100%;cursor: move;width: ' + hot[index].width + 'px;height:' + hot[index].height + 'px;left:' + hot[index].x + 'px;top:' + hot[index].y + 'px;' + background + '"></span>');
            }
          })
          //启动拖拽
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').find('.hot-box').each(function (index, element) {
            $(element).Tdrag({
              scope: $(element).parent(),
              cbMove: function (val) {
                var currentData = JSON.parse($(".cube-set-drag-area").find(".cube-initial").eq(current).attr('data'));
                $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content .customLayout-box').each(function (index, element) {
                  if ($(element).find('.hot-box').length > 0) {
                    var left = $(element).find(".hot-box").css('left');
                    var top = $(element).find(".hot-box").css('top');
                    var data_index = $(element).find(".hot-box").attr('data-index') * 1;
                    currentData.hot[index].x = parseFloat(left); //替换data里x轴
                    currentData.hot[index].y = parseFloat(top); //替换data里y轴
                    $(".cube-setting-layer").find(".hot-columns-dynamics").find('.x').eq(index).val(parseFloat(left));
                    $(".cube-setting-layer").find(".hot-columns-dynamics").find('.y').eq(index).val(parseFloat(top));
                  }
                })
                $(".cube-set-drag-area").find(".cube-initial").eq(current).attr('data', JSON.stringify(currentData));
              },
              cbEnd: function () {
                IndexController.updatePcData();//更新数据
              }
            });
          })
        }
      }
    },
    /**
     * 解析抽奖-圆模板
     * @param num  奖项个数
     * @param lotteryCircleBg 抽奖图片
     * @param contactBg 联系方式按钮
     * @param prizeBg 我的奖品按钮
     * @param bg 背景图
     * @param type 布局类型
     */
    renderlotteryCircle: function (num, lotteryCircleBg, contactBg, prizeBg, bg, type) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      var num = Number(num);
      if (num) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.angle').text(360 / num);
      }

      if (bg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotteryCircle-bg').css("background-image", "url(" + bg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotteryCircle-bg').removeAttr('style');
      }

      if (lotteryCircleBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.turntable-bg').find('.turntable-pic').attr('src', lotteryCircleBg);
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.turntable-bg').find('.turntable-pic').attr('src', 'https://img.ichunt.com/images/ichunt/202303/10/ed68d3caee2395f7a3c3c4d7138a3ca1.png');
      }

      if (contactBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.contactBg').empty().css("background-image", "url(" + contactBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.contactBg').empty().text('请填写联系方式').css("background-image", "url(" + contactBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      }

      if (prizeBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.prizeBg').empty().css("background-image", "url(" + prizeBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.prizeBg').empty().text('查看我的奖品').css("background-image", "url(" + prizeBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      }
    },
    /**
     * 解析抽奖-方模板
     * @param width  各奖项长/宽
     * @param lotterySquareBg 抽奖图片
     * @param contactBg 联系方式按钮
     * @param prizeBg 我的奖品按钮
     * @param bg 背景图
     * @param type 布局类型
     */
    renderlotterySquare: function (width, lotterySquareBg, contactBg, prizeBg, bg, type) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      var width = Number(width);
      if (width) {
        let total_width = Number(width) * 3 + 22;
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').css("width", total_width);
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').css("height", total_width);
        $(".cube-set-drag-area .cube-initial").eq(current).find('.btn-wrap').css("width", total_width);
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box1').css({
          left: 0,
          top: 0,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box2').css({
          left: width + 11,
          top: 0,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box3').css({
          left: (width * 2) + 22,
          top: 0,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box4').css({
          left: (width * 2) + 22,
          top: width + 11,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box5').css({
          left: (width * 2) + 22,
          top: (width * 2) + 22,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box6').css({
          left: width + 11,
          top: (width * 2) + 22,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box7').css({
          left: 0,
          top: (width * 2) + 22,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box8').css({
          left: 0,
          top: width + 11,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.btnClick').css({
          left: width + 11,
          top: width + 11,
          width: width,
          height: width
        });

      }

      if (bg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-bg').css("background-image", "url(" + bg + ")").css("background-position", "center top").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-bg').removeAttr('style');
      }

      if (lotterySquareBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').css("background-image", "url(" + lotterySquareBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "contain");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').css("background-image", "url('https://img.ichunt.com/images/ichunt/202303/02/6ba6bcfe5451234780fc410fe5d51645.png')").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "contain");
      }

      if (contactBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.contactBg').empty().css("background-image", "url(" + contactBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.contactBg').empty().text('请填写联系方式').css("background-image", "url(" + contactBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      }
      if (prizeBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.prizeBg').empty().css("background-image", "url(" + prizeBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.prizeBg').empty().text('查看我的奖品').css("background-image", "url(" + prizeBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      }
    },
    /**
     * 解析视频模块
     * @param data
     */
    renderVideo: function (data, type) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      var html = `<video src="${data.url}" poster="https://img.ichunt.com/images/ichunt/202403/28/2fc1a444b3dda034481628b0c287edaa.jpg" webkit-playsinline="" playsinline="" class="uni-video-video" style="position: relative;object-fit: fill;width: 100%;height: 100%;z-index: 999"></video>`
      $(".cube-set-drag-area .cube-initial").eq(current).find('.video').empty().html(html);
    },
    /**
     * 解析商品列表模板
     */
    renderShopList: function (params) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      var html = '<img src="https://img.ichunt.com/images/ichunt/202404/23/9c3df2f7aa1b9bc71ff2598c2a8a3011.png" class="iedgeImg" style="display: block;margin: 0 auto;"/>'
      $(".cube-set-drag-area .cube-initial").eq(current).find('.shopList').empty().html(html);
      IndexController.updatePcData(); //更新数据
    },
    /**
     * 解析轮播模块
     * @param data
     */
    renderCarousel: function (data, type) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      var numColumns = parseInt(data.num) || 1; // 确保num是一个有效的数字
      var $carouselContainer = $(".cube-set-drag-area .cube-initial").eq(current).find('.carousel'); // 获取轮播容器
      $carouselContainer.empty(); // 清空轮播容器

      // 将pic字符串分割成数组
      var picValue = data.pic.split(','); // 获取图片地址数组

      // 根据num创建ul和li元素
      var ulHtml = `<div class="bd" style="width: 100%;height: 520px;overflow: hidden;"><ul style="width: 100%;height: 520px;">`;
      for (var i = 1; i <= numColumns; i++) {
        var columnKey = 'column_' + i; // 创建键名，如 'column_1', 'column_2' 等
        var hrefValue = data[columnKey] || ''; // 获取对应的href值
        var imageSrc = picValue[i - 1] || ''; // 获取对应的图片地址

        ulHtml += `<li style="float:left;width: 100%;height: 100%;">
                  <a style="display: block;width: 100%;height: 100%;" href="${hrefValue}" target="_blank">
                    <img src="${imageSrc}" alt="轮播图${i}" style="object-fit: cover;width: 100%;height: 100%;display: block;">
                  </a>
                </li>`;
      }
      ulHtml += '</ul></div>'; // 结束ul标签

      // 将创建的html设置到轮播容器中
      $carouselContainer.html(ulHtml);
    },
    /**
     * 上传组件 背景图
     */
    uploadChange(preview) {
      var index = $(".cube-setting-layer").attr('data-index');
      var type = $(".cube-setting-layer").attr('data-type');
      //图片预览
      if (preview) {
        layer.photos({
          photos: '.cube-setting-layer .layui-tab-content .layui-tab-item.layui-show',
          anim: 5
        });
      } else {
        $(".cube-setting-layer .layui-tab-content").find(".layui-tab-item.layui-show").find(".uploadPic").each(function (index, element) {
          layui.upload.render({
            elem: $(element),
            url: oss_url + '/uploadFile?sys_type=7',
            accept: 'file',
            exts: 'jpg|png|gif|jpeg|png|mp4',
            before: function () {
              layer.load(2);
            },
            done: function (res) {
              layer.closeAll('loading');
              if (res.code === 0) {
                $(this.item).parent().parent().find('.pic').show().attr('src', res.data.oss_file_url);
                $(this.item).parent().parent().find('.pic').next().val(res.data.oss_file_url);
                //图片预览
                layer.photos({
                  photos: '.cube-setting-layer .layui-tab-content .layui-tab-item.layui-show',
                  anim: 5
                });
              } else {
                layer.msg(res.msg);
              }
            },
            error: function (index, upload) {
              layer.closeAll('loading');
              layer.msg('网络出现问题，请重试！');
            }
          });
        });
      }
    },
    /**
     * 事件绑定
     * @returns {Window.IndexController}
     */
    handleBind: function () {

      //系统原件监听
      $(document).on('change', '#page_navigation_bar', function () {
        var val = this.checked;
        IndexController.updatePcData();
      });

      //右侧快键入口监听
      $(document).on('change', '#page_right_bar', function () {
        var val = this.checked;
        IndexController.updatePcData();
      });

      //清除图片
      $(document).on('click', '.clearPic', function () {
        $(this).parent().parent().find('img.pic').hide().attr('src', '').next().val('');
      })

      //清除背景图片
      $(document).on('click', '.clearBg', function () {
        $("body").css("background-image", "url('')").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "contain");
        $("#page_background").hide().attr('src', '');
        IndexController.updatePcData();
      })

      //编辑楼层名字
      $(document).on('click', '.floor-text', function () {
        var page_index = $(this).parent().parent().parent().attr('data-index');
        var text = $(this).prev().text();
        var self = $(this);
        layer.prompt({title: '修改楼层名字', formType: 0, value: text,}, function (value, index) {
          self.prev().text(value);
          $(".cube-set-drag-area .cube-initial").eq(page_index).attr('data-name', value)
          layer.closeAll();
          IndexController.updatePcData();
        });
      })

      //热区区域点击
      $(document).on('click', '.hot-box', function (event) {
        return false;
      });

      //楼层第一列如果配置了href点击跳转
      $(document).on('click', '.cube-set-drag-area .cube-set-drag-content-row', function (event) {
        var href = $(this).attr('href');
        if (href) {
          toUrl(href);
        }
        return false;
      });

      //切换活动名称
      $(document).on('change', '.env', function () {
        var val = $(this).val();
        if (val == 1) {
          window.location.href = '/web/activity/set/?from=pc&id=' + id;
        } else if (val == 2) {
          window.location.href = '/web/activity/set/h5?from=h5&id=' + id;
        }
      });

      //背景色
      $(document).on('input', '.cube-set-head #page_color', debounce(function (e) {
        var val = $(this).val();
        $("body").css('background-color', val);
        $(".cube-set-drag-area").find('.customLayout ').css('background-color', val);
        IndexController.updatePcData();
      }, 500));

      //楼层删除
      $(document).on('click', '.cube-set-drag-area .delete', function (event) {
        var self = $(this);
        layer.confirm('是否要<em class="warm-color">删除</em>该楼层？', {
          skin: 'layui-layer-admin',
          title: '提示',
          shade: .1,
          resize: false,
          offset: '250px',
          move: false
        }, function (index) {
          self.parent().parent().parent().remove();
          IndexController.sortNumChange(); //更新排序
          IndexController.updatePcData(); //更新数据
          $(".cube-setting-layer").hide(); //楼层隐藏
          layer.close(index);
        });
      });

      //全局楼层状态切换
      form.on('switch(statusChange)', function (data) {
        var current = data.value * 1;
        var elem = data.elem;
        var checked = data.elem.checked;
        if (checked) {
          $(".cube-set-drag-area .cube-initial").eq(current).show();
        } else {
          $(".cube-set-drag-area .cube-initial").eq(current).hide();
        }
        IndexController.updatePcData(); //更新数据
      });

      //楼层tab切换
      element.on('tab(tabChange)', function (data) {
        var floor = $(".cube-setting-layer").attr('data-index');
        var index = data.index;
        if (index == 0) {
          //布局设置
        } else if (index == 1) {
          //全局楼层
          IndexController.floorsChangeGlobal();
        } else if (index == 2) {
          //热区
          var data = $(".cube-set-drag-area").find(".cube-initial").eq(floor).attr('data');
          if (data) {
            $("#hotSubmit").removeClass("layui-btn-disabled").attr("disabled", false);
            IndexController.settingHotRender(JSON.parse(data));
          } else {
            $("#hotSubmit").addClass("layui-btn-disabled").attr("disabled", true);
            layer.msg('请先设置布局设置');
          }
        }
      });

      //楼层上移
      $(document).on('click', '.cube-set-drag-area .move-up', function (event) {
        var parent = $(this).parent().parent().parent(); //拿到父级
        var prev = parent.prev(); //获取同胞元素的上一个元素
        parent.insertBefore(prev); //将它插入到当前元素之后，实现当前元素上移
        IndexController.sortNumChange(); //更新排序
        IndexController.updatePcData(); //更新数据
      });

      //楼层下移
      $(document).on('click', '.cube-set-drag-area .move-down', function (event) {
        var parent = $(this).parent().parent().parent(); // 拿到父级
        var next = parent.next(); //获取同胞元素的下一个元素
        parent.insertAfter(next); //将它插入到当前元素之前，实现当前元素下移
        IndexController.sortNumChange(); //更新排序
        IndexController.updatePcData(); //更新数据
      });

      //楼层设置显示
      $(document).on('click', '.cube-set-drag-area .settingObj', function (event) {
        var index = $(this).parent().parent().parent().attr('data-index') * 1;
        var type = $(this).parent().parent().parent().attr('data-type');
        var uuid = $(this).parent().parent().parent().attr('data-uuid');
        var data = $(this).parent().parent().parent().attr('data');
        var data_name = $('.cube-set-drag-area .cube-initial').eq(index).attr('data-name') || '';

        var tpl = floorHtml.innerHTML;
        layui.laytpl(tpl).render(type, function (html) {
          $(".cube-setting-layer").empty().append(html);
          //判断是否自定义楼层名字
          if (data_name) {
            $(".cube-setting-layer").show().find('.floor').empty().text(data_name); //楼层数字标记
          } else {
            $(".cube-setting-layer").show().find('.floor').empty().text('楼层' + (index + 1)); //楼层数字标记
          }
          $(".cube-setting-layer").show().find('.floor-name').empty().text('（' + textVal[type] + '）'); //楼层数字标记
          $(".cube-setting-layer").attr('data-index', index);
          $(".cube-setting-layer").attr('data-type', type);
          $(".cube-setting-layer").attr('data-uuid', uuid);
          //判断是否存储数据
          if (data) {
            if (type == 'customLayout') {
              IndexController.settingRender(JSON.parse(data));
            }else if (type == 'lotteryCircle') {
              IndexController.settingRenderLotteryCircle(JSON.parse(data));
              IndexController.uploadChange(); //第一次调用是初始化上传组件
            } else if (type == 'lotterySquare') {
              IndexController.settingRenderLotterySquare(JSON.parse(data));
              IndexController.uploadChange(); //第一次调用是初始化上传组件
            }  else if (type == 'shopList') {
              IndexController.settingRenderShopList(JSON.parse(data));
            } else if (type == 'video') {
              IndexController.settingRenderVideo(JSON.parse(data));
            } else if (type == 'carousel') {
              IndexController.settingRenderCarousel(JSON.parse(data));
            }
          } else {
            if (type == 'shopList') {
              IndexController.uploadChangeSKU(); //初始化sku上传
              IndexController.getZhuanYingSupList(); //初始化供应商
            }
            IndexController.uploadChange();
          }

          //启用拖动
          $(".cube-setting-layer").Tdrag({
            scope: 'body',
            handle: ".title"
          });

          $(window).resize(function () {
            $(".cube-setting-layer").css('left', 'inherit');
          });


          layui.form.render();
        });
      });

      //楼层收缩
      $(document).on('click', '.cube-setting-layer .toggle', function (event) {
        $(".cube-setting-layer").slideUp();
      });

      //元件库收缩
      $(document).on('click', '.left-menu .shousuo-box', function (event) {
        $(".left-menu").toggleClass('shousuo-box-curr');
        if ($(this).parent().hasClass('shousuo-box-curr')) {
          $(this).attr('title', '展开');
        } else {
          $(this).attr('title', '收缩');
        }
      });

      //上传背景图
      layui.upload.render({
        elem: '#page_background_upload',
        url: oss_url + '/uploadFile?sys_type=7',
        accept: 'file',
        exts: 'jpg|png|gif|jpeg|png',
        before: function () {
          layer.load(2);
        },
        done: function (res) {
          layer.closeAll('loading');
          if (res.code === 0) {
            $(this.item).parent().find('.page-background').attr('src', res.data.oss_file_url).show();
            $("body").css("background-image", "url(" + res.data.oss_file_url + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "contain");
            IndexController.updatePcData();
            //图片预览
            layer.photos({
              photos: '.cube-set-head',
              anim: 5
            });
          } else {
            layer.msg(res.msg);
          }
        },
        error: function (index, upload) {
          layer.closeAll('loading');
          layer.msg('网络出现问题，请重试！');
        }
      });

      // 选择视频元素并绑定点击事件
      $(document).on('click', '.uni-video-video', function () {
        this.play();
      });

      return this;
    }
  }

  IndexController.init();

})
