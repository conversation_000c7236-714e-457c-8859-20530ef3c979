//存class数字数组
function  abbcqqqqx(){
    var arrb=[
        ['asfgdqwer','asfgdtyhg','asfgdpolk','asfgdpoqw'],
        ['asfgdrfdf','asfgderfd','asfgdwdsa','asfgdpoer'],
        ['asfgdasde','asfgdqwsz','asfgdrtgd','asfgdpovv'],
        ['asfgdwsxc','asfgdwsxz','asfgdrfvb','asfgdpoee'],
        ['asfgdqazs','asfgdqasd','asfgdqwag','asfgdpogh'],
        ['asfgdrtyh','asfgdyutr','asfgdeews','asfgdpotg'],
        ['asfgdpluj','asfgdikjf','asfgdesgj','asfgdpfff'],
        ['asfgdtrdb','asfgdiksf','asfgdsgkp','asfgdprty'],
        ['asfgdpehl','asfgdstgb','asfgderll','asfgdpokf'],
        ['asfgdpehg','asfgdstgf','asfgderlf','asfgdpogk']
    ];
    return arrb

}
window.abbcqqqqx=abbcqqqqx;
//处理数字
function aggxen(number,ele){
    var num=String(number);
    var list=abbcqqqqx();
    var ht="";
    for(var i=0;i<num.length;i++){
        var tlist=list[Number(num[i])];
        var rad=Math.floor(Math.random()*4);
        var cl=tlist[rad];
        ht+='<font class="'+cl+' '+cl+'1'+' '+cl+'9'+' '+cl+'9'+' '+cl+'3'+' '+cl+'x'+' '+cl+'k'+' '+'"></font>'
    }
    ele.html(ht)
}
window.aggxen=aggxen;
//反向解密
function aggxde(str){
    if(str&&str.indexOf("font")!=-1){
        var sff=(str.split('class="'));
            sff.shift();
            var numb="";
            var listb=abbcqqqqx();
            for(var i=0;i<sff.length;i++){
                var ghjk=sff[i].split(" ")[0];
                for(var j=0;j<listb.length;j++){
                    if(listb[j].indexOf(ghjk) != -1){
                        numb+=j;   
                    }
                }
            }
            return Number(numb)
    }
  
}
window.aggxde=aggxde;