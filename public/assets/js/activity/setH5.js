layui.use(['admin', 'index', 'form', 'table', 'laydate', 'element', 'upload', 'xmSelect'], function () {
  var admin = layui.admin;
  var index = layui.index;
  var form = layui.form;
  var table = layui.table;
  var laydate = layui.laydate;
  var element = layui.element;
  var upload = layui.upload;
  var xmSelect = layui.xmSelect;
  var id = getRequest('id') || '';
  var from = getRequest('from') || ''; //标记pc h5
  var uploadClass = ['columnOneHref', 'columnTwoHref', 'columnThreeHref', 'columnFourHref'];
  var uploadName = ['columnOneUrl', 'columnTwoUrl', 'columnThreeUrl', 'columnFourUrl'];
  var page_color = '#FFFFFF'; //页面颜色
  var page_background = ''; //页面背景
  var page_navigation_bar = false; //顶部导航栏
  var page_right_bar = false; //右侧快键入口

  //定义基本元件名称
  var textVal = {
    customLayout: '自定义布局',
    lotteryCircle: '抽奖-圆',
    lotterySquare: '抽奖-方',
    coupon: '优惠券',
    shopList: '商品列表',
    formModule: '表单模块'
  }

  window.IndexController = {
    init: function () {
      this.created(this).getActivityElementList().handleBind(this);
    },
    created: function () {
      this.getInfo(this);
      return this;
    },
    /**
     * 读取配置
     */
    getInfo() {
      Request('/api/activity/getInfo', 'GET', {id: id}, function (res) {
        if (res.code == 0) {
          if (res.data) {
            document.title = res.data.web_title; //更新页面标题
            var web_html = res.data.h5_html;
            var web_html_config = res.data.h5_html_config;
            //已过期
            if (res.data.activity_status == -1) {
              layer.open({
                type: 1,
                title: false,
                offset: '50px',
                closeBtn: 0,
                area: ['400px', 'auto'],
                shadeClose: false,
                resize: false,
                move: false,
                scrollbar: true,
                content: '<h2 style="padding: 10px;text-align: center;font-weight: bold;">该活动已过期，不可编辑!</h2>',
                success: function (layero, dIndex) {

                }
              });
            }
            //预览地址
            if (from == 'pc') {
              //系统元件是否选中
              var page_navigation_bar = web_html_config ? JSON.parse(web_html_config).baseConfig.page_navigation_bar : '';
              var page_right_bar = web_html_config ? JSON.parse(web_html_config).baseConfig.page_right_bar : '';
              if (page_navigation_bar == 1) {
                $("#page_navigation_bar").prop("checked", 'checked');
              }
              if (page_right_bar == 1) {
                $("#page_right_bar").prop("checked", 'checked');
              }
              $("#preview").attr('href', res.data.web_url + "?is_preview=1");
            }
            if (from == 'h5') {
              $("#preview").attr('href', res.data.h5_url + "?is_preview=1");
            }
            if (web_html_config) {
              web_html_config = JSON.parse(web_html_config);
              //页面背景图
              if (web_html_config.baseConfig && web_html_config.baseConfig.page_background) {
                $("body").css("background-image", "url(" + web_html_config.baseConfig.page_background + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "contain");
                $("#page_background").attr('src', web_html_config.baseConfig.page_background).show();
                //页面背景图片预览
                layer.photos({
                  photos: '.cube-set-head',
                  anim: 5
                });
              }
              //页面颜色
              if (web_html_config.baseConfig && web_html_config.baseConfig.page_color) {
                $("body").css('background-color', web_html_config.baseConfig.page_color);
                $("#page_color").val(web_html_config.baseConfig.page_color);
              }
            }
            if (web_html) {
              $(".web-html").empty().html(web_html);
            }
            $(".cube-setting-layer").hide(); //楼层关闭
            IndexController.customLayoutChange(); //自定义布局初始化
            IndexController.lotterySquareChange(); //抽奖-方初始化
            IndexController.lotteryCircleChnage(); //抽奖-圆初始化
            IndexController.couponChnage(); //优惠券-初始化
            IndexController.shopListChange(); //商品列表-初始化
            IndexController.formModuleChange(); //商品列表-初始化
          }
        }
      })
    },
    /**
     *获取活动元件列表
     */
    getActivityElementList: function () {
      Request('/api/activityElement/list?', 'GET', {}, function (res) {
        if (res.code == 0) {
          var tpl = elementHtml.innerHTML;
          layui.laytpl(tpl).render(res.data.list, function (html) {
            $("#element").append(html);
            IndexController.componentDragChange();
          });
        }
      })
      return this;
    },
    /**
     * 基本元件拖拽布局
     */
    componentDragChange: function () {
      //元件绑定拖拽事件
      var childs = document.querySelectorAll('.child');
      var body = document.body;

      childs = Array.from(childs);
      childs.forEach(function (item, index) {
        item.ondragstart = function (event) {
          console.log('我开始在被拖拽')
        }
        item.ondrag = function () {
          console.log('拖拽过程中')
        }
        item.ondragend = function (event) {
          var id = item.id;
          //根据所拖的组件选择对应的模板类型
          var tpl = layoutHtml.innerHTML;
          //商品列表只能配置一个
          if (id == 'shopList') {
            var length = $(".cube-set-drag-area").find('.shopList').length;
            if (length >= 1) {
              layer.msg('已配置商品列表，请勿重复配置');
              return false;
            }
          }
          if (id == 'lotteryCircle') {
            var length = $(".cube-set-drag-area").find('.lotteryCircle').length;
            if (length >= 1) {
              layer.msg('已配置圆盘抽奖，请勿重复配置');
              return false;
            }
          }
          if (id == 'lotterySquare') {
            var length = $(".cube-set-drag-area").find('.lotterySquare').length;
            if (length >= 1) {
              layer.msg('已配置九宫格抽奖，请勿重复配置');
              return false;
            }
          }
          if (id == 'coupon') {
            var length = $(".cube-set-drag-area").find('.coupon').length;
            if (length >= 1) {
              layer.msg('已配置优惠券，请勿重复配置');
              return false;
            }
          }

          layui.laytpl(tpl).render(id, function (html) {
            $(".cube-set-drag-area").append(html);
            IndexController.sortNumChange(); //更新排序
            if (id == 'customLayout') {
              //自定义布局
              IndexController.customLayoutChange();
            } else if (id == 'lotteryCircle') {
              //抽奖-圆
              IndexController.lotteryCircleChnage();
            } else if (id == 'lotterySquare') {
              //抽奖-方
              IndexController.lotterySquareChange();
            } else if (id == 'coupon') {
              //优惠券
              IndexController.couponChnage();
            } else if (id == 'shopList') {
              //商品列表
              IndexController.shopListChange();
            } else if (id == 'formModule') {
              //表单模块
              IndexController.formModuleChange();
            } else {
              layer.msg('正在开发中，敬请期待！');
            }
          });
          console.log('拖拽结束')
        }
      })

      document.body.ondragenter = function (event) {
        event.preventDefault();
      }
      document.body.ondragover = function (event) {
        event.preventDefault();
      }

      body.ondrop = function (event) {
        event.stopPropagation();
      }


      return this;
    },
    /**
     * 更新数据
     */
    updatePcData: function () {

      var params = {
        id: id,
        html: $(".web-html").html(),
        config: {
          baseConfig: {
            page_color: $("#page_color").val(),
            page_background: $("#page_background").attr('src') || '',
            page_navigation_bar: $("#page_navigation_bar").is(':checked') ? 1 : 0,
            page_right_bar: $("#page_right_bar").is(':checked') ? 1 : 0,
          }
        }
      }

      $(".cube-set-drag-area .cube-initial").each(function (index, element) {
        var data_type = $(element).attr('data-type');
        var data = $(element).attr('data');
        var data_uuid = $(element).attr('data-uuid');
        if (data_uuid) {
          if (data) {
            var assign = $.extend({}, JSON.parse(data), {
              basic_elements: data_type
            });
            params.config[data_uuid] = assign;
          } else {
            params.config[data_uuid] = {
              basic_elements: data_type
            };
          }
        }
      });

      Request('/api/activity/updateH5Data', 'POST', params, function (res) {
        if (res.code == 0) {

        } else {
          layer.msg(res.msg);
        }
      })
    },
    /**
     * 自定义布局
     * @param obj
     */
    customLayoutChange: function () {

      //楼层宽度监听
      $(document).on('input', '.customLayout .width', debounce(function (e) {
        var width = layui.form.val('floorSettingForm').width;
        var height = layui.form.val('floorSettingForm').height;
        var columns = layui.form.val('floorSettingForm').columns;
        var spacing = layui.form.val('floorSettingForm').spacing;

        if (columns == 1) {
          layui.form.val('floorSettingForm', {
            columnWidthOne: width,
            spacing: 0
          })
        }
        if (width > 750) {
          layui.form.val('floorSettingForm', {
            columnWidthOne: 750,
            width: 750
          })
        }

      }, 800));

      //楼层列数监听
      layui.form.on('select(columnsChange)', function (data) {
        var width = layui.form.val('floorSettingForm').width * 1;
        var height = layui.form.val('floorSettingForm').height * 1;
        var columns = layui.form.val('floorSettingForm').columns;
        var spacing = layui.form.val('floorSettingForm').spacing * 1;
        var current = $(".cube-setting-layer").attr('data-index') * 1;

        if (columns == 1) {
          layui.form.val('floorSettingForm', {
            columnWidthOne: width,
            spacing: 0
          })
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + width + '" style="width: 55px;">');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 2) {
          let widthPx = Number(width) - Number(spacing);
          widthPx = (widthPx / 2).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx
          })
          //动态列计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 3) {
          let widthPx = Number(width) - (Number(spacing) * 2);
          widthPx = (widthPx / 3).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx
          })
          //动态列计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 4) {
          let widthPx = Number(width) - (Number(spacing) * 3);
          widthPx = (widthPx / 4).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx
          })
          //动态列计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        }

        //动态列数
        var htmlDynamicsArr = [];
        for (let i = 0; i < columns; i++) {
          htmlDynamicsArr.push('<div class="layui-form-item"> <label class="layui-form-label">第' + (i + 1) + '列</label> <div class="layui-input-block" style="margin-left: 62px;"> <input type="text" name="' + uploadClass[i] + '" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url"> </div> <div class="row bothSide verCenter bar-wrap"> <div> <a class="layui-btn layui-btn-sm uploadPic">上传图片</a> <a href="javascript:;" class="clearPic">清除图片</a><p class="tip">支持扩展名png/jpg/gif/mp4</p> </div> <div> <img src="" alt="" class="pic" style="display: none"><input type="hidden" name="' + uploadName[i] + '" value=""/> </div> </div> </div>')
        }
        $(".cube-setting-layer").find(".customLayout").find('.columns-dynamics').empty().html(htmlDynamicsArr.join(''));
        IndexController.uploadChange(); //初始化上传组件
        IndexController.calcTotal();
      });

      //间距监听
      $(document).on('input', '.customLayout .spacing', debounce(function (e) {
        var width = layui.form.val('floorSettingForm').width * 1;
        var height = layui.form.val('floorSettingForm').height * 1;
        var columns = layui.form.val('floorSettingForm').columns;
        var spacing = layui.form.val('floorSettingForm').spacing * 1;

        if (columns == 1) {
          layui.form.val('floorSettingForm', {
            columnWidthOne: width,
            spacing: 0
          })
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + width + '" style="width: 55px;">');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 2) {
          let widthPx = Number(width) - Number(spacing);
          widthPx = (widthPx / 2).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx
          })
          //动态列计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 3) {
          let widthPx = Number(width) - (Number(spacing) * 2);
          widthPx = (widthPx / 3).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx
          })
          //动态列计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        } else if (columns == 4) {
          let widthPx = Number(width) - (Number(spacing) * 3);
          widthPx = (widthPx / 4).toFixed(1);
          layui.form.val('floorSettingForm', {
            columnWidthOne: widthPx,
            columnWidthTwo: widthPx,
            columnWidthThree: widthPx,
            columnWidthFour: widthPx
          })
          //动态列计算
          let htmlArr = [];
          htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
          $(".cube-setting-layer").find(".customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
        }

        IndexController.calcTotal();

      }, 800));

      //列宽监听
      $(document).on('input', '.customLayout .columnWidth', debounce(function (e) {
        IndexController.calcTotal();
      }, 800));

      //楼层保存按钮
      layui.form.on('submit(save)', function (data) {
        var width = layui.form.val('floorSettingForm').width;
        var height = layui.form.val('floorSettingForm').height;
        var columns = layui.form.val('floorSettingForm').columns;
        var spacing = layui.form.val('floorSettingForm').spacing;
        var total = $('.columnWidthDynamics').find('.total').text() * 1;
        var current = $(".cube-setting-layer").attr("data-index") * 1;
        var type = $(".cube-setting-layer").attr("data-type");
        if (columns != 1) {
          if (total != width) {
            layer.msg('请核对width之和等于' + width);
            return false;
          }
        }
        var columnWidth = {
          columnWidthOne: layui.form.val('floorSettingForm').columnWidthOne,
          columnWidthTwo: layui.form.val('floorSettingForm').columnWidthTwo,
          columnWidthThree: layui.form.val('floorSettingForm').columnWidthThree,
          columnWidthFour: layui.form.val('floorSettingForm').columnWidthFour
        };
        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data.field)); //存储单个配置
        IndexController.render(width, height, columns, spacing, columnWidth, '', type); //视图更新
        IndexController.updatePcData(); //更新数据
      });

      //楼层热区保存按钮
      layui.form.on('submit(hotSubmit)', function (data) {
        var width = layui.form.val('floorSettingForm').width;
        var height = layui.form.val('floorSettingForm').height;
        var columns = layui.form.val('floorSettingForm').columns * 1;
        var spacing = layui.form.val('floorSettingForm').spacing;
        var total = $('.columnWidthDynamics').find('.total').text() * 1;
        var current = $(".cube-setting-layer").attr("data-index") * 1;
        var type = $(".cube-setting-layer").attr("data-type");
        var hot = [];
        var floorSettingParmam = layui.form.val('floorSettingForm');
        var columnWidth = {
          columnWidthOne: layui.form.val('floorSettingForm').columnWidthOne,
          columnWidthTwo: layui.form.val('floorSettingForm').columnWidthTwo,
          columnWidthThree: layui.form.val('floorSettingForm').columnWidthThree,
          columnWidthFour: layui.form.val('floorSettingForm').columnWidthFour
        };

        //收集热区字段
        for (let i = 0; i < columns; i++) {
          hot.push({
            width: $(".cube-setting-layer").find('.hot-area').find('.width').eq(i).val() || 0,
            height: $(".cube-setting-layer").find('.hot-area').find('.height').eq(i).val() || 0,
            x: $(".cube-setting-layer").find('.hot-area').find('.x').eq(i).val() || 0,
            y: $(".cube-setting-layer").find('.hot-area').find('.y').eq(i).val() || 0,
            url: $(".cube-setting-layer").find('.hot-area').find('.url').eq(i).val() || '',
            pic: $(".cube-setting-layer").find('.hot-area').find('.pic').eq(i).attr('src') || ''
          })
        }
        var data = $.extend({}, floorSettingParmam, {
          hot: hot
        });
        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data)); //存储单个配置
        IndexController.render(width, height, columns, spacing, columnWidth, hot, type); //视图更新
        IndexController.updatePcData(); //更新数据
      });

      //热区-宽度监听
      $(document).on('input', '.hot-area .width', debounce(function (e) {

      }, 800));

    },
    /**
     * 抽奖-方
     * @param obj
     */
    lotterySquareChange: function () {

      //抽奖ID监听
      $(document).on('input', '.lotterySquare .lotteryId', debounce(function (e) {
        var val = $(this).val();
        IndexController.checkLottery(val, $(this).parent().parent().parent().find(':submit'));
      }, 800));

      //各奖项长/宽监听
      $(document).on('input', '.lotterySquare .width', debounce(function (e) {
        var val = $(this).val() * 1;
        if (val) {
          $(this).parent().next().find(".total-width").text((val * 3) + 22);
        }
      }, 800));

      //楼层保存按钮
      layui.form.on('submit(lotterySquareSubmit)', function (data) {
        var width = layui.form.val('lotterySquareForm').width;
        var lotterySquareBg = layui.form.val('lotterySquareForm').lotterySquareBg;
        var contactBg = layui.form.val('lotterySquareForm').contactBg;
        var prizeBg = layui.form.val('lotterySquareForm').prizeBg;
        var bg = layui.form.val('lotterySquareForm').bg;
        var type = $(".cube-setting-layer").attr("data-type");
        var current = $(".cube-setting-layer").attr("data-index") * 1;

        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data.field)); //存储单个配置

        IndexController.renderlotterySquare(width, lotterySquareBg, contactBg, prizeBg, bg, type); //视图更新

        IndexController.updatePcData(); //更新数据

      });
    },
    /**
     * 抽奖-圆
     * @param obj
     */
    lotteryCircleChnage: function () {
      //抽奖ID监听
      $(document).on('input', '.lotteryCircle .lotteryId', debounce(function (e) {
        var val = $(this).val();
        IndexController.checkLottery(val, $(this).parent().parent().parent().find(':submit'));
      }, 800));

      //奖项个数监听
      $(document).on('input', '.lotteryCircle .num', debounce(function (e) {
        var val = $(this).val() * 1;
        if (val) {
          $(this).parent().next().find('.angle').text(360 / val);
        } else {
          $(this).parent().next().find('.angle').text(0);
        }
      }, 800));

      //楼层保存按钮
      layui.form.on('submit(lotteryCircleSubmit)', function (data) {
        var num = layui.form.val('lotteryCircleForm').num;
        var lotteryCircleBg = layui.form.val('lotteryCircleForm').lotteryCircleBg;
        var contactBg = layui.form.val('lotteryCircleForm').contactBg;
        var prizeBg = layui.form.val('lotteryCircleForm').prizeBg;
        var bg = layui.form.val('lotteryCircleForm').bg;
        var type = $(".cube-setting-layer").attr("data-type");
        var current = $(".cube-setting-layer").attr("data-index") * 1;

        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data.field)); //存储单个配置

        IndexController.renderlotteryCircle(num, lotteryCircleBg, contactBg, prizeBg, bg, type); //视图更新
        IndexController.updatePcData(); //更新数据

      });
    },
    /**
     * 优惠券
     * @param obj
     */
    couponChnage: function () {
      //优惠券个数监听
      $(document).on('input', '.coupon .couponNum', debounce(function (e) {
        var couponNum = $(this).val() * 1;
        if (couponNum > 10) {
          $(this).val(10);
          couponNum = 10;
        }
        var htmlDynamicsArr = [];
        for (let i = 0; i < couponNum; i++) {
          htmlDynamicsArr.push('<div class="layui-form-item"> <label style="width: 105px;" class="layui-form-label required">批次' + (i + 1) + '</label> <div class="layui-input-block" style="margin-left: 105px;"> <input type="text"  lay-verify="required" lay-reqtext="请填写批次号" lay-vertype="tips" autocomplete="off" placeholder="批次号" class="layui-input columnCoupon"> </div> <div class="row bothSide verCenter bar-wrap" style="padding-left: 105px;"> <div> <a class="layui-btn layui-btn-sm uploadPic">上传图片</a> <p class="tip">支持扩展名png/jpg/gif</p> </div> <div> <img src="" alt="" class="pic" style="display: none"><input type="hidden" class="columnCouponUrl" value=""/> </div> </div> </div>')
        }
        $(".cube-setting-layer .coupon").find('.columns-dynamics').empty().html(htmlDynamicsArr.join(''));
        IndexController.uploadChange(); //初始化上传组件
      }, 800));

      //优惠券批次监听
      $(document).on('input', '.coupon .columnCoupon', function () {
        var self = $(this);
        var val = $(this).val();
        Request('/api/activityElement/checkCoupon', 'GET', {
          coupon_sns: val
        }, function (res) {
          if (res.code == 0) {
            $("#couponSubmit").removeClass("layui-btn-disabled").attr("disabled", false);
            self.attr('data-coupon-id', res.data[0].coupon_id);
          } else {
            $("#couponSubmit").addClass("layui-btn-disabled").attr("disabled", true);
            layer.msg(res.msg);
          }
        })
      });

      //楼层保存按钮
      layui.form.on('submit(couponSubmit)', function (data) {
        var bg = layui.form.val('couponForm').bg;
        var couponNum = layui.form.val('couponForm').couponNum;
        var pattern = layui.form.val('couponForm').pattern;
        var couponLength = layui.form.val('couponForm').couponLength;
        var couponWidth = layui.form.val('couponForm').couponWidth;
        var columnCoupon = []; //优惠券批次
        var columnCouponId = []; //优惠券批次
        var columnCouponUrl = []; //优惠券图片
        var type = $(".cube-setting-layer").attr("data-type");
        var current = $(".cube-setting-layer").attr("data-index") * 1;

        $(".coupon .columnCoupon").each(function (index, element) {
          columnCoupon.push($(element).val());
          columnCouponId.push($(element).attr('data-coupon-id'));
        });

        $(".coupon .columnCouponUrl").each(function (index, element) {
          if ($(element).val() == '') {
            layer.msg('请上传优惠券批次' + (index + 1) + '的图片');
            return false;
          } else {
            columnCouponUrl.push($(element).val());
          }
        });

        if (columnCoupon.length != columnCouponUrl.length) {
          return false;
        }

        var params = $.extend({}, {columnCoupon: JSON.stringify(columnCoupon)}, {columnCouponUrl: JSON.stringify(columnCouponUrl)}, {columnCouponId: JSON.stringify(columnCouponId)}, data.field);

        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(params)); //存储单个配置
        IndexController.renderCoupon(bg, couponNum, pattern, columnCoupon, columnCouponUrl, columnCouponId, couponLength, couponWidth, type); //视图更新
        IndexController.updatePcData(); //更新数据
      });
    },
    /**
     * 商品列表
     * @param obj
     */
    shopListChange: function () {

      //新增行-专营
      $(document).on('click', '#addRow', function () {
        var html = '<tr><td><select name="type" lay-filter="typeChange" class="type"><option selected value="2">专营</option><option value="3">代购</option></select></td><td class="supplier"><input type="text" placeholder="输入1个供应商编码" class="layui-input supplier_codes" style="width:122px"> <input type="text" placeholder="输入1个供应商ID" class="layui-input supplier_ids" style="display:none;width:110px"></td><td class="classify"><input type="text" placeholder="逗号隔开" class="layui-input class_ids"></td><td class="brand"><input type="text" placeholder="逗号隔开" class="layui-input brand_ids"></td><td class="code"><input type="text" placeholder="逗号隔开" class="layui-input canal_code"></td><td class="label"><input type="text" placeholder="逗号隔开" class="layui-input goods_labels"></td><td><button type="button" class="layui-btn layui-btn-sm layui-btn-danger delete">删除</button></td></tr>';
        $("#rowArea").append(html);
        layui.form.render();
      });

      //新增行-自营
      $(document).on('click', '#addRowSelf', function () {
        var html = '<tr><td class="classify"><input type="hidden" class="type" value="1"><input type="text" placeholder="逗号隔开" class="layui-input class_ids"></td><td class="brand"><input type="text" placeholder="逗号隔开" class="layui-input brand_ids"></td><td><button type="button" class="layui-btn layui-btn-sm layui-btn-danger delete">删除</button></td></tr>';
        $("#rowAreaSelf").append(html);
        layui.form.render();
      });

      //供应商ID监听
      $(document).on('input', '.shopList .supplier_ids', debounce(function (e) {
        //验证代购供应商id是否存在
        var supplier_ids = $(this).val();
        var self = $(this);
        if (supplier_ids) {
          Request('/api/activityElement/checkSupplierIds', 'GET', {
            supplier_ids: supplier_ids
          }, function (res) {
            if (res.code === 0) {
              if (res.data[supplier_ids]) {
                self.attr('data-supplier-ids', res.data[supplier_ids].supplier_id);
                self.attr('data-supplier-names', res.data[supplier_ids].supplier_name);
                self.removeClass('layui-input-error');
                $("#shopListSubmit").removeClass("layui-btn-disabled").attr("disabled", false);
              } else {
                self.removeAttr('data-supplier-ids');
                self.removeAttr('data-supplier-names');
                self.addClass('layui-input-error');
                $("#shopListSubmit").addClass("layui-btn-disabled").attr("disabled", true);
              }
            } else {
              layer.msg(res.msg);
            }
          })
        }
      }, 800));

      //供应商编码监听
      $(document).on('input', '.shopList .supplier_codes', debounce(function (e) {
        //验证代购供应商id是否存在
        var supplier_codes = $(this).val();
        var self = $(this);
        if (supplier_codes) {
          Request('/api/activityElement/checkSupplierCodes', 'GET', {
            supplier_codes: supplier_codes
          }, function (res) {
            if (res.code === 0) {
              if (res.data[supplier_codes]) {
                self.attr('data-supplier-ids', res.data[supplier_codes].supplier_id);
                self.attr('data-supplier-codes', res.data[supplier_codes].supplier_code);
                self.attr('data-supplier-names', res.data[supplier_codes].supplier_name);
                self.removeClass('layui-input-error');
                $("#shopListSubmit").removeClass("layui-btn-disabled").attr("disabled", false);
              } else {
                self.removeAttr('data-supplier-ids');
                self.removeAttr('data-supplier-codes');
                self.removeAttr('data-supplier-names');
                self.addClass('layui-input-error');
                $("#shopListSubmit").addClass("layui-btn-disabled").attr("disabled", true);
              }
            } else {
              layer.msg(res.msg);
            }
          })
        }
      }, 800));

      //活动价ID监听
      $(document).on('input', '.shopList .activity_price_id', debounce(function (e) {
        //验证代购供应商id是否存在
        var activity_price_id = $(this).val();
        var self = $(this);
        if (activity_price_id) {
          Request('/api/activityElement/checkActivityPriceId', 'GET', {
            activity_price_id: activity_price_id
          }, function (res) {
            if (res.code === 0) {
              self.removeClass('layui-input-error');
              $("#shopListSubmit").removeClass("layui-btn-disabled").attr("disabled", false);
            } else {
              layer.msg(res.msg);
              self.addClass('layui-input-error');
              $("#shopListSubmit").addClass("layui-btn-disabled").attr("disabled", true);
            }
          })
        }
      }, 800));

      //商品分类监听
      layui.form.on('select(typeChange)', function (data) {
        var val = data.value;
        //去掉自营
        if (val == 2) {
          //专营
          $(data.elem).parent().parent().find('.supplier').find('.supplier_codes').show();
          $(data.elem).parent().parent().find('.supplier').find('.supplier_ids').hide();
          $(data.elem).parent().parent().find('.classify').children().show();
          $(data.elem).parent().parent().find('.brand').children().show();
          $(data.elem).parent().parent().find('.code').children().show();
          $(data.elem).parent().parent().find('.label').children().show();
        } else if (val == 3) {
          //代购
          $(data.elem).parent().parent().find('.supplier').find('.supplier_codes').hide();
          $(data.elem).parent().parent().find('.supplier').find('.supplier_ids').show();
          $(data.elem).parent().parent().find('.classify').children().show();
          $(data.elem).parent().parent().find('.brand').children().show();
          $(data.elem).parent().parent().find('.code').children().hide();
          $(data.elem).parent().parent().find('.label').children().hide();
        }
      });

      //商品列表删除-专营
      $(document).on('click', '#rowArea .delete', function () {
        $(this).parent().parent().remove();
      });

      //商品列表删除-自营
      $(document).on('click', '#rowAreaSelf .delete', function () {
        $(this).parent().parent().remove();
      });

      //商品列表选择维度监听
      form.on('radio(dimensionChange)', function (data) {
        var val = data.value;
        if (val == 1) {
          layui.form.val('shopListForm', {
            is_activity_price: 1,
            is_sku_id_upload: 0,
            is_sku_property: 0
          });
        } else if (val == 2) {
          layui.form.val('shopListForm', {
            is_activity_price: 0,
            is_sku_id_upload: 1,
            is_sku_property: 0
          });
        } else if (val == 3) {
          layui.form.val('shopListForm', {
            is_activity_price: 0,
            is_sku_id_upload: 0,
            is_sku_property: 1
          });
        }
        var tpl = dimensionHtml.innerHTML;
        layui.laytpl(tpl).render(val, function (html) {
          $(".dimension-box").empty().html(html);
          if (val == 1) {
            IndexController.initActivityPrice(0); //初始化活动价
            $(".cube-set .cube-setting-layer").css('overflow-y', 'visible');
          } else if (val == 2) {
            IndexController.uploadChangeSKU(); //初始化sku上传
            $("#shopListSubmit").removeClass("layui-btn-disabled").attr("disabled", false);
            $(".cube-set .cube-setting-layer").css('overflow-y', 'auto');
          } else if (val == 3) {
            $("#shopListSubmit").removeClass("layui-btn-disabled").attr("disabled", false);
            $(".cube-set .cube-setting-layer").css('overflow-y', 'auto');
          }
          layui.form.render();
        });
      });

      //商品分类监听
      form.on('radio(skuTypeChange)', function (data) {
        var val = data.value;
        var tpl = shopSortHtml.innerHTML;
        layui.laytpl(tpl).render(val, function (html) {
          $("#shopSort").empty().html(html);
          layui.form.render();
        });
      });

      //楼层保存按钮
      layui.form.on('submit(shopListSubmit)', function (data) {
        var type = $(".cube-setting-layer").attr("data-type");
        var current = $(".cube-setting-layer").attr("data-index") * 1;
        var params = {
          is_filter_brand: $("#is_filter_brand").is(':checked') ? 1 : 0, //是否过滤品牌
          is_filter_supplier: $("#is_filter_supplier").is(':checked') ? 1 : 0, //是否过滤供应商
          is_search: $("#is_search").is(':checked') ? 1 : 0, //搜索框配置
          is_search_color: $("#is_search_color").val() || '#ff0000', //搜索框配置颜色
          is_activity_price: layui.form.val('shopListForm').is_activity_price, //是否设置活动价
          activity_price_ids: layui.form.val('shopListForm').activity_price_ids || '', //活动价id
          is_sku_id_upload: layui.form.val('shopListForm').is_sku_id_upload, //是否上传sku
          is_sku_property: layui.form.val('shopListForm').is_sku_property, //是否设置sku属性
          sku_id_upload_file: layui.form.val('shopListForm').sku_id_upload_file || '', //文件地址
          sku_ids: layui.form.val('shopListForm').sku_ids || '', //sku详细参数
          dimensionChange: layui.form.val('shopListForm').dimensionChange, //维度
          is_sku_type: layui.form.val('shopListForm').is_sku_type || '',//商品分类
          sku_property_data: []//商品属性
        };

        var propertyObj = layui.form.val('shopListForm').is_sku_type == 1 ? $("#rowAreaSelf tr") : $("#rowArea tr");

        //收集商品属性
        propertyObj.each(function (index, element) {
          if ($(element).find('.supplier_codes').is(':visible')) {
            params.sku_property_data.push({
              type: $(element).find('.type').val(), //类型1 自营,2专营,3代购
              supplier_ids: $(element).find('.supplier_codes').attr('data-supplier-ids') || '', //供应商id,逗号分隔(代购才有供应商id),不能超过100个
              supplier_codes: $(element).find('.supplier_codes').attr('data-supplier-codes') || '', //供应商编码,逗号分隔(专营才有供应商编码),不能超过100个
              supplier_names: $(element).find('.supplier_codes').attr('data-supplier-names') || '', //供应商名称,供应商id对应
              class_ids: $(element).find('.class_ids').val() || '', //分类id,逗号分隔,不能超过100个
              brand_ids: $(element).find('.brand_ids').val() || '', //品牌id,逗号分隔,不能超过100个
              canal_code: $(element).find('.canal_code').val() || '', //专营内部编码,逗号分隔,不能超过100个
              goods_labels: $(element).find('.goods_labels').val() || '' //专营渠道标签,逗号分隔 1 国内现货，2国际现货， 3 猎芯期货 ,4 询价现货
            });
          } else {
            params.sku_property_data.push({
              type: $(element).find('.type').val(), //类型1 自营,2专营,3代购
              supplier_ids: $(element).find('.supplier_ids').attr('data-supplier-ids') || '',
              supplier_codes: $(element).find('.supplier_ids').attr('data-supplier-codes') || '',
              supplier_names: $(element).find('.supplier_ids').attr('data-supplier-names') || '',
              class_ids: $(element).find('.class_ids').val() || '',
              brand_ids: $(element).find('.brand_ids').val() || '',
              canal_code: $(element).find('.canal_code').val() || '',
              goods_labels: $(element).find('.goods_labels').val() || ''
            });
          }
        });

        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(params)); //存储单个配置
        IndexController.syncGetGoodsList(params); //同步商品列表
      });
    },
    /**
     * 表单模块
     */
    formModuleChange: function () {
      //自定义字段个数
      $(document).on('input', '.formModule .fieldNum', debounce(function (e) {
        var val = $(this).val() * 1;
        if (val > 10) {
          $(this).val(10);
        }

        if (val > 0) {
          var htmlArr = [];
          for (var i = 0; i < val; i++) {
            htmlArr.push('  <div class="layui-form-item">' +
              '                            <label class="layui-form-label required" style="width: 127px;">输入框' + (i + 1) + '</label>' +
              '                            <div class="layui-input-inline">' +
              '                                <input type="text"  lay-verify="required" lay-reqtext="请填写该输入框名称" lay-vertype="tips" autocomplete="off" placeholder="请填写该输入框名称" class="layui-input input_name">' +
              '                            </div>' +
              '                        </div>');
          }
          $(".cube-setting-layer .formModule .columns-dynamics").empty().html(htmlArr.join(''));
        }
      }, 800));

      //楼层保存按钮
      layui.form.on('submit(formModuleSubmit)', function (data) {
        var type = $(".cube-setting-layer").attr("data-type");
        var current = $(".cube-setting-layer").attr("data-index") * 1;

        var form_data = [];
        $(".formModule .columns-dynamics .input_name").each(function (index, element) {
          form_data.push({
            input_name: $(element).val()
          })
        })
        var data = $.extend({}, {form_data: form_data}, data.field, {activity_id: id});
        $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(data)); //存储单个配置
        IndexController.renderFormModule(data); //视图更新
        IndexController.updatePcData(); //更新数据
      });
    },
    /**
     * 同步商品列表
     */
    syncGetGoodsList: function (params) {
      var data = $.extend({}, params, {
        page_size: 10,
        page: 1
      });
      Request('/sync/activity/getGoodsList', 'POST', data, function (res) {
        if (res.code == 0) {
          IndexController.renderShopList(res.data, params); //视图更新
        } else {
          layer.msg(res.msg);
        }
      })
    },
    /**
     * 列宽总和计算
     */
    calcTotal: function () {
      var columnWidthOne = layui.form.val('floorSettingForm').columnWidthOne * 1 || 0;
      var columnWidthTwo = layui.form.val('floorSettingForm').columnWidthTwo * 1 || 0;
      var columnWidthThree = layui.form.val('floorSettingForm').columnWidthThree * 1 || 0;
      var columnWidthFour = layui.form.val('floorSettingForm').columnWidthFour * 1 || 0;
      var columns = layui.form.val('floorSettingForm').columns * 1 || 0;
      var spacing = layui.form.val('floorSettingForm').spacing * 1 || 0;

      if (columns == 2) {
        var total = columnWidthOne + columnWidthTwo + columnWidthThree + columnWidthFour + spacing;
        $('.columnWidthDynamics').find('.total').text(total.toFixed(0));
      } else if (columns == 3) {
        var total = columnWidthOne + columnWidthTwo + columnWidthThree + columnWidthFour + (spacing * 2);
        $('.columnWidthDynamics').find('.total').text(total.toFixed(0));
      } else if (columns == 4) {
        var total = columnWidthOne + columnWidthTwo + columnWidthThree + columnWidthFour + (spacing * 3);
        $('.columnWidthDynamics').find('.total').text(total.toFixed(0));
      }

    },
    /**
     * 全局楼层设置
     * @returns {string}
     */
    floorsChangeGlobal: function () {
      var data = [];
      $(".cube-set-drag-area .cube-initial").each(function (index, element) {
        data.push({
          name: textVal[$(element).attr('data-type')],
          status: $(element).is(":visible")
        })
      });

      table.render({
        elem: '#list',
        page: false,
        data: data,
        cols: [
          [
            {type: 'numbers', title: '楼层', width: 50, align: 'center'},
            {field: 'name', title: '元件名称', align: 'center'},
            {
              field: 'name', title: '状态', width: 80, align: 'center', templet(d) {
                if (d.status) {
                  return '<input type="checkbox" lay-filter="statusChange" lay-skin="switch" lay-text="启用|禁用" checked value="' + d.LAY_TABLE_INDEX + '"/>'
                } else {
                  return '<input type="checkbox" lay-filter="statusChange" lay-skin="switch" lay-text="启用|禁用"  value="' + d.LAY_TABLE_INDEX + '"/>'
                }
              }
            },
            {
              field: 'name', title: '操作', width: 120, align: 'center', templet(d) {
                if (d.LAY_TABLE_INDEX == 0) {
                  return '<a class="layui-btn layui-btn-xs global-down" lay-event="global-down" data-index="' + d.LAY_TABLE_INDEX + '">下移</a>'
                } else if (d.LAY_TABLE_INDEX == data.length - 1) {
                  return '<a class="layui-btn layui-btn-xs global-up" lay-event="global-up" data-index="' + d.LAY_TABLE_INDEX + '">上移</a>'
                } else {
                  return '<a class="layui-btn layui-btn-xs global-up" lay-event="global-up" data-index="' + d.LAY_TABLE_INDEX + '">上移</a><a class="layui-btn layui-btn-xs global-down" lay-event="global-down" data-index="' + d.LAY_TABLE_INDEX + '">下移</a>'
                }
              }
            }
          ]
        ],
        done: function (res, curr, count) {
          layui.form.render();
        }
      });

      //触发单元格工具事件
      table.on('tool(list)', function (obj) {
        var tr = obj.tr;
        switch (obj.event) {
          //上移
          case 'global-up':
            var index = $(tr).find('.global-up').attr('data-index');
            $('.cube-set-drag-area .cube-initial').eq(index).find('.setting').find('.move-up').trigger('click');
            IndexController.floorsChangeGlobal(); //更新全局楼层设置
            break;
          //下移
          case 'global-down':
            var index = $(tr).find('.global-down').attr('data-index');
            $('.cube-set-drag-area .cube-initial').eq(index).find('.setting').find('.move-down').trigger('click');
            IndexController.floorsChangeGlobal(); //更新全局楼层设置
            break;
        }
      });

    },
    /**
     * 更新楼层索引
     */
    sortNumChange: function () {
      $(".cube-set-drag-area .cube-initial").each(function (index, element) {
        $(element).attr('data-index', index);
      });
    },
    /**
     * 楼层设置读取配置-自定义布局
     * @param data
     */
    settingRender: function (data) {
      var width = data.width;
      var spacing = data.spacing || 0;
      //根据列数动态生成列宽和列数
      if (data.columns == 1) {
        layui.form.val('floorSettingForm', {
          columnWidthOne: width,
          spacing: 0
        })
        let htmlArr = [];
        htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + width + '" style="width: 55px;">');
        $(".cube-setting-layer .customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
      } else if (data.columns == 2) {
        let widthPx = Number(width) - Number(spacing);
        widthPx = (widthPx / 2).toFixed(1);
        layui.form.val('floorSettingForm', {
          columnWidthOne: widthPx,
          columnWidthTwo: widthPx
        })
        //动态列计算
        let htmlArr = [];
        htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
        $(".cube-setting-layer .customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
      } else if (data.columns == 3) {
        let widthPx = Number(width) - (Number(spacing) * 2);
        widthPx = (widthPx / 3).toFixed(1);
        layui.form.val('floorSettingForm', {
          columnWidthOne: widthPx,
          columnWidthTwo: widthPx,
          columnWidthThree: widthPx
        })
        //动态列计算
        let htmlArr = [];
        htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
        $(".cube-setting-layer .customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
      } else if (data.columns == 4) {
        let widthPx = Number(width) - (Number(spacing) * 3);
        widthPx = (widthPx / 4).toFixed(1);
        layui.form.val('floorSettingForm', {
          columnWidthOne: widthPx,
          columnWidthTwo: widthPx,
          columnWidthThree: widthPx,
          columnWidthFour: widthPx
        })
        //动态列计算
        let htmlArr = [];
        htmlArr.push('<input type="text" name="columnWidthOne" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthTwo" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthThree" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><span style="margin: 0 4px">+' + spacing + '</span><input type="text" name="columnWidthFour" lay-verify="title" autocomplete="off" placeholder="请输入列宽" class="layui-input columnWidth" value="' + widthPx + '" style="width: 55px;"><em class="total"></em>');
        $(".cube-setting-layer .customLayout").find(".columnWidthDynamics").empty().html(htmlArr.join(''));
      }

      //动态列数
      var htmlDynamicsArr = [];
      for (let i = 0; i < data.columns; i++) {
        htmlDynamicsArr.push('<div class="layui-form-item"> <label class="layui-form-label">第' + (i + 1) + '列</label> <div class="layui-input-block" style="margin-left: 62px;"> <input type="text" name="' + uploadClass[i] + '" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url"> </div> <div class="row bothSide verCenter bar-wrap"> <div> <a class="layui-btn layui-btn-sm uploadPic">上传图片</a><a href="javascript:;" class="clearPic">清除图片</a> <p class="tip">支持扩展名png/jpg/gif/mp4</p> </div> <div> <img src="" alt="" class="pic" style="display: none"><input type="hidden" name="' + uploadName[i] + '" value=""/> </div> </div> </div>')
      }
      $(".cube-setting-layer .customLayout").find('.columns-dynamics').empty().html(htmlDynamicsArr.join(''));

      layui.form.val('floorSettingForm', {
        width: data.width,
        height: data.height,
        columns: data.columns,
        spacing: data.spacing,
        columnWidthOne: data.columnWidthOne || '',
        columnWidthTwo: data.columnWidthTwo || '',
        columnWidthThree: data.columnWidthThree || '',
        columnWidthFour: data.columnWidthFour || '',
        columnOneHref: data.columnOneHref || '',
        columnTwoHref: data.columnTwoHref || '',
        columnThreeHref: data.columnThreeHref || '',
        columnFourHref: data.columnFourHref || '',
        columnOneUrl: data.columnOneUrl || '',
        columnTwoUrl: data.columnTwoUrl || '',
        columnThreeUrl: data.columnThreeUrl || '',
        columnFourUrl: data.columnFourUrl || ''
      })

      if (data.columnOneUrl) {
        $(".cube-setting-layer").find(".customLayout").find("input[name='columnOneUrl']").prev().attr('src', data.columnOneUrl).show();
      }
      if (data.columnTwoUrl) {
        $(".cube-setting-layer").find(".customLayout").find("input[name='columnTwoUrl']").prev().attr('src', data.columnTwoUrl).show();
      }
      if (data.columnThreeUrl) {
        $(".cube-setting-layer").find(".customLayout").find("input[name='columnThreeUrl']").prev().attr('src', data.columnThreeUrl).show();
      }
      if (data.columnFourUrl) {
        $(".cube-setting-layer").find(".customLayout").find("input[name='columnFourUrl']").prev().attr('src', data.columnFourUrl).show();
      }

      IndexController.uploadChange(); //初始化上传组件
      IndexController.calcTotal(); //列宽总和计算

    },
    /**
     * 楼层设置读取配置-自定义布局-热区
     */
    settingHotRender: function (data) {
      $(".cube-setting-layer").find(".hot-area").find('.columns').attr('disabled', 'disabled');
      //根据布局设置的列数显示热区的列数，不可编辑
      layui.form.val('hotForm', {
        columns: data.columns,
      })
      if (data.columns) {
        var columns = Number(data.columns);
        var hot = data.hot || [];
        var htmlArr = [];
        for (let i = 0; i < columns; i++) {
          htmlArr.push('<div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列宽度</label>' +
            '                            <div class="layui-input-inline">' +
            '                                <input type="text" name="width"  autocomplete="off" placeholder="请输入宽度" class="layui-input width" value="' + (hot.length > 0 ? hot[i].width : '') + '">' +
            '                            </div>' +
            '                            <div class="layui-form-mid layui-word-aux">px</div>' +
            '                        </div>' +
            '                        <div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列高度</label>' +
            '                            <div class="layui-input-inline">' +
            '                                <input type="text" name="height" autocomplete="off" placeholder="请输入高度" class="layui-input height" value="' + (hot.length > 0 ? hot[i].height : '') + '">' +
            '                            </div>' +
            '                            <div class="layui-form-mid layui-word-aux">px</div>' +
            '                        </div>' +
            '                        <div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列X轴</label>' +
            '                            <div class="layui-input-inline" style="width: 108px;">' +
            '                                <input type="text" name="x" autocomplete="off" placeholder="请输入X轴位置" class="layui-input x" value="' + (hot.length > 0 ? hot[i].x : '') + '">' +
            '                            </div>' +
            '                            <div class="layui-form-mid layui-word-aux">（可拖动热区调整）</div>' +
            '                        </div>' +
            '                        <div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列Y轴</label>' +
            '                            <div class="layui-input-inline" style="width: 108px;">' +
            '                                <input type="text" name="y"  autocomplete="off" placeholder="请输入Y轴位置" class="layui-input y" value="' + (hot.length > 0 ? hot[i].y : '') + '">' +
            '                            </div>' +
            '                            <div class="layui-form-mid layui-word-aux">（可拖动热区调整）</div>' +
            '                        </div>' +
            '                        <div class="layui-form-item">' +
            '                            <label class="layui-form-label" style="width: 90px;">第' + (i + 1) + '列地址</label>' +
            '                            <div class="layui-input-block" style="margin-left: 90px;">' +
            '                                <input type="text" name="columnOneHref" lay-verify="title" autocomplete="off" placeholder="跳转地址" class="layui-input url" value="' + (hot.length > 0 ? hot[i].url : '') + '">' +
            '                            </div>' +
            '                            <div class="row bothSide verCenter bar-wrap" style="padding-left: 90px;">' +
            '                                <div>' +
            '                                    <a class="layui-btn layui-btn-sm uploadPic">上传图片</a>' +
            '                                    <a href="javascript:;" class="clearPic">清除图片</a>' +
            '                                    <p class="tip">支持扩展名png/jpg/gif/mp4</p>' +
            '                                </div>' +
            '                                <div>' +
            '                                    <img src="' + (hot.length > 0 ? hot[i].pic : '') + '" alt="" class="pic" style="' + (hot.length > 0 ? (hot[i].pic ? 'display:block' : 'display:none') : 'display:none') + '">' +
            '                                    <input type="hidden" name="columnOneUrl" value="' + (hot.length > 0 ? hot[i].pic : '') + '">' +
            '                                </div>' +
            '                            </div>' +
            '                        </div>');

        }
        $(".cube-setting-layer").find(".hot-area").find('.hot-columns-dynamics').empty().html(htmlArr.join(''));
      }
      IndexController.uploadChange(); //初始化上传组件
    },
    /**
     * 楼层设置读取配置-抽奖-圆
     * @param data
     */
    settingRenderLotteryCircle: function (data) {
      layui.form.val('lotteryCircleForm', {
        lotteryId: data.lotteryId,
        num: data.num,
        lotteryCircleBg: data.lotteryCircleBg,
        contactBg: data.contactBg,
        prizeBg: data.prizeBg,
        bg: data.bg,
        prizeMsg: data.prizeMsg
      })

      if (data.num) {
        let num = Number(data.num);
        $(".cube-setting-layer").find(".lotteryCircle").find(".angle").text(360 / num);
      }

      if (data.lotteryCircleBg) {
        $(".cube-setting-layer").find(".lotteryCircle").find("input[name='lotteryCircleBg']").prev().attr('src', data.lotteryCircleBg).show();
      }

      if (data.contactBg) {
        $(".cube-setting-layer").find(".lotteryCircle").find("input[name='contactBg']").prev().attr('src', data.contactBg).show();
      }

      if (data.prizeBg) {
        $(".cube-setting-layer").find(".lotteryCircle").find("input[name='prizeBg']").prev().attr('src', data.prizeBg).show();
      }

      if (data.bg) {
        $(".cube-setting-layer").find(".lotteryCircle").find("input[name='bg']").prev().attr('src', data.bg).show();
      }
    },
    /**
     * 楼层设置读取配置-抽奖-方
     * @param data
     */
    settingRenderLotterySquare: function (data) {
      layui.form.val('lotterySquareForm', {
        lotteryId: data.lotteryId,
        width: data.width,
        lotterySquareBg: data.lotterySquareBg,
        contactBg: data.contactBg,
        prizeBg: data.prizeBg,
        bg: data.bg,
        prizeMsg: data.prizeMsg
      })

      if (data.width) {
        let width = data.width * 1;
        $(".cube-setting-layer").find(".lotterySquare").find('.total-width').text((width * 3) + 22);
      }

      if (data.lotterySquareBg) {
        $(".cube-setting-layer").find(".lotterySquare").find("input[name='lotterySquareBg']").prev().attr('src', data.lotterySquareBg).show();
      }

      if (data.contactBg) {
        $(".cube-setting-layer").find(".lotterySquare").find("input[name='contactBg']").prev().attr('src', data.contactBg).show();
      }

      if (data.prizeBg) {
        $(".cube-setting-layer").find(".lotterySquare").find("input[name='prizeBg']").prev().attr('src', data.prizeBg).show();
      }

      if (data.bg) {
        $(".cube-setting-layer").find(".lotterySquare").find("input[name='bg']").prev().attr('src', data.bg).show();
      }
    },
    /**
     * 楼层设置读取配置-优惠券
     * @param data
     */
    settingRenderCoupon(data) {
      if (data.columnCoupon) {
        var columnCoupon = JSON.parse(data.columnCoupon);
        var columnCouponUrl = JSON.parse(data.columnCouponUrl);
        var columnCouponId = JSON.parse(data.columnCouponId);
      }
      layui.form.val('couponForm', {
        bg: data.bg,
        couponNum: data.couponNum,
        pattern: data.pattern,
        couponLength: data.couponLength,
        couponWidth: data.couponWidth
      })

      if (data.bg) {
        $(".cube-setting-layer").find(".coupon").find("input[name='bg']").prev().attr('src', data.bg).show();
      }

      //优惠券批次
      if (columnCoupon.length > 0) {
        var htmlDynamicsArr = [];
        for (let i = 0; i < columnCoupon.length; i++) {
          htmlDynamicsArr.push('<div class="layui-form-item"> <label style="width: 105px;" class="layui-form-label required">优惠券批次' + (i + 1) + '</label> <div class="layui-input-block" style="margin-left: 105px;"> <input type="text" value="' + columnCoupon[i] + '" data-coupon-id="' + columnCouponId[i] + '"   lay-verify="required" lay-reqtext="请填写批次号" lay-vertype="tips" autocomplete="off" placeholder="批次号" class="layui-input columnCoupon"> </div> <div class="row bothSide verCenter bar-wrap" style="padding-left: 105px;"> <div> <a class="layui-btn layui-btn-sm uploadPic">上传图片</a> <p class="tip">支持扩展名png/jpg/gif</p> </div> <div> <img src="' + columnCouponUrl[i] + '" alt="" class="pic"><input type="hidden" class="columnCouponUrl"  value="' + columnCouponUrl[i] + '"/> </div> </div> </div>')
        }
        $(".cube-setting-layer .coupon").find('.columns-dynamics').empty().html(htmlDynamicsArr.join(''));
        IndexController.uploadChange(); //初始化上传组件
      }

    },
    /**
     * 楼层设置读取配置-商品列表
     * @param data
     */
    settingRenderShopList(data) {
      //品牌
      if (data.is_filter_brand) {
        $(".cube-setting-layer").find(".shopList").find('#is_filter_brand').prop('checked', 'checked');
      }

      //供应商
      if (data.is_filter_supplier) {
        $(".cube-setting-layer").find(".shopList").find('#is_filter_supplier').prop('checked', 'checked');
      }

      //搜索框
      if (data.is_search) {
        $(".cube-setting-layer").find(".shopList").find('#is_search').next().next().show().val(data.is_search_color);
        $(".cube-setting-layer").find(".shopList").find('#is_search').prop('checked', 'checked');
      }

      //选择维度
      layui.form.val('shopListForm', {
        dimensionChange: data.dimensionChange
      })

      var tpl = dimensionHtml.innerHTML;
      layui.laytpl(tpl).render(data.dimensionChange, function (html) {
        $(".dimension-box").empty().html(html);
        if (data.dimensionChange == 1) {
          var ids='';
          if (data.activity_price_ids) {
            ids = data.activity_price_ids.split(',');
          }
          IndexController.initActivityPrice(data.activity_price_id); //初始化活动价ID
          //活动价
          layui.form.val('shopListForm', {
            activity_price_id: data.activity_price_id,
            is_activity_price: 1,
            is_sku_id_upload: 0,
            is_sku_property: 0
          })

        } else if (data.dimensionChange == 2) {
          //SKUID
          IndexController.uploadChangeSKU(); //初始化sku上传
          if (data.sku_id_upload_file) {
            $(".cube-setting-layer").find(".shopList").find(".layui-icon-download-circle").attr('href', data.sku_id_upload_file);
            $(".cube-setting-layer").find(".shopList").find("#sku_id_upload").text('重新上传');
          }
          layui.form.val('shopListForm', {
            sku_id_upload_file: data.sku_id_upload_file,
            sku_ids: data.sku_ids,
            is_activity_price: 0,
            is_sku_id_upload: 1,
            is_sku_property: 0
          })
        } else if (data.dimensionChange == 3) {
          //商品属性
          layui.form.val('shopListForm', {
            is_activity_price: 0,
            is_sku_id_upload: 0,
            is_sku_property: 1,
            is_sku_type: data.is_sku_type //商品分类选中
          })
          var tpl = skuPropertyHtml.innerHTML;
          if (data.sku_property_data.length > 0) {
            var json = $.extend({}, {data: data.sku_property_data}, {is_sku_type: data.is_sku_type});
            layui.laytpl(tpl).render(json, function (html) {
              //根据商品分类来渲染模板
              $(".cube-setting-layer").find("#shopSort").empty().html(html);
              layui.form.render();
            });
          }
        }
        layui.form.render();
      });


    },
    /**
     * 楼层设置读取配置-表单模块
     * @param data
     */
    settingRenderForm(data) {
      var form_data = data.form_data;

      layui.form.val('formModuleForm', {
        bg: data.bg,
        switch: data.switch,
        company_name: data.company_name,
        fieldNum: data.fieldNum,
        submitMsg: data.submitMsg,
        submitUrl: data.submitUrl,
        submitBtn: data.submitBtn
      })

      if (form_data.length > 0) {
        var htmlArr = [];
        for (var i = 0; i < form_data.length; i++) {
          htmlArr.push('  <div class="layui-form-item">' +
            '                            <label class="layui-form-label required" style="width: 127px;">输入框' + (i + 1) + '</label>' +
            '                            <div class="layui-input-inline">' +
            '                                <input type="text"  value="' + form_data[i].input_name + '" lay-verify="required" lay-reqtext="请填写该输入框名称" lay-vertype="tips" autocomplete="off" placeholder="请填写该输入框名称" class="layui-input input_name">' +
            '                            </div>' +
            '                        </div>');
        }
        $(".cube-setting-layer .formModule .columns-dynamics").empty().html(htmlArr.join(''));
      }

      if (data.bg) {
        $(".cube-setting-layer").find(".formModule").find("input[name='bg']").prev().attr('src', data.bg).show();
      }

    },
    /**
     * 解析自定义布局渲染视图
     * @param width 宽度
     * @param height 高度
     * @param column 列数
     * @param spacing 间距
     * @param columnWidth 列宽-接收对象
     * @param hot 热区-接收对象
     * @param type 布局类型
     */
    render: function (width, height, column, spacing, columnWidth, hot, type) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').removeAttr('href').removeAttr('target');
      if (width) {
        $(".cube-set-drag-area").find(".cube-initial").eq(current).css({
          width: width
        });

      }
      if (height) {
        $(".cube-set-drag-area").find(".cube-initial").eq(current).css({
          height: height
        });
      }
      if (spacing != 0) {
        $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().css({
          background: $("#page_color").val() ? $("#page_color").val() : '#FFFFFF'
        });
      }
      if (column) {
        if (column == 1) {
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').addClass('cube-set-drag-content-row').empty().css({background: "#f8f8f8"});
        } else if (column == 2) {
          if (spacing != 0) {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;"></a>');
          } else {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;"></a>');
          }
        } else if (column == 3) {
          if (spacing != 0) {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;"></a>');
          } else {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;"></a>');
          }
        } else if (column == 4) {
          if (spacing != 0) {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;margin-right:' + spacing + 'px"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthFour + 'px;height: 100%;"></a>');
          } else {
            $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthOne + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthTwo + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width: ' + columnWidth.columnWidthThree + 'px;height: 100%;border-right: 1px dashed salmon;"></a><a class="customLayout-box" style="position:relative;width:' + columnWidth.columnWidthFour + 'px;height: 100%;"></a>');
          }
        }
      }
      var pic_arr = [];
      var href_arr = [];
      $(".columns-dynamics .pic").each(function (index, element) {
        let picUrl = $(element).attr('src');
        let href_val = $(element).parent().parent().parent().find('.url').val();
        if (column == 1) {
          if ($(element).attr('src')) {
            $(".cube-set-drag-area .cube-initial").eq(current).find('.cube-set-drag-content').css("background-image", "url(" + picUrl + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
          }
          if ($(element).parent().parent().parent().find('.url').val()) {
            $(".cube-set-drag-area .cube-initial").eq(current).find('.cube-set-drag-content').attr('href', href_val).attr('target', '_blank');
          }
        } else {
          if ($(element).attr('src')) {
            $(".cube-set-drag-area .cube-initial").eq(current).find('a').eq(index).css("background-image", "url(" + picUrl + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
          }
          if ($(element).parent().parent().parent().find('.url').val()) {
            $(".cube-set-drag-area .cube-initial").eq(current).find('a').eq(index).attr('href', href_val).attr('target', '_blank');
          }
        }
      });

      //热区区域
      if (hot.length > 0) {
        if (column == 1) {
          var background = hot[0].pic ? 'background:url(' + hot[0].pic + ');background-position:center center;background-repeat:no-repeat;background-size:cover;' : 'background-color:rgba(0,0,0,0.5);';
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').empty().append('<span class="hot-box" data-href="' + hot[0].url + '" style="position: absolute;z-index:99;max-width: 100%;max-height: 100%;cursor: move;width: ' + hot[0].width + 'px;height:' + hot[0].height + 'px;left:' + hot[0].x + 'px;top:' + hot[0].y + 'px;' + background + '"></span>');
          //启动拖拽
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').find('.hot-box').Tdrag({
            scope: ".cube-set-drag-content-row",
            cbMove: function (val) {
              var currentData = JSON.parse($(".cube-set-drag-area").find(".cube-initial").eq(current).attr('data'));
              $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content-row').each(function (index, element) {
                if ($(element).find('.hot-box').length > 0) {
                  var left = $(element).find(".hot-box").css('left');
                  var top = $(element).find(".hot-box").css('top');
                  var data_index = $(element).find(".hot-box").attr('data-index') * 1;
                  currentData.hot[index].x = parseFloat(left); //替换data里x轴
                  currentData.hot[index].y = parseFloat(top); //替换data里y轴
                  $(".cube-setting-layer").find(".hot-columns-dynamics").find('.x').eq(index).val(parseFloat(left));
                  $(".cube-setting-layer").find(".hot-columns-dynamics").find('.y').eq(index).val(parseFloat(top));
                }
              })
              $(".cube-set-drag-area").find(".cube-initial").eq(current).attr('data', JSON.stringify(currentData));
            },
            cbEnd: function () {
              IndexController.updatePcData();//更新数据
            }
          });
        } else {
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content a').each(function (index, element) {
            if (Number(hot[index].width) > 0 && Number(hot[index].height) > 0) {
              var background = hot[index].pic ? 'background:url(' + hot[index].pic + ');background-position:center center;background-repeat:no-repeat;background-size:cover;' : 'background-color:rgba(0,0,0,0.5);';
              $(element).empty().append('<span class="hot-box" data-index="' + index + '" data-href="' + hot[index].url + '" style="position: absolute;z-index:99;max-width: 100%;max-height: 100%;cursor: move;width: ' + hot[index].width + 'px;height:' + hot[index].height + 'px;left:' + hot[index].x + 'px;top:' + hot[index].y + 'px;' + background + '"></span>');
            }
          })
          //启动拖拽
          $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content').find('.hot-box').each(function (index, element) {
            $(element).Tdrag({
              scope: $(element).parent(),
              cbMove: function (val) {
                var currentData = JSON.parse($(".cube-set-drag-area").find(".cube-initial").eq(current).attr('data'));
                $(".cube-set-drag-area").find(".cube-initial").eq(current).find('.cube-set-drag-content .customLayout-box').each(function (index, element) {
                  if ($(element).find('.hot-box').length > 0) {
                    var left = $(element).find(".hot-box").css('left');
                    var top = $(element).find(".hot-box").css('top');
                    var data_index = $(element).find(".hot-box").attr('data-index') * 1;
                    currentData.hot[index].x = parseFloat(left); //替换data里x轴
                    currentData.hot[index].y = parseFloat(top); //替换data里y轴
                    $(".cube-setting-layer").find(".hot-columns-dynamics").find('.x').eq(index).val(parseFloat(left));
                    $(".cube-setting-layer").find(".hot-columns-dynamics").find('.y').eq(index).val(parseFloat(top));
                  }
                })
                $(".cube-set-drag-area").find(".cube-initial").eq(current).attr('data', JSON.stringify(currentData));
              },
              cbEnd: function () {
                IndexController.updatePcData();//更新数据
              }
            });
          })
        }
      }
    },
    /**
     * 解析抽奖-圆模板
     * @param num  奖项个数
     * @param lotteryCircleBg 抽奖图片
     * @param contactBg 联系方式按钮
     * @param prizeBg 我的奖品按钮
     * @param bg 背景图
     * @param type 布局类型
     */
    renderlotteryCircle: function (num, lotteryCircleBg, contactBg, prizeBg, bg, type) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      var num = Number(num);
      if (num) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.angle').text(360 / num);
      }
      if (bg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotteryCircle-bg').css("background-image", "url(" + bg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotteryCircle-bg').removeAttr('style');
      }

      if (lotteryCircleBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.turntable-bg').find('.turntable-pic').attr('src', lotteryCircleBg);
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.turntable-bg').find('.turntable-pic').attr('src', 'https://img.ichunt.com/images/ichunt/202303/10/ed68d3caee2395f7a3c3c4d7138a3ca1.png');
      }

      if (contactBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.contactBg').empty().css("background-image", "url(" + contactBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.contactBg').empty().text('请填写联系方式').css("background-image", "url(" + contactBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      }

      if (prizeBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.prizeBg').empty().css("background-image", "url(" + prizeBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.prizeBg').empty().text('查看我的奖品').css("background-image", "url(" + prizeBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      }

    },
    /**
     * 解析抽奖-方模板
     * @param width  各奖项长/宽
     * @param lotterySquareBg 抽奖图片
     * @param contactBg 联系方式按钮
     * @param prizeBg 我的奖品按钮
     * @param bg 背景图
     * @param type 布局类型
     */
    renderlotterySquare: function (width, lotterySquareBg, contactBg, prizeBg, bg, type) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      var width = Number(width);
      if (width) {
        let total_width = Number(width) * 3 + 22;
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').css("width", total_width);
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').css("height", total_width);
        $(".cube-set-drag-area .cube-initial").eq(current).find('.btn-wrap').css("width", total_width);
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box1').css({
          left: 0,
          top: 0,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box2').css({
          left: width + 11,
          top: 0,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box3').css({
          left: (width * 2) + 22,
          top: 0,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box4').css({
          left: (width * 2) + 22,
          top: width + 11,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box5').css({
          left: (width * 2) + 22,
          top: (width * 2) + 22,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box6').css({
          left: width + 11,
          top: (width * 2) + 22,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box7').css({
          left: 0,
          top: (width * 2) + 22,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.box8').css({
          left: 0,
          top: width + 11,
          width: width,
          height: width
        });
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').find('.btnClick').css({
          left: width + 11,
          top: width + 11,
          width: width,
          height: width
        });

      }
      if (bg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-bg').css("background-image", "url(" + bg + ")").css("background-position", "center top").css("background-repeat", "no-repeat").css("background-size", "contain");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotteryCircle-bg').removeAttr('style');
      }

      if (lotterySquareBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.lotterySquare-content').css("background-image", "url(" + lotterySquareBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "contain");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.turntable-bg').find('.turntable-pic').attr('src', 'https://img.ichunt.com/images/ichunt/202303/10/ed68d3caee2395f7a3c3c4d7138a3ca1.png');
      }

      if (contactBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.contactBg').empty().css("background-image", "url(" + contactBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.contactBg').empty().text('请填写联系方式').css("background-image", "url(" + contactBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      }

      if (prizeBg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.prizeBg').empty().css("background-image", "url(" + prizeBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.prizeBg').empty().text('查看我的奖品').css("background-image", "url(" + prizeBg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      }

    },
    /**
     * 解析优惠券模板
     * @param bg 背景图
     * @param couponNum 优惠券个数
     * @param pattern 样式
     * @param columnCoupon 优惠券批次返回数组
     * @param columnCouponUrl 优惠券图片返回数组
     * @param columnCouponId 优惠券id
     * @param couponLength 优惠券尺寸 长
     * @param couponWidth  优惠券尺寸 宽
     * @param type 布局类型
     */
    renderCoupon: function (bg, couponNum, pattern, columnCoupon, columnCouponUrl, columnCouponId, couponLength, couponWidth, type) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      if (bg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.coupon').css("background-image", "url(" + bg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.coupon').removeAttr('style');
      }
      var tpl = couponHtml.innerHTML;
      layui.laytpl(tpl).render({
        pattern: pattern,
        columnCoupon: columnCoupon,
        columnCouponUrl: columnCouponUrl,
        columnCouponId: columnCouponId,
        couponLength: couponLength,
        couponWidth: couponWidth
      }, function (html) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.coupon').empty().html(html);
        //轮播图初始化
        if ($(".cube-set-drag-area .cube-initial").eq(current).find('.coupon').find(".carousel").find('li').length > 3) {
          $(".cube-set-drag-area .cube-initial").eq(current).find('.coupon').find(".hd").find('a').show();
        } else {
          $(".cube-set-drag-area .cube-initial").eq(current).find('.coupon').find(".hd").find('a').hide();
        }
        layui.form.render();
      });
    },
    /**
     * 解析商品列表模板
     */
    renderShopList: function (data, params) {
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      var tpl = lyDataTmp.innerHTML;
      var json = $.extend({}, data, {
        configParams: params
      });
      layui.laytpl(tpl).render(json, function (html) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.shopList ').empty().html(html);
        IndexController.updatePcData(); //更新数据
      });
    },
    /**
     * 解析表单模块
     */
    renderFormModule: function (data) {
      var fieldNum = data.fieldNum * 1;
      var form_data = data.form_data;
      var current = $(".cube-setting-layer").attr('data-index') * 1;
      var company = data.company_name;
      var submitBtn = data.submitBtn;
      var bg = data.bg;

      if (bg) {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.formModule').parent().css("background-image", "url(" + bg + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "cover");
      } else {
        $(".cube-set-drag-area .cube-initial").eq(current).find('.formModule').parent().removeAttr('style');
      }

      if (company == 0) {
        $("#company_name").hide();
      } else if (company == 1) {
        $("#company_name").show().find('.layui-form-label').removeClass('required');
        $("#company_name").show().find('.company_name').removeAttr('lay-verify');
      } else if (company == 2) {
        $("#company_name").show();
        $("#company_name").show().find('.layui-form-label').addClass('required');
        $("#company_name").show().find('.company_name').attr('lay-verify', 'required');
      }

      if (submitBtn) {
        $(".formModule #formModuleFilterSubmit").empty().text(submitBtn);
      }

      if (fieldNum > 0) {
        var htmlArr = [];
        for (var i = 0; i < fieldNum; i++) {
          htmlArr.push('<div class="layui-form-item">' +
            '                            <label class="layui-form-label required">' + form_data[i].input_name + '</label>' +
            '                            <div class="layui-input-block">' +
            '                                <input type="text" lay-verify="required" lay-reqtext="请输入' + form_data[i].input_name + '" lay-vertype="tips" placeholder="请输入' + form_data[i].input_name + '" autocomplete="off" class="layui-input input_value">' +
            '                            </div>' +
            '                        </div>');
        }
        $(".columns-dynamics-form").empty().html(htmlArr.join(''));
      }
    },
    /**
     * 验证抽奖方案是否存在
     */
    checkLottery: function (lotteryId, obj) {
      Request('/api/activityElement/checkLottery', 'GET', {
        lottery_ids: lotteryId
      }, function (res) {
        if (res.code === 0) {
          $(obj).removeClass("layui-btn-disabled").attr("disabled", false);
        } else {
          $(obj).addClass("layui-btn-disabled").attr("disabled", true);
          layer.msg(res.msg);
        }
      })
    },
    /**
     * 上传组件 背景图
     */
    uploadChange(preview) {
      var index = $(".cube-setting-layer").attr('data-index');
      var type = $(".cube-setting-layer").attr('data-type');
      //图片预览
      if (preview) {
        layer.photos({
          photos: '.cube-setting-layer .layui-tab-content .layui-tab-item.layui-show',
          anim: 5
        });
      } else {
        $(".cube-setting-layer .layui-tab-content").find(".layui-tab-item.layui-show").find(".uploadPic").each(function (index, element) {
          layui.upload.render({
            elem: $(element),
            url: oss_url + '/uploadFile?sys_type=7',
            accept: 'file',
            exts: 'jpg|png|gif|jpeg|png|mp4',
            before: function () {
              layer.load(2);
            },
            done: function (res) {
              layer.closeAll('loading');
              if (res.code === 0) {
                $(this.item).parent().parent().find('.pic').show().attr('src', res.data.oss_file_url);
                $(this.item).parent().parent().find('.pic').next().val(res.data.oss_file_url);
                //图片预览
                layer.photos({
                  photos: '.cube-setting-layer .layui-tab-content .layui-tab-item.layui-show',
                  anim: 5
                });
              } else {
                layer.msg(res.msg);
              }
            },
            error: function (index, upload) {
              layer.closeAll('loading');
              layer.msg('网络出现问题，请重试！');
            }
          });
        });
      }
    },
    /**
     * 上传sku ID
     */
    uploadChangeSKU: function () {
      layui.upload.render({
        elem: "#sku_id_upload",
        url: oss_url + '/uploadFile?sys_type=7',
        accept: 'file',
        exts: 'xls|xlsx',
        before: function () {
          layer.load(2);
        },
        done: function (res) {
          layer.closeAll('loading');
          if (res.code === 0) {
            layer.msg('上传成功');
            layui.form.val('shopListForm', {
              sku_id_upload_file: res.data.oss_file_url
            })
            $(this.item).text('重新上传');
            $(this.item).parent().find('.layui-icon-download-circle').attr('href', res.data.oss_file_url);
            IndexController.getSkuIdsByFile(res.data.oss_file_url);
          } else {
            layer.msg(res.msg);
          }
        },
        error: function (index, upload) {
          layer.closeAll('loading');
          layer.msg('网络出现问题，请重试！');
        }
      });
    },
    /**
     * 获取活动价列表
     * @param activity_price_id
     * @param ids 选中的id
     */
    initActivityPrice: function (activity_price_id,ids) {
      const params = {
        status: 1,
        page: 1,
        limit: 1000
      };
      Request('/api/priceActivity/getPriceActivityList', 'POST', params, function (res) {
        if (res.code == 0) {
          const filteredData = res.data.map(item => {
            const filteredItem = {
              name: item.activity_name,
              value: item.id
            };
            if (item.id === activity_price_id) {
              filteredItem.selected = true;
            }
            return filteredItem;
          });
          xmSelect.render({
            el: '#activity_price_ids',
            searchTips: '请输入搜索',
            model: {
              label: {
                type: 'text'
              }
            },
            toolbar: {
              show: true,
              list: ['CLEAR']
            },
            initValue: ids,
            filterable: true,
            data: filteredData,
            name: 'activity_price_ids',
            on: function (res) {

            }
          })
          layui.form.render();
        }
      })
    },
    /**
     * 获取文件中的skuIds
     */
    getSkuIdsByFile: function (file_url) {
      Request('/api/activityElement/getSkuIdsByFile', 'GET', {
        file_url: file_url
      }, function (res) {
        if (res.code === 0) {
          layui.form.val('shopListForm', {
            sku_ids: res.data
          })
        }
      })
    },
    /**
     * 事件绑定
     * @returns {Window.IndexController}
     */
    handleBind: function () {

      //系统原件监听
      $(document).on('change', '#page_navigation_bar', function () {
        var val = this.checked;
        IndexController.updatePcData();
      });

      $(document).on('change', '#page_right_bar', function () {
        var val = this.checked;
        IndexController.updatePcData();
      });

      //清除图片
      $(document).on('click', '.clearPic', function () {
        $(this).parent().parent().find('img.pic').hide().attr('src', '').next().val('');
      })

      //清除背景图片
      $(document).on('click', '.clearBg', function () {
        $("body").css("background-image", "url('')").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "contain");
        $("#page_background").hide().attr('src', '');
        IndexController.updatePcData();
      })

      //编辑楼层名字
      $(document).on('click', '.floor-text', function () {
        var page_index = $(this).parent().parent().parent().attr('data-index');
        var text = $(this).prev().text();
        var self = $(this);
        layer.prompt({title: '修改楼层名字', formType: 0, value: text,}, function (value, index) {
          self.prev().text(value);
          $(".cube-set-drag-area .cube-initial").eq(page_index).attr('data-name', value)
          layer.closeAll();
          IndexController.updatePcData();
        });
      })

      //热区区域点击
      $(document).on('click', '.hot-box', function (event) {
        return false;
      });

      //楼层第一列如果配置了href点击跳转
      $(document).on('click', '.cube-set-drag-area .cube-set-drag-content-row', function (event) {
        var href = $(this).attr('href');
        if (href) {
          toUrl(href);
        }
        return false;
      });

      //切换活动名称
      $(document).on('change', '.env', function () {
        var val = $(this).val();
        if (val == 1) {
          window.location.href = '/web/activity/set/?from=pc&id=' + id;
        } else if (val == 2) {
          window.location.href = '/web/activity/set/h5?from=h5&id=' + id;
        }
      });

      //背景色
      $(document).on('input', '.cube-set-head #page_color', debounce(function (e) {
        var val = $(this).val();
        $("body").css('background-color', val);
        $(".cube-set-drag-area").find('.customLayout ').css('background-color', val);
        IndexController.updatePcData();
      }, 800));

      //楼层删除
      $(document).on('click', '.cube-set-drag-area .delete', function (event) {
        var self = $(this);
        layer.confirm('是否要<em class="warm-color">删除</em>该楼层？', {
          skin: 'layui-layer-admin',
          title: '提示',
          shade: .1,
          resize: false,
          offset: '250px',
          move: false
        }, function (index) {
          self.parent().parent().parent().remove();
          IndexController.sortNumChange(); //更新排序
          IndexController.updatePcData(); //更新数据
          $(".cube-setting-layer").hide(); //楼层隐藏
          layer.close(index);
        });
      });

      //全局楼层状态切换
      form.on('switch(statusChange)', function (data) {
        var current = data.value * 1;
        var elem = data.elem;
        var checked = data.elem.checked;
        var data = JSON.parse($(".cube-set-drag-area .cube-initial").eq(current).attr('data'));

        if (checked) {
          var params = $.extend({}, data, {disable: 1});
          $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(params));
          $(".cube-set-drag-area .cube-initial").eq(current).show();
        } else {
          var params = $.extend({}, data, {disable: 0});
          $(".cube-set-drag-area .cube-initial").eq(current).attr('data', JSON.stringify(params));
          $(".cube-set-drag-area .cube-initial").eq(current).hide();
        }
        IndexController.updatePcData(); //更新数据
      });

      //楼层tab切换
      element.on('tab(tabChange)', function (data) {
        var floor = $(".cube-setting-layer").attr('data-index');
        var index = data.index;
        if (index == 0) {
          //布局设置
        } else if (index == 1) {
          //全局楼层
          IndexController.floorsChangeGlobal();
        } else if (index == 2) {
          //热区
          var data = $(".cube-set-drag-area").find(".cube-initial").eq(floor).attr('data');
          if (data) {
            $("#hotSubmit").removeClass("layui-btn-disabled").attr("disabled", false);
            IndexController.settingHotRender(JSON.parse(data));
          } else {
            $("#hotSubmit").addClass("layui-btn-disabled").attr("disabled", true);
            layer.msg('请先设置布局设置');
          }
        }
      });

      //楼层上移
      $(document).on('click', '.cube-set-drag-area .move-up', function (event) {
        var parent = $(this).parent().parent().parent(); //拿到父级
        var prev = parent.prev(); //获取同胞元素的上一个元素
        parent.insertBefore(prev); //将它插入到当前元素之后，实现当前元素上移
        IndexController.sortNumChange(); //更新排序
        IndexController.updatePcData(); //更新数据
      });

      //楼层下移
      $(document).on('click', '.cube-set-drag-area .move-down', function (event) {
        var parent = $(this).parent().parent().parent(); // 拿到父级
        var next = parent.next(); //获取同胞元素的下一个元素
        parent.insertAfter(next); //将它插入到当前元素之前，实现当前元素下移
        IndexController.sortNumChange(); //更新排序
        IndexController.updatePcData(); //更新数据
      });

      //楼层设置显示
      $(document).on('click', '.cube-set-drag-area .settingObj', function (event) {
        var index = $(this).parent().parent().parent().attr('data-index') * 1;
        var type = $(this).parent().parent().parent().attr('data-type');
        var uuid = $(this).parent().parent().parent().attr('data-uuid');
        var data = $(this).parent().parent().parent().attr('data');
        var data_name = $('.cube-set-drag-area .cube-initial').eq(index).attr('data-name') || '';

        var tpl = floorHtml.innerHTML;
        layui.laytpl(tpl).render(type, function (html) {
          $(".cube-setting-layer").empty().append(html);
          //判断是否自定义楼层名字
          if (data_name) {
            $(".cube-setting-layer").show().find('.floor').empty().text(data_name); //楼层数字标记
          } else {
            $(".cube-setting-layer").show().find('.floor').empty().text('楼层' + (index + 1)); //楼层数字标记
          }
          $(".cube-setting-layer").show().find('.floor-name').empty().text('（' + textVal[type] + '）'); //楼层数字标记
          $(".cube-setting-layer").attr('data-index', index);
          $(".cube-setting-layer").attr('data-type', type);
          $(".cube-setting-layer").attr('data-uuid', uuid);
          //判断是否存储数据
          if (data) {
            if (type == 'customLayout') {
              IndexController.settingRender(JSON.parse(data));
            } else if (type == 'lotteryCircle') {
              IndexController.settingRenderLotteryCircle(JSON.parse(data));
              IndexController.uploadChange(); //第一次调用是初始化上传组件
            } else if (type == 'lotterySquare') {
              IndexController.settingRenderLotterySquare(JSON.parse(data));
              IndexController.uploadChange(); //第一次调用是初始化上传组件
            } else if (type == 'coupon') {
              IndexController.settingRenderCoupon(JSON.parse(data));
            } else if (type == 'shopList') {
              IndexController.settingRenderShopList(JSON.parse(data));
              IndexController.uploadChange(); //第一次调用是初始化上传组件
            } else if (type == 'formModule') {
              IndexController.settingRenderForm(JSON.parse(data));
              IndexController.uploadChange(); //第一次调用是初始化上传组件
            }
          } else {
            IndexController.uploadChange();
          }

          //根据所选组件默认width的调整
          if (type == 'shopList') {
            $(".cube-setting-layer").css({
              width: '1000px'
            });
          } else {
            $(".cube-setting-layer").css({
              width: '450px'
            });
          }

          //启用拖动
          $(".cube-setting-layer").Tdrag({
            scope: 'body',
            handle: ".title"
          });

          $(window).resize(function () {
            $(".cube-setting-layer").css('left', 'inherit');
          });


          layui.form.render();
        });
      });

      //楼层收缩
      $(document).on('click', '.cube-setting-layer .toggle', function (event) {
        $(".cube-setting-layer").slideUp();
      });

      //元件库收缩
      $(document).on('click', '.left-menu .shousuo-box', function (event) {
        $(".left-menu").toggleClass('shousuo-box-curr');
        if ($(this).parent().hasClass('shousuo-box-curr')) {
          $(this).attr('title', '展开');
        } else {
          $(this).attr('title', '收缩');
        }
      });

      //搜索框监听
      $(document).on('change', '#is_search', function (event) {
        if ($(this).prop('checked')) {
          $(this).next().next().show();
        } else {
          $(this).next().next().hide();
          $(this).next().next().val('');
        }
      });

      //上传背景图
      layui.upload.render({
        elem: '#page_background_upload',
        url: oss_url + '/uploadFile?sys_type=7',
        accept: 'file',
        exts: 'jpg|png|gif|jpeg|png',
        before: function () {
          layer.load(2);
        },
        done: function (res) {
          layer.closeAll('loading');
          if (res.code === 0) {
            $(this.item).parent().find('.page-background').attr('src', res.data.oss_file_url).show();
            $("body").css("background-image", "url(" + res.data.oss_file_url + ")").css("background-position", "center").css("background-repeat", "no-repeat").css("background-size", "contain");
            IndexController.updatePcData();
            //图片预览
            layer.photos({
              photos: '.cube-set-head',
              anim: 5
            });
          } else {
            layer.msg(res.msg);
          }
        },
        error: function (index, upload) {
          layer.closeAll('loading');
          layer.msg('网络出现问题，请重试！');
        }
      });

      return this;
    }
  }

  IndexController.init();

})