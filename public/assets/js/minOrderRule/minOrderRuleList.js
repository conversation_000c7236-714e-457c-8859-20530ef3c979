layui.use(['form', 'laydate', 'element', 'table', 'index', 'xmSelect'], function () {
    const form = layui.form;
    const table = layui.table;
    const layer = layui.layer;
    const index = layui.index;
    const xmSelect = layui.xmSelect;
    let where = {};

    table.render({
        elem: '#list',
        url: '/api/minOrderRule/getMinOrderRuleList',
        method: 'post',
        size: 'sm',
        cellMinWidth: 80,
        request: {
            limitName: 'limit'
        },
        toolbar: '#toolbar',
        defaultToolbar: ['filter'],
        where: {},
        limit: 15,
        loading: true,
        cols: [[
            { field: 'rule_id', title: '序号', align: 'center', width: 60 },
            { field: 'supplier_id', title: '渠道ID', align: 'center', width: 120 },
            { field: 'supplier', title: '渠道名称', align: 'center', width: 120 },
            { field: 'supplier_code', title: '供应商编码', align: 'center', width: 120 },
            { field: 'supplier_name', title: '供应商名称', align: 'center' },
            {
                field: 'status', title: '状态', align: 'center', width: 80, templet: function (d) {
                    return d.status == 1 ? '<span class="layui-badge layui-bg-green">启用</span>' : '<span class="layui-badge layui-bg-red">禁用</span>';
                }
            },
            {
                field: 'cny_amount', title: '订单最小金额(CNY)', align: 'center', width: 150, templet: function (d) {
                    return d.cny_amount == 0 ? '无限制' : '¥' + d.cny_amount;
                }
            },
            {
                field: 'usd_amount', title: '订单最小金额(USD)', align: 'center', width: 150, templet: function (d) {
                    return d.usd_amount == 0 ? '无限制' : '$' + d.usd_amount;
                }
            },
            { field: 'create_name', title: '创建人', align: 'center', width: 100 },
            { field: 'create_time', title: '创建时间', align: 'center', width: 160 },
            { field: 'update_name', title: '更新人', align: 'center', width: 100 },
            { field: 'update_time', title: '更新时间', align: 'center', width: 160 },
            { field: 'edit', title: '操作', templet: '#edit', width: 210, fixed: 'right' },
        ]],
        id: 'list',
        page: {}
    });

    form.on('submit(load)', function (data) {
        where = data.field;
        table.reload('list', {
            page: {
                curr: 1
            },
            where: data.field,
        });
    });

    table.on('toolbar(list)', function (obj) {
        let checkStatus = table.checkStatus('list');
        let data = checkStatus.data;
        switch (obj.event) {
            case "add":
                layer.open({
                    type: 2,
                    area: ['70%', '70%'],
                    fixed: false,
                    title: '新增订单规则',
                    content: '/web/minOrderRule/saveMinOrderRule',
                    end: function () {
                        table.reload('list', {
                            page: {
                                curr: 1
                            },
                            where: where,
                        });
                    }
                });
                break;
        }
    });

    table.on('tool(list)', function (obj) {
        let event = obj.event;
        let ruleId = obj.data.rule_id;
        let supplierName = obj.data.supplier_name;

        if (event === 'edit') {
            layer.open({
                type: 2,
                area: ['70%', '70%'],
                fixed: false,
                title: '修改订单规则 - ' + supplierName,
                content: '/web/minOrderRule/saveMinOrderRule?rule_id=' + ruleId,
                end: function () {
                    table.reload('list', {
                        page: {
                            curr: 1
                        },
                        where: where,
                    });
                }
            });
        }

        if (event === 'delete') {
            layer.confirm('你确定要删除该订单规则吗？', {
                icon: 3,
                title: '提示'
            }, function (index) {
                const url = '/api/minOrderRule/deleteMinOrderRule';
                const data = {
                    rule_id: ruleId
                };
                $.post(url, data, function (res) {
                    if (res.code === 0) {
                        table.reload('list');
                        layer.msg("删除成功", { icon: 6 });
                    } else {
                        layer.msg(res.msg, { icon: 5 });
                    }
                });
                layer.close(index);
            });
        }

        if (event === 'log') {
            layer.open({
                type: 2,
                area: ['800px', '500px'],
                fixed: false,
                title: '操作日志 - ' + supplierName,
                content: '/web/minOrderRule/operationLog?rule_id=' + ruleId
            });
        }

        if (event === 'enable') {
            changeStatus(ruleId, 1);
        }

        if (event === 'disable') {
            changeStatus(ruleId, 2);
        }
    });

    function changeStatus(ruleId, status) {
        const statusText = status === 1 ? '启用' : '禁用';
        layer.confirm('你确定要' + statusText + '该订单规则吗？', {
            icon: 3,
            title: '提示'
        }, function (index) {
            const url = '/api/minOrderRule/changeStatus';
            const data = {
                rule_id: ruleId,
                status: status
            };
            $.post(url, data, function (res) {
                if (res.code === 0) {
                    table.reload('list');
                    layer.msg(statusText + "成功", { icon: 6 });
                } else {
                    layer.msg(res.msg, { icon: 5 });
                }
            });
            layer.close(index);
        });
    }
});
