layui.use(['form', 'layer', 'admin', 'index'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;

    form.on('submit(saveForm)', function (data) {
        verifyDataAndSave(data.field);
    });

    $('.cancel').click(function () {
        admin.closeThisDialog();
    });

    //监听supplier_id变化,如果值为17,那么就展示supplier_code,否则隐藏
    form.on('select(supplier_id)', function (data) {
        if (data.value == 17) {
            $('#supplier_code_selector').show();
        } else {
            $('#supplier_code_selector').hide();
        }
    });

    //监听type变化,如果值为2,那么就展示usd_amount,否则隐藏
    form.on('radio(type)', function (data) {
        $('#rule_div').hide();
        switch (data.value) {
            case 1:
                $('#order_amount_section').show();
                break;
            case 2:
                $('#order_amount_ladder_section').show();
                break;
            case 3:
                break;
            case 4:
                break;
        }
    });


    function verifyDataAndSave(formData) {
        // 表单验证
        if (!formData.supplier_id) {
            layer.msg('请选择渠道', {icon: 5});
            return false;
        }

        if (formData.cny_amount === '' && formData.usd_amount === '') {
            layer.msg('人民币和美金订单最小金额至少填写一项', {icon: 5});
            return false;
        }

        // 金额验证
        if (formData.cny_amount !== '' && (isNaN(formData.cny_amount) || parseFloat(formData.cny_amount) < 0)) {
            layer.msg('人民币订单最小金额必须大于等于0', {icon: 5});
            return false;
        }

        if (formData.usd_amount !== '' && (isNaN(formData.usd_amount) || parseFloat(formData.usd_amount) < 0)) {
            layer.msg('美金订单最小金额必须大于等于0', {icon: 5});
            return false;
        }

        saveMinOrderRule(formData);
    }

    function saveMinOrderRule(formData) {
        admin.btnLoading('.saveForm', '保存中');

        $.ajax({
            url: '/api/minOrderRule/saveMinOrderRule',
            type: 'post',
            data: formData,
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (!res) return layer.msg('网络错误，请重试', {icon: 5});
                if (res.code === 0) {
                    admin.btnLoading('.saveForm', false);
                    admin.btnLoading('.cancel', false);
                    layer.msg(res.msg, {icon: 6});
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 500);
                } else {
                    admin.btnLoading('.saveForm', false);
                    admin.btnLoading('.cancel', false);
                    layer.msg(res.msg, {icon: 5});
                }
            },
            error: function () {
                admin.btnLoading('.saveForm', false);
                admin.btnLoading('.cancel', false);
                return layer.msg('网络错误，请重试', {icon: 5});
            }
        });
    }
});
