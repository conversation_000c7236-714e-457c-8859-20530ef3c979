layui.use(['form', 'layer', 'admin', 'index'], function () {
    let form = layui.form;
    let layer = layui.layer;
    let admin = layui.admin;
    let index = layui.index;

    form.on('submit(saveForm)', function (data) {
        verifyDataAndSave(data.field);
    });

    $('.cancel').click(function () {
        admin.closeThisDialog();
    });

    // 监听supplier_id变化,如果值为17,那么就展示supplier_code,否则隐藏
    form.on('select(supplier_id)', function (data) {
        if (data.value == 17) {
            $('#supplier_code_selector').show();
        } else {
            $('#supplier_code_selector').hide();
        }
    });

    // 监听打卷费是否收费
    form.on('radio(rolling_is_charge)', function(data) {
        if (data.value == 1) {
            $('#rolling_config_section').show();
            // 根据当前选中的配置方式决定显示哪个区域
            const configType = $('input[name="rolling_rule[config_type]"]:checked').val();
            toggleRollingSection(configType);
        } else {
            $('#rolling_config_section').hide();
            $('#rolling_all_section').hide();
            $('#rolling_package_section').hide();
        }
    });

    // 监听打卷费配置方式
    form.on('radio(rolling_config_type)', function(data) {
        toggleRollingSection(data.value);
        if (data.value == 1) {
            $('.package-keyword-column').hide();
        } else {
            $('.package-keyword-column').show();
        }
    });

    // 监听操作费是否收费
    form.on('radio(operation_is_charge)', function(data) {
        if (data.value == 1) {
            $('#operation_config_section').show();
            // 根据当前选中的配置方式决定显示哪个区域
            const configType = $('input[name="operation_rule[config_type]"]:checked').val();
            toggleOperationSection(configType);
        } else {
            $('#operation_config_section').hide();
            $('#operation_all_section').hide();
            $('#operation_package_section').hide();
        }
    });

    // 监听操作费配置方式
    form.on('radio(operation_config_type)', function(data) {
        toggleOperationSection(data.value);
        if (data.value == 1) {
            $('.package-keyword-column').hide();
        } else {
            $('.package-keyword-column').show();
        }
    });

    // 切换打卷费区域显示
    function toggleRollingSection(configType) {
        if (configType == 1) {
            $('#rolling_all_section').show();
            $('#rolling_package_section').hide();
            $('.rolling-package-keyword-column').hide();
        } else {
            $('#rolling_all_section').hide();
            $('#rolling_package_section').show();
            $('.rolling-package-keyword-column').show();
        }
    }

    // 切换操作费区域显示
    function toggleOperationSection(configType) {
        if (configType == 1) {
            $('#operation_all_section').show();
            $('#operation_package_section').hide();
            $('.operation-package-keyword-column').hide();
        } else {
            $('#operation_all_section').hide();
            $('#operation_package_section').show();
            $('.operation-package-keyword-column').show();
        }
    }

    function verifyDataAndSave(formData) {
        saveOtherFeeRule(formData);
    }

    function saveOtherFeeRule(formData) {
        admin.btnLoading('.saveForm', '保存中');

        $.ajax({
            url: '/api/otherFeeRule/saveOtherFeeRule',
            type: 'post',
            data: formData,
            dataType: 'json',
            timeout: 10000,
            success: function (res) {
                if (!res) return layer.msg('网络错误，请重试', {icon: 5});
                if (res.code === 0) {
                    admin.btnLoading('.saveForm', false);
                    admin.btnLoading('.cancel', false);
                    layer.msg(res.msg, {icon: 6});
                    setTimeout(function () {
                        admin.closeThisDialog();
                    }, 500);
                } else {
                    admin.btnLoading('.saveForm', false);
                    admin.btnLoading('.cancel', false);
                    layer.msg(res.msg, {icon: 5});
                }
            },
            error: function () {
                admin.btnLoading('.saveForm', false);
                admin.btnLoading('.cancel', false);
                return layer.msg('网络错误，请重试', {icon: 5});
            }
        });
    }

    // 页面加载时初始化显示状态
    $(document).ready(function() {
        // 初始化打卷费区域
        if ($('input[name="rolling_rule[is_charge]"]:checked').val() == 1) {
            const rollingConfigType = $('input[name="rolling_rule[config_type]"]:checked').val();
            toggleRollingSection(rollingConfigType);
        }

        // 初始化操作费区域
        if ($('input[name="operation_rule[is_charge]"]:checked').val() == 1) {
            const operationConfigType = $('input[name="operation_rule[config_type]"]:checked').val();
            toggleOperationSection(operationConfigType);
        }
    });
});
