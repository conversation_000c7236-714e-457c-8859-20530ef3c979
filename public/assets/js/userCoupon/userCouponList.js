layui.use(['form', 'laydate', 'element', 'table', 'index'], function () {
    const form = layui.form;
    const table = layui.table;
    const laydate = layui.laydate;
    const index = layui.index;

    //日期
    laydate.render({
        elem: 'input[name=use_time]'
        , type: 'datetime'
        , trigger: 'click'
        , range: '~' //或 range: '~' 来自定义分割字符
    });

    table.render({
        elem: '#list',
        url: '/api/userCoupon/getUserCouponList',
        method: 'post',
        toolbar: '#toolbar',
        defaultToolbar: ['filter'],
        size: 'sm',
        cellMinWidth: 80,//全局定义常规单元格的最小宽度
        request: {
            limitName: 'limit' //每页数据量的参数名，默认：limit
        },
        where: {},
        limit: 15,
        loading: true,
        cols: [[
            {field: 'user_coupon_id', title: '优惠券领取ID', align: 'center', width: 120},
            {field: 'coupon_name', title: '优惠券名称', align: 'center', width: 180},
            {field: 'coupon_desc', title: '优惠券描述', align: 'center', width: 180},
            {field: 'user_account', title: '客户账号', align: 'center', width: 150},
            {field: 'adtag', title: '渠道来源', align: 'center'},
            {field: 'coupon_sn', title: '优惠券批次', align: 'center', width: 180},
            {field: 'coupon_type_name', title: '优惠券类型', align: 'center'},
            {field: 'status_name', title: '使用状态', align: 'center'},
            {field: 'order_sn', title: '订单编号', align: 'center'},
            {field: 'create_time', title: '领取时间', align: 'center', width: 150},
            {field: 'end_time', title: '到期时间', align: 'center', width: 150},
            {field: 'use_time', title: '使用时间', align: 'center', width: 150},
        ]],
        id: 'list',
        page: {}
    });

    form.on('submit(load)', function (data) {
        table.reload('list', {
            page: {
                curr: 1 //重新从第 1 页开始
            },
            where: data.field,
        });
    });

    table.on('toolbar(list)', function(obj){
        if (obj.event === 'export') {
            let formValue = form.val("user_coupon_list_form");
            let params = JSON.stringify(formValue);
            window.open('/api/userCoupon/exportUserCouponList?params=' + params, '_blank');
        }
    });

});
