layui.config({}).extend({}).use(['admin', 'index', 'form', 'table', 'laydate'], function () {
  var admin = layui.admin;
  var index = layui.index;
  var form = layui.form;
  var table = layui.table;
  var laydate = layui.laydate;

  window.IndexController = {
    init: function () {
      this.created(this).render(this).handleBind(this);
    },
    created: function () {

      return this;
    },
    render: function () {

      table.render({
        elem: '#list',
        url: '/api/activityElement/list',
        toolbar: '#toolbar',
        method: 'POST',
        cols: [[
          {field: 'id', title: 'ID', width: 50, align: 'center'},
          {field: 'element_name', title: '模板名称', align: 'center'},
          {field: 'element_code', title: '视图标记', align: 'center'},
          {
            field: 'element_status_name', title: '状态', align: 'center', templet(d) {
              var status = d.element_status == 1 ? "checked" : "";
              return '<input type="checkbox"  lay-skin="switch" data-id="' + d.id + '" lay-text="启用|禁用" lay-filter="statusChange" ' + status + '>'
            }
          },
          {
            field: '', title: '操作', width: 80, align: 'center', fixed: 'right', templet(d) {
              return '<a class="layui-btn layui-btn-xs" lay-event="update">编辑</a>'
            }
          }
        ]],
        parseData: LayUiTableParseData,
        done: function (res, curr, count) {
          layui.form.render();
        }
      });

      //查询搜索
      form.on('submit(getList)', function (data) {
        table.reload('list', {
          where: data.field,
          page: {
            curr: 1
          }
        });
      });

      //监听头工具栏事件
      table.on('toolbar(list)', function (obj) {

        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;

        switch (obj.event) {
          //添加模块配置
          case 'activityElementAdd':
            layer.open({
              type: 1,
              title: '添加模块配置',
              offset: '50px',
              area: ['450px', 'auto'],
              shadeClose: false,
              resize: false,
              move: false,
              content: $('#activityElementAddHtml').html(),
              success: function (layero, dIndex) {
                layero.find('.layui-layer-content').css('overflow', 'visible');
                layui.form.render();
              }
            });
            break;
        }
      });

      //触发单元格工具事件
      table.on('tool(list)', function (obj) {
        var data = obj.data;
        switch (obj.event) {
          //编辑模块
          case 'update':
            layer.open({
              type: 1,
              title: '编辑模块配置',
              offset: '50px',
              area: ['450px', 'auto'],
              shadeClose: false,
              resize: false,
              move: false,
              content: $('#activityElementAddHtml').html(),
              success: function (layero, dIndex) {
                layero.find('.layui-layer-content').css('overflow', 'visible');

                layui.form.val('activityElementAddForm', {
                  id: data.id,
                  element_name: data.element_name,
                  element_code: data.element_code
                });

                layui.form.render();
              }
            });
            break;
        }
      });
      return this;
    },
    /**
     * 事件绑定
     */
    handleBind: function () {
      //添加，编辑模块配置
      layui.form.on('submit(activityElementAddSubmit)', function (data) {
        if (data.field.id) {
          var url = '/api/activityElement/update';
        } else {
          var url = '/api/activityElement/add';
        }
        Request(url, 'POST', data.field, function (res) {
          admin.btnLoading($(data.elem), '');
          if (res.code == 0) {
            layer.msg('操作成功', {shift: 0, time: 2000}, function () {
              layer.closeAll();
              table.reloadData('list', {
                page: {
                  curr: 1
                }
              });
            });
          } else {
            admin.btnLoading($(data.elem), false);
            layer.msg(res.msg);
          }
        });
      });

      //状态监听
      layui.form.on('switch(statusChange)', function (data) {
        var id = $(data.elem).attr("data-id");
        if (data.elem.checked) {
          var status = 1;
        } else {
          var status = -1;
        }
        Request('/api/activityElement/updateStatus', 'POST', {id: id, status: status}, function (res) {
          if (res.code == 0) {
            layer.msg('操作成功', {shift: 0, time: 2000}, function () {
              layer.closeAll();
              table.reloadData('list', {
                page: {
                  curr: 1
                }
              });
            });
          } else {
            layer.msg(res.msg);
          }
        });
      });
      return this;
    }
  }

  IndexController.init();

});
