<?php
return [

    'erp_domain'     => get_resource_config_section('app', 'pur')["erp_domain"],
    'scm_domain'     => get_resource_config_section('app', 'pur')["scm_domain"],
    'erp_login_name' => get_resource_config_section('app', 'pur')["erp_login_name"],
    'erp_db_name'    => get_resource_config_section('app', 'pur')["erp_db_name"],
    "num_format"=>[
        "1"=>"一",
        "2"=>"二",
        "3"=>"三",
        "4"=>"四",
        "5"=>"五",
        "6"=>"六",
        "7"=>"七",
        "8"=>"八",
        "9"=>"九",
    ],
    "ladder_price_redis_connection_name"=>"frq",
    //销售组阶梯价 代购
    "ladder_price_rediskey_daigou"=>"magic_cube_price_rule_channel",
    //销售组阶梯价 专营
    "ladder_price_rediskey_zhuanying"=>"magic_cube_price_rule_v2",

    //销售组阶梯价 代购全局默认
    "ladder_price_rediskey_default_daigou"=>"magic_cube_price_rule_channel_default",

    //销售组阶梯价 专营全局默认
    "ladder_price_rediskey_default_zhuanying"=>"magic_cube_price_rule_v2_default",

    //渠道折扣 代购
    "channel_discount_daigou"=>"magic_cube_channel_discount_daigou",
    //渠道折扣专营
    "channel_discount_zhuanying"=>"magic_cube_channel_discount_zhuanying",


    //渠道折扣专营 全局默认
    "channel_discount_default_zhuanying"=>"magic_cube_channel_discount_default_zhuanying",
    //渠道折扣代购 全局默认
    "channel_discount_default_daigou"=>"magic_cube_channel_discount_default_daigou",

    "liexin_zhuanying_supplier_id" => 17,

    //专营默认售价组阶梯信息  初始化使用
    "default_zy_salePriceGroup_ladder"=>'{
              "1": {
                "ladder_price_egt50_lt200": 1,
                "ladder_price_egt200": 1
              },
              "2": {
                "ladder_price_egt50_lt200": 3,
                "ladder_price_egt200": 2
              },
              "3": {
                "ladder_price_egt50_lt200": 6,
                "ladder_price_egt200": 4
              },
              "4": {
                "ladder_price_egt50_lt200": 10,
                "ladder_price_egt200": 8
              },
              "5": {
                "ladder_price_egt50_lt200": 15,
                "ladder_price_egt200": 16
              },
              "6": {
                "ladder_price_egt50_lt200": 17,
                "ladder_price_egt200": 18
              },
              "7": {
                "ladder_price_egt50_lt200": 19,
                "ladder_price_egt200": 20
              },
              "8": {
                "ladder_price_egt50_lt200": 21,
                "ladder_price_egt200": 22
              },
              "9": {
                "ladder_price_egt50_lt200": 23,
                "ladder_price_egt200": 24
              }
            }',

    //默认代购初始化数据 利润阶梯都是0
    "default_daigou_salePriceGroup_ladder"=>'{"1":{"ratio":0,"ratio_usd":0},"2":{"ratio":0,"ratio_usd":0},"3":{"ratio":0,"ratio_usd":0},"4":{"ratio":0,"ratio_usd":0},"5":{"ratio":0,"ratio_usd":0},"6":{"ratio":0,"ratio_usd":0},"7":{"ratio":0,"ratio_usd":0},"8":{"ratio":0,"ratio_usd":0},"9":{"ratio":0,"ratio_usd":0}}',

    //全局默认代购初始化数据  默认代购全局 利润阶梯都是0
    "global_default_daigou_salePriceGroup_ladder"=>'{"ladder_price":[{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1}]}',


    //全局默认专营初始化售价组 默认都是10%
    "global_default_zhuanying_salePriceGroup_ladder"=>'{"cost_ladder_price_egt50_lt200":[{"purchases":1,"price":1.1,"price_usd":1.1},{"purchases":3,"price":1.1,"price_usd":1.1},{"purchases":6,"price":1.1,"price_usd":1.1},{"purchases":10,"price":1.1,"price_usd":1.1},{"purchases":15,"price":1.1,"price_usd":1.1},{"purchases":17,"price":1.1,"price_usd":1.1},{"purchases":19,"price":1.1,"price_usd":1.1},{"purchases":21,"price":1.1,"price_usd":1.1},{"purchases":23,"price":1.1,"price_usd":1.1}],"cost_ladder_price_egt200":[{"purchases":1,"price":1.1,"price_usd":1.1},{"purchases":2,"price":1.1,"price_usd":1.1},{"purchases":4,"price":1.1,"price_usd":1.1},{"purchases":8,"price":1.1,"price_usd":1.1},{"purchases":16,"price":1.1,"price_usd":1.1},{"purchases":18,"price":1.1,"price_usd":1.1},{"purchases":20,"price":1.1,"price_usd":1.1},{"purchases":22,"price":1.1,"price_usd":1.1},{"purchases":24,"price":1.1,"price_usd":1.1}],"ladder_price_egt50_lt200":[{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.12,"ratio_usd":1.12},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1}],"ladder_price_egt200":[{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1},{"ratio":1.1,"ratio_usd":1.1}],"is_set_lowest_profit":true,"ladder_price_mini_profit_level":9}',
];
