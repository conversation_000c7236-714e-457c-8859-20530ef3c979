<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [
        // 新魔方
        'mysql' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_topic')['host'],
            'database' => get_resource_config_section('db', 'db_topic')['db'],       // liexin_crm
            'username' => get_resource_config_section('db', 'db_topic')['user'],
            'password' => get_resource_config_section('db', 'db_topic')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        //旧CRM中 tableconfig，mysql
        'cms' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_cms')['host'],
            'database' => get_resource_config_section('db', 'db_cms')['db'],
            'username' => get_resource_config_section('db', 'db_cms')['user'],
            'password' => get_resource_config_section('db', 'db_cms')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],

        // 旧CRM中 web
        'web' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_liexin')['host'],
            'database' => get_resource_config_section('db', 'db_liexin')['db'],     // liexin
            'username' => get_resource_config_section('db', 'db_liexin')['user'],
            'password' => get_resource_config_section('db', 'db_liexin')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],

        // 旧CRM中 behavior
        'behavior' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_behavior')['host'],
            'database' => get_resource_config_section('db', 'db_behavior')['db'],   // liexin_behavior
            'username' => get_resource_config_section('db', 'db_behavior')['user'],
            'password' => get_resource_config_section('db', 'db_behavior')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],


        // 旧CRM中 order
        'order' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_order')['host'],
            'database' => get_resource_config_section('db', 'db_order')['db'],    // liexin_order
            'username' => get_resource_config_section('db', 'db_order')['user'],
            'password' => get_resource_config_section('db', 'db_order')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],

        // 旧CRM中 supply
        'supply' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_sc')['host'],
            'database' => get_resource_config_section('db', 'db_sc')['db'],  // liexin_supply_chain
            'username' => get_resource_config_section('db', 'db_sc')['user'],
            'password' => get_resource_config_section('db', 'db_sc')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],

        //自营数据库
        'self' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_data')['host'],
            'database' => get_resource_config_section('db', 'db_data')['db'],  // liexin_supply_chain
            'username' => get_resource_config_section('db', 'db_data')['user'],
            'password' => get_resource_config_section('db', 'db_data')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],

        'mongodb' => [
            'driver' => 'mongodb',
            'host' => get_resource_config_section('mongodb', 'mongo')['host'],
            'port' => get_resource_config_section('mongodb', 'mongo')['port'],
            'database' => get_resource_config_section('mongodb', 'mongo')['db'],
            'username' => get_resource_config_section('mongodb', 'mongo')['user'],
            'password' => get_resource_config_section('mongodb', 'mongo')['passwd'],
            'options'  => [
                'database' => 'ichunt'
            ]
        ],

        //--------------------------------------------------------------------------------

        'purchase' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_purchase')['host'],
            'database' => get_resource_config_section('db', 'db_purchase')['db'],
            'username' => get_resource_config_section('db', 'db_purchase')['user'],
            'password' => get_resource_config_section('db', 'db_purchase')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],

        'spu' => [ //spu数据库
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_spu')['host'],
            'database' => get_resource_config_section('db', 'db_spu')['db'],
            'username' => get_resource_config_section('db', 'db_spu')['user'],
            'password' => get_resource_config_section('db', 'db_spu')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => 'lie_',
            'strict' => false,
        ],

        'os_log' => [ //actionlog数据库
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_os_log')['host'],
            'database' => get_resource_config_section('db', 'db_os_log')['db'],
            'username' => get_resource_config_section('db', 'db_os_log')['user'],
            'password' => get_resource_config_section('db', 'db_os_log')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => false,
        ],

        'supplier' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_supp')['host'],
            'database' => get_resource_config_section('db', 'db_supp')['db'],   // liexin_ass
            'username' => get_resource_config_section('db', 'db_supp')['user'],
            'password' => get_resource_config_section('db', 'db_supp')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => 'lie_',
            'strict' => false,
            'engine' => null,
        ],
        'flow' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_flow')['host'],
            'database' => get_resource_config_section('db', 'db_flow')['db'],   // liexin_flow
            'username' => get_resource_config_section('db', 'db_flow')['user'],
            'password' => get_resource_config_section('db', 'db_flow')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],
        'ucenter' => [
            'driver' => 'mysql',
            'host' => get_resource_config_section('db', 'db_ucenter')['host'],
            'database' => get_resource_config_section('db', 'db_ucenter')['db'],   // liexin_ucenter
            'username' => get_resource_config_section('db', 'db_ucenter')['user'],
            'password' => get_resource_config_section('db', 'db_ucenter')['passwd'],
            'port' => 3306,
            'charset' => 'utf8',
            'collation' => 'utf8_general_ci',
            'prefix' => '',
            'strict' => false,
            'engine' => null,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [
        'cluster' => false,
        'default' => [
            'host'     => env('REDIS_HOST', 'redis.liexindev.me'),
            'password' => env('REDIS_PASSWORD', "d=icDb29mLy2s"),
            'port'     => env('REDIS_PORT', 6379),
            'database' => 0,
        ],
        'read' => [
            'host'     => env('REDIS_READ_HOST', 'redis.liexindev.me'),
            'password' => env('REDIS_READ_PASSWORD', "d=icDb29mLy2s"),
            'port'     => env('REDIS_READ_PORT', 6379),
            'database' => 0,
        ],
        'frq' => [
            'host' => get_resource_config_section('redis', 'frq')['host'],
            'password' => get_resource_config_section('redis', 'frq')['passwd'],
            'port' => get_resource_config_section('redis', 'frq')['port'],
            'database' => 0,
            'prefix' => env('PREFIX', '')
        ],
        'sku' => [
            'host' => get_resource_config_section('redis', 'sku')['host'],
            'password' => get_resource_config_section('redis', 'sku')['passwd'],
            'port' => get_resource_config_section('redis', 'sku')['port'],
            'database' => 0,
            'prefix' => env('PREFIX', '')
        ],
        'spu' => [
            'host' => get_resource_config_section('redis', 'spu')['host'],
            'password' => get_resource_config_section('redis', 'spu')['passwd'],
            'port' => get_resource_config_section('redis', 'spu')['port'],
            'database' => 0,
            'prefix' => env('PREFIX', '')
        ],
        'user' => [
            'host' => get_resource_config_section('redis', 'user')['host'],
            'password' => get_resource_config_section('redis', 'user')['passwd'],
            'port' => get_resource_config_section('redis', 'user')['port'],
            'database' => 0,
            'prefix' => env('PREFIX', '')
        ],
        'user_new' => [
            'host' => get_resource_config_section('redis', 'user_new')['host'],
            'password' => get_resource_config_section('redis', 'user_new')['passwd'],
            'port' => get_resource_config_section('redis', 'user_new')['port'],
            'database' => 0,
            'prefix' => env('PREFIX', '')
        ],
    ],


];
