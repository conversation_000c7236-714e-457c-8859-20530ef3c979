<?php
return [
    // 是否开启报错写入
    'enabled' => boolval(get_resource_config_section('app', 'cube')["monitorDing_enabled"]),

    // curl证书验证, 线下环境不用开启
    'curl_verify' => boolval(get_resource_config_section('app', 'cube')["monitorDing_curl_verify"]),

    'web_name'=>get_resource_config_section('app', 'cube')["monitorDing_web_name"],

    // webhook的值
    'webhook' => get_resource_config_section('app', 'cube')["monitorDing_webhook"],
];
