<?php

use Illuminate\Support\Facades\Route;

//同步相关的路由
Route::namespace('Sync')->group(function () {
    Route::match(["get", "post"], '/test', 'TestController@test');//测试接口

    Route::match(["get", "post"], '/activity/getInfoByCode', 'ActivityController@getInfoByCode');    // 根据活动地址code获得活动信息
    Route::match(["get", "post"], '/activity/getGoodsList', 'ActivityController@getGoodsList');    //根据配置参数,获取商品列表数据
    Route::match(["get", "post"], '/activity/getGoodsCategoryList', 'ActivityController@getGoodsCategoryList');//根据配置参数,获取商品分类列表数据


});
