<?php

use Illuminate\Support\Facades\Route;

//接口相关路由
Route::namespace('Api')->group(function () {
    //附件接口
    Route::match(["get", "post"], '/uploadFile/addFile', 'UploadFileApiController@addFile');    // 新增附件
    //任务列表
    Route::match(["get", "post"], '/task/list', 'TaskController@list');    // 任务列表

    //活动
    Route::match(["get", "post"], '/activity/list', 'ActivityController@list');    // 活动列表
    Route::match(["get", "post"], '/activity/add', 'ActivityController@add');    // 新增活动
    Route::match(["get", "post"], '/activity/update', 'ActivityController@update');    // 更新活动
    Route::match(["get", "post"], '/activity/delete', 'ActivityController@delete');    // 删除活动
    Route::match(["get", "post"], '/activity/getInfo', 'ActivityController@getInfo');    // 获取活动信息
    Route::match(["get", "post"], '/activity/copy', 'ActivityController@copy');    // 复制活动
    Route::match(["get", "post"], '/activity/activityEnable', 'ActivityController@activityEnable');    // 更新活动状态
    Route::match(["get", "post"], '/activity/updatePcData', 'ActivityController@updatePcData');    // 更新pc端数据
    Route::match(["get", "post"], '/activity/updateH5Data', 'ActivityController@updateH5Data');    // 更新h5端数据

    //活动元件
    Route::match(["get", "post"], '/activityElement/list', 'ActivityElementController@list');    // 活动元件列表
    Route::match(["get", "post"], '/activityElement/add', 'ActivityElementController@add');    // 新增活动元件
    Route::match(["get", "post"], '/activityElement/update', 'ActivityElementController@update');    // 更新活动元件
    Route::match(["get", "post"], '/activityElement/delete', 'ActivityElementController@delete');    // 删除活动元件
    Route::match(["get", "post"], '/activityElement/updateStatus', 'ActivityElementController@updateStatus');  // 更新活动元件状态
    Route::match(["get", "post"], '/activityElement/getInfo', 'ActivityElementController@getInfo');    // 获取活动元件信息
    Route::match(["get", "post"], '/activityElement/checkLottery', 'ActivityElementController@checkLottery');  // 验证抽奖id
    Route::match(["get", "post"], '/activityElement/checkCoupon', 'ActivityElementController@checkCoupon');   //验证优惠券编码是否存在
    Route::match(["get", "post"], '/activityElement/checkActivityPriceId', 'ActivityElementController@checkActivityPriceId');   //验证活动价id
    Route::match(["get", "post"], '/activityElement/checkSupplierIds', 'ActivityElementController@checkSupplierIds'); //验证供应商id是否存在
    Route::match(["get", "post"], '/activityElement/checkSupplierCodes', 'ActivityElementController@checkSupplierCodes');   //验证供应商编码是否存在
    Route::match(["get", "post"], '/activityElement/getSkuIdsByFile', 'ActivityElementController@getSkuIdsByFile'); // 通过文件获取skuId
    Route::match(["get", "post"], '/activityElement/deleteSkuNameByActivityId', 'ActivityElementController@deleteSkuNameByActivityId'); // 删除活动id
    Route::match(["get", "post"], '/activityElement/getGoodsList', 'ActivityElementController@getGoodsList'); // 根据配置参数,获取商品列表数据
    Route::match(["get", "post"], '/activityElement/getZhuanYingSupList', 'ActivityElementController@getZhuanYingSupList'); // 获取专营渠道

    //价格活动
    Route::match(["get", "post"], '/priceActivity/getPriceActivityList', 'PriceActivityController@getPriceActivityList'); //获取价格活动列表
    Route::match(["get", "post"], '/priceActivity/savePriceActivity', 'PriceActivityController@savePriceActivity'); //保存价格活动
    Route::match(["get", "post"], '/priceActivity/publishPriceActivity', 'PriceActivityController@publishPriceActivity'); //发布价格活动
    Route::match(["get", "post"], '/priceActivity/deletePriceActivity', 'PriceActivityController@deletePriceActivity'); //删除价格活动
    Route::match(["get", "post"], '/priceActivity/exportPriceActivityStatistics', 'PriceActivityController@exportPriceActivityStatistics'); //删除价格活动


    //公用数据接口
    Route::match(["get", "post"], '/commonData/handleData', 'CommonDataController@handleData'); //处理数据接口
    Route::match(["get", "post"], '/commonData/getSupplierList', 'CommonDataController@getSupplierList'); //获取供应商列表
    Route::match(["get", "post"], '/commonData/getSelfClassList', 'CommonDataController@getSelfClassList'); //获取自营分类
    Route::match(["get", "post"], '/commonData/getCanalList', 'CommonDataController@getCanalList'); //获取供应商编码列表
    Route::match(["get", "post"], '/commonData/checkBrandNameList', 'CommonDataController@checkBrandNameList'); //校验普通品牌
    Route::match(["get", "post"], '/commonData/checkStandardBrandNameList', 'CommonDataController@checkStandardBrandNameList'); //校验标准品牌
    Route::match(["get", "post"], '/commonData/searchStandardBrand', 'CommonDataController@searchStandardBrand'); //搜索标准品牌

    //优惠券
    Route::match(["get", "post"], '/coupon/getCouponList', 'CouponController@getCouponList'); //获取优惠券列表
    Route::match(["get", "post"], '/coupon/saveCoupon', 'CouponController@saveCoupon'); //保存优惠券
    Route::match(["get", "post"], '/coupon/analysisIssueCouponFile', 'CouponController@analysisIssueCouponFile'); //解析发放优惠券文件
    Route::match(["get", "post"], '/coupon/issueCoupon', 'CouponController@issueCoupon'); //发放优惠券
    Route::match(["get", "post"], '/coupon/deleteCoupon', 'CouponController@deleteCoupon'); //删除优惠券
    Route::match(["get", "post"], '/coupon/reviewCoupons', 'CouponController@reviewCoupons'); //审核优惠券
    //用户优惠券
    Route::match(["get", "post"], '/userCoupon/getUserCouponList', 'UserCouponController@getUserCouponList'); //获取用户优惠券列表
    Route::match(["get", "post"], '/userCoupon/exportUserCouponList', 'UserCouponController@exportUserCouponList'); //导出用户优惠券列表

    //抽奖活动管理
    Route::match(["get", "post"], '/lottery/getLotteryList', 'LotteryController@getLotteryList'); //获取抽奖活动列表
    Route::match(["get", "post"], '/lottery/saveLottery', 'LotteryController@saveLottery'); //保存抽奖活动
    Route::match(["get", "post"], '/lottery/deleteLottery', 'LotteryController@deleteLottery'); //删除抽奖活动

    //用户中奖管理
    Route::match(["get", "post"], '/userLottery/getUserLotteryList', 'UserLotteryController@getUserLotteryList'); //获取用户中奖列表
    Route::match(["get", "post"], '/userLottery/addUserLottery', 'UserLotteryController@addUserLottery'); //新增用户中奖
    Route::match(["get", "post"], '/userLottery/issueUserLottery', 'UserLotteryController@issueUserLottery'); //发放用户中奖
    Route::match(["get", "post"], '/userLottery/issuePrizeOther', 'UserLotteryController@issuePrizeOther'); //平台发放现金券

    //商品售价组
    Route::match(["get", "post"], '/goodsSalePriceGroup/list', 'GoodsSalePriceGroupController@list'); //获取商品售价组列表
    Route::match(["get", "post"], '/goodsSalePriceGroup/add', 'GoodsSalePriceGroupController@add'); //新增商品售价组
    Route::match(["get", "post"], '/goodsSalePriceGroup/importGoodsNames', 'GoodsSalePriceGroupController@importGoodsNames'); //新增商品售价组导入型号
    Route::match(["get", "post"], '/goodsSalePriceGroup/update', 'GoodsSalePriceGroupController@update'); //编辑商品售价组
    Route::match(["get", "post"], '/goodsSalePriceGroup/disable', 'GoodsSalePriceGroupController@disable'); //商品售价组列表-禁用启用
    Route::match(["get", "post"], '/goodsSalePriceGroup/getGoodsSalePriceGroupInfo', 'GoodsSalePriceGroupController@getGoodsSalePriceGroupInfo'); //获取商品售价组详情
    Route::match(["get", "post"], '/goodsSalePriceGroup/getPriorityLevel', 'GoodsSalePriceGroupController@getPriorityLevel'); //获取商品售价组优先级
    Route::match(["get", "post"], '/goodsSalePriceGroup/getOperationLogs', 'GoodsSalePriceGroupController@getOperationLogs'); //获取商品售价组操作日志

    //渠道折扣管理
    Route::match(["get", "post"], '/channelDiscount/list', 'ChannelDiscountController@list'); //获取渠道组列表
    Route::match(["get", "post"], '/channelDiscount/disable', 'ChannelDiscountController@disable'); //启用禁用渠道折扣
    Route::match(["get", "post"], '/channelDiscount/add', 'ChannelDiscountController@add'); //新增用渠道折扣
    Route::match(["get", "post"], '/channelDiscount/update', 'ChannelDiscountController@update'); //编辑用渠道折扣
    Route::match(["get", "post"], '/channelDiscount/getChannelDidInfo', 'ChannelDiscountController@getChannelDidInfo'); //渠道折扣详情
    Route::match(["get", "post"], '/channelDiscount/getPriorityLevel', 'ChannelDiscountController@getPriorityLevel'); //获取渠道折扣优先级
    Route::match(["get", "post"], '/channelDiscount/getRelationChanDis', 'ChannelDiscountController@getRelationChanDis'); //获取扣渠道关联的折扣信息

    //商品价格体系
    Route::match(["get", "post"], '/goodsPriceSystem/list', 'GoodsPriceSystemController@list'); //获取商品价格体系列表


    //供应商特殊币种配置
    Route::match(["get", "post"], '/price/currencyConfig/addCurrencyConfig', 'GoodsPriceSystemController@addCurrencyConfig');//新增供应商特殊币种配置
    Route::match(["get", "post"], '/price/getCurrencyConfigList', 'GoodsPriceSystemController@getCurrencyConfigList');//获取供应商特殊币种配置列表
    Route::match(["get", "post"], '/price/disableCurrencyConfig', 'GoodsPriceSystemController@disableCurrencyConfig');//启用禁用供应商特殊币种配置
    Route::match(["get", "post"], '/price/updateUsToCnStatus', 'GoodsPriceSystemController@updateUsToCnStatus');//更新美金转人民币状态


    //价格预警记录
    Route::match(["get", "post"], '/priceWarning/list', 'PriceWarningController@list'); //获取价格预警列表
    Route::match(["get", "post"], '/priceWarning/dealwith', 'PriceWarningController@dealwith'); //标记已处理
    Route::match(["get", "post"], '/priceWarning/recertify', 'PriceWarningController@recertify'); //重新验证


    //汇率查询
    Route::match(["get", "post"], '/rate/getRateList', "RateController@getRateList"); //获取汇率列表
    Route::match(["get", "post"], '/rate/getErpRate', "RateController@getErpRate"); //获取汇率列表

    //日志管理
    Route::match(["get", "post"], '/log/list', "LogController@getLogList"); //日志列表接口
    Route::match(["get", "post"], '/log/getUserList', "LogController@getUserList"); //日志列表获取操作用户

    //自定义表单数据管理
    Route::match(["get", "post"], '/customFormData/getCustomFormDataList', 'CustomFormDataController@getCustomFormDataList'); //获取自定义表单数据列表
    Route::match(["get", "post"], '/customFormData/getCustomFormData', 'CustomFormDataController@getCustomFormData'); //查看表单数据具体信息
    Route::match(["get", "post"], '/customFormData/exportCustomFormData', 'CustomFormDataController@exportCustomFormData'); //导出表单数据

    //自定义价格模块
    Route::match(["get", "post"], '/customPrice/getCustomPriceList', 'CustomPriceController@getCustomPriceList'); //获取自定义价格列表
    Route::match(["get", "post"], '/customPrice/saveCustomPrice', 'CustomPriceController@saveCustomPrice'); //保存自定义价格
    Route::match(["get", "post"], '/customPrice/auditCustomPrice', 'CustomPriceController@auditCustomPrice'); //审核自定义价格
    Route::match(["get", "post"], '/customPrice/enableCustomPrice', 'CustomPriceController@enableCustomPrice'); //启用禁用自定义价格

    //订单规则管理
    Route::match(["get", "post"], '/minOrderRule/getMinOrderRuleList', 'MinOrderRuleController@getMinOrderRuleList'); //获取订单规则列表
    Route::match(["get", "post"], '/minOrderRule/saveMinOrderRule', 'MinOrderRuleController@saveMinOrderRule'); //保存订单规则
    Route::match(["get", "post"], '/minOrderRule/deleteMinOrderRule', 'MinOrderRuleController@deleteMinOrderRule'); //删除订单规则
    Route::match(["get", "post"], '/minOrderRule/changeStatus', 'MinOrderRuleController@changeStatus'); //启用禁用订单规则
    Route::match(["get", "post"], '/minOrderRule/getOperationLogs', 'MinOrderRuleController@getOperationLogs'); //获取操作日志

    //运费规则管理
    Route::match(["get", "post"], '/shippingRule/getShippingRuleList', 'ShippingRuleController@getShippingRuleList'); //获取运费规则列表
    Route::match(["get", "post"], '/shippingRule/saveShippingRule', 'ShippingRuleController@saveShippingRule'); //保存运费规则
    Route::match(["get", "post"], '/shippingRule/deleteShippingRule', 'ShippingRuleController@deleteShippingRule'); //删除运费规则
    Route::match(["get", "post"], '/shippingRule/changeStatus', 'ShippingRuleController@changeStatus'); //启用禁用运费规则
    Route::match(["get", "post"], '/shippingRule/getOperationLogs', 'ShippingRuleController@getOperationLogs'); //获取操作日志

    // 其他费用规则管理
    Route::match(["get", "post"], '/otherFeeRule/getOtherFeeRuleList', 'OtherFeeRuleController@getOtherFeeRuleList'); //获取其他费用规则列表
    Route::match(["get", "post"], '/otherFeeRule/saveOtherFeeRule', 'OtherFeeRuleController@saveOtherFeeRule'); //保存其他费用规则
    Route::match(["get", "post"], '/otherFeeRule/deleteOtherFeeRule', 'OtherFeeRuleController@deleteOtherFeeRule'); //删除其他费用规则
    Route::match(["get", "post"], '/otherFeeRule/changeStatus', 'OtherFeeRuleController@changeStatus'); //启用禁用其他费用规则
    Route::match(["get", "post"], '/otherFeeRule/getOperationLogs', 'OtherFeeRuleController@getOperationLogs'); //获取操作日志
});
