CREATE TABLE `shipping_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
  `supplier_code` varchar(50) NOT NULL DEFAULT '' COMMENT '供应商编码',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1启用 2禁用 3删除',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '配置方式：1固定金额 2按重量计算',
  `rule` text NOT NULL COMMENT '具体规则，JSON字符串',
  `create_uid` int(11) NOT NULL DEFAULT '0' COMMENT '创建人ID',
  `create_name` varchar(50) NOT NULL DEFAULT '' COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_uid` int(11) NOT NULL DEFAULT '0' COMMENT '更新人ID',
  `update_name` varchar(50) NOT NULL DEFAULT '' COMMENT '更新人姓名',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_supplier_code` (`supplier_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='运费规则表';
