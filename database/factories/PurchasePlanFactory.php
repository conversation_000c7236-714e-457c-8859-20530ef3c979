<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\HTTP\Models\PurchasePlanModel;
use Faker\Generator as Faker;

$factory->define(PurchasePlanModel::class, function (Faker $faker) {
    return [
        //
        'purchase_plan_sn' => $faker->name,
        'company_name' => $faker->company(),
        'company_id' => $faker->numberBetween(10000, 2000),
        'currency' => $faker->numberBetween(1, 2),
        'create_uid' => 1000,
        'create_name' => 'admin',
        'create_time' => time(),
        'delivery_warehouse_id' => $faker->numberBetween(1, 100),
        'delivery_warehouse_name' => $faker->company(),
        'status' => $faker->numberBetween(-3, 2),
        'remark' => '',
    ];
});
