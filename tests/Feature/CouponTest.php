<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Support\Facades\Http;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Http\Models\Cube\UserCouponModel;
use App\Http\Models\Cube\CouponModel;
use Illuminate\Support\Facades\DB;
use Mockery;

class CouponTest extends TestCase
{
    /**
     * 测试获取最佳优惠券功能
     *
     * @return void
     */
    public function testGetBestCoupon()
    {
        // 模拟商品服务的响应
        Http::fake([
            config('website.goods_server') . '/synchronization*' => Http::response([
                'errcode' => 0,
                'data' => [
                    [
                        'goods_id' => 123,
                        'supplier_id' => 10000, // 自营供应商ID
                        'brand_id' => 456,
                        'class_id' => 789,
                        'goods_type' => 3,  // 自营商品类型
                        'canal' => 'test_canal'
                    ],
                    [
                        'goods_id' => 456,
                        'supplier_id' => 20000, // 联营供应商ID
                        'brand_id' => 789,
                        'class_id' => 123,
                        'goods_type' => 1,  // 联营商品类型
                        'canal' => 'test_canal2'
                    ]
                ]
            ], 200)
        ]);

        // 模拟优惠券仓库
        $mockRepository = Mockery::mock('App\\Repositories\\CouponRepository');
        $mockRepository->shouldReceive('getAvailableCoupons')
            ->once()
            ->andReturn(collect([
                (object) [
                    'user_coupon_id' => 1,
                    'coupon_id' => 101,
                    'coupon_type' => 1, // 抵扣券
                    'sale_amount' => 10,
                    'require_amount' => 100,
                    'coupon_mall_type' => 1, // 全站
                    'coupon_goods_range' => 1, // 全站
                    'max_preferential_amount' => 0,
                    'coupon' => (object) [
                        'coupon_name' => '满100减10'
                    ]
                ],
                (object) [
                    'user_coupon_id' => 2,
                    'coupon_id' => 102,
                    'coupon_type' => 2, // 折扣券
                    'sale_amount' => 0.8, // 8折
                    'require_amount' => 100,
                    'coupon_mall_type' => 1, // 全站
                    'coupon_goods_range' => 1, // 全站
                    'max_preferential_amount' => 50,
                    'coupon' => (object) [
                        'coupon_name' => '满100打八折'
                    ]
                ]
            ]));

        $this->app->instance('App\\Repositories\\CouponRepository', $mockRepository);

        // 发送请求（使用JSON格式）
        $response = $this->postJson('/api/open/coupon/getBestCoupon', [
            'user_id' => 1000,
            'goods_list' => [
                ['goods_id' => 123, 'price' => 100],
                ['goods_id' => 456, 'price' => 200]
            ],
            'org_id' => 1
        ]);

        // 断言响应状态码
        $response->assertStatus(200);

        // 打印响应内容，用于调试
        echo "Response: " . json_encode($response->json(), JSON_PRETTY_PRINT);

        // 断言响应结构
        $response->assertJsonStructure([
            'status',
            'data' => [
                'user_coupon_id',
                'coupon_id',
                'coupon_name',
                'coupon_type',
                'sale_amount',
                'preferential'
            ]
        ]);

        // 断言返回的是折扣券（因为优惠金额更高）
        $response->assertJson([
            'status' => true,
            'data' => [
                'user_coupon_id' => 2,
                'coupon_id' => 102,
                'coupon_type' => 2,
                'coupon_name' => '满100打八折'
            ]
        ]);
    }

    /**
     * 测试当没有可用优惠券时的情况
     */
    public function testGetBestCouponWithNoCoupons()
    {
        // 模拟优惠券仓库
        $mockRepository = Mockery::mock('App\\Repositories\\CouponRepository');
        $mockRepository->shouldReceive('getAvailableCoupons')
            ->once()
            ->andReturn(collect([])); // 返回空集合

        $this->app->instance('App\\Repositories\\CouponRepository', $mockRepository);

        // 发送请求（使用JSON格式）
        $response = $this->postJson('/api/open/coupon/getBestCoupon', [
            'user_id' => 1000,
            'goods_list' => [
                ['goods_id' => 123, 'price' => 100]
            ],
            'org_id' => 1
        ]);

        // 断言响应状态码
        $response->assertStatus(200);

        // 断言返回错误信息
        $response->assertJson([
            'status' => false,
            'message' => '没有可用的优惠券'
        ]);
    }

    /**
     * 测试完成后清理模拟
     */
    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
